<?php

use App\Constants\PlatformConst;
use App\Models\Shop;
use App\Providers\Socialite\AlbbProvider;
use App\Providers\Socialite\Alc2mProvider;
use App\Providers\Socialite\CnProvider;
use App\Providers\Socialite\DyProvider;
use App\Providers\Socialite\KsProvider;
use App\Providers\Socialite\JdProvider;
use App\Providers\Socialite\PddProvider;
use App\Providers\Socialite\WxProvider;
use App\Providers\Socialite\TaobaoProvider;
use App\Providers\Socialite\WxspProvider;
use App\Providers\Socialite\XhsProvider;
use App\Services\Client\DyClient;
use App\Services\Client\KsClient;
use App\Services\Client\WxClient;
use App\Utils\LogUtil;
use FireSoil\TopClient\Facades\TopClient as TopClientFacade;
use Illuminate\Contracts\Bus\Dispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use Overtrue\Socialite\SocialiteManager;
use TopClient\TopClient;

if (!function_exists('TopDecryptClient')) {
	/**
	 * 解密订单数据
	 * @param string $originalValue
	 * @param string $type
	 * @param string $session
	 * @return bool|string
	 */
	function TopDecryptClient(string $originalValue, string $type, string $session)
	{
		$c             = new \App\Services\Decrypt\TopClient();
		$c->appkey     = env('TB_CLIENT_ID', '21339677');
		$c->secretKey  = env('TB_CLIENT_SECRET', '77cfb02adae3828a53870540757e2e62');
		$c->gatewayUrl = env('TB_GATEWAYURL', 'https://eco.taobao.com/router/rest');;
		$randrom       = env('TB_RANDOM', 'YhLTJuYnhJE8XkPFtASAjASMAdD2WClI9JkcMgQc9PI=');

		$client = new \App\Services\Decrypt\Security\SecurityClient($c, $randrom);
		$yac    = new \App\Services\Decrypt\Security\YacCache();
		$client->setCacheClient($yac);

		if (!$client->isEncryptData($originalValue, $type)) {
			return false;
		}

		return $client->decrypt($originalValue, $type, $session);
	}
}

if (!function_exists('dataDesensitization')) {
	/**
	 * 数据脱敏
	 * @param $string
	 * @param array $regular
	 * @param string $re
	 * @return bool|string
	 */
	function dataDesensitization($string, $regular = [1, 3, 5], $re = '*')
	{
		if (empty($string)) {
			return false;
		}
		$strarr = array();
		$mb_strlen = mb_strlen($string);
		while ($mb_strlen) {//循环把字符串变为数组
			$strarr[] = mb_substr($string, 0, 1, 'utf8');
			$string = mb_substr($string, 1, $mb_strlen, 'utf8');
			$mb_strlen = mb_strlen($string);
		}

		$strlen = count($strarr);

		for ($i = 1; $i <= $strlen; $i++) {
			if (in_array($i + 1, $regular)) {
				$strarr[$i] = $re;
			}
		}

		return implode('', $strarr);
	}
}

if (!function_exists('dataDesensitizationOpt')) {
    /**
     * 数据脱敏
     * @param $string
     * @param array $regular
     * @param string $re
     * @return bool|string
     */
    function dataDesensitizationOpt($string, $regular = [1, 3, 5], $re = '*')
    {
        if (empty($string)) {
            return '';
        }
        $strarr = preg_split('//u', $string, -1, PREG_SPLIT_NO_EMPTY);
        $strlen = count($strarr);
        for ($i = 1; $i <= $strlen; $i++) {
            if (in_array($i + 1, $regular)) {
                $strarr[$i] = $re;
            }
        }
        return implode('', $strarr);
    }
}



if (!function_exists('handleOrderIdStr')) {
	/**
	 * 拼接订单ID字符串
	 * @param $order
	 * @return string
	 */
    if (!function_exists('handleOrderIdStr')) {
        /**
         * 拼接订单ID字符串
         * @param $order
         * @param array $orderItemOId
         * @return string
         */
        function handleOrderIdStr($order, array $orderItemOId=[]): string
        {
            $orderIdsArr = [];
            $orderId = $order['id'];
            if (!empty($orderItemOId) && array_key_exists($orderId, $orderItemOId)) {
                $orderId = $order['id'] . ':' . implode(',', $orderItemOId[$orderId]);
            }
            if (isset($order->mergeOrders) && count($order->mergeOrders) > 0) {
                $orderIdsArr = collect($order->mergeOrders)->pluck('id')->toArray();
                foreach ($orderIdsArr as $key=>$item) {
                    if (!empty($orderItemOId) && array_key_exists($item, $orderItemOId)) {
                        $orderIdsArr[$key] = $item . ':' . implode(',', $orderItemOId[$item]);
                    }
                }
            }
            array_push($orderIdsArr, $orderId);

            return implode('_', collect($orderIdsArr)->sort()->all());
        }
    }
}
/**
 * 拼接订单ID字符串
 * @param $order
 * @return string
 */
if (!function_exists('handlePackIdStr')) {
    /**
     * 拼接订单ID字符串
     * @param $pack
     * @return string
     */
    function handlePackIdStr($pack)
    {
        return implode(',',array_pluck($pack['orders'],'oid'));
    }
}

if (!function_exists('clearStrSpecialChar')) {
	/**
	 * 特殊符号清除
	 * @param string $str
	 * @return string
	 */
    function clearStrSpecialChar(string $str)
    {
        /**
         * 字符串特殊符号处理
         * \x{4e00}-\x{9fa5} 中文
         * a-zA-Z0-9 英文数字
         * \x21-\x7e ascii码表
         * \s 空格
         */

        $pattern ='/[\x{4e00}-\x{9fa5}a-zA-Z0-9\x21-\x7e\s·~@#%…&*￥！（）—\\-+={}|《》？：“”【】、；‘\'，。]/u';
        preg_match_all($pattern,$str,$result);
        return join('', $result[0]);
        return $string;
    }
}

if (!function_exists('trimBlankChars')) {
    /**
     * 清除空白字符: 空格、制表符、换行符
     * @param string $str
     * @return string
     */
    function trimBlankChars(string $str): string//删除空格
    {
        $str = (string)$str;
        $search = array("\t", "\n", "\r"," ");
        $replace = array("", "", "","");
        return str_replace($search, $replace, $str);

    }
}

if (!function_exists('result')) {

    /**
     * 常用返回值处理
     *
     * @param boolean $success
     * @param string $message
     * @param array $data
     * @return array
     */
    function result(bool $success, string $message = "", array $data = []): array
    {
        return array_merge([
            "success" => $success,
            "message" => $message,
        ], $data);
    }
}


if (!function_exists('getMicrotime')) {

	function getMicrotime() {
		list($msec, $sec) = explode(' ', microtime());
		return (float)sprintf('%.0f', (floatval($msec) + floatval($sec)) * 1000);
	}
}

if (!function_exists('makeNo')) {

    /**
     * 生成32位编号
     * @param string $sign length 6
     * @return string
     */
    function makeNo(string $sign): string
    {
        $mic = explode(" ", microtime());
        return $sign . date('YmdHis') . rand(1000, 9999) . substr($mic[0], 2);
    }
}

if (!function_exists('socialite')) {
    /**
     * 授权管理器
     * @return SocialiteManager
     * <AUTHOR>
     */
    function socialite()
    {
        $config = array_merge(config('socialite', []), config('services', []));
        $socialiteManager = new SocialiteManager($config, app('request'));
        $socialiteManager->extend(PlatformConst::TAOBAO, function ($config) {
            $configTmp = $config->get(PlatformConst::TAOBAO);
            return new TaobaoProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::PDD, function ($config) {
            $configTmp = $config->get(PlatformConst::PDD);
            return new PddProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::KS, function ($config) {
            $configTmp = $config->get(PlatformConst::KS);
            return new KsProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
	    $socialiteManager->extend(PlatformConst::WX, function ($config) {
		    $configTmp = $config->get(PlatformConst::WX);
		    return new WxProvider(
			    app('request'),
			    $configTmp['client_id'],
			    $configTmp['client_secret'],
			    $configTmp['redirect']
		    );
	    });
        $socialiteManager->extend(PlatformConst::WXSP, function ($config) {
            $configTmp = $config->get(PlatformConst::WXSP);
            return new WxspProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']
            );
        });
        $socialiteManager->extend(PlatformConst::JD, function ($config) {
            $configTmp = $config->get(PlatformConst::JD);
            return new JdProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::DY, function ($config) {
            $configTmp = $config->get(PlatformConst::DY);
            return new DyProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::XHS, function ($config) {
            $configTmp = $config->get(PlatformConst::XHS);
            return new XhsProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::ALBB, function ($config) {
            $configTmp = $config->get(PlatformConst::ALBB);
            return new AlbbProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::ALC2M, function ($config) {
            $configTmp = $config->get(PlatformConst::ALC2M);
            return new Alc2mProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        $socialiteManager->extend(PlatformConst::CN, function ($config) {
            $configTmp = $config->get(PlatformConst::CN);
            return new CnProvider(
                app('request'),
                $configTmp['client_id'],
                $configTmp['client_secret'],
                $configTmp['redirect']);
        });
        return $socialiteManager;
    }
}

if (!function_exists('topClient')) {
    /**
     * @return TopClient
     * <AUTHOR>
     */
    function topClient()
    {
        return TopClientFacade::connection();
    }
}

if (!function_exists('dyClient')) {
    /**
     * @return DyClient
     */
    function dyClient()
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        return (new DyClient($appKey, $secretKey));
    }
}

if (!function_exists('ksClient')) {
    /**
     * @return KsClient
     */
    function ksClient()
    {
        $appKey = config('socialite.ks.client_id');
        $secretKey = config('socialite.ks.client_secret');
        return new KsClient($appKey, $secretKey);
    }
}

if (!function_exists('wxClient')) {
    /**
     * @return WxClient
     */
    function wxClient()
    {
        $appKey    = config('socialite.wx.client_id');
        $secretKey = config('socialite.wx.client_secret');
        return new WxClient($appKey, $secretKey);
    }
}


if (!function_exists('objectToArray')) {

    function objectToArray($d)
    {
        if (is_object($d)) {
            // Gets the properties of the given object
            // with get_object_vars function
            $d = get_object_vars($d);
        }

        if (is_array($d)) {
            /*
            * Return array converted to object
            * Using __FUNCTION__ (Magic constant)
            * for recursive call
            */
            return array_map(__FUNCTION__, $d);
        } else {
            // Return array
            return $d;
        }
    }
}

if (!function_exists('redis')) {

    /**
     *
     * @param string $name
     * @return Predis\Client
     * <AUTHOR>
     */
    function redis($name = 'default')
    {
        return Redis::connection($name);
    }
}

if (!function_exists('redisGlobal')) {
    /**
     * 全局 redis
     * 跨项目用，没有redis key 的前缀。
     * @param string $name
     * @return Predis\Client
     * <AUTHOR>
     */
    function redisGlobal($name = 'default'): \Predis\Client
    {
        $config = config('database.redis.' . $name);
        return new Predis\Client([
            'scheme' => 'tcp',
            'host' => $config['host'],
            'port' => $config['port'],
            'password' => $config['password'],
            'database' => $config['database'],
        ]);
    }
}

if (!function_exists('formatToYuan')) {

    /**
     * 格式化成元
     * @param string $amount
     * @return string
     * <AUTHOR>
     */
    function formatToYuan($amount)
    {
        if ((int)$amount == 0){
            return 0;
        }
        return number_format($amount / 100, 2, '.', '');
    }
}

if (!function_exists('getRandStr')) {

    /**
     * 取得随机字符串
     *
     * @param int $length
     * @param string $type
     * @param string $prefix
     * @return string
     */
    function getRandStr($length = 32, $type = '', $prefix = '')
    {
        if ($type == 'num') {
            $chars = "123456789";
        } elseif ($type == 'str') {
            $chars = "abcdefghijklmnopqrstuvwxyz";
        } elseif ($type == 'cap') {
            $chars = "ABCDEFGHIJKLMNPQRSTUVWXYZ0123456789";
        } else {
            $chars = "ABCDEFGHIJKLMNPQRSTUVWXYZ0123456789";
        }

        $str = "";
        for ($i = 0; $i < $length; $i++) {
            $str .= substr($chars, mt_rand(0, strlen($chars) - 1), 1);
        }

        if ($prefix) {
            $str = $prefix . $str;
        }

        return $str;
    }

	/**
	 * 判断字符串是否为 Json 格式
	 *
	 * @param  string  $data  Json 字符串
	 * @param  bool    $assoc 是否返回关联数组。默认返回对象
	 *
	 * @return array|bool|object 成功返回转换后的对象或数组，失败返回 false
	 */
	function isJson($data = '', $assoc = false) {
		$data = json_decode($data, $assoc);
		if (($data && is_object($data)) || (is_array($data) && !empty($data))) {
			return $data;
		}
		return false;
	}

    if (!function_exists('throw_error_code_exception')) {
        function throw_error_code_exception(array $statusCode, $data = null, $info = '')
        {
            !empty($info) && $statusCode[1] = $statusCode[1] .':'. $info;
            throw new \App\Exceptions\ErrorCodeException($statusCode, $data, $info);
        }
    }
    if (!function_exists('encryptApiSign')) {
        /**
         * @param $array array 请求参数数组
         * @param $secret string app_key
         * @return string
         */
        function encryptApiSign($array, $secret)
        {
            ksort($array);
            $text = http_build_query($array);

            return md5(urldecode($text) . $secret);
        }
    }
    if (!function_exists('decryptApiSign')) {
        function decryptApiSign($serverArray, $secret)
        {
            $clientSign = $serverArray['sign'];
            unset($serverArray['sign']);
            $reServerSign = encryptApiSign($serverArray, $secret);
            $res = $clientSign == $reServerSign ? true : false;

            if (!$res) {
                \Log::info('签名错误 params:' .json_encode($serverArray) . ' client_sign:'. $clientSign . ' server_sign:'.$reServerSign);
            }
            return $res;
        }
    }
    if (!function_exists('decryptApiSignV3')) {
        function decryptApiSignV3($params, $secret)
        {
            $clientSign = $params['sign'];
            unset($params['sign']);
            ksort($params);
            $params = array_map(function ($item) {
                if (is_array($item)) {
                    $item = jsonEncode($item);
                }
                return $item;
            }, $params);
            $text = urldecode(http_build_query($params));
            $reServerSign =  md5($text .'@'. $secret);
//            $reServerSign = encryptApiSign($serverArray, $secret);
            return $clientSign == $reServerSign;
        }
    }
    if (!function_exists('encryptApiSignV4')) {
        function encryptApiSignV4(string $app_id, string $app_key, $timestamp, string $biz_json)
        {
            $str = "$app_id&$timestamp&$app_key@$biz_json";
//            Log::debug('encryptApiSignV4:'.$str);
//            Log::debug('encryptApiSignV4Md5:'.md5($str));
            return md5($str);
        }
    }

    if (!function_exists('dataDesensitizationForOpenApi')) {
        function dataDesensitizationForOpenApi($string, $start = 0, $length = 0, $re = '*')
        {
            if (empty($string)) {
                return false;
            }
            if (mb_strlen($string) <= $start) {
                return $string;
            }
            $strarr = array();
            $mb_strlen = mb_strlen($string);
            while ($mb_strlen) {//循环把字符串变为数组
                $strarr[] = mb_substr($string, 0, 1, 'utf8');
                $string = mb_substr($string, 1, $mb_strlen, 'utf8');
                $mb_strlen = mb_strlen($string);
            }
            $strlen = count($strarr);
            $begin = $start >= 0 ? $start : ($strlen - abs($start));
            $end = $last = $strlen - 1;
            if ($length > 0) {
                $end = $begin + $length - 1;
            } elseif ($length < 0) {
                $end -= abs($length);
            }
            for ($i = $begin; $i <= $end; $i++) {
                $strarr[$i] = $re;
            }
            if ($begin > $end || $begin > $last || $end > $last) return false;
            return implode('', $strarr);
        }
    }

    if (!function_exists( 'addressDesensitization')) {
        function addressDesensitization($string, $re= "*") {
            $mb_strlen = mb_strlen($string);
            $strarr = [];
            //循环把字符串变为数组
            while ($mb_strlen) {
                $strarr[] = mb_substr($string, 0, 1, 'utf8');
                $string = mb_substr($string, 1, $mb_strlen, 'utf8');
                $mb_strlen = mb_strlen($string);
            }
            $result = [];
            foreach ($strarr as $key => $value) {
                // 详细地址第2 5 6 11 12位掩码
                if (in_array($key, [1,4,5,10,11])) {
                    $result[$key] = $re;
                } elseif (preg_match('/([0-9]|零|一|二|三|四|五|六|七|八|九|十)/', $value)) {
                    $result[$key] = '*';
                } else{
                    $result[$key] = $value;
                }
            }
            return implode('', $result);
        }
    }
    if (!function_exists('request')) {
        /**
         * Get an instance of the current request or an input item from the request.
         *
         * @param string $key
         * @param mixed $default
         * @return Request|string|array
         */
        function request($key = null, $default = null)
        {
            if (is_null($key)) {
                return app('request');
            }
            return app('request')->input($key, $default);
        }
    }
    if (!function_exists('redisDelByEqual')) {

        /**
         * redis 删除 key，值相等才删除
         * @param $redisKey string redis key
         * @param $identity string 标识，值相等才删除
         * @param string $name
         * @return mixed
         *<AUTHOR>
         */
        function redisDelByEqual(string $redisKey, string $identity, $name = 'default')
        {
            $script =  <<<LUA
if redis.call('get', KEYS[1]) == ARGV[1]
then
    return redis.call('del', KEYS[1])
else
    return 0
end
LUA;
            return redis($name)->eval($script, 1, $redisKey, $identity);
        }
    }

    if (!function_exists('getSqlByQuery')) {
        /**
         * <AUTHOR>
         * @param $query
         * @return string
         */
        function getSqlByQuery($query): string
        {
            // 处理 like 的 %
            $sql = str_replace('%', '%%', $query->toSql());
            $sql = str_replace('?', '"%s"', $sql);
            return sprintf($sql, ...$query->getBindings());
        }
    }
}

if (!function_exists('isPhoneNumber')) {

    /**
     * 是否是手机号
     * <AUTHOR>
     * @param string $str
     * @return false|int
     */
    function isPhoneNumber(string $str)
    {
        return preg_match("/^1[3-9]\d{9}$/", $str);
    }
}
if (!function_exists('isFactory')) {

    /**
     * 是否是厂家
     * <AUTHOR>
     * @return false|int
     */
    function isFactory()
    {
        return config('app.factory') == 1;
    }
}

if (!function_exists('timeToDateTimeStr')) {

    /**
     * 时间戳转日期时间
     * @param int $time
     * @return string
     * <AUTHOR>
     */
    function timeToDateTimeStr(int $time): string
    {
        return date('Y-m-d H:i:s', $time);
    }
}
if (!function_exists('batchLock')) {
    /**
     * 批量加锁
     * @param string $prefix
     * @param array $lockIdArr
     * @param Closure $callback
     * @param int $second
     * @return mixed|null
     * @throws \App\Exceptions\ApiException
     */
    function batchLock(string $prefix, array $lockIdArr, Closure $callback, int $second = 60)
    {
        $lockSuccessArr = [];
        $lockIdentity = uniqid();
        $redis    = redis('cache');
        $res = null;
        try {
            // 订单加锁 60秒超时
            foreach ($lockIdArr as $lockId) {
                $redisKey = $prefix . $lockId;
                $bool = $redis->set($redisKey, $lockIdentity, 'nx', 'ex' , $second);
                if (!$bool) {
                    // 任意一个加锁失败
                    throw new \App\Exceptions\ApiException(\App\Constants\ErrorConst::SYSTEM_LOCK_ERROR);
                }
                $lockSuccessArr[] = $lockId;
            }
            $res = $callback();
        } finally {
            // 解锁
            foreach ($lockSuccessArr as $lockId) {
                $redisKey = $prefix . $lockId;
                redisDelByEqual($redisKey, $lockIdentity, 'cache');
            }
        }
        return $res;
    }
}
if (!function_exists('transformBool')) {

    /**
     * 转换 前端的 bool字段 成 bool 类型
     * @param $mixedBool
     * @return bool
     * <AUTHOR>
     */
    function transformBool($mixedBool): bool
    {
        switch ($mixedBool) {
            case true:
            case 'true':
            case '1':
                $bool = true;
                break;
            case 'false':
            case '0':
            case '':
            case false:
            default:
                $bool = false;
                break;
        }
        return $bool;
    }
}
if (!function_exists('ksortByRecursion')) {

    /**
     * 递归版的 ksort
     * @param  $array
     * @param int $flags
     * @return bool
     * <AUTHOR>
     */
    function ksortByRecursion(&$array, int $flags = SORT_REGULAR): bool
    {
        if (!is_array($array)) {
            return false;
        }
        ksort($array);
        foreach ($array as $k=>$v) {
            ksortByRecursion($array[$k]);
        }
        return true;
    }
}

if (!function_exists('recursionCamelCase')) {

    /**
     * 递归把字段转小驼峰
     * <AUTHOR>
     * @param array $data
     * @return array
     */
    function recursionCamelCase(array $data)
    {
        $arr = [];
        foreach ($data as $key => $value) {
            $newKey = $key;
            if (is_string($key)) {
                $newKey = Str::camel($key);
            }
            if (is_array($value)) {
                $value = recursionCamelCase($value);
            }
            $arr[$newKey] = $value;
        }
        return $arr;
    }
}

if (!function_exists('request')) {
    /**
     * @return \Illuminate\Http\Request
     */
    function request()
    {
        return app(\Illuminate\Http\Request::class);
    }
}
if (!function_exists('getRealIp')) {
    function getRealIp()
    {
        $host_ip = request()->header('x-forwarded-for', request()->ip());
        $host_ips = explode(',', $host_ip);
        return array_get($host_ips, 0, '');
    }
}

if (!function_exists('createShopCode')) {
    function createShopCode()
    {
        do {
            $shopCode = md5(session_create_id());
            $count = \App\Models\Shop::query()->where('shop_code', $shopCode)->count();
        } while($count != 0);
        return $shopCode;
    }
}

if (!function_exists('getRangeDate')) {
    function getRangeDate($startDate, $endDate)
    {
        $array = [];
        $startDate = date('Y-m-d', strtotime($startDate));
        $endDate = date('Y-m-d', strtotime($endDate));
        $start = strtotime($startDate);
        $end = strtotime($endDate);
        // 防止程序异常出现死循环，一年最多366天(闰年)所以这里给此固定值
        $limit = 366;
        $i = 1;
        while ($limit--) {
            $next = strtotime("+$i day", $start);
            $i++;
            if ($next < $end) {
                $array[] = date('Y-m-d', $next);
            } else {
                break;
            }
        }

        array_unshift($array, $startDate);
        array_push($array, $endDate);

        return $array;
    }
}

if (!function_exists('ksEncryptSwitch')) {
    function ksEncryptSwitch($shopId)
    {
        return true;
        if (!\App\Utils\Environment::isKs()) {
            return false;
        }
        // 重复调用也只会请求一次
        global $ksEncryptSwitchRet;
        global $ksEncryptShopRet;
        //快手密文总开关
        $ksEncryptSwitch = 'ks_encrypt_switch';
        //快手密文测试店铺
        $ksEncryptShop = 'ks_encrypt_shop';

        if (empty($ksEncryptSwitchRet) || empty($ksEncryptShopRet)) {
            $ksEncryptSwitchRet = Cache::remember($ksEncryptSwitch, 60, function () use ($ksEncryptSwitch) {
                return \App\Models\SystemConfig::query()->where('key', $ksEncryptSwitch)->first();
            });
//            $ksEncryptSwitchRet = \App\Models\SystemConfig::query()->where('key', $ksEncryptSwitch)->first();
            $ksEncryptShopRet = Cache::remember($ksEncryptShop, 60, function () use ($ksEncryptShop) {
                return \App\Models\SystemConfig::query()->where('key', $ksEncryptShop)->first();
            });
//            $ksEncryptShopRet = \App\Models\SystemConfig::query()->where('key', $ksEncryptShop)->first();
        }
        if (!empty($ksEncryptSwitchRet)) {
            if ($ksEncryptSwitchRet->value) {
                return true;
            } else {
                if (!empty($ksEncryptShopRet)) {
                    $ksEncryptShopArr = json_decode($ksEncryptShopRet->value, true);
                    if (in_array($shopId, $ksEncryptShopArr)) {
                        return true;
                    }
                }
            }
            return false;
        }

        return false;
    }
}

if (!function_exists("filterZeroChar")) {
    // 过滤 0 宽字符
    function filterZeroChar($data) {
        $str = json_encode($data,true);//转换为Unicode编码

        $patterns     = []; //正则表达式
        $replacements = []; //替换成的字符
        //公共
        $patterns[0] = '/®/';
        $replacements[0] = '';

        //零宽字符&#8203;
        $patterns[1] = '/&#8203;/';
        $replacements[1] = '';

        //零宽字符&#8203;
        $patterns[2] = '#\\\u200[bcde]#us';
        $replacements[2] = '';
        $str = preg_replace($patterns, $replacements, $str);
        $str = json_decode($str);//解码Unicode编码

        return $str;
    }
}

if (!function_exists("StatisticsCost")) {
    function StatisticsCost($info) {
        $cost = round((microtime(true) - LARAVEL_START) * 1000, 2);
        \Log::info($info.' 总耗时:'.$cost);
    }
}

if (!function_exists('GenerateSignForCommission')) {
    function GenerateSignForCommission($formParams, $pushData)
    {
        //生成签名
        $mergeData = array_merge($pushData, $formParams);
        ksort($mergeData);
        $params = array_map(function ($item) {
            if (is_array($item)) {
                $item = json_encode($item, JSON_UNESCAPED_UNICODE);
            }
            return $item;
        }, $mergeData);
        $text = urldecode(http_build_query($params));
        $sign = md5($text . '@' . 'f59304d8fe5115236376589e6d55ca2d');

        return $sign;
    }
}
if (!function_exists("getWpCodesByUnionWpCode")) {
    /**
     * 通过 unionWpCode 获取对应的 wpCode 列表
     * <AUTHOR>
     * @param string $unionWpCode
     * @return array|mixed
     */
    function getWpCodesByUnionWpCode(string $unionWpCode) {
        $express_company_list = config('express_company');
        $express_company_list = collect($express_company_list)->groupBy('unionWpCode')->map(function ($items){
            return collect($items)->pluck('wpCode')->unique()->toArray();
        })->toArray();
        return $express_company_list[$unionWpCode] ?? [];
    }
}
if (!function_exists("jsonEncode")) {
    /**
     * json 编码 不转移中文和斜杠
     * <AUTHOR>
     * @param mixed $value
     * @return false|string
     */
    function jsonEncode($value) {
        return json_encode($value, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
}

//if (! function_exists('dispatch')) {
//    /**
//     * Dispatch a job to its appropriate handler.
//     *
//     * @param  mixed  $job
//     * @return mixed
//     */
//    function dispatch($job)
//    {
//
//        $jobName=get_class($job);
//        \Log::info('触发任务'.$jobName);
//        $queues = config("xhprof.Queues");
//        $matchQueue=false;
//        foreach ($queues as $queue){
//            if(empty($queue)){
//                continue;
//            }
//            if(str_contains($jobName,$queue)){
//                $matchQueue=true;
//                break;
//            }
//        }
//        $rand = rand(0, 100);
//        $isProfile = config("xhprof.QueueEnable") && $matchQueue && $rand < config("xhprof.QueueRate");
//        if($isProfile){
//            xhprof_enable(XHPROF_FLAGS_CPU + XHPROF_FLAGS_MEMORY, []);
//        }
//        $response = app(Dispatcher::class)->dispatch($job);
//        return $response;
//        if($isProfile){
//            $xhprofData = xhprof_disable();
//            $xhprofRuns = new XHProfRuns_Default();
//            $xhprofRuns->save_run($xhprofData, 'xhprof_laravel');
//        }
//        return $response;
//    }
//}

if (!function_exists("appEncrypt")) {
    /**
     * 对称加密
     * <AUTHOR>
     * @param $value
     * @return string
     */
    function appEncrypt($value) {
        $encrypt = openssl_encrypt($value, config('app.cipher'), config('app.key'), 0, mb_substr(config('app.key'), 0, 16));
        return base64_encode($encrypt);
    }
}
if (!function_exists("appDecrypt")) {
    /**
     * 对称解密
     * <AUTHOR>
     * @param $value
     * @return string
     */
    function appDecrypt($value) {
        $value = base64_decode($value);
        return openssl_decrypt($value, config('app.cipher'), config('app.key'), 0, mb_substr(config('app.key'), 0, 16));
    }
}
if (!function_exists("valueIsNull")) {
    /**
     * 判断值是空的
     * @param mixed $value
     * @param bool $filterZero 是否过滤0的值，true 0被判断成空，false 0不被判断成空
     * @return bool
     */
    function valueIsNull($value, bool $filterZero = true): bool
    {
        $value = is_string($value) ? trim($value) : $value;
        $haystack = [null, 'null', '', 'undefined'];
        if ($filterZero) {
            $haystack[] = 0;
        }
        return in_array($value, $haystack, true);
    }
}

/**
 * 如果没有mb_str_split 函数，定义一个
 */
if(!function_exists("mb_str_split")){
    function mb_str_split($str, $length = 1, $encoding = 'UTF-8'):array
    {
        $result = array();
        for ($i = 0; $i < mb_strlen($str, $encoding); $i += $length) {
            $result[] = mb_substr($str, $i, $length, $encoding);
        }
        return $result;
    }
}
/**
 * 获取所有省级行政区域
 */
if(!function_exists("getAllProvinces")){
    function getAllProvinces(): array
    {
        $provinces = array(
            "黑龙江省",
            "吉林省",
            "辽宁省",
            "河北省",
            "甘肃省",
            "青海省",
            "陕西省",
            "河南省",
            "山东省",
            "江苏省",
            "浙江省",
            "安徽省",
            "福建省",
            "江西省",
            "广东省",
            "海南省",
            "四川省",
            "贵州省",
            "云南省",
            "山西省",
            "湖北省",
            "湖南省",
            "广西壮族自治区",
            "内蒙古自治区",
            "西藏自治区",
            "新疆维吾尔自治区",
            "宁夏回族自治区",
            "北京市",
            "天津市",
            "上海市",
            "重庆市",
            "香港特别行政区",
            "澳门特别行政区"
        );
        return $provinces;
    }
}
if(!function_exists("getAllProvincesBy2Words")){
    function getAllProvincesBy2Words(): array
    {
        return array(
            "黑龙",
            "吉林",
            "辽宁",
            "河北",
            "甘肃",
            "青海",
            "陕西",
            "河南",
            "山东",
            "江苏",
            "浙江",
            "安徽",
            "福建",
            "江西",
            "广东",
            "海南",
            "四川",
            "贵州",
            "云南",
            "山西",
            "湖北",
            "湖南",
            "广西",
            "内蒙",
            "西藏",
            "新疆",
            "宁夏",
            "北京",
            "天津",
            "上海",
            "重庆",
            "香港",
            "澳门"
        );
    }
}
/**
 * 批量处理订单号后面带 A 的
 */
if(!function_exists("handleOrderIdsWithA")){
    function handleOrderIdsWithA($orderIdArr): array
    {
        $arr = [];
        if (empty($orderIdArr)){
            return $arr;
        }
        foreach ($orderIdArr as $item) {
            if (\App\Utils\Environment::isDy() && strpos($item, 'A') === false) {
                $arr[] = $item . 'A';
            }else{
                $arr[] = $item;
            }
        }
        return $arr;
    }
}

/**
 * 判断是否直辖市
 */
if(!function_exists("isAddressMunicipality")){
    function isAddressMunicipality($cityName): bool
    {
        // 定义中国直辖市列表
        $municipalities = [
            '北京市',
            '天津市',
            '上海市',
            '重庆市',
        ];

        // 判断城市名是否在直辖市列表中
        return in_array($cityName, $municipalities);
    }
}

if (!function_exists("tidAddA")){
    function tidAddA($value): string
    {
        $value = strval($value);
        if (!\App\Utils\Environment::isDy()) {
            return $value;
        }
        if(!ends_with($value,"A")){
            $value = $value."A";
        }
        return $value;
    }
}

/**
 * 循环数组，判断元素结尾是否包含A，不包含则添加A
 */
if (!function_exists("batchAddA")){
    function batchAddA(array $arr): array
    {
        foreach ($arr as $key => $value){
            $arr[$key] = tidAddA($value);
        }
        return $arr;
    }
}

if(!function_exists("isPhoneNumber")){
    function isPhoneNumber($number) {
        // 正则表达式匹配11位数字且以1开头的手机号码
        $pattern = '/^1[0-9]{10}$/';
        // 使用preg_match进行匹配，返回值为0表示不匹配，非0表示匹配成功
        if (preg_match($pattern, $number)) {
            return true;
        } else {
            return false;
        }
    }
}


/**
 * 加法
 */
if (!function_exists('round_bcadd')) {
    function round_bcadd(string $left_operand, string $right_operand, int $scale = 2): string
    {
        return bcround(bcadd($left_operand, $right_operand, $scale + 1), $scale);
    }
}
/**
 * 减法

 */
if(!function_exists('round_bcsub')){
    function round_bcsub(string $left_operand, string $right_operand, int $scale = 2): string
    {
        return bcround(bcsub($left_operand, $right_operand, $scale + 1), $scale);
    }
}



if(!function_exists('bcround')){
    /**
     * 四舍五入
     * @param string $number
     * @param int $precision
     * @return string
     */
    function bcround(string $number, int $precision): string
    {
        if ($precision >= 0) {
            if (str_contains($number, '.')) {
                if ($number[0] != '-')
                    return bcadd($number, '0.' . str_repeat('0', $precision) . '5', $precision);
                return bcsub($number, '0.' . str_repeat('0', $precision) . '5', $precision);
            }
            return $number;
        } else {
            $pow = bcpow(10, -$precision);
            return bcmul(bcround(bcdiv($number, $pow, -$precision), 0), $pow);
        }
    }
}


if(!function_exists('round_bcmul')){
    function round_bcmul(string $left_operand, string $right_operand, int $scale = 2): string
    {
        return bcround(bcmul($left_operand, $right_operand, $scale + 1), $scale);
    }
}


if(!function_exists('round_bcsub')){

    /**
     * 减法
     * @param string $left_operand
     * @param string $right_operand
     * @param int $scale
     * @return string
     */
    function round_bcsub(string $left_operand, string $right_operand, int $scale = 2): string
    {
        return bcround(bcsub($left_operand, $right_operand, $scale + 1), $scale);
    }
}


if(!function_exists('bc_max')){

    /**
     * 两个字符串数字进行比，返回大的那个
     * 用bc_bc
     */
    function bc_max(string $left_operand, string $right_operand): string{
        return bccomp($left_operand, $right_operand) > 0 ? $left_operand : $right_operand;
    }
}


if(!function_exists('get_array_value')){
    /**
     * 获取数组内容，调用的是ArrayUtil::getArrayValue方法
     * @see \App\Utils\ArrayUtil::getArrayValue()
     */
    function get_array_value(array $array ,string $key, $default = null)
    {
        return \App\Utils\ArrayUtil::getArrayValue($array,$key,$default);
    }
}
if(!function_exists('gen_waybill_wp_index')){

    /**
     * 生成运单和包裹的索引
     */
    function gen_waybill_wp_index(string $waybill_code, string $wp_code): string
    {
        return $waybill_code . '-' . $wp_code;
    }
}
if(!function_exists('format_exception')){

    /**
     * 格式化异常输出
     */
    function format_exception(Exception $e): array
    {
        return [
            'message' => $e->getMessage(),
            'class' => get_class($e),
            'code' => $e->getCode(),
            'line' => $e->getLine(),
            'file' => $e->getFile(),
            'trace' => explode(PHP_EOL, $e->getTraceAsString()),
        ];
    }
}

if(!function_exists('jdJcqLog')){
    function jdJcqLog()
    {
        return LogUtil::jdJcq();
    }
}


if(!function_exists('removeNonChineseFirstChar')){
    /**
     * 去除非中文首个字符
     */
    function removeNonChineseFirstChar($str) {
        // 检查第一个字符是否是中文
        if (preg_match('/^[\x{4e00}-\x{9fff}]/u', $str)) {
            // 如果是中文，直接返回原字符串
            return $str;
        } else {
            // 如果不是中文，移除第一个字符
            return substr($str, 1);
        }
    }

}

if(!function_exists('strIsTid')){

    /**
     * 字符串是否是订单号
     * @param $str
     * @return bool
     */
    function strIsTid($str)
    {
        // jd  303865285920
        // dy  6935282838992918432A
        // pdd 241015-331024985662452
        //
        if (\App\Utils\Environment::isDy()){
            $str = rtrim($str, 'A');
        }
        // 定义正则表达式模式，
        $pddPattern = '/^\d{6}-\d{15}$/';

        if (is_numeric($str) && strlen($str) > 11) {
            return true;
        }elseif(preg_match($pddPattern, $str)){
            return true;
        }
        return false;
    }
}

if(!function_exists('groupByProperty')){

    /**
     * 按照某个属性分组
     * @param $objects
     * @param $property
     * @return array
     */
    function groupByProperty($objects, $property): array
    {
        $grouped = [];
        foreach ($objects as $object) {
            if (isset($object->$property)) {
                $groupKey = $object->$property;
                if (!isset($grouped[$groupKey])) {
                    $grouped[$groupKey] = [];
                }
                $grouped[$groupKey][] = $object;
            }
        }
        return $grouped;
    }
}
