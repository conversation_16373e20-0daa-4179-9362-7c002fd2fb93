<?php
/** @var <PERSON><PERSON>\Lumen\Routing\Router $router */
$router->group([
    'prefix' => '',
    'middleware' => ['logger', 'check.token', 'hide.privacy', 'rate.limiter:600,1'],
], function () use ($router) {
    $router->get('/user/me', 'UserController@me');
    $router->get('/get_shop_code', 'UserController@getShopCode');
    $router->get('/get_shops_by_user', 'UserController@getShops');
    $router->get('/get_all_bind_shops', 'Shop\ShopBindController@getAllBindShops');
//    $router->get('/get_user_templates', 'Template\TemplateController@getTemplates');

    $router->get('/get_user_templates_by_api', 'Template\TemplateController@getTemplatesByApi');
    $router->get('/get_my_all_companies', 'CompanyController@myCompanies');
    $router->get('/get_pop_waybill_accounts', 'CompanyController@getPopWaybillAccounts');
    $router->get('/shop/change/{shopId}', 'UserController@changeShop');
    $router->post('/shop/dy/mcTokens', 'Shop\DyController@getMcTokens');
    $router->post('/shop/jd/npsTokens', 'Shop\JdController@getNpsTokens');
    $router->post('/user/check_auth', 'UserController@checkAuth');
    $router->post('/user/change-password', 'UserController@changePwd');
    $router->put('/user/change/phone', 'UserController@changePhone');
    $router->post('/user/edition', 'UserController@saveUserEdition');
    $router->post('/waybill-compare/upload', 'Waybill\WaybillHistoryCompareController@upload');
    $router->post('/waybill-compare/count', 'Waybill\WaybillHistoryCompareController@count');
    $router->post('/waybill-compare/get_Compare_Data', 'Waybill\WaybillHistoryCompareController@getCompareData');
    $router->post('/user/change-auth', 'Shop\ShopBindController@changeAuth');
    $router->post('/user/regen-invite-code', 'UserController@reGenInviteCode');
    $router->post('/user/find-invitor', 'UserController@findInvitor');
    $router->delete('/user/{id}', 'Shop\ShopBindController@unbindShop');
    $router->post('/user/batch-unbind', 'Shop\ShopBindController@batchUnbindShop');
    // $router->post('/user/bind-invite-code', 'UserController@bindInviteCode');
    $router->get('/user/branch_shop_list', 'UserController@getBranchShopList');
    $router->post('/user/editBindShop', 'UserController@editBindShop');
    //查询用户续费订购
    $router->get('/check-order', 'PlatformOrderController@checkOrder');
    $router->post('/user/login-success', 'UserController@loginSuccess');
    $router->get('/common/export_list', 'CommonController@exportList');
    $router->get('/user/syncTokenFromNew', 'UserController@syncTokenFromNew');

    $router->get('activity/list', 'ActivityController@list');
    //sms短信相关
    $router->group([
        'prefix' => 'sms',
    ], function () use ($router) {
        $router->post('save/phone', 'SmsController@savePhone');
        $router->post('change/password', 'SmsController@changePassword');
    });

    //电子面单授权相关
    $router->group([
        'prefix' => 'waybill',
    ], function () use ($router) {
        $router->post('/', 'WaybillController@index');
        $router->post('waybill_auth_last', 'WaybillController@waybillLast');
        $router->get('waybill_search', 'WaybillController@waybillSearch');
        $router->get('waybill_query', 'WaybillController@waybillQuery');
        $router->get('waybill_search/{ownerId}', 'WaybillController@getWaybillByOwnerId');
        $router->post('auth_url', 'WaybillController@authUrl');
        $router->post('auth', 'WaybillController@auth');
        $router->delete('/{ownerId}', 'WaybillController@unbind');
        $router->post('add_waybill_share', 'WaybillController@addWaybillShare');
        $router->post('waybill_search_get', 'WaybillController@getWaybillByOwnerIdAndBranch');
        $router->post('get_share_waybill', 'WaybillController@getShareWaybill');
        $router->post('add_share_template', 'WaybillController@addShareTemplate');

    });

    //云仓

    $router->group([
        'prefix' => 'warehouse',
    ], function () use ($router) {
        $router->post('summary_report', 'Warehouse\WarehouseReportController@summaryReport');
        $router->get('export', 'Warehouse\WarehouseReportController@export');
    });


    //单号分享
    $router->group([
        'prefix' => 'waybill_share',
    ], function () use ($router) {
        $router->post('/log', 'Waybill\WaybillShareActionController@queryWaybillShareLog');
        $router->post('/waybill_share_statistic', 'Waybill\WaybillShareActionController@statisticWaybill');
        $router->post('/query_waybill_share_detail', 'Waybill\WaybillShareActionController@queryShareWaybillDetail');
        $router->post('query_waybillaccount_page', 'Waybill\WaybillShareActionController@queryWaybillAccountPage');
        $router->put('add_waybill_share_count/{id}', 'Waybill\WaybillShareActionController@addShareCount');
        $router->put('remove_waybill_share_count/{id}', 'Waybill\WaybillShareActionController@removeShareCount');
        $router->put('update_waybill_share/{id}', 'Waybill\WaybillShareActionController@updateShare');
        $router->post('show_waybillcode_count', 'Waybill\WaybillShareActionController@showWaybillcodeCount');
        $router->get('/export', 'Waybill\WaybillShareActionController@export');
        $router->get('/export_list', 'Waybill\WaybillShareActionController@exportList');

    });

    //快递电子面单
    $router->group([
        'prefix' => 'template',
    ], function () use ($router) {
        $router->get('/common_templates', 'Template\TemplateController@commonTemplates');
        $router->get('/platform_templates', 'Template\TemplateController@getPlatformDefinedTemplates');
        $router->get('/get_cloud_templates', 'Template\TemplateController@getCloudPrintStdTemplates');
        $router->get('/custom_area_contents', 'Template\TemplateController@customAreaContents');
        $router->post('/', 'Template\TemplateController@add');
        $router->post('/customerge', 'Template\TemplateController@custoMergeSave');
        $router->post('/print', 'Template\TemplateController@print');
        $router->get('/judge', 'Template\TemplateController@judge');
        $router->put('/{id}/default', 'Template\TemplateController@default');
        $router->put('/{id}/default_print', 'Template\TemplateController@defaultPrint');
        $router->put('/{id}', 'Template\TemplateController@edit');
        $router->post('/updateCompany', 'Template\TemplateController@updateCompany');
        $router->post('/updateServiceList', 'Template\TemplateController@updateServiceList');
        $router->post('/updatePrintContent', 'Template\TemplateController@updatePrintContent');
        $router->delete('/{id}', 'Template\TemplateController@delete');
    });

    //合并单模板组合
    $router->group([
        'prefix' => 'customerge',
    ], function () use ($router) {
        $router->post('/{id}', 'TemplateMergeController@getTemplateMerge');
        $router->post('/', 'TemplateMergeController@add');
        $router->put('/{id}', 'TemplateMergeController@edit');
        $router->delete('/{id}', 'TemplateMergeController@delete');
    });


    //快递公司
    $router->group([
        'prefix' => 'company',
    ], function () use ($router) {
        $router->get('/', 'CompanyController@index');
        $router->post('/', 'CompanyController@add');
        $router->put('/{id}', 'CompanyController@edit');
        $router->post('/{id}/status', 'CompanyController@switch');
    });

    //发货地址
    $router->group([
        'prefix' => 'shipping_address',
    ], function () use ($router) {
        $router->get('/', 'ShippingAddressController@index');
        $router->get('/default', 'ShippingAddressController@getDefault');
        $router->put('/{id}/default', 'ShippingAddressController@default');
        $router->put('/{id}', 'ShippingAddressController@edit');
        $router->post('/', 'ShippingAddressController@add');
        $router->delete('/{id}', 'ShippingAddressController@delete');
        $router->get('/search', 'ShippingAddressController@ReceiverSearch');
        $router->get('/recipient', 'ShippingAddressController@ReceiverList');
        $router->put('/recipient/{id}/default', 'ShippingAddressController@ReceiverDefault');
    });


    //自由打印
    $router->group([
        'prefix' => 'customize_order',
    ], function () use ($router) {
        $router->get('/', 'CustomizeOrderController@index');
        $router->get('/statistic', 'CustomizeOrderController@statistic');
        $router->get('/auto_relation', 'CustomizeOrderController@autoRelation');
        $router->post('/', 'CustomizeOrderController@add');
        $router->post('/print', 'CustomizeOrderController@printNew');
        $router->post('/batch', 'CustomizeOrderController@batchDelete');
        $router->post('/get_old_WaybillCode', 'CustomizeOrderController@getOldWaybillCode');
        $router->post('/get_waybill_history', 'CustomizeOrderController@getWaybillHistory');
        $router->put('/{id}', 'CustomizeOrderController@edit');
        $router->post('/batch_add_manual_orders', 'CustomizeOrderController@upload');
        $router->post('/notify_print_result', 'CustomizeOrderController@notifyPrintResult');
        $router->post('/waybill_recovery', 'CustomizeOrderController@waybillRecovery');
        $router->delete('/{id}', 'CustomizeOrderController@destroy');
        $router->post('/waybill_recovery_code', 'CustomizeOrderController@batchRecoveryCode');
        $router->post('/failed_order_list', 'CustomizeOrderController@failedOrderList');
        $router->post('/upload_failed_order', 'CustomizeOrderController@uploadFailedOrder');
        $router->post('/delete_failed_order', 'CustomizeOrderController@deleteFailedOrder');
        $router->post('/batchDecryption', 'CustomizeOrderController@batchDecryption');

    });

    //发货记录
    $router->group([
        'prefix' => 'delivery',
    ], function () use ($router) {
        $router->get('/', 'DeliveryController@index');
        $router->get('/export', 'DeliveryController@asyncExport');
        $router->get('/browser_download', 'DeliveryController@browserDownload');
    });

    //退款管理
    $router->group([
        'prefix' => 'after_sale',
    ], function () use ($router) {
        $router->get('/', 'AfterSaleController@index');
        $router->post('/agree_refund', 'AfterSaleController@agreeRefund');
        $router->post('/refuse_refund', 'AfterSaleController@refuseRefund');
        $router->post('/agree_return', 'AfterSaleController@agreeReturn');
        $router->post('/refuse_return', 'AfterSaleController@refuseReturn');
        $router->post('/get_refund_reject_reason', 'AfterSaleController@getRefundRejectReason');
        $router->get('/get_refund_reject_address', 'AfterSaleController@getRefundRejectAddress');
    });

    //发货单与备货单
    $router->group([
        'prefix' => 'delivery_template',
    ], function () use ($router) {
        $router->get('/list', 'DeliveryTemplateNewController@getDeliveryTemplate');
        $router->post('/save', 'DeliveryTemplateNewController@add');
        $router->post('/delete', 'DeliveryTemplateNewController@delete');
        $router->put('/update/{id}', 'DeliveryTemplateNewController@edit');

        $router->post('/', 'DeliveryTemplateController@add');
        $router->post('/store', 'DeliveryTemplateController@store');
        $router->post('/pick', 'DeliveryTemplateController@pickTemplate');
        $router->get('/pick', 'DeliveryTemplateController@getpickTemplate');
        $router->get('/', 'DeliveryTemplateController@getDeliveryTemplate');
        $router->get('/store', 'DeliveryTemplateController@getStoreTemplate');
        $router->put('/{id}', 'DeliveryTemplateController@edit');
        $router->put('/store/{id}', 'DeliveryTemplateController@updateStore');
    });

    //打印日志
    $router->group([
        'prefix' => 'print_record',
    ], function () use ($router) {
        $router->get('/', 'PrintRecordController@index');
        $router->get('/export', 'PrintRecordController@export');
        $router->get('/export_list', 'PrintRecordController@exportList');
    });

    //取号记录
    $router->group([
        'prefix' => 'waybill_history',
    ], function () use ($router) {
        $router->get('/', 'Waybill\WaybillHistoryController@index');
        $router->get('/statistics', 'Waybill\WaybillHistoryController@statistics');
        $router->post('/recovery', 'Waybill\WaybillHistoryController@recovery');
        $router->get('/export', 'Waybill\WaybillHistoryController@asyncExport');
        $router->post('/exportRetry', 'Waybill\WaybillHistoryController@exportRetry');
        $router->get('/browser_download', 'Waybill\WaybillHistoryController@browserDownload');
    });
    $router->group([
        'prefix' => 'remark',
    ], function () use ($router) {
        $router->get('/index', 'RemarkController@index');
        $router->post('/create', 'RemarkController@create');
        $router->post('/update', 'RemarkController@update');
        $router->post('/delete', 'RemarkController@delete');
    });

    //平台订单
    $router->group([
        'prefix' => 'order',
    ], function () use ($router) {
        $router->post('/', 'OrderController@index');
        $router->post('/valid_orders', 'OrderController@validOrders');
        $router->put('/receiver', 'OrderController@editReceiver');
        $router->put('/address', 'OrderController@editAddress');
        $router->post('/tab_num', 'OrderController@tabNum');
        $router->post('/refund_tab_num', 'OrderController@refundTabNum');
        $router->post('{id}/seller_note_list', 'Order\OrderEditController@sellerNoteList');
        $router->post('/print', 'Order\OrderPrintController@print');
        $router->post('/printWarehouse', 'Order\OrderPrintController@printWarehouse');
        $router->post('/printTag', 'Order\OrderPrintController@printTag');
        $router->post('/delivery', 'Order\OrderDeliveryController@delivery');
        $router->post('/redelivery', 'Order\OrderDeliveryController@redelivery');
        $router->post('/san_delivery', 'Order\OrderDeliveryController@scanDeliver');
        $router->post('/san_delivery_v2', 'Order\OrderDeliveryController@scanDeliverV2');
        $router->post('/cancel_preshipment', 'Order\OrderPreshipmentController@cancelPreshipment');
        $router->post('/batch_notify_online', 'Order\OrderDeliveryController@uploadDelivery');
        $router->post('/customDelivery', 'Order\OrderDeliveryController@customDelivery');
        $router->post('/preshipmentDelivery', 'Order\OrderPreshipmentController@preshipmentDelivery');
//        $router->post('/waybill_recovery', 'OrderController@waybillRecovery');
        $router->post('/waybill_recovery_nums', 'OrderController@waybillRecovery');
        $router->post('/lock', 'OrderController@lock');
        $router->post('/notify_print_result', 'OrderController@notifyPrintResult');
        $router->post('/notify_print_result_no', 'OrderController@notifyPrintResultNo');
        $router->post('/notify_print_result_index', 'OrderController@notifyPrintResultIndex');
        $router->post('/sync/pull', 'OrderController@syncPull');
        $router->post('/set_flag', 'OrderController@setFlag');
        $router->post('/batch_set_remark', 'OrderController@setRemark');
        $router->post('/batch_edit_remark', 'Order\OrderEditController@batchSetRemark');
        // 订单统计 备货单
        $router->post('/order_statistics', 'Order\OrderStatisticsController@orderStatistics');
        $router->post('/order_statistics_summary', 'Order\OrderStatisticsController@orderStatisticsSummary');
        $router->post('/order_stocking', 'Order\OrderStatisticsController@orderStocking');
//        $router->post('/order_sku_value', 'Order\OrderStatisticsController@getskuValuelist');
        $router->post('/query_goods_name', 'OrderController@queryGoodsName');
        $router->get('/liveOrderItemInfo', 'OrderController@liveOrderItemInfo');
        $router->get('/{tid}', 'OrderController@orderInfo');
        $router->get('/sync/time', 'OrderController@syncTime');
//        $router->post('/sync/condition', 'OrderController@syncCondition');
        $router->post('/decrypt', 'OrderController@decrypt');
        // 同步残余订单
        $router->post('/sync/sync_history_order', 'OrderController@syncHistoryOrder');
        //平台备注
//        $router->put('/platform_memo/{id}', 'Order\OrderEditController@editPlatformMemo');

        $router->post('/search', 'OrderController@postSearch');
        // 同步订单通过时间范围
        $router->post('/sync/order_by_scope', 'Order\OrderSyncController@syncOrderByScope');
        $router->get('/sync/order_schedule', 'Order\OrderSyncController@getSyncOrderSchedule');
        //异常订单数量
        $router->get('/abnormal/count', 'OrderController@abnormalOrderCount');
        //异常订单提醒
        $router->get('/abnormal/list', 'OrderController@abnormalOrderList');
        $router->get('/abnormal/listV2', 'Order2Controller@abnormalOrderListV2');
        //异常订单修改状态
        $router->put('/abnormal/edit', 'OrderController@editAbnormalOrder');
        //打印前检测订单
        $router->post('/check_order', 'OrderController@printCheck');
        $router->post('/check_waybill', 'Order\OrderPrintController@printCheckWaybill');
        $router->post('/check_orders', 'Order\OrderPrintController@printCheckOrders');
        // 手动拆单
        $router->put('/split', 'OrderController@orderSplit');
        // 包裹管理
        $router->put('/packageManager', 'Order2Controller@packageManager');
        $router->post('/shippedList', 'Order2Controller@shippedList');
        $router->post('/shippedListV2', 'Order2Controller@shippedListV2');
        $router->post('/shippedListV2Count', 'Order2Controller@shippedListV2Count');
        $router->post('/resendList', 'Order2Controller@resendList');
        $router->post('/resendListCount', 'Order2Controller@resendListCount');
        $router->post('/shippedListCount', 'Order2Controller@shippedListCount');
        $router->post('/splitPackageList', 'Order2Controller@splitPackageList');
        $router->post('/preShipmentList', 'Order2Controller@preShipmentList');
        // 批量获取订单信息
        $router->post('/info/batch', 'OrderController@getOrderInfoBatch');
        $router->post('/infoAndItem/batch', 'OrderController@getOrderAndItemInfoBatch');
        $router->post('/decrypt', 'OrderController@decrypt');
        $router->post('/upload_order_sn', 'Order\OrderEditController@uploadOrder');
        $router->post('/set_remark', 'OrderController@batchSetRemark');
        // 获取打印中列表总数
        $router->get('/printing/count', 'OrderController@getPrintingListCount');
        // 获取打印中列表
        $router->get('/printing/list', 'OrderController@getPrintingList');
        // 批量修改打印状态
        $router->put('/print_status', 'OrderController@editPrintStatus');
        //发货统计
        $router->post('/get_delivery_data', 'Order\OrderStatisticsController@getDeliveryData');
        //发货统计新版
        $router->post('/delivery_report', 'Order\OrderStatisticsController@deliveryReport');

        //已发货（按商品）
        $router->post('/delivery_statistics', 'Order\OrderStatisticsController@deliveryStatistics');


        //发货单打印
        $router->post('/shipping_order_log', 'OrderController@shippingOrderLog');
        //获取操作日志
        $router->post('get_order_operation_logs', 'OrderController@getOrderOperationLog');
        //批量修改sku属性
        $router->post('/update_sku_value', 'OrderController@batchUpdateSkuValue');
        //批量更新订单
        $router->post('/batch_update_order', 'OrderController@batchUpdateOrder');
        // 批量通过订单号同步订单
        $router->post('/batch_sync_order_tids', 'OrderController@batchSyncOrderByTids');
        // 获取解密参数
        $router->post('/decryption_params', 'OrderController@getDecryptionParams');
        // 上报解密成功
        $router->post('/report_decryption_success', 'OrderController@reportDecryptionSuccess');

        $router->post('/quality/check-status', 'OrderController@qualityCheckStatus');

        // 厂家
        $router->post('/factory/assign', 'Order\OrderFactoryController@assign');
        $router->post('/factory/assign-cancel', 'Order\OrderFactoryController@assignCancel');
        $router->get('/factory/shipments-order-list', 'Order\OrderFactoryController@getShipmentsOrderList');

        $router->post('/secret/tb-delay', 'Order\OrderSecretController@tbDelay');

        $router->get('/mergable/count', 'Order2Controller@countMergable');
        $router->post('/mergable/count', 'Order2Controller@countMergable');
        $router->post('/mergable/list', 'Order2Controller@getMergableList');
        $router->post('/authorList', 'Order2Controller@authorList');
        $router->post('/livePrintOrderList', 'Order2Controller@livePrintOrderList');
        $router->post('/livePrintOrderComplete', 'Order2Controller@livePrintOrderComplete');
        $router->get('/brief/get', 'Order\OrderInfoController@getOrder');

    });

    //物流信息
    $router->group([
        'prefix' => 'logistic',
    ], function () use ($router) {
        $router->post('/day-statistic-get', 'Logistics\LogisticsController@dayStatisticGet');
        $router->post('/day-statistic-count', 'Logistics\LogisticsController@dayStatisticCount');
        $router->post('/exception-statistic-export', 'Logistics\LogisticsController@exceptionStatisticExport');
        $router->post('/exception-statistic-get', 'Logistics\LogisticsController@exceptionStatisticGet');
        $router->post('/exception-statistic-count', 'Logistics\LogisticsController@exceptionStatisticCount');
        $router->post('/discardOrderTrace', 'Logistics\LogisticsController@discardOrderTrace');
        $router->post('/syncOrderTrace', 'Logistics\LogisticsController@syncOrderTrace');
    });

    //商品信息
    $router->group([
        'prefix' => 'goods',
    ], function () use ($router) {
        $router->get('/', 'Goods\GoodsController@index');
        $router->put('/{id}', 'Goods\GoodsController@edit');
        $router->post('/search', 'Goods\GoodsController@searchGoods');
        $router->post('/edit_goods_by_numiid', 'Goods\GoodsController@editByNumiid');
        $router->post('/batch/set', 'Goods\GoodsController@batchSetAlias');
        $router->post('/batchEdit', 'Goods\GoodsController@batchEdit');
        $router->put('/sku/{id}', 'Goods\GoodsController@skuEdit');
        $router->post('/sync/goods', 'Goods\GoodsController@syncGoods');
        $router->get('/sync/schedule', 'Goods\GoodsController@getSyncGoodsSchedule');
        $router->get('/sync/time', 'Goods\GoodsController@getSyncGoodsTime');
        $router->post('/commodity_sku/save', 'Commodity\CommodityController@saveCommoditySku');
    });

    //标签模板
    $router->group([
        'prefix' => 'tag-template',
    ], function () use ($router) {
        $router->get('/index', 'TagTemplateController@index');
        $router->post('/create', 'TagTemplateController@create');
        $router->post('/update', 'TagTemplateController@update');
        $router->post('/delete', 'TagTemplateController@delete');
    });

    // 黑名单
    $router->group([
        'prefix' => 'blacklist',
    ], function () use ($router) {
        $router->get('/index', 'BlacklistController@index');
        $router->post('/createByOrder', 'BlacklistController@createByOrder');
        $router->post('/delete', 'BlacklistController@delete');
    });

    //用户使用统计
    $router->group([
        'prefix' => 'admin',
        'middleware' => ['check.admin'],
    ], function () use ($router) {
        $router->get('/', 'Admin\AdminController@index');
        $router->get('/order/summary', 'Admin\AdminController@summary');
        $router->get('/user/statistics', 'Admin\AdminController@userStatistics');
        $router->post('/login_token', 'Admin\AdminController@loginToken');
        $router->get('/outer/user', 'Admin\AdminController@outerUserList');
        $router->delete('/outer/user/{id}', 'Admin\AdminController@deleteOuterUser');
        $router->post('/outer/adduser', 'Admin\AdminController@createOuterUser');
        $router->put('/outer/user', 'Admin\AdminController@editOuterUser');
        $router->get('/user/feed-back', 'Admin\AdminController@getUserFeedBack');
        $router->post('/edit_notice', 'Admin\AdminController@editNotice');
        $router->post('/notice_config', 'Admin\AdminController@noticeConfig');
        $router->get('/order/get', 'Admin\AdminController@getOrder');
        $router->get('/other/platform/vas', 'Admin\AdminController@otherPlatformVas');
        $router->get('/platform-order/list', 'Admin\AdminController@platformOrderList');
        $router->put('/platform-order/update', 'Admin\AdminController@updatePlatformOrder');
        $router->post('/platform', 'Admin\PlatformOrderAdminController@search');
        $router->post('/platform/browser_download', 'Admin\PlatformOrderAdminController@browserDownload');
        $router->get('/shop/get', 'Admin\AdminController@getShop');
        $router->post('/shop/sync_switch', 'Admin\AdminController@syncSwitch');
        $router->get('/system-config/getSysConfig', 'Admin\AdminController@getSysConfig');
        $router->post('/system-config/updateSysConfig', 'Admin\AdminController@updateSysConfig');
        $router->post('/ossUpload', 'Admin\AdminController@ossUpload');

        $router->post('/activity/create', 'ActivityController@create');
        $router->post('/activity/update', 'ActivityController@update');
        $router->get('/activity/list', 'ActivityController@list');
        $router->post('/shop/editShopExtra', 'Admin\AdminController@editShopExtra');

        $router->get('/expressCompany/list', 'Admin\ExpressCompanyController@index');
        $router->post('/expressCompany/create', 'Admin\ExpressCompanyController@create');
        $router->post('/expressCompany/update', 'Admin\ExpressCompanyController@update');
        $router->post('/expressCompany/delete', 'Admin\ExpressCompanyController@delete');
    });

    // 查询模板
    $router->group([
        'prefix' => 'query_template',
    ], function () use ($router) {
        $router->get('/', 'QueryTemplateController@index');
        $router->post('/', 'QueryTemplateController@create');
        $router->put('/', 'QueryTemplateController@update');
        $router->delete('/{id}', 'QueryTemplateController@delete');
    });

    // 查询地址区域
    $router->group([
        'prefix' => 'query_area',
    ], function () use ($router) {
        $router->get('/', 'QueryAreaController@index');
        $router->post('/', 'QueryAreaController@create');
        $router->put('/{id}', 'QueryAreaController@update');
        $router->get('/info', 'QueryAreaController@info');
        $router->delete('/{id}', 'QueryAreaController@delete');
        $router->post('/batch_set_order', 'QueryAreaController@batchSetOrder');
    });
    $router->group([
        'prefix' => 'shipping_fee_template',
    ], function () use ($router) {
        $router->get('/list', 'Shipping\ShippingFeeTemplateController@list');
        $router->post('/save', 'Shipping\ShippingFeeTemplateController@save');
        $router->post('/deleteById', 'Shipping\ShippingFeeTemplateController@deleteById');
        $router->get('/company_shipping_fee_templates', 'Shipping\ShippingFeeTemplateController@companyShippingFeeTemplates');
        $router->post("/create_company_shipping_fee_template", 'Shipping\ShippingFeeTemplateController@createCompanyShippingFeeTemplate');
        $router->post("/update_company_shipping_fee_template", 'Shipping\ShippingFeeTemplateController@updateCompanyShippingFeeTemplate');
        $router->post("/delete_company_shipping_fee_template", 'Shipping\ShippingFeeTemplateController@deleteCompanyShippingFeeTemplate');
    });

    //店铺设置
    $router->group([
        'prefix' => 'setting',
    ], function () use ($router) {
        $router->get('/', 'Shop\ShopSettingController@index');
        $router->get('/shop', 'Shop\ShopSettingController@getCurrentShop');
        $router->get('/village_setting', 'Shop\ShopSettingController@villageSetting');
        $router->post('/', 'Shop\ShopSettingController@edit');
        $router->post('/merge_order', 'Shop\ShopSettingController@mergeOrder');
        $router->post('/auto_reset_template', 'Shop\ShopSettingController@autoResetTemplate');
        $router->post('/browser_download', 'Shop\ShopSettingController@browserDownload');
        $router->post('/shop_identifier', 'Shop\ShopSettingController@shopIdentifier');
        $router->post('/change_shop_switch', 'Shop\ShopSettingController@changeShopSwitch');
        $router->post('/update/extra', 'Shop\ShopSettingController@updateExtra');
        $router->get('/extra_info', 'Shop\ShopSettingController@getExtraInfo');
        $router->put('/shop_info', 'Shop\ShopSettingController@updateShopInfo');
        $router->get('/deliver', 'Shop\ShopSettingController@getDeliverSetting');
        $router->post('/deliver', 'Shop\ShopSettingController@setDeliverSetting');
    });
    //店铺
    $router->group([
        'prefix' => 'shop',
    ], function () use ($router) {
        $router->post('/bind/sort', 'Shop\ShopBindController@sort');
        $router->get('/bind/merchant-list', 'Shop\ShopBindController@getMerchantList');
        $router->get('/bind/factory-list', 'Shop\ShopBindController@getFactoryList');
        $router->delete('/bind/factory-shop', 'Shop\ShopBindController@unbindFactoryShop');
        $router->put('/bind/factory-bind-info', 'Shop\ShopBindController@updateFactoryBindInfo');
    });
    //系统设置
    $router->group([
        'prefix' => 'system_config',
    ], function () use ($router) {
        $router->get('/notice_config', 'SystemConfigController@noticeConfig');
    });

    // 地址
    $router->group([
        'prefix' => '/platform_address',
    ], function () use ($router) {
        // 地址列表
        $router->get('/list', 'PlatformAddressController@index');
        $router->post('/update', 'PlatformAddressController@update');
        $router->post('/create', 'PlatformAddressController@create');
        $router->post('/sync', 'PlatformAddressController@syncAddress');
    });


    //意见反馈
    $router->group([
        'prefix' => 'feed_back',
    ], function () use ($router) {
        $router->post('/save', 'FeedBackController@postFeedBack');
        $router->delete('/{id}', 'FeedBackController@delete');
    });

    //异常
    $router->group([
        'prefix' => 'exception',
    ], function () use ($router) {
        $router->post('/post-exception', 'ExceptionController@postException');
        $router->post('/log-remark', 'ExceptionController@postLogRemark');
    });

    //特殊店铺定制需求订单打标相关接口
    $router->group([
        'prefix' => 'customization',
    ], function () use ($router) {
        //店铺列表相关
        $router->get('/shop_list', 'CustomizationController@shopList');
        $router->post('/add_shop', 'CustomizationController@addShop');
        $router->post('/del_shop', 'CustomizationController@delShop');
        //搜索订单
        $router->post('/order_list', 'CustomizationController@orderList');
        //打标
        $router->post('/add_flag', 'CustomizationController@addFlag');
        //操作记录
        $router->post('/log', 'CustomizationController@logList');
    });
    // 地址
    $router->group([
        'prefix' => '/address',
    ], function () use ($router) {
        // 地址列表
        $router->get('/list', 'AddressController@getList');
        $router->get('/list-v2', 'AddressController@getListV2');
    });

    // 区域分配
    $router->group([
        'prefix' => '/district_assign',
    ], function () use ($router) {
        // 区域分配
        $router->get('/list', 'DistrictAssignController@getList');
        $router->post('/batch_update', 'DistrictAssignController@batchUpdate');
        $router->get('/express_list', 'DistrictAssignController@getExpressList');
        $router->post('/batch_set_order', 'DistrictAssignController@batchSetOrder');
        $router->delete('/batch_delete', 'DistrictAssignController@batchDelete');
        $router->delete('/reset', 'DistrictAssignController@reset');
        $router->post('/import', 'DistrictAssignController@import');
        $router->get('/export', 'DistrictAssignController@export');
    });

    $router->group(['prefix' => '/agent_print',], function () use ($router) {
        $router->get('/factory/search_shops', 'Shop\AgentPrintController@searchShopsOfFactory');
        $router->get('/factory/has_shops', 'Shop\AgentPrintController@hasShops');
    });

    // 自定义分组
    $router->group([
        'prefix' => '/custom_group',
    ], function () use ($router) {
        $router->get('/list', 'CustomGroupController@index');
    });

    // 操作日志
    $router->group([
        'prefix' => '/operation_log',
    ], function () use ($router) {
        $router->get('/list', 'OperationLogController@index');
    });


    //取号记录
    $router->group([
        'prefix' => 'factory_order',
    ], function () use ($router) {
        $router->post('/', 'FactoryOrderController@index');
        $router->post('/sync_order_by_scope', 'FactoryOrderController@syncOrderByScope');
        $router->post('/print', 'FactoryOrderController@print');
        $router->post('/delivery', 'FactoryOrderController@delivery');
        $router->post('/waybill_recovery', 'FactoryOrderController@waybillRecovery');
        $router->post('/lock', 'FactoryOrderController@lock');
        $router->get('/sync/order_schedule', 'FactoryOrderController@getSyncOrderSchedule');
        $router->post('/query_goods_name', 'FactoryOrderController@queryGoodsName');
        $router->post('/get_bind_shop_list', 'FactoryOrderController@getBindShopList');
        $router->post('/check', 'FactoryOrderController@printCheck');
        $router->post('/print_notify', 'FactoryOrderController@notifyPrintResult');
    });

    //批次列表
    $router->group([
        'prefix' => 'batch',
    ], function () use ($router) {
        $router->get('/', 'BatchController@index');
        $router->get('/indexByPrintRecord', 'BatchController@indexByPrintRecord');
        $router->get('/indexByWaybillHistories', 'BatchController@indexByWaybillHistories');
        $router->get('/indexByShippedList', 'BatchController@indexByShippedList');
    });

    // 店铺分组
    $router->group([
        'prefix' => 'shop_group',
    ], function () use ($router) {
        $router->get('/index', 'ShopGroupController@index');
        $router->post('/create', 'ShopGroupController@create');
        $router->get('/show', 'ShopGroupController@show');
        $router->post('/update', 'ShopGroupController@update');
        $router->post('/delete', 'ShopGroupController@delete');
    });

    $router->group([
        'prefix' => 'subscription'
    ], function () use ($router) {
        $router->get('/main_product', 'Subscription\SubscriptionController@mainProduct');
        $router->get('/orders', 'Subscription\SubscriptionController@pageQueryOrders');
        $router->get('/orders/{id}', 'Subscription\SubscriptionController@orderDetail');
        $router->post('/orders', 'Subscription\SubscriptionController@create');
        $router->get('/orders/{id}/alipay', 'Subscription\SubscriptionController@aliPayOrder');
        $router->get('/orders/{id}', 'Subscription\SubscriptionController@orderDetail');
        $router->post('/orders/{id}/refund', 'Subscription\SubscriptionController@orderRefund');
        $router->post('/orders/{id}/refund_check', 'Subscription\SubscriptionController@orderRefundCheck');
        $router->post('/orders/upgrade_check', 'Subscription\SubscriptionController@upgradeCheck');
        $router->post('/orders/{id}/cancel', 'Subscription\SubscriptionController@orderCancel');

    });

});

//授权
$router->group([
    'prefix' => '',
    'middleware' => ['hide.privacy'],
], function () use ($router) {
    //物流轨迹传送
    $router->post('/saveTrace', 'LogisticController@saveTrace');
    //订单变更推送消息
    $router->post('/msg', 'Order\OrderMessageController@dyPushMsg');
    $router->post('/pay_msg', 'PlatformOrderController@pushPayMsg');
    $router->post('/xhs/msg', 'Xhs\XhsMessageController@notify');
    $router->post('/alipay/notify', 'Alipay\CallbackController@notify');
    $router->get('/alipay/result', 'Alipay\CallbackController@result');
    $router->post('/wx/order/notify', 'PublicController@wxOrderNotify');
    $router->post('/shop/ticket/notify', 'PublicController@getTicket');
    $router->post('/shop/callback/{appId}', 'PublicController@wxNotify');
    $router->get('/template/contentPrintTemplate', 'Template\PublicTemplateController@contentPrintTemplate');

    //ks订购消息推送
    $router->post('/addPaidOrder', 'PlatformOrderController@addPaidOrder');
    $router->post('/ks/push_msg', 'Order\OrderMessageController@ksPushMsg');
    $router->post('/tb/qm_msg', 'Order\OrderMessageController@tbQmMsg');
    $router->group([
        'middleware' => ['logger'],
    ], function () use ($router) {
        $router->post('/user/oauth', 'UserController@oauth');
        $router->get('/user/oauth/{platform}', 'UserController@getOauth');
        $router->get('/user/oauthByOauthData', 'UserController@oauthByOauthData');
        $router->post('/user/oauthByOauthUser', 'UserController@oauthByOauthUser');
    });
    $router->post('/user/auth', 'UserController@auth');
    $router->get('/user/auth/{platform}', 'UserController@oauth');
    $router->get('/get_all_company_accounts', 'CompanyController@config');
    $router->get('/get_platform_company', 'Template\TemplateController@getPlatformCompany');
    $router->get('/get/authCodeUrl/{platformCode}', 'UserController@getCodeUrl');
    //直营快递公司
    $router->get('/get_directly_company_accounts', 'CompanyController@getDirectlyCompany');
    // 这2个是快手搬家回传数据用的route
    $router->post('/user/getAccessToken', 'UserController@getAccessToken');
    $router->post('/user/ksLogin', 'UserController@ksLogin');

    // 前台没有登录时候的sms相关接口
    $router->post('/sms/noauth/phone/register', 'SmsController@noAuthBindPhone');
    $router->post('/sms/change/password', 'SmsController@noAuthChangePwd');
    $router->post('/sms/obtain/code', 'SmsController@obtainCode');
    $router->post('/sms/valid/code', 'SmsController@validCode');

    $router->post('/notify/dy-spi/{method}', 'NotifyController@dySPI');
    $router->get('/notify/dy-spi/{method}', 'NotifyController@dySPI');
    $router->get('/common/export_file', 'CommonController@exportFile');
    $router->get('/export_file', 'CommonController@exportFile');
    $router->get('/export/export_file', 'DeliveryController@exportFile');
    $router->get('/waybill_share/export_file', 'Waybill\WaybillShareActionController@exportFile');

    // 打印 通过搜索
    $router->post('/print_by_search', 'OrderController@printBySearchMock');

    // 通用功能
    $router->post('/common/import_parse_excel', 'CommonController@importParseExcel');

    $router->post('/order/batchSetOrderNickname', 'Order2Controller@batchSetOrderNickname');

});


$router->group([
    'prefix' => 'medias/v1',
    'middleware' => ['api.log'],
], function () use ($router) {
    $router->group([
        'prefix' => '/shop',
        'middleware' => ['check.sign']
    ], function () use ($router) {
        //外部机构获取店铺信息并生成token
        $router->get('/info', 'OpenApiController@getShopInfo');
        //外部机构刷新token
        $router->get('/refresh_token', 'OpenApiController@refreshToken');
    });
    // 外部机构订单相关接口
    $router->group([
        'prefix' => '/order',
        'middleware' => ['check.sign', 'check.shop.token']
    ], function () use ($router) {
        // 获取订单列表
        $router->get('/list', 'OpenApiController@getOrderList');
        // 获取订单详情
        $router->get('/info', 'OpenApiController@getOrderInfo');
        // 发货接口
        $router->post('/deliver_goods', 'OpenApiController@deliverGoods');
    });

    // 网店列表查询接口
    $router->get('/waybill/wp_branch_list', [
        'uses' => 'OpenApiController@getWpBranchList',
        'middleware' => 'check.sign'
    ]);
    // 面单账号查询
    $router->get('/waybill/account_list', [
        'uses' => 'OpenApiController@getAccountList',
        'middleware' => 'check.sign'
    ]);

    //外部机构电子面单相关接口
    $router->group([
        'prefix' => '/waybill',
        'middleware' => ['check.sign', 'check.shop.token']
    ], function () use ($router) {
        // 获取单号
        $router->post('/get_waybill_code', 'OpenApiController@getWaybillCode');
        // 单号回收
        $router->post('/waybill_cancel', 'OpenApiController@wayBillCancel');
        // 自由打印
        $router->post('/free_get_waybill_code', 'OpenApiController@freeGetWaybillCode');
    });

    //外部机构自由订单接口
    $router->group([
        'prefix' => '/free_order',
        'middleware' => ['check.sign']
    ], function () use ($router) {
        //批量上传自由订单
        $router->post('/batch_upload_free_order', 'OpenApiController@batchUploadFreeOrder');
        //批量获取自由订单打印状态
        $router->post('/batch_get_free_order_status', 'OpenApiController@batchGetFreeOrderStatus');
    });

});

$router->group([
    'prefix' => 'medias/v2',
    'middleware' => ['api.log'],
], function () use ($router) {
    $router->group([
        'prefix' => '/shop',
        'middleware' => ['check.sign']
    ], function () use ($router) {
        //外部机构获取店铺信息并生成token
        $router->get('/info', 'OpenApiController@getShopInfoV2');
        //外部机构刷新token
        $router->get('/refresh_token', 'OpenApiController@refreshTokenV2');
    });

    //外部机构电子面单相关接口
    $router->group([
        'prefix' => '/waybill',
        'middleware' => ['check.sign', 'check.shop.token']
    ], function () use ($router) {
        // 获取单号
        $router->post('/get_waybill_code', 'OpenApiController@getWaybillCodeV2');
        // 自由打印
        $router->post('/free_get_waybill_code', 'OpenApiController@freeGetWaybillCodeV2');
        // 获取抖音打印params参数
        $router->post('/get_dy_params', 'OpenApiController@getDYPrintParams');
        // 抖音根据运单号获取打印数据
        $router->post('/get_dy_print_data', 'OpenApiController@getDYPrintData');
    });
});

$router->group([
    'prefix' => 'v3',
    'middleware' => ['api.log'],
], function () use ($router) {
    // 不验证签名
    $router->post('/check_sign', 'OpenApiV3Controller@checkSign');

    $router->group([
        'middleware' => ['check.sign.v3']
    ], function () use ($router) {
        $router->group([
            'middleware' => ['check.shop.code', 'check.api.shop.bind']
        ], function () use ($router) {
            // order
            $router->post('/order/list', 'OpenApiV3Controller@getOrderList');
            $router->post('/order/sync_list', 'OpenApiV3Controller@getSyncOrderList');
            $router->post('/order/info', 'OpenApiV3Controller@getOrderInfo');
            $router->post('/order/deliver_goods', 'OpenApiV3Controller@deliverGoods');
            $router->post('order/batch_set', 'OpenApiV3Controller@orderBatchSet');
            $router->post('order/factory_assign', 'OpenApiV3Controller@factoryAssign');
            $router->post('order/factory_assign_cancel', 'OpenApiV3Controller@factoryAssignCancel');
            $router->post('order/refund_apply_list', 'Api\JdOpenApiV3Controller@refundApplyList');
            $router->post('order/sync_remarks', 'Api\OpenApiV3OrderController@getSyncRemarks');

            // goods 屏蔽掉了商品查询
//            $router->post('/goods/list', 'OpenApiV3Controller@getGoodsList');
            // shop
            $router->post('/shop/update_setting', 'Api\OpenApiV3ShopController@updateSetting');
            //JD青龙取号
            $router->post('/waybill/jd/get_etms_waybill_code', 'Api\JdOpenApiV3Controller@getEtmsWaybillcode');
            $router->post('/waybill/jd/send_etms_waybill', 'Api\JdOpenApiV3Controller@sendEtmsWaybill');
            $router->post('/waybill/jd/printing_print_data_pull_data', 'Api\JdOpenApiV3Controller@printingPrintDataPullData');
            $router->post('/waybill/jd/update_buy_order_sop_waybill', 'Api\JdOpenApiV3Controller@updateBuyOrderSopWaybill');
            $router->post('/service/jd/view_service_refund', 'Api\JdOpenApiV3Controller@viewServiceAndRefund');

            //售后
            $router->post('/after_sale/refund_apply_list', 'Api\OpenApiV3AfterSaleController@refundApplyList');
            $router->post('/after_sale/refund_apply_detail', 'Api\OpenApiV3AfterSaleController@refundDetail');
        });
        // waybill
        $router->post('/waybill/waybill_cancel', 'OpenApiV3Controller@wayBillCancel');
        $router->post('/waybill/wp_branch_list', 'OpenApiV3Controller@getWpBranchList');
        $router->post('/waybill/get_waybill_code', 'OpenApiV3Controller@getWayBillCode');
        $router->post('/waybill/get_trace_info', 'OpenApiV3Controller@getTraceInfo');

        // shop
        $router->post('/shop/info', 'Api\OpenApiV3ShopController@getShopInfo');
        $router->post('/gen_oauth_state', 'Api\OpenApiV3ShopController@genOauthState');
        $router->post('/shop/bind_code', 'Api\OpenApiV3ShopController@bindShopCode');
    });
});


$openApiV4Func = function () use ($router) {
    $router->group([
        'middleware' => ['check.sign.version']
    ], function () use ($router) {
        $router->group([
            'middleware' => ['check.shop.code.and.wp.shop.code', 'check.api.shop.bind']
        ], function () use ($router) {
            $router->post('/do-api', 'OpenApiV4Controller@doApi');
            $router->post('/shop/info', 'OpenApiV4Controller@getShopInfo');
            $router->post('/waybill/wayBillCode', 'OpenApiV4Controller@getWayBillCode');
            $router->post('/waybill/outsideWaybill', 'OpenApiV4Controller@outsideWaybill');
        });
        $router->post('/shop/bindCode', 'OpenApiV4Controller@bindShopCode');
        $router->post('/get-app-auth-info', 'OpenApiV4Controller@getAppAuthInfo');
        $router->post('/update_related_app_ids', 'OpenApiV4Controller@updateAppRelatedAppIds');
        $router->post('/get-oauth-url', 'OpenApiV4Controller@genOauthUrl');
        $router->post('/order/details', 'Api\OpenApiV4OrderController@batchGetOrderInfo');

        $router->post('/get-jd-mask-receiver', 'OpenApiV4Controller@getJdMaskReceiverInfo');
//        $router->post('/get-valid-shop-code-list', 'OpenApiV4Controller@getValidShopCodeList'); 关闭接口

    });
    $router->post('/check-sign', 'OpenApiV4Controller@checkSign');
};
$openApiV4Middleware = ['api.log', 'appid.rate.limiter', 'appid.ip.whitelist'];
$router->group([
    'prefix' => 'v4',
    'middleware' => $openApiV4Middleware,
], $openApiV4Func);
// 别名，兼容新项目的路由
$router->group([
    'prefix' => 'open-api/v4',
    'middleware' => $openApiV4Middleware,
], $openApiV4Func);

$router->group([
    'prefix' => 'jd',
    'middleware' => ['logger'],
], function () use ($router) {
    $router->get('/customer', 'Template\TemplateController@getJdTemplate');
});
$router->group([
    'prefix' => 'ks',
    'middleware' => ['logger'],
], function () use ($router) {
    $router->get('/customer', 'Template\TemplateController@getKsTemplate');
});
$router->group([
    'prefix' => 'xhs',
    'middleware' => ['logger'],
], function () use ($router) {
    $router->get('/customer', 'Template\TemplateController@getXhsTemplate');
});
