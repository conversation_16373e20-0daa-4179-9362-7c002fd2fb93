<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/17
 * Time: 15:54
 */

namespace App\Constants;

class PlatformConst
{
    //店铺
    const TAOBAO = 'taobao_top';
    const PDD = 'pdd';
    const KS = 'ks';
    const JD = 'jd';
    const WX = 'wx';
    const DY = 'dy';
    const WXSP = 'wxsp';
    const XHS = 'xhs';
    const ALBB = 'albb';
    const ALC2M = 'alc2m';
    const CN = 'cn';

    const PLATFORM_NAME = [
        self::TAOBAO => '淘宝',
        self::PDD => '拼多多',
        self::KS => '快手',
        self::JD => '京东',
        self::WX => '微信',
        self::DY => '抖音',
        self::WXSP => '微信视频号',
        self::XHS => '小红书',
        self::ALBB => '阿里巴巴',
        self::ALC2M => '淘工厂',
        self::CN => '菜鸟'
    ];
    const ALL_PLATFORM = [
        self::TAOBAO,
        self::PDD,
        self::KS,
        self::JD,
        self::WX,
        self::DY,
        self::WXSP,
    ];

    const WAYBILL_MAPPING = [
        self::TAOBAO => self::WAYBILL_TB,
        self::PDD => self::WAYBILL_PDD,
        self::KS => self::WAYBILL_KS,
        self::JD => self::WAYBILL_JD,
        self::WXSP => self::WAYBILL_WXSP,
        self::DY => self::WAYBILL_DY,
        self::XHS => self::WAYBILL_XHS
    ];
    // 支持追加的平台
    const SUPPORT_APPEND_PLATFORM = [
        self::DY,
        self::KS,
        self::TAOBAO,
    ];

    //电子面单
    const PDD_WB = 'pddwb';   //pdd站外面单
    const TWC = 'twc';    //第三方淘宝面单
    const NEW_TWC = 'newtwc'; //自己的淘宝服务
    const LINK = 'link';   //菜鸟Link
    const TB = 'taobao'; //淘宝

    const WAYBILL_PDD_WB = 1; //pdd wb
    const WAYBILL_TB = 2; //淘宝站外
    const WAYBILL_CNLINK = 3; //菜鸟Link
    const WAYBILL_PDD = 4; //pdd 站内
    const WAYBILL_TB_TOP = 5; //淘宝站内
    const WAYBILL_DY = 6; //抖音
    const WAYBILL_JD = 7; //京东
    const WAYBILL_KS = 8; //快手

    const WAYBILL_WXSP = 9; //微信视频号
    const WAYBILL_XHS = 10; //小红书


    const WAYBILL_AUTH_MAP = [
        self::WAYBILL_PDD => "拼多多",

        self::WAYBILL_PDD_WB => "拼多多站外面单",
        self::WAYBILL_TB => "淘宝站外",
        self::WAYBILL_CNLINK => "菜鸟",
        self::WAYBILL_JD => "京东",
        self::WAYBILL_KS => "快手",
        self::WAYBILL_WXSP => "微信视频号",
        self::WAYBILL_XHS => "小红书",
        self::WAYBILL_DY => "抖音",
        self::WAYBILL_TB_TOP => "淘宝",
    ];

    const APP_ID_TB_XXXXX = 'xxxxx'; //淘宝app_id
}
