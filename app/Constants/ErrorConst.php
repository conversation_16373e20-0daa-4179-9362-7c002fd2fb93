<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/25
 * Time: 17:00
 */

namespace App\Constants;


class ErrorConst
{
    // 系统级别
    const SYSTEM_ERROR = [1000, '服务器错误'];
    const SYSTEM_DB_EXECUTION_TIMEOUT = [1101, '查询超时，请缩小时间范围，或者请稍后再试'];
    const SYSTEM_LOCK_ERROR = [1102, '加锁失败，请稍后操作！'];
    const SYSTEM_VALIDATE = [1103, '加锁失败，请稍后操作！'];
    const SIGN_ERROR = [1001, '签名错误'];
    const PARAM_ERROR = [self::PARAM_ERROR_CODE, '参数错误'];

    const OPERATE_ERROR = [1002, '操作失败'];

    // 业务级别
    /**
     * 订单
     */
    const ORDER_REPEAT_SYNC = [2001, '正在同步中，请勿频繁操作'];
    const ORDER_DATA_EMPTY = [2002, '订单数据为空'];
    const ORDER_STATUS_UPDATE_FAIL = [2003, '订单发货状态修改失败'];
    const ORDER_PRINT_DATA_EMPTY = [2004, '没有需要打印的订单'];
    const ORDER_DELIVERY_FAIL = [2005, '订单发货失败'];
    const ORDER_DELIVERY_REQUEST_FAIL = [2006, '订单发货请求失败'];
    const ORDER_DELIVERED = [2007, '订单已发货，请勿重复发货'];
    const ORDER_LOCK_FAIL_BY_STATUS = [2008, '锁定失败，订单状态不允许'];
    const ORDER_WAYBILL_RECYCLED_DELIVERED_FAIL = [2009, '运单号已回收，发货失败'];
    const ORDER_NOT_FOUND = [2010, '订单不存在'];
    const ORDER_SAVE_FAIL = [2011, '订单保存失败'];
    const ORDER_SHIPPED = [2012, '订单已发货'];
    const ORDER_HAS_REFUND = [2013, '订单有售后'];
    const ORDER_ASSIGNED = [2014, '订单已被分配'];
    const ORDER_NOT_YOURS = [2015, '订单不属于你'];
    const ORDER_NOT_ASSIGN = [2016, '订单未分配'];
    const ORDER_DIST_CANCEL = [2020, '订单已被取消分配'];
    const ORDER_EXIST_PRINTED = [2021, '订单存在已打印的'];
    const ORDER_FACTORY_RETURN_ERROR = [2022, '订单回传失败'];
    const ORDER_FACTORY_RECOVERY_ERROR = [2022, '订单回传失败'];

    /**
     * 电子面单
     */
    const WAYBILL_SERVICE_NOT_OPEN = [2101, '未开通该公司电子面单服务'];
    const WAYBILL_SHARE_TEMPLATE_NOT_CREATE = [2102, '分享的快递公司模板还未创建，请创建后再分享'];
    const WAYBILL_AUTH_LOSE = [2103, '电子面单授权信息丢失'];
    const WAYBILL_SHARE_FREEZE = [2104, '电子面单处于被冻结状态，请联系单号分享者'];
    const WAYBILL_SHARE_INSUFFICIENT_BALANCE = [2105, '电子面单余额不足，请联系单号分享者'];
    const WAYBILL_INSUFFICIENT_BALANCE = [2106, '电子面单余额不足'];
    const WAYBILL_UPDATE_FAIL = [2107, '电子面单更新失败'];
    const WAYBILL_TEMPLATE_NOT_FOUND = [2108, '电子面单模板不存在'];
    const WAYBILL_INVALID_FAIL = [2109, '电子面单作废失败'];
    const WAYBILL_AUTH_FAIL = [2110, '电子面单授权失败，请重试'];
    const WAYBILL_NOT_FOUND = [2111, '面单号不存在'];
    const WAYBILL_CODE_NOT_FOUND = [2112, '电子面单号不存在'];
    const WAYBILL_PDD_NOT_SUPPORT = [2113, '拼多多电子面单暂不支持'];
    const WAYBILL_GET_ERROR = [2114, '获电子面单号错误，请稍后再试'];
    const WAYBILL_GET_PRINT_DATA_ERROR = [2115, '获取电子面单打印数据错误，请稍后再试'];
    const WAYBILL_RECOVERY_ERROR = [2116, '电子面单已回收，操作失败'];
    const WAYBILL_AUTH_EXPIRED = [2117, '电子面单授权已过期'];



    /**
     * 其他
     */
    const COMPANY_BALANCE_UPDATE_FAIL = [2201, '公司账户余额更新失败'];
    const PLATFORM_TYPE_ERROR = [2202, '平台类型错误'];
    const NUMBER_CANT_NEGATIVE_NUMBER = [2203, '数量不能是负数'];
    const NOT_SET_DELIVERY_ADDRESS = [2204, '未设置默认发货地址'];
    const PRINTED_RECORD_ADD_FAIL = [2205, '打印记录添加失败'];
    const PRINTED_DATA_ABNORMAL = [2206, '订单获取打印数据异常'];
    const CROSS_PLATFORM_PRINTING_NOT_SUPPORTED = [2207, '暂不支持跨平台模板打印'];
    const FILE_ERROR_OR_DATA_EMPTY = [2208, '请上传正确模板文件和填写数据'];
    const DELIVERY_RECORD_EXCEPTION = [2209, '发货记录异常'];
    const PACKAGE_ORDER_SAVE_FAIL = [2210, '订单包裹保存失败'];
    const DISTRICT_EXIST = [2211, '该地区已存在，请勿重复提交'];
    const NOT_SUPPORT_LOGISTICS = [2212, '不支持的物流公司'];
    const FILE_PARSE_FAILURE = [2213, '文件解析失败，请重试'];
    const TIME_EXCEEDS_RANGE = [2214, '时间范围超出'];
    const PRINT_BUTTON_ERROR = [2215, '打印按钮错误'];
    const FILE_EXTENSION_NOT_SUPPORTED = [2216, '文件格式不支持'];


    /**
     * 用户
     */
    const USER_CREATE_FAIL = [2301, '用户信息创建失败'];
    const USER_TOKEN_EXPIRED = [2302, '用户登录过期'];
    const STATE_EXPIRED = [2303, 'State 已过期'];


    /**
     *
     * 前端api请求错误
     */
    const API_REQUEST_FAIL = [2401, '前端api请求错误'];

    /**
     * 第三方平台
     */
    const PLATFORM_SERVER_ERROR = [2500, '平台错误'];
    const PLATFORM_SHOP_AUTH_EXPIRED = [2501, '店铺授权过期，请重新授权'];
    const PLATFORM_REQUEST_LIMIT = [2502, '请求太频繁，请稍后再试'];
    const PLATFORM_ANTISPAM = [2503, '平台风控，操作失败'];
    const ENVIRONMENT_ERROR = [2504, '您的环境存在安全风险，请稍后再试'];
    const PLATFORM_GET_ORDER_EMPTY = [2505, '平台获取订单数据为空'];
    const PLATFORM_RETURN_EMPTY = [2506, '平台返回数据为空'];
    const PLATFORM_DY_ERROR = [2507, '抖音平台错误'];
    const PLATFORM_NO_PERMISSIONS = [2508, '操作权限不足'];
    const PLATFORM_SHOP_AUTH_CANCELED = [2509, '店铺授权已被关闭，请重新授权'];
    const PLATFORM_GET_REFUND_APPLY_LIST_EMPTY = [2510, '平台获取退款审核单列表数据为空'];

    const PLATFORM_ERROR = [2511, '平台错误'];
    const PLATFORM_METHOD_NOT_SUPPORT = [2512, '不支持的方法(method)'];

    /**
     * 授权相关的错误集合
     */
    const AUTH_ERRORS=[self::PLATFORM_SHOP_AUTH_CANCELED[0],self::PLATFORM_SHOP_AUTH_EXPIRED[0]];



    /**
     * 店铺
     */
    const SHOP_NOT_FOUND = [2601, '店铺不存在'];
    const SHOP_NOT_BELONG_YOU = [2602, '店铺不属于你'];
    const SHOP_FACTORY_NOT_BIND = [2603, '店铺和厂家未绑定'];
    const SHOP_FACTORY_NOT_FOUND = [2604, '厂家不存在'];

    const SHOP_SCENE_BIND_LIMIT = [2605, '店铺绑定场景已达上限'];
    const NOT_SUPPORT_MODE = [2606, '不支持的模式'];
    const PRINT_DATA_NOT_FOUND = [2607, '打印数据不存在'];
    const NOT_FOUND_PACKAGE_ID = [2608, '未找到包裹ID'];
    const ERROR_CUSTOM_DISTRICT_NOT_FOUND = [2609, '区域未找到'];
    const NOT_FOUND_WAYBILL = [2610, '未找到电子面单号'];
    const SHOP_FREE_USER_NOT_SUPPORT = [2611, '免费用户不支持绑定，请付费购买后再试'];
    const PARAM_ERROR_CODE = 1002;
    const OPERATION_FAIL = 1003;
    public const ERROR_MSG_PRESHIPMENT_UNSUPPORT_APPEND = "预发货不支持追加发货";
    const SHOP_NOT_EXIST = [2612, '店铺不存在'];

    const UN_SUPPORT_OPERATION=[2613,"不支持的操作"];
    const PLATFORM_RESPONSE_EMPTY = [2614, '平台返回数据为空'];

}
