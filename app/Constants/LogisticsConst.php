<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/3/18
 * Time: 17:44
 */

namespace App\Constants;

class LogisticsConst
{
    //邮政电商标快
    const EYB = 'EYB';
    // 极兔快递
    const JT = 'JT';
    // 顺丰
    const SF = 'SF';
    // 顺丰快运
    const SFKY = 'SFKY';
    // 中通
    const ZTO = 'ZTO';
    // 中通快运
    const ZTOKY = 'ZTOKY';
    // 中通国际
    const ZTOGJ = 'ZTOGJ';
    // 韵达快递
    const YUNDA = 'YUNDA';
    // 韵达快运
    const YDKY = 'YDKY';
    // 韵达国际
    const YDGJ = 'YDGJ';
    // 圆通
    const YTO = 'YTO';
    // 圆通承诺达（物流）
    const YTOCND = 'YTOCND';
    // 邮政快递
    const POSTB = 'POSTB';
    // EMS
    const EMS = 'EMS';
    // EMS经济快递
    const EMSJJ = 'EMSJJ';
    // 百世快递
    const BESTKD = 'BESTKD';
    //京东快运
    const JDKY = 'JDKY';
    // 百世快运
    const BESTKY = 'BESTKY';
    // 京东
    const JD = 'JD';
    // 申通快递
    const STO = 'STO';
    // 申通快运
    const STOKY = 'STOKY';
    // 申通国际
    const STOGJ = 'STOGJ';
    // 丰网
    const FENGWANG = 'FENGWANG';
    // 德邦快递
    const DBKD = 'DBKD';
    // 德邦快运
    const DBKY = 'DBKY';
    // 宅急送
    const ZJS = 'ZJS';
    // 天地华宇
    const HOAU = 'HOAU';
    // 天天
    const TT = 'TT';
    // 亚风速递
    const AIR = 'AIR';
    // 优速快递
    const UC = 'UC';
    // 日日顺物流
    const RRS = 'RRS';
    // 跨越速运
    const KYSY = 'KYSY';
    // D速物流
    const SDSD = 'SDSD';
    // 安能快递
    const ANKD = 'ANKD';
    // 安能快运
    const ANKY = 'ANKY';
    // 承诺达特快
    const OTP = 'OTP';
    // 安迅物流
    const AXWL = 'AXWL';
    // 京广速递
    const SZKKE = 'SZKKE';
    // 顺心捷达
    const SXJD = 'SXJD';
    // 九曳供应链
    const JIUYE = 'JIUYE';
    // 高捷物流
    const GJWL = 'GJWL';
    // 递速物流
    const DSWL = 'DSWL';
    // 速尔快递
    const SURE = 'SURE';
    // 丹鸟
    const DANNIAO = 'DANNIAO';
    // 快捷快递
    const FAST = 'FAST';
    // 国通快递
    const GTO = 'GTO';
    // 全峰快递
    const QFKD = 'QFKD';
    // 卡行天下
    const KXTX = 'KXTX';
    // 苏宁快递
    const SNWL = 'SNWL';
    // 如风达
    const RFD = 'RFD';
    // 联邦快递
    const FEDEX = 'FEDEX';
    // 众邮快递
    const ZYKD = 'ZYKD';
    // 快弟来了
    const KDLL = 'KDLL';
    // 快弟来了
    const NZSY = 'NZSY';
    // 中铁快运
    const ZTKY = 'ZTKY';
    // 1688货运
    const HY1688 = '1688HY';
    // 韵达同城
    const YDTC = 'YDTC';
    // 壹米滴答
    const YMDD = '壹米滴答';
    // 远成快运
    const YCKY = 'YCKY';
    // 加运美快运
    const JMYK = 'JMKY';
    // 速腾物流
    const STWL = 'STWL';
    // 中通冷链

    const ZTWL = 'ZTWL';
    //吉时达物流
    const JSD = 'jilinjishi';
    const ZTLL = 'ZTLL';

    //中铁智慧物流
    const ZTFBKY = 'ZTFBKY';
    //平安达腾飞快递
    const PADTFKD = 'PADTFKD';
    //汇森速运
    const HSSY = 'HSSY';
    //京东医药
    const ZTOINT = 'ZTOINT';
    //速腾快递
    const STKD = 'STKD';
    //加运美
    const JYM = 'JYM';
    //红背心
    const HBX = 'HBX';
    //达达秒送
    const DDTCKS = 'DDTCKS';
    //京东医药
    const JDYY = 'JDYY';
    //海信物流
    const SAVOR = 'SAVOR';
    //京东大件物流
    const JDDJ = 'JDDJ';
    //安得物流
    const ANNTO = 'ANNTO';
    //贝业新兄弟
    const BYL = 'BYL';
    //邮政快递包裹
    const ZGYZZHDD = 'ZGYZZHDD';

    /**
     * 吉时达物流
     */
    const JILINJISHI='jilinjishi';

    const NAME_MAP = [
        self::JT => '极兔快递',
        self::SF => '顺丰快递',
        self::SFKY => '顺丰快运',
        self::ZTO => '中通快递',
        self::ZTOKY => '中通快运',
        self::ZTOGJ => '中通国际',
        self::YUNDA => '韵达快递',
        self::YDKY => '韵达快运',
        self::YDGJ => '韵达国际',
        self::YTO => '圆通快递',
        self::YTOCND => '圆通承诺达',
        self::POSTB => '邮政快递',
        self::EMS => 'EMS',
        self::EMSJJ => 'EMS经济快递',
        self::BESTKD => '百世快递',
        self::JDKY => '京东快运',
        self::BESTKY => '百世快运',
        self::JD => '京东快递',
        self::STO => '申通快递',
        self::STOKY => '申通快运',
        self::STOGJ => '申通国际',
        self::FENGWANG => '丰网',
        self::DBKD => '德邦快递',
        self::DBKY => '德邦快运',
        self::ZJS => '宅急送',
        self::HOAU => '天地华宇',
        self::TT => '天天',
        self::AIR => '亚风速递',
        self::UC => '优速快递',
        self::RRS => '日日顺物流',
        self::KYSY => '跨越速运',
        self::SDSD => 'D速物流',
        self::ANKD => '安能快递',
        self::ANKY => '安能快运',
        self::OTP => '承诺达特快',
        self::AXWL => '安迅物流',
        self::SZKKE => '京广速递',
        self::SXJD => '顺心捷达',
        self::JIUYE => '九曳供应链',
        self::GJWL => '高捷物流',
        self::DSWL => '递速物流',
        self::SURE => '速尔快递',
        self::DANNIAO => '丹鸟',
        self::FAST => '快捷快递',
        self::GTO => '国通快递',
        self::QFKD => '全峰快递',
        self::KXTX => '卡行天下',
        self::SNWL => '苏宁快递',
        self::RFD => '如风达',
        self::FEDEX => '联邦快递',
        self::ZYKD => '众邮快递',
        self::KDLL => '快弟来了	',
        self::NZSY => '哪吒速运',
        self::ZTKY => '中铁快运',
        self::HY1688 => '1688货运',
        self::YDTC => '韵达同城',
        self::YMDD => '壹米滴答',
        self::YCKY => '远成快运',
        self::JMYK => '加运美快运',
        self::STWL => '速腾物流',
        self::ZTLL => '中通冷链',
        self::EYB => '邮政电商标快',
        self::ZTWL => "中铁智慧物流"
    ];

    /**
     * 区域分配可用快递数组
     */
    const DISTRICT_ASSIGN_ARRAY = [
        self::JT,
        self::SF,
        self::ZTO,
        self::YUNDA,
        self::YTO,
        self::POSTB,
        self::EMS,
        self::BESTKD,
        self::JD,
        self::STO,
        self::FENGWANG,
        self::DBKD,
        self::DANNIAO,
        self::ZJS,
        self::BESTKD,
    ];

}
