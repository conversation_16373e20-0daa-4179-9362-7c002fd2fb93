<?php

namespace App\Constants;

class DoudianMsgTag
{
	// 表示的是消息类型，1为支付成功消息通知类型，2为卖家授权类型
	const MSG_TYPE_ORDER = 1;
	const MSG_TYPE_AUTH  = 2;

	//订单消息类型
	const TAG_TRADE_CREATE            = 100; //订单创建消息
	const TAG_TRADE_PAID              = 101; //订单支付/确认消息
	const TAG_TRADE_SELLER_SHIP       = 102; //卖家发货消息
	const TAG_TRADE_SUCCESS           = 103; //交易完成消息
	const TAG_TRADE_LOGISTICS_CHANGED = 104; //发货物流变更消息
	const TAG_TRADE_ADDRESS_CHANGED   = 105; //买家收货信息变更消息
	const TAG_TRADE_CANCELED          = 106; //订单取消消息
	const TAG_TRADE_PARTLY_SELLER_SHIP = 108; //卖家部分发货消息

	const TAG_TRADE_ORDER_TAG_PUSH = 150; //订单标签变更事件

    //退款消息
	const TAG_REFUND_CREATED              = 200; //买家发起售后申请消息
	const TAG_REFUND_AGREED               = 201; //同意退款消息
	const TAG_REFUND_APPLY_AGREED         = 202; //同意退货申请消息
	const TAG_REFUND_BUYER_RETURN_GOODS   = 203; //买家退货给卖家消息
	const TAG_REFUND_REFUSED              = 204; //拒绝退款消息
	const TAG_REFUND_RETURN_APPLY_REFUSED = 205; //拒绝退货申请消息
	const TAG_REFUND_SUCCESS              = 206; //退款成功消息
	const TAG_REFUND_CLOSED               = 207; //售后关闭消息

    CONST TAG_LOGISTICS_ORDER_TAG_PUSH = 10003; //订单标记推送 弃用了


    const NEED_MSG_TAG_ARR = [
//        self::TAG_TRADE_PAID,
        self::TAG_TRADE_SELLER_SHIP,
//        self::TAG_TRADE_SUCCESS,
        self::TAG_TRADE_ADDRESS_CHANGED,
//        self::TAG_TRADE_CANCELED,
        self::TAG_TRADE_PARTLY_SELLER_SHIP,
        self::TAG_TRADE_LOGISTICS_CHANGED,
        self::TAG_REFUND_AGREED,
        self::TAG_REFUND_APPLY_AGREED,
        self::TAG_REFUND_SUCCESS,
        self::TAG_REFUND_CLOSED,
        self::TAG_REFUND_CREATED,
    ];
}
