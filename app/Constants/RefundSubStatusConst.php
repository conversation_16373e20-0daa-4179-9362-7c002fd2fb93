<?php

namespace App\Constants;

class RefundSubStatusConst
{
    // 商家处理中 商家拒绝退款 退款完成 商家拒绝退货退款 待买家退货 退货待商家收货 退货退款完成 退货退款关闭  换货待买家发货 换货待商家收货 换货待买家收货 换货完成 平台处理中 售后关闭
    // 商家处理中 1 商家拒绝退款 2 退款完成 3 商家拒绝退货退款 4 待买家退货 5 退货待商家收货 6 退货退款完成 7 退货退款关闭 8 换货待买家发货 9 换货待商家收货 10 换货待买家收货 11 换货完成 12 平台处理中 13 售后关闭 14 售后关闭
    /**
     * 无
     */
    const NONE = 0;

    /**
     * 商家处理中
     */
    const MERCHANT_PROCESSING = 1;
    /**
     * 商家拒绝退款
     */
    const MERCHANT_REFUSE_REFUND = 2;
    /**
     * 退款完成
     */
    const REFUND_COMPLETE = 3;
    /**
     * 商家拒绝退货退款
     */
    const MERCHANT_REFUSE_RETURN = 4;
    /**
     * 待买家退货
     */
    const WAIT_BUYER_RETURN = 5;
    /**
     * 退货待商家收货
     */
    const RETURN_WAIT_MERCHANT = 6;
    /**
     * 退货退款完成
     */
    const RETURN_REFUND_COMPLETE = 7;
    /**
     * 退货退款关闭
     */
    const RETURN_REFUND_CLOSE = 8;
    /**
     * 换货待买家发货
     */
    const WAIT_BUYER_EXCHANGE = 9;
    /**
     * 换货待商家收货
     */
    const WAIT_MERCHANT_EXCHANGE = 10;
    /**
     * 换货待买家收货
     */
    const WAIT_BUYER_EXCHANGE_RECEIVE = 11;

    /**
     * 换货完成
     */
    const EXCHANGE_COMPLETE = 12;

    /**
     * 换货关闭
     */
    const EXCHANGE_CLOSE = 13;

    /**
     * 平台处理中
     */
    const PLATFORM_PROCESSING = 14;

    /**
     * 商家拒绝换货
     */
    const MERCHANT_REFUSE_EXCHANGE = 15;




    /**
     * 售后关闭(取消)
     */
    const REFUND_CLOSE =  100;

    /**
     * 售后完成数组
     */
    const REFUND_COMPLETE_ARRAY = [
        self::REFUND_COMPLETE,
        self::RETURN_REFUND_COMPLETE,
        self::EXCHANGE_COMPLETE,
    ];

    /**
     * 售后关闭数组
     */
    const REFUND_CLOSE_ARRAY = [
        self::RETURN_REFUND_CLOSE,
        self::EXCHANGE_CLOSE,
        self::REFUND_CLOSE,
    ];

    /**
     *
     * 售后中数组（不包括完成和失败）
     */
    const REFUND_PROCESSING_ARRAY = [
        self::MERCHANT_PROCESSING,
        self::MERCHANT_REFUSE_REFUND,
        self::MERCHANT_REFUSE_RETURN,
        self::WAIT_BUYER_RETURN,
        self::RETURN_WAIT_MERCHANT,
        self::WAIT_BUYER_EXCHANGE,
        self::WAIT_MERCHANT_EXCHANGE,
        self::WAIT_BUYER_EXCHANGE_RECEIVE,
        self::PLATFORM_PROCESSING,
        self::MERCHANT_REFUSE_EXCHANGE,
    ];

    /**
     * @return int[]
     */
    public static function getAll()
    {
        return array_merge(self::REFUND_COMPLETE_ARRAY, self::REFUND_CLOSE_ARRAY, self::REFUND_PROCESSING_ARRAY);
    }

}

