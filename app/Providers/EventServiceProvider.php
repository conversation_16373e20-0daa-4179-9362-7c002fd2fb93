<?php

namespace App\Providers;

use App\Events\Orders\OrderAllocation4PrintEvent;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderDeliveryEvent;
use App\Events\Orders\OrderDeliveryFailEvent;
use App\Events\Orders\OrderFactoryAssignCancelEvent;
use App\Events\Orders\OrderFactoryAssignEvent;
use App\Events\Orders\OrderLockEvent;
use App\Events\Orders\OrderPreshipmentCancelEvent;
use App\Events\Orders\OrderPreshipmentFailEvent;
use App\Events\Orders\OrderPreshipmentJoinEvent;
use App\Events\Orders\OrderRedeliveryEvent;
use App\Events\Orders\OrderShippingOrder;
use App\Events\Orders\OrderUnLockEvent;
use App\Events\Orders\OrderWaybillEvent;
use App\Events\Orders\OrderFlagEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\Orders\OrderWaybillFailEvent;
use App\Events\Shops\ShopSettingUpdateEvent;
use App\Events\Subscription\SubscriptionOrderRefunded;
use App\Events\Subscription\SubscriptionOrderSucceeded;
use App\Events\Users\UserLoginEvent;
use App\Events\Waybills\WaybillRecycledEvent;
use App\Listeners\DyEventSubscriber;
use App\Listeners\LogReportEventSubscriber;
use App\Listeners\OperationLogListener;
use App\Listeners\OrderAllocation4PrintRelationListener;
use App\Listeners\SubscriptionOrderListener;
use Laravel\Lumen\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        UserLoginEvent::class => [
            OperationLogListener::class,
        ],
        OrderDeliveryEvent::class => [
            OperationLogListener::class,
        ],
        OrderPrintEvent::class => [
            OperationLogListener::class,
        ],
        OrderDecryptEvent::class => [
            OperationLogListener::class,
        ],
        OrderUpdateEvent::class => [
            OperationLogListener::class,
        ],
        OrderFlagEvent::class => [
            OperationLogListener::class,
        ],
        OrderWaybillEvent::class => [
            OperationLogListener::class,
        ],
        OrderWaybillFailEvent::class => [
            OperationLogListener::class,
        ],
        OrderShippingOrder::class => [
            OperationLogListener::class,
        ],
        OrderFactoryAssignEvent::class => [
            OperationLogListener::class,
        ],
        OrderFactoryAssignCancelEvent::class => [
            OperationLogListener::class,
        ],
        WaybillRecycledEvent::class => [
            OperationLogListener::class,
        ],
        OrderAllocation4PrintEvent::class => [
            OrderAllocation4PrintRelationListener::class,
        ],
        OrderLockEvent::class => [
            OperationLogListener::class,
        ],
        OrderUnLockEvent::class => [
            OperationLogListener::class,
        ],
        OrderRedeliveryEvent::class => [
            OperationLogListener::class,
        ],
        OrderDeliveryFailEvent::class => [
            OperationLogListener::class,
        ],
        ShopSettingUpdateEvent::class => [
            OperationLogListener::class,
        ],
        OrderPreshipmentCancelEvent::class => [
            OperationLogListener::class,
        ],
        OrderPreshipmentJoinEvent::class => [
            OperationLogListener::class,
        ],
        OrderPreshipmentFailEvent::class => [
            OperationLogListener::class,
        ],
        SubscriptionOrderSucceeded::class => [
            SubscriptionOrderListener::class
        ],
        SubscriptionOrderRefunded::class => [
            SubscriptionOrderListener::class
        ],
    ];

    /**
     * 需要注册的订阅者类。
     *
     * @var array
     */
    protected $subscribe = [
        LogReportEventSubscriber::class,
        DyEventSubscriber::class,
    ];

}
