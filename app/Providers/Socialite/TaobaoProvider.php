<?php

/*
 * This file is part of the overtrue/socialite.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace App\Providers\Socialite;

use App\Services\Client\TbClient;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use Overtrue\Socialite\AccessToken;
use Overtrue\Socialite\AccessTokenInterface;
use Overtrue\Socialite\AuthorizeFailedException;
use Overtrue\Socialite\InvalidStateException;
use Overtrue\Socialite\ProviderInterface;
use Overtrue\Socialite\User;
use ShopSellerGetRequest;

/**
 * Class TaobaoProvider.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @see    https://open.taobao.com/doc.htm?docId=102635&docType=1&source=search [Taobao - OAuth 2.0 授权登录]
 */
class TaobaoProvider extends \Overtrue\Socialite\Providers\TaobaoProvider
{
    protected $stateless = true;

    protected function getTbClient($token)
    {
        $appKey = config('socialite.taobao_top.client_id');
        $secretKey = config('socialite.taobao_top.client_secret');
        return new TbClient($appKey, $secretKey,$token);
    }
    /**
     * @inheritDoc
     */
    protected function getUserInfoByToken($tokenObj)
    {
        \Log::info('getUserInfoByToken:',[$tokenObj]);
        if (Environment::isTaoBao() && in_array(config('taobaotop.connections.app.app_key'),['12565698','23695186'])) {
                // 店伙计 没有权限
            return [
                'shop_name' => urldecode($tokenObj['taobao_user_nick']),
                'shop_id' => $tokenObj['taobao_user_id'],
                'shop_logo' => '',
            ];
        }
        $tbClient = $this->getTbClient($tokenObj['access_token']);
        $request = new ShopSellerGetRequest;
        $request->setFields("sid,title,pic_path");
        list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($request);
        $response = $this->getHttpClient()->post($requestUrl, [
            'form_params' => $apiParams,
        ]);
        $res = json_decode($response->getBody(), true);
        \Log::info('taobao shop info',[$res]);
//        {"shop_seller_get_response":{"shop":{"pic_path":"\/72\/d8\/TB1MriSGXXXXXaOXXXXSutbFXXX.jpg","sid":35482043,"title":"查米真丝工厂店"},"request_id":"15sbhaw5zs8bj"}
        $shop = $res['shop_seller_get_response']['shop']??[];
        if (empty($shop['title'])) {
            throw new AuthorizeFailedException('授权错误: ' . ($res['error_response']['sub_msg'] ?? json_encode($res, JSON_UNESCAPED_UNICODE)), $res);
        }
        return [
            'shop_name' => $shop['title'],
            'shop_id' => $shop['sid'],
            'shop_logo' => 'https://logo.taobao.com/shop-logo'.$shop['pic_path'],
        ];
    }


    /**
     * Get the access token for the given code.
     *
     * @param string $code
     *
     * @return array
     */
    public function getAccessToken($code)
    {
        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'query' => $this->getTokenFields($code),
        ]);

        $res = json_decode($response->getBody(), true);

        if (empty($res['access_token'])) {
            \Log::error('getAccessToken',[$res]);
            throw new AuthorizeFailedException('Authorize Failed: ' . json_encode($res, JSON_UNESCAPED_UNICODE), $res);
        }
        return $res;
    }


    /**
     * Map the raw user array to a Socialite User instance.
     *
     * @param array $user
     *
     * @return User
     */
    protected function mapUserToObject(array $user)
    {
        $name = $this->arrayItem($user, 'taobao_user_nick');
        $name = urldecode($name);

        return new User([
            'id' => $this->arrayItem($user, 'taobao_user_id'),
            'nickname' => $name,
            'username' => $name,
            'name' => $name,
            'avatar' => '',
        ]);
    }

    /**
     * @param AccessTokenInterface|null $token
     * @return User
     * @throws \ReflectionException
     * <AUTHOR>
     */
    public function user(AccessTokenInterface $token = null)
    {
        if (is_null($token) && $this->hasInvalidState()) {
            throw new InvalidStateException();
        }

        $token = $token ?: $this->getAccessToken($this->getCode());

        $user = $this->getUserInfoByToken($token);

        $info = array_merge($token, $user);
        $user = $this->mapUserToObject($info)->merge(['original' => $info]);

        return $user->setToken(new AccessToken($token))->setProviderName($this->getName());
    }

    /**
     * @param array $authDataArr
     * @return User
     * @throws \ReflectionException
     * <AUTHOR>
     */
    public function userByAuthData(array $authDataArr)
    {
        $shop = [
            'shop_name' => $authDataArr['shop']['title'],
            'shop_id' => $authDataArr['shop']['sid'],
            'shop_logo' => 'https://logo.taobao.com/shop-logo'.$authDataArr['shop']['pic_path'],
        ];
        $user = array_merge($authDataArr, $shop);
        $user = $this->mapUserToObject($user)->merge(['original' => $user]);
        return $user->setToken(new AccessToken($authDataArr))->setProviderName($this->getName());
    }

}
