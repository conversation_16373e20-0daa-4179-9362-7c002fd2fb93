<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:43
 */

namespace App\Providers\Socialite;


use App\Services\Client\DyClient;
use App\Traits\ProviderUseState;
use GuzzleHttp\ClientInterface;
use Overtrue\Socialite\AccessToken;
use Overtrue\Socialite\AccessTokenInterface;
use Overtrue\Socialite\AuthorizeFailedException;
use Overtrue\Socialite\InvalidStateException;
use Overtrue\Socialite\ProviderInterface;
use Overtrue\Socialite\Providers\AbstractProvider;
use Overtrue\Socialite\User;

/**
 * Class DyProvider
 * @package App\Providers\Socialite
 * @see https://op.jinritemai.com/docs/guide-docs/9/22
 */
class DyProvider extends AbstractProvider implements ProviderInterface
{

    /**
     * 授权码
     * @var string
     */
    protected $codeUrl = 'https://fxg.jinritemai.com/index.html';
    /**
     * 访问令牌
     * @var string
     */
    protected $baseUrl = 'https://openapi-fxg.jinritemai.com';
//    protected $baseUrl = 'http://proxy-dy.mayiapps.cn';


    protected function buildAuthUrlFromBase($url, $state)
    {
//        return $url.'#/ffa/open/applicationAuthorize?'.http_build_query($this->getCodeFields($state), '', '&', $this->encodingType);
	    return 'https://fuwu.jinritemai.com/authorize?service_id=' . config('socialite.dy.service_id') . '&state=' . $state;
    }

    /**
     * @inheritDoc
     */
    protected function getAuthUrl($state)
    {
        return $this->buildAuthUrlFromBase($this->codeUrl, $state);
    }

    /**
     * @inheritDoc
     */
    protected function getTokenUrl()
    {
        //return $this->baseUrl.'/oauth2/access_token';
        return $this->baseUrl.'/token/create';
    }

    /**
     * @inheritDoc
     */
    protected function getUserByToken(AccessTokenInterface $token)
    {
        return $token->getAttributes();
    }

    /**
     * @inheritDoc
     */
    protected function mapUserToObject(array $user)
    {
        return new User([
            'id' => $this->arrayItem($user, 'shop_id'),
            'nickname' => $this->arrayItem($user, 'shop_name'),
            'username' => $this->arrayItem($user, 'shop_name'),
            'name' => $this->arrayItem($user, 'shop_name'),
            'avatar' => '',
        ]);
    }

    /**
     * Get the access token for the given code.
     *
     * @param string $code
     *
     * @return \Overtrue\Socialite\AccessTokenInterface
     */
    public function getAccessToken($code)
    {
        if ($this->accessToken) {
            return $this->accessToken;
        }

        $postKey = 'query';

        $response = $this->getHttpClient()->get($this->getTokenUrl(), [
            $postKey => $this->getTokenFields($code),
        ]);

        return $this->parseAccessToken($response->getBody());
    }

    /**
     * Get the POST fields for the token request.
     *
     * @param string $code
     *
     * @return array
     */
    protected function getTokenFields($code)
    {
        $data = [
            //'app_id' => $this->clientId,
            //'app_secret' => $this->clientSecret,
            'code' => $code,
//            'redirect_uri' => $this->redirectUrl,
            'grant_type' => 'authorization_code',
        ];
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        $client = new DyClient($appKey, $secretKey);

        return $client->buildRequestData($data, 'token.create');
    }
    /**
     * Get the GET parameters for the code request.
     *
     * @param string|null $state
     *
     * @return array
     */
    protected function getCodeFields($state = null)
    {
        $fields = array_merge([
            'app_id' => $this->clientId,
            'redirect_uri' => $this->redirectUrl,
            'scope' => $this->formatScopes($this->scopes, $this->scopeSeparator),
            'response_type' => 'code',
        ], $this->parameters);

        if ($this->usesState()) {
            $fields['state'] = $state;
        }

        return $fields;
    }

    /**
     * Get the access token from the token response body.
     *
     * @param \Psr\Http\Message\StreamInterface|array $body
     *
     * @return \Overtrue\Socialite\AccessTokenInterface
     */
    protected function parseAccessToken($body)
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        if (empty($body['data'])) {
            throw new AuthorizeFailedException('Authorize Failed: empty data,'.json_encode($body, JSON_UNESCAPED_UNICODE), $body);
        }
        $body = $body['data'];
        if (empty($body['access_token'])) {
            throw new AuthorizeFailedException('Authorize Failed: empty access_token,'.json_encode($body, JSON_UNESCAPED_UNICODE), $body);
        }

        return new AccessToken($body);
    }

}
