<?php

/*
 * This file is part of the overtrue/socialite.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace App\Providers\Socialite;

use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use Log;
use Overtrue\Socialite\AccessToken;
use Overtrue\Socialite\AccessTokenInterface;
use Overtrue\Socialite\AuthorizeFailedException;
use Overtrue\Socialite\ProviderInterface;
use Overtrue\Socialite\User;

/**
 * Class 阿里巴巴的Provider.
 *
 *
 * @see    https://open.1688.com/api/apidoclist.htm?id=624396
 */
class AlbbProvider  extends TaobaoProvider  implements ProviderInterface
{
    protected $stateless = true;

    public  function getAuthUrl($state): string
    {
        return $this->buildAuthUrlFromBase("https://auth.1688.com/oauth/authorize",$state);
    }


    public  function getCodeFields($state = null): array
    {
        $fields = [
            'client_id' => $this->getConfig()->get('client_id'),
            'redirect_uri' => $this->redirectUrl,
            'view' => $this->view,
            'site' => '1688',
            'response_type' => 'code',
        ];

        if ($state) {
            $fields['state'] = $state;
        }
        return $fields;
    }

    protected function getTokenUrl(): string
    {
//        https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/YOUR_APPKEY?grant_type=authorization_code&need_refresh_token=true&client_id= YOUR_APPKEY&client_secret= YOUR_APPSECRET&redirect_uri=YOUR_REDIRECT_URI&code=CODE
//        注：此接口必须使用POST方法提交；必须使用https
//getToken接口参数说明：
//a) grant_type为授权类型，使用authorization_code即可
//b) need_refresh_token为是否需要返回refresh_token，如果返回了refresh_token，原来获取的refresh_token也不会失效，除非超过半年有效期
//c) client_id为app唯一标识，即appKey
//d) client_secret为app密钥
//e) redirect_uri为app入口地址
//f) code为授权完成后返回的一次性令牌
//g) 调用getToken接口不需要签名
//注：如果超过code有效期（2分钟）或者已经使用code获取了一次令牌，code都将失效，需要返回第二步重新获取code
//        https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/YOUR_APPKEY?grant_type=authorization_code&need_refresh_token=true&client_id= YOUR_APPKEY&client_secret= YOUR_APPSECRET&redirect_uri=YOUR_REDIRECT_URI&code=CODE
        return "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/".$this->getConfig()->get('client_id').'?grant_type=authorization_code&need_refresh_token=true';
    }

    /**
     * 调用接口获取用户信息
     * @param AccessTokenInterface $token
     * @return array
     */
    protected function getUserByToken(AccessTokenInterface $token): array
    {
        Log::debug('getUserByToken');
        return [];
    }

    protected function  mapUserToObject(array $user)
    {
        return new User([
            'id' => $user['shop_id'] ?? '',
            'nickname' => $user['shop_name'] ?? '',
            'username' => $user['name'] ?? '',
            'name' => $user['shop_name'] ?? '',
            'avatar' => '',
        ]);
    }

    /**
     */
    public function getAccessToken($code)
    {
        $tokenFields = $this->getTokenFields($code)+ ['grant_type' => 'authorization_code', 'view' => $this->view,'need_refresh_token'=>'true'];
        $response = $this->getHttpClient()->post($this->getTokenUrl(), [
            'query' => $tokenFields
        ]);

//        $accessToken = $this->parseAccessToken($response->getBody());
        $res = $response->getBody();

        if (!is_array($res)) {
            $res = json_decode($res, true);
        }
        Log::info("获取token", [$res,$tokenFields]);
        return $res;
    }
    protected function getUserInfoByToken($tokenObj)
    {
        \Log::info('getUserInfoByToken:',[$tokenObj]);
        $orderService = OrderServiceManager::create();
        if (empty($tokenObj['access_token'])){
            throw new AuthorizeFailedException('授权错误: access_token 为空，'.json_encode($tokenObj, JSON_UNESCAPED_UNICODE),$tokenObj);
        }
        $orderService->setAccessToken($tokenObj['access_token']);
        $res = $orderService->sendByCustom("post_form", 'com.alibaba.account:alibaba.account.basic', [], []);
        \Log::info('alibaba.account.basic',[$res]);

        if (empty($res['result']['loginId'])) {
            throw new AuthorizeFailedException('授权错误: ' . ($res['error_response']['sub_msg'] ?? json_encode($res, JSON_UNESCAPED_UNICODE)), $res);
        }
        $sellerName = $res['result']['supplierName']??$res['result']['loginId'];
        return [
            'name' => $sellerName,
            'shop_name' => $sellerName,
            'shop_id' => $res['result']['memberId'],
            'shop_logo' => 'https://cbu01.alicdn.com'.$res['result']['icon'],
        ];
    }




//    protected function getTokenFields(string $code): array
//    {
//        $fields = ['client_id' => $this->getConfig()->get('client_id'),
//            'client_secret' => $this->getConfig()->get('client_secret'),
////            'code' => $code,
//            'redirect_uri' => $this->redirectUrl,
//            'grant_type' => 'authorization_code', 'view' => "web"];
//        var_dump($fields);
//        return $fields;
//    }
    /**
     * @param array $authDataArr
     * @return User
     * @throws \ReflectionException
     * <AUTHOR>
     */
    public function userByAuthData(array $authDataArr)
    {
        $sellerName = $authDataArr['result']['supplierName'] ?? $authDataArr['result']['loginId'];
        $shop = [
            'name' => $sellerName,
            'shop_name' => $sellerName,
            'shop_id' => $authDataArr['result']['memberId'],
            'shop_logo' => 'https://cbu01.alicdn.com' . $authDataArr['result']['icon'],
        ];
        $user = array_merge($authDataArr, $shop);
        $user = $this->mapUserToObject($user)->merge(['original' => $user]);
        return $user->setToken(new AccessToken($authDataArr))->setProviderName($this->getName());
    }

}
