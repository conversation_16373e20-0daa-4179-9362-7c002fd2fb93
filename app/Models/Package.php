<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Model;

class Package extends Model
{
    protected $fillable = [
        'user_id',
        'shop_id',
        'print_status',
        'recycled_at',
        'waybill_code',
        'tids',
        'wp_code',
        'template_id',
        'auth_source',
        'tid_oids',
        'batch_no',
        'waybill_status',
        'sub_waybill_codes',
        'goods_info',
        'version',
        'status',
        'print_order_status',
        'is_split',
        'take_waybill_at',
        'total_num',
        'to_shop_id',
        'pre_shipment_status',
        'operation_shop_id',
        'send_at',
        'print_at',
        'promise_ship_time',
        'logistic_delivery_time',
        'company_id',
        'source_type',
        'delivery_type',
        'waybill_wp_index', // 原单号
        'send_scene',
        'pre_shipment_at',
        'send_waybill_type',
        'multi_package_main_waybill_code',
    ];
    const PRINT_STATUS_NO  = 0;         //未打印
    const PRINT_STATUS_YES = 1;         //已打印



    const WAYBILL_STATUS_DOING = 1;     //取号中
    const WAYBILL_STATUS_SUCCESS = 2;   //取号成功
    const WAYBILL_STATUS_ERROR = 3;     //取号失败

    const ORDER_STATUS_PAYMENT   = 30; //已付款
    const ORDER_STATUS_PART_DELIVERED = 35; //部分发货
    const ORDER_STATUS_DELIVERED = 40; //已发货

    const PRE_SHIPMENT_STATUS_NO = 0; //未设置成预发货
    const PRE_SHIPMENT_STATUS_YES = 1; //设置成预发货，等待发货

    const PRE_SHIPMENT_STATUS_PAUSE = 2; //预发货暂停

    const PRE_SHIPMENT_STATUS_FINISHED = 3; //预发货完成

    const SOURCE_TYPE_PRINT = 0; // 打印创建
    const SOURCE_TYPE_INTERNAL_DELIVERY = 1; // 内部发货
    const SOURCE_TYPE_EXTERNAL_DELIVERY = 2; // 外部发货

    // delivery_type 发货类型 0:无,1:首次发货,2:变更单号,3:补发,4:换货,88:外部发货,99:其他
    const DELIVERY_TYPE_NO = 0; // 无
    const DELIVERY_TYPE_FIRST = 1; // 首次发货
    const DELIVERY_TYPE_CHANGE = 2; // 变更单号
    const DELIVERY_TYPE_BE_CHANGED = 21; // 被变更单号
    const DELIVERY_TYPE_REPLACEMENT = 3; // 补发
    const DELIVERY_TYPE_RETURN = 4; // 换货
    const DELIVERY_TYPE_EXTERNAL = 88; // 外部发货
    const DELIVERY_TYPE_OTHER = 99; // 其他

    const DELIVERY_TYPE_ARRAY = [
        self::DELIVERY_TYPE_FIRST,
        self::DELIVERY_TYPE_CHANGE,
        self::DELIVERY_TYPE_REPLACEMENT,
        self::DELIVERY_TYPE_RETURN,
        self::DELIVERY_TYPE_OTHER
    ];
    const DELIVERY_TYPE_NAME_MAPPING = [
        self::DELIVERY_TYPE_FIRST => '首次发货',
        self::DELIVERY_TYPE_CHANGE => '变更单号',
        self::DELIVERY_TYPE_REPLACEMENT => '补发',
        self::DELIVERY_TYPE_BE_CHANGED => '被变更单号',
        self::DELIVERY_TYPE_RETURN => '换货',
        self::DELIVERY_TYPE_EXTERNAL => '外部发货',
        self::DELIVERY_TYPE_OTHER => '其他',
    ];
    const SEND_SCENE_SINGLE=1; //整单发货

    const SEND_SCENE_MULTI=2; //拆单发货

    // 发货单号类型 1:普通单号 2:一单多包主包裹 3:一单多包从包裹 4:拆单主包裹 5:拆单从包裹 6:空包裹
    const SEND_WAYBILL_TYPE_NORMAL = 1; //普通单号
    const SEND_WAYBILL_TYPE_MULTI_MAIN = 2; //一单多包主包裹
    const SEND_WAYBILL_TYPE_MULTI_SUB = 3; //一单多包从包裹
    const SEND_WAYBILL_TYPE_SPILT_MAIN = 4; //拆单主包裹
    const SEND_WAYBILL_TYPE_SPILT_SUB = 5; //拆单从包裹
    const SEND_WAYBILL_TYPE_EMPTY = 6; //空包裹




    public function orders()
    {
        return $this->belongsToMany(\App\Models\Fix\Order::class, 'package_orders', 'package_id', 'order_id');
    }
    public function order()
    {
        return $this->belongsTo(\App\Models\Fix\Order::class, 'order_id', 'id');
    }
    public function orderCipherInfo()
    {
        return $this->belongsTo('App\Models\OrderCipherInfo', 'order_id', 'order_id');
    }
    public function packageOrders()
    {
        return $this->hasMany(PackageOrder::class, 'package_id', 'id');
    }
    public function sendPackageOrders()
    {
        return $this->hasMany(PackageOrder::class, 'package_id', 'id')
            ->where('source_type', self::SOURCE_TYPE_INTERNAL_DELIVERY);
    }
    public function printPackageOrders()
    {
        return $this->hasMany(PackageOrder::class, 'package_id', 'id')
            ->where('source_type', self::SOURCE_TYPE_PRINT);
    }
    public function waybillHistory()
    {
        return $this->hasOne(WaybillHistory::class, 'package_id', 'id');
    }
    public function packageOrderItems()
    {
        return $this->belongsToMany('App\Models\Fix\OrderItem', 'package_orders', 'package_id',
            'order_item_id','id');
    }
    public function template()
    {
        return $this->belongsTo('App\Models\Template', 'template_id', 'id')->withTrashed();
    }
    public function orderTrace()
    {
        return $this->hasOne(OrderTraceList::class, 'express_no', 'waybill_code');
    }
    public function subMultiPackages()
    {
        return $this->hasMany(Package::class, 'multi_package_main_waybill_code', 'waybill_code')->from(DB::raw('packages' . ' force index(packages_multi_package_main_waybill_code_index)'));
    }

    /**
     * 根据运单号和物流公司编码查找
     * @param string $waybillCode
     * @param string|null $wpCode
     * @return Package|null
     */

    public static function findByWaybillCode(string $waybillCode,?string $wpCode=null):?Package{
        $query = self::where('waybill_code', $waybillCode);
        if ($wpCode) {
            $query->where('wp_code', $wpCode);
        }
        return $query->first();
    }
}
