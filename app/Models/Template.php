<?php

namespace App\Models;

use App\Constants\PlatformConst;
use App\Constants\TemplateURLConst;
use App\Exceptions\ApiException;
use App\Services\BusinessException;
use App\Services\OssService;
use App\Services\PrintDataService;
use App\Services\Waybill\Taobao\NewTBApi;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use App\Utils\WaybillUtil;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Models\Waybill;
use Illuminate\Support\Facades\Log;

class Template extends Model
{
    use SoftDeletes;

    const WAYBILL_DEFAULT_NO = 1; //不是默认
    const WAYBILL_DEFAULT_YES = 2; //默认

    //合单默认配置
    const MERGE_CONFIG_DEFAULT_76_133 = '[{"id":11,"aliasName":"卖家备注","showName":"卖家备注","value":"&lt;%=data.remark%&gt;","width":82,"height":178,"top":1,"left":205,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isChecked":true},{"id":24,"aliasName":"发货内容","showName":"发货内容","value":"&lt;%=data.contents%&gt;","width":204,"height":178,"top":0,"left":0,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isMerge":false,"isChecked":true},{"id":20,"aliasName":"商品数量水印","showName":"2件","value":"&lt;%=data.watermark%&gt;","width":80,"height":50,"top":92,"left":152,"fontSize":40,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","color":"rgba(0, 0, 0, 0.4)","custom":false,"isMerge":false,"isChecked":true}]';
    const MERGE_CONFIG_DEFAULT_100_180 = '[{"id":11,"aliasName":"卖家备注","showName":"卖家备注","value":"&lt;%=data.remark%&gt;","width":82,"height":178,"top":1,"left":205,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isChecked":true},{"id":24,"aliasName":"发货内容","showName":"发货内容","value":"&lt;%=data.contents%&gt;","width":204,"height":178,"top":0,"left":0,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isMerge":false,"isChecked":true},{"id":20,"aliasName":"商品数量水印","showName":"2件","value":"&lt;%=data.watermark%&gt;","width":80,"height":50,"top":92,"left":152,"fontSize":40,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","color":"rgba(0, 0, 0, 0.4)","custom":false,"isMerge":false,"isChecked":true}]';

    //普通打印配置
    const NORMAL_CONFIG_76_133 = '[{"id":11,"aliasName":"卖家备注","showName":"卖家备注","value":"&lt;%=data.remark%&gt;","width":82,"height":178,"top":1,"left":205,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isChecked":true},{"id":24,"aliasName":"发货内容","showName":"发货内容","value":"&lt;%=data.contents%&gt;","width":204,"height":178,"top":0,"left":0,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isMerge":false,"isChecked":true},{"id":20,"aliasName":"商品数量水印","showName":"2件","value":"&lt;%=data.watermark%&gt;","width":80,"height":50,"top":92,"left":152,"fontSize":40,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","color":"rgba(0, 0, 0, 0.4)","custom":false,"isMerge":false,"isChecked":true}]';
    const NORMAL_CONFIG_100_180 = '[{"id":11,"aliasName":"卖家备注","showName":"卖家备注","value":"&lt;%=data.remark%&gt;","width":82,"height":178,"top":1,"left":205,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isChecked":true},{"id":24,"aliasName":"发货内容","showName":"发货内容","value":"&lt;%=data.contents%&gt;","width":204,"height":178,"top":0,"left":0,"fontSize":12,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","custom":false,"isMerge":false,"isChecked":true},{"id":20,"aliasName":"商品数量水印","showName":"2件","value":"&lt;%=data.watermark%&gt;","width":80,"height":50,"top":92,"left":152,"fontSize":40,"fontFamily":"simsun","fontWeight":"bold","position":"absolute","color":"rgba(0, 0, 0, 0.4)","custom":false,"isMerge":false,"isChecked":true}]';

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'company_id',
        'auth_source',
        'type',
        'style',
        'template_url',
        'name',
        'description',
        'default_print',
        'custom_config',
        'default',
        'picture',
        'picture_height',
        'picture_width',
        'waybill_type',
        'show_logo',
        'horizontal',
        'vertical',
        'width',
        'height',
        'parent_template_id',
        'merge_template_url',
        'time_delivery',
        'insure',
        'wp_code',
        'wp_name',
        'owner_id',
        'owner_name',
        'sender_name',
        'sender_mobile',
        'sender_province',
        'sender_city',
        'sender_district',
        'sender_address',
        'shipping_address_id',
        'created_at',
        'updated_at',
        'deleted_at',
        'service_list',
        'jd_template',
        'platform_template_id',
        'parent_part',
        'belong_shop_name',
        'belong_shop_id',
        'print_contents'

    ];
    //模板尺寸
    const TEMPLATE_SIZE_76_130 = "76mm*130mm";
    const TEMPLATE_SIZE_100_180 = "100mm*180mm";
    const TEMPLATE_SIZE_100_150 = "100mm*150mm";
    const TEMPLATE_SIZE_100_113 = "100mm*113mm";

    public static function firstByIdAndShopId($template_id, $shop_id)
    {
        return self::where('id', $template_id)->where('shop_id', $shop_id)->first();
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 创建电子面单模板
     * @param array $templateArr
     * @param int $userId
     * @param int $shopId
     * @return array
     * @throws BusinessException
     * @throws ApiException
     */
    public static function generate(array $templateArr, int $userId, int $shopId): array
    {
//        Log::info('generate', [$templateArr, $userId, $shopId]);
        $addrArr = [
            'province' => $templateArr['province'],
            'city' => $templateArr['city'],
            'district' => $templateArr['district'],
            'detail' => $templateArr['detail'],
            'street' => $templateArr['street_name'] ?? ''
        ];
        //先创建快递公司
        $company = Company::generate(
            $templateArr['wp_code'],
            $templateArr['branch_code'],
            $userId,
            $shopId,
            $templateArr['owner_id'],
            $templateArr['owner_name'],
            $templateArr['auth_source'],
            $addrArr,
            $templateArr['platform_account_id'] ?? null,
            $templateArr['platform_shop_id'] ?? null,
            array_get($templateArr, 'extended_info'),
            array_get($templateArr, 'brand_code')


        );
        if (!$company) {
            throw new BusinessException('快递公司创建异常!');
        }
        $platformTemplateId = $templateArr['platform_template_id'] ?? "0";
//        Log::info('templateUrl', [$platformTemplateId,$platformTemplateId==0, $templateArr['merge_template_url']]);
        if ($platformTemplateId != "single") {
            //如果传了平台模板的ID，就不获取平台目标的URL了
            $templateUrl = self::getTemplateUrl($userId, $shopId, $templateArr);
        }
        // 生成京东自由打印模板
        $jdTemplate = null;
        if (config('app.platform') == 'jd') {
            $jdTemplate = [
                "top" => "0cm",
                "left" => "0.0cm",
                "width" => "76cm",
                "height" => "50cm",
            ];
            $allItem = [];
            foreach (json_decode($templateArr['merge_template_url'], true) as $item) {
                $key = PrintDataService::getCustomKey($item['value']);
                $item['top'] = $templateArr['size'] == Template::TEMPLATE_SIZE_76_130 ? ($item['top'] + 316) : ($item['top'] + 330);
                $tempTemplate = [
                    'type' => $item['id'] != 20 ? 'text' : 'water',
                    'top' => $item['top'],
                    'left' => $item['left'],
                    'width' => $item['width'],
                    'height' => $item['height'],
                    'content' => '@{' . $key . '}',
                    'justifyContent' => $item['id'] != 20 ? 'flex-start' : 'center',
                    'fontName' => '微软雅黑',
                    'fontWeight' => $item['id'] != 20 ? 'bold' : 'normal',
                    'fontSize' => $item['id'] != 20 ? 12 : 26,
                    'lineHeight' => 1
                ];

                if ($item['id'] == 20) {
                    $tempTemplate['alpha'] = 0.65;
                }
                $allItem[] = $tempTemplate;
            }
            $jdTemplate['items'] = $allItem;
            $jdTemplate = base64_encode(json_encode($jdTemplate, JSON_UNESCAPED_UNICODE));
        }
        if (config('app.platform') == 'ks') {
            $str = "<?xml version='1.0'?>";
            //$str .= "<layout id='CUSTOM_AREA' width='76' height='50' left='0' top='80'>";
            $layoutWidth = number_format($templateArr['width'], 2);
            $layoutHeight = number_format($templateArr['height'], 2);
            $layoutTop = 81;
            if ($templateArr['size'] != Template::TEMPLATE_SIZE_76_130) {
                $layoutTop = 150;
            }
            $str .= sprintf('<layout id="CUSTOM_AREA" width="%s" height="%s" left="0" top="%s" splitable="false" >', $layoutWidth, $layoutHeight, $layoutTop);
            foreach (json_decode($templateArr['merge_template_url'], true) as $item) {
                $left = number_format(($item['left'] / 3.78), 2);
                $top = number_format(($item['top'] / 3.78), 2);
                $width = number_format(($item['width'] / 3.78), 2);
                $height = number_format(($item['height'] / 3.78), 2);
                $fontFamily = $item['fontFamily'];
                $fontWeight = $item['fontWeight'];
                $fontSize = intval($item['fontSize'] * 0.75);
                if ($item['id'] == 18) {
                    $key = 'custom';
                } else {
                    $key = PrintDataService::getCustomKey($item['value']);
                }
                $zindex = 1;
                if ($key == 'watermark') {
                    $zindex = 111;
                }
                //$str .= "<text left='".$left."' top='".$top."' width='".$width."' height='".$height."' style='fontFamily:".$fontFamily.";fontWeight:".$fontWeight.";fontSize:".$fontSize.";'><![CDATA[<%=_data.$key%>]]></text>";
                $str .= '<text left="' . $left . '" top="' . $top . '" width="' . $width . '" height="' . $height . '"  style="fontFamily:SimSun;fontSize:' . $fontSize . ';zIndex:' . $zindex . ';fontWeight:' . $fontWeight . ';"><![CDATA[ <%= _data.' . $key . ' %> ]]></text>';
            }
            switch ($templateArr['size']) {
                default:
                case Template::TEMPLATE_SIZE_76_130:
                    $nextContentHeight = intval(130 - $layoutHeight);
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $nextContentHeight = intval(180 - $layoutHeight);
                    break;
                case Template::TEMPLATE_SIZE_100_150:
                    $nextContentHeight = intval(150 - $layoutHeight);
                    break;
            }
            $str .= '<text left="0" top="' . $layoutHeight . '" width="' . $layoutWidth . '" height="' . $nextContentHeight . '" style="fontFamily:SimSun;fontSize:14;zIndex:111;fontWeight:bold;"><![CDATA[ <%= _data.nextContents %> ]]></text>';
            $str .= "</layout>";
            $jdTemplate = $str;
        }
        $data = [
            'user_id' => $userId,
            'shop_id' => $shopId,
            'company_id' => $company->id,
            'auth_source' => $templateArr['auth_source'],
            'style' => $templateArr['style'],
            'type' => $templateArr['type'],
            'template_url' => $templateUrl ?? '',
            'name' => $templateArr['name'],
            'description' => $templateArr['description'] ?? '',
            'default_print' => $templateArr['default_print'] ?? '',
            'custom_config' => $templateArr['content'],
            'picture' => $templateArr['picture'],
            'picture_height' => $templateArr['picture_height'],
            'picture_width' => $templateArr['picture_width'],
            'waybill_type' => $templateArr['waybill_type'],
            'width' => $templateArr['width'],
            'height' => $templateArr['height'],
            'time_delivery' => $templateArr['time_delivery'] ?? 0,
            'insure' => $templateArr['insure'] ?? 0,
            'show_logo' => $templateArr['show_logo'] ?? 0,
            'horizontal' => $templateArr['horizontal'] ?? 0,
            'vertical' => $templateArr['vertical'] ?? 0,
            'wp_code' => $templateArr['wp_code'],
            'wp_name' => $templateArr['wp_name'],
            'parent_template_id' => $templateArr['parent_template_id'],
            'merge_template_url' => $templateArr['merge_template_url'] ?? null,
            'owner_id' => $templateArr['owner_id'],
            'owner_name' => $templateArr['owner_name'],
            'service_list' => $templateArr['service_list'],
            'shipping_address_id' => isset($templateArr['shopping_address_id']) ? $templateArr['shopping_address_id'] : 0,
            'jd_template' => $jdTemplate,
            'platform_template_id' => $platformTemplateId ?? null,
            'belong_shop_name' => $templateArr['belong_shop_name'] ?? $templateArr['owner_name'],
            'belong_shop_id' => $templateArr['belong_shop_id'] ?? $shopId,
            'parent_part' => $templateArr['parentPart'] ?? 0,
        ];

        $shopExtra = ShopExtra::query()->where('shop_id', $shopId)->first();
        if (!empty($shopExtra) && empty($shopExtra->print_config)) {
            $shopExtra->print_config = ShopExtra::DEFAULT_PRINT_CONFIG;
            $shopExtra->save();
        }

        $count = Template::where('shop_id', $shopId)->count();
        if ($count == 0) {
            $data['default'] = Template::WAYBILL_DEFAULT_YES;
        }
        $template = self::create($data);
        if (!$template) {
            throw new BusinessException('面单模板创建失败！');
        }

        return $template->toArray();
    }

    /**
     * 更新打印内容
     * @param int $id
     * @param string|null $printContents
     * @return void
     */
    public static function updatePrintContents(int $id, ?string $printContents)
    {
        $template = self::findOrFail($id);
        if (empty($printContents)) {
            $template->print_contents = null;
        } else {
            $template->print_contents = $printContents;
        }
        $jd_template = $template->jd_template;
        if (!empty($jd_template)) {
            $printContentsArr = json_decode($printContents, true);
            if ($printContentsArr['goodsPaging']) { // 是否分页
                $splitableStr = 'splitable="true"';
            } else {
                $splitableStr = 'splitable="false"';
            }
            // 替换 splitable="false" 和 splitable="true" 为 $splitableStr
            $template->jd_template = str_replace(['splitable="false"', 'splitable="true"'], $splitableStr, $jd_template);
        }
        $template->save();
    }

    /**
     * 修改模板信息
     * @param int $id
     * @param array $templateArr
     * @return bool
     * @throws BusinessException
     */
    public static function edit(int $id, array $templateArr): bool
    {
        $template = Template::findOrFail($id);
        $company = Company::findOrFail($template->company_id);
        if (!$company) {
            throw new BusinessException('快递公司创建异常!');
        }
        // 生成京东自由打印模板
        $jdTemplate = null;
        if (Environment::isJd()) {
            $jdTemplate = [
                "top" => "0cm",
                "left" => "0.0cm",
                "width" => "76cm",
                "height" => "50cm",
            ];
            $allItem = [];
            foreach (json_decode($templateArr['mergeContent'], true) as $item) {
                preg_match('/=data.([a-zA-Z]+)/', $item['value'], $ch);
                $item['top'] = $templateArr['size'] == Template::TEMPLATE_SIZE_76_130 ? ($item['top'] + 316) : ($item['top'] + 350);
                $tempTemplate = [
                    'type' => $item['id'] != 20 ? 'text' : 'water',
                    'top' => $item['top'],
                    'left' => $item['left'],
                    'width' => $item['width'],
                    'height' => $item['height'],
                    'content' => '@{' . $ch[1] . '}',
                    'justifyContent' => $item['id'] != 20 ? 'flex-start' : 'center',
                    'fontName' => '微软雅黑',
                    'fontWeight' => $item['id'] != 20 ? 'bold' : 'normal',
                    'fontSize' => $item['id'] != 20 ? 12 : 26,
                    'lineHeight' => 1
                ];

                if ($item['id'] == 20) {
                    $tempTemplate['alpha'] = 0.65;
                }
                $allItem[] = $tempTemplate;
            }
            $jdTemplate['items'] = $allItem;
            $jdTemplate = base64_encode(json_encode($jdTemplate, JSON_UNESCAPED_UNICODE));
        }

        if (config('app.platform') == 'ks') {
            $str = "<?xml version='1.0'?>";
            //$str .= "<layout id='CUSTOM_AREA' width='76' height='50' left='0' top='80'>";
            $layoutWidth = number_format($templateArr['width'], 2);
            $layoutHeight = number_format($templateArr['height'], 2);
            $layoutTop = 81;
            if ($templateArr['size'] != Template::TEMPLATE_SIZE_76_130) {
                $layoutTop = 150;
            }
            $printContentsArr = json_decode($template->print_contents, true);
            if ($printContentsArr['goodsPaging']) { // 是否分页
                $splitableStr = 'true';
            } else {
                $splitableStr = 'false';
            }
//            $str .= '<layout id="CUSTOM_AREA" width="' . $layoutWidth . '" height="' . $layoutHeight . '" left="0" top="81" '.$splitableStr.' >';
            $str .= sprintf('<layout id="CUSTOM_AREA" width="%s" height="%s" left="0" top="%s" splitable="%s" >', $layoutWidth, $layoutHeight, $layoutTop, $splitableStr);
            foreach (json_decode($templateArr['mergeContent'], true) as $item) {
                $left = number_format(($item['left'] / 3.78), 2);
                $top = number_format(($item['top'] / 3.78), 2);
                $width = number_format(($item['width'] / 3.78), 2);
                $height = number_format(($item['height'] / 3.78), 2);
                $fontFamily = $item['fontFamily'];
                $fontWeight = $item['fontWeight'];
                $fontSize = intval($item['fontSize'] * 0.75);
                if ($item['id'] == 18) {
                    $key = 'custom';
                } else {
                    $key = PrintDataService::getCustomKey($item['value']);
                }
                $zindex = 1;
                if ($key == 'watermark') {
                    $zindex = 111;
                }
                //$str .= "<text left='".$left."' top='".$top."' width='".$width."' height='".$height."' style='fontFamily:".$fontFamily.";fontWeight:".$fontWeight.";fontSize:".$fontSize.";'><![CDATA[<%=_data.$key%>]]></text>";
                $str .= '<text left="' . $left . '" top="' . $top . '" width="' . $width . '" height="' . $height . '"  style="fontFamily:SimSun;fontSize:' . $fontSize . ';zIndex:' . $zindex . ';fontWeight:' . $fontWeight . ';"><![CDATA[ <%= _data.' . $key . ' %> ]]></text>';
            }
            switch ($templateArr['size']) {
                default:
                case Template::TEMPLATE_SIZE_76_130:
                    $nextContentHeight = intval(130 - $layoutHeight);
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $nextContentHeight = intval(180 - $layoutHeight);
                    break;
                case Template::TEMPLATE_SIZE_100_150:
                    $nextContentHeight = intval(150 - $layoutHeight);
                    break;
            }
            $nextContentsTop = intval($layoutHeight) + 10;
            $nextContentsWidth = $layoutWidth - 2;
            $nextContentHeight = $nextContentHeight - 10;
            $str .= '<text left="2" top="' . $nextContentsTop . '" width="' . $nextContentsWidth . '" height="' . $nextContentHeight . '" style="fontFamily:SimSun;fontSize:14;zIndex:111;fontWeight:bold;"><![CDATA[ <%= _data.nextContents %> ]]></text>';
            $str .= "</layout>";
            $jdTemplate = $str;
        }

        $data = [
            'style' => $templateArr['style'],
            //'template_url'        => $templateUrl,
            'name' => $templateArr['name'],
            'description' => $templateArr['description'] ?? '',
            'default_print' => $templateArr['default_print'] ?? '',
            'custom_config' => $templateArr['content'],
            'picture' => $templateArr['picture'],
            'picture_height' => $templateArr['picture_height'],
            'picture_width' => $templateArr['picture_width'],
            'waybill_type' => $templateArr['waybill_type'],
            'width' => $templateArr['width'],
            'height' => $templateArr['height'],
            'time_delivery' => $templateArr['time_delivery'],
            'insure' => $templateArr['insure'] ?? 0,
            'show_logo' => $templateArr['show_logo'] ?? 0,
            'horizontal' => $templateArr['horizontal'] ?? 0,
            'vertical' => $templateArr['vertical'] ?? 0,
            'wp_code' => $templateArr['wp_code'],
            'wp_name' => $templateArr['wp_name'],
            'parent_template_id' => $templateArr['parent_template_id'],
            'merge_template_url' => $templateArr['mergeContent'],
            'owner_id' => $templateArr['owner_id'],
            'owner_name' => $templateArr['owner_name'],
            'sender_name' => $templateArr['sender_name'] ?? '',
            'sender_mobile' => $templateArr['sender_mobile'] ?? '',
            'shipping_address_id' => $templateArr['shipping_address_id'] ?? 0,
            'goods_new_page' => $templateArr['goods_new_page'] ? 1 : 0,
            'belong_shop_name' => $templateArr['belong_shop_name'] ?? $templateArr['owner_name'],
            'belong_shop_id' => $templateArr['belong_shop_id'] ?? $templateArr['shop_id'],
            'service_list' => $templateArr['service_list'],
            'jd_template' => $jdTemplate,
        ];

        $ret = Template::where('id', $id)->update($data);
        if (!$ret) {
            throw new BusinessException('修改异常！');
        }

        return true;
    }

    public static function assemPrintItems(array $customConfig)
    {
        foreach ($customConfig as $value) {
            $itemName = self::getCustomKey($value['value']);
            if ($itemName == 'watermark') {
                $itemStyle = '{"fontSize":"' . $value["fontSize"] . '","fontWeight":"' . $value["fontWeight"] . '"}';
                $printItems[] = [
                    'itemType' => 'watermark',
                    'locationX' => $value['left'],
                    'locationY' => $value['top'],
                    'width' => $value['width'],
                    'height' => $value['height'],
                    'itemName' => $value['showName'],
                    'itemStyle' => $itemStyle,
                    'tip' => $value['aliasName'] == '自定义文字' ? '自定义' : $itemName,

                ];
            } else {
                $printItems[] = [
                    'itemType' => 'goodsinfo',
                    'locationX' => $value['left'],
                    'locationY' => $value['top'],
                    'width' => $value['width'],
                    'height' => $value['height'],
                    'itemName' => $value['showName'],
                    'tip' => $value['aliasName'] == '自定义文字' ? '自定义' : $itemName,
                ];
            }
        }
        return $printItems;
    }

    /**
     * 修改模板信息
     * @param $sender
     * @param $orderIdStr
     * @param $template
     * @param $customConfig
     * @param $waybillCode
     * @return array
     */
    public static function templateTestData($sender, $orderIdStr, $template, $customConfig, $waybillCode)
    {
        $printNextItemBeans = self::assemPrintItems($customConfig);
        $data = [
            "template" => [
                "width" => $template['width'],
                "height" => $template['height'],
            ],
            "printNextItemBeans" => $printNextItemBeans,
        ];
        //发货地址，若没有，就给默认地址
        $ship = ShippingAddress::where('user_id', $template['user_id'])
            ->where('shop_id', $template['shop_id'])
            ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_YES)
            ->where('is_default', ShippingAddress::IS_DEFAULT_YES)
            ->first();

        if ($template['auth_source'] == Waybill::AUTH_SOURCE_PDD_WB ||
            $template['auth_source'] == Waybill::AUTH_SOURCE_PDD
        ) {
            return [
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        //'encryptedData' => $encryptedData,
                        //'ver'           => '3',
                        'templateURL' => $template['template_url'],
                        'data' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $ship['province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $ship['city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $ship['district'],
                                    'detail' => isset($sender['detail']) ? $sender['detail'] : $ship['address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $ship['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $ship['mobile'],
                                'phone' => '',
                            ],
                            'recipient' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $ship['province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $ship['city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $ship['district'],
                                    'detail' => isset($sender['detail']) ? $sender['detail'] : $ship['address'],
                                    'town' => '',
                                ],
                                'name' => '测试',
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $ship['mobile'],
                                'phone' => '',
                            ],
                            'routingInfo' => [
                                'bigShotName' => '386',
                                'bigShotCode' => '',
                                'originBranchCode' => '',
                                'originBranchName' => '',
                                'endBranchCode' => '576901',
                                'endBranchName' => '区域件',
                                'threeSegmentCode' => '386'
                            ],
                            'waybillCode' => $waybillCode,
                            'qrCode' => $waybillCode,
                            'wpCode' => $template['wp_code'],
                            'templateUrl' => $template['template_url'],
                            'signature' => '',
                        ],
                        'userid' => '122',
                    ],
                    [
                        'data' => $data,
                        'templateUrl' => 'https://printer-static.oss-cn-zhangjiakou.aliyuncs.com/template/pdd_extra.xml'
                    ]
                ],
            ];
        } else if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
            $printData = [];
            return [
                'express_code' => array_get($template, 'wp_code', ''),
                'express_no' => array_get($printData, 'waybill_code', ''),
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        "templateURL" => $template['template_url'],
                        "signature" => "",
                        'data' => [
                            "shippingOption" => "",
                            //寄件人信息
                            'receiverInfo' => [
                                'address' => [
                                    'provinceName' => $sender['province'],
                                    'cityName' => $sender['city'],
                                    'districtName' => $sender['district'],
                                    'detailAddress' => $sender['detail'],
                                    'town' => '',
                                ],
                                'contact' => [
                                    'mobile' => $sender['mobile'],
                                    'name' => $sender['sender_name']
                                ]
                            ],
                            'senderInfo' => [
                                'address' => [
                                    'provinceName' => $sender['province'],
                                    'cityName' => $sender['city'],
                                    'districtName' => $sender['district'],
                                    'detailAddress' => $sender['detail'],
                                    'town' => '',
                                ],
                                'contact' => [
                                    'mobile' => $sender['mobile'],
                                    'name' => $sender['sender_name']
                                ]
                            ],
                            'routingInfo' => [
                                'consolidation' => [
                                    'name' => $printData['package_center_name'] ?? '',
                                    'code' => $printData['package_center_code'] ?? ''
                                ],
                                'sortation' => [
                                    'name' => $printData['short_address_name'] ?? '',
                                    'code' => $printData['short_address_code'] ?? '',
                                ],
                                'markDestination' => [
                                    'name' => '测试',
                                    'code' => '001'
                                ],
                                'package' => [
                                    'name' => '测试',
                                    'code' => '001'
                                ],
                                'routeCode' => $printData['sort_code'] ?? '',
                            ],
                            'services' => [
                                'SVC_COD' => [],
                                'SVC_INSURE' => []
                            ],
                            'jdService' => [
                                'sourceSortCenterName' => ''
                            ],
                            'shippingInfo' => [],
                            'print_time' => date('Y-m-d H:i:s', time()),
                            'trackNo' => '75504297229648',
                        ],
                        'addData' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['detail']) ? $sender['detail'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ]
                        ]
                    ],
                    [
                        'data' => $data,
                        'templateURL' => $template['wp_code'] == 'shunfeng' ? TemplateURLConst::HTTPS_PRINTER_STATIC_OSS_CN_ZHANGJIAKOU_ALIYUNCS_COM_TEMPLATE_CUSTOM_XML : TemplateURLConst::HTTPS_SDDY_MAYIAPPS_CN_DY_EXTRA_XML
                    ]
                ]
            ];
        } else if ($template['auth_source'] == Waybill::AUTH_SOURCE_KS) {
            $newData = [];
            foreach ($data['printNextItemBeans'] as $item) {
                if ($item['tip'] == '自定义') {
                    $newData['custom'] = $item['itemName'];
                } else {
                    $newData[$item['tip']] = $item['itemName'];
                }
            }
            return [
                'documentID' => $orderIdStr,
                'waybillCode' => $waybillCode,
                'ksOrderFlag' => true,
                'contents' => [
                    [
                        "templateURL" => $template['template_url'],
                        "signature" => '2847ee7f6d7cd235edb788ca14f878fb',
                        "encryptedData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
                        'key' => 'VWh3llFZauaRO5o2LqDBByrUWsQNNS0zhlRr/jms8cUtHIW5OUWWG7SM/7KDN2WHiHUB3ThLyuSIOS+HyWReN5LsSiS/Pl/U/x6fOEEm2c9mNVlCcu1ZPkMM0eYIHeuxmlbsesnSet92ubrZQ36JHdX3aSfWrIsEqaNPnqjrEz8=',
                        'ver' => '1',
                        'addData' => [
                            'senderInfo' => [
                                'address' => [
                                    'provinceName' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'cityName' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'districtName' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detailAddress' => isset($sender['address']) ? $sender['address'] : $template['sender_address'],
                                    'town' => '',
                                    'countryCode' => 'CHN'
                                ],
                                'contact' => [
                                    'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                    'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                    'phone' => '',
                                ]
                            ]
                        ]
                    ],
                    [
                        'data' => array_merge($newData, ["printNextItemBeans" => $data['printNextItemBeans']]),
                        'customData' => $newData,
                        'templateURL' => env('APP_DOMAIN') . '/api/ks/customer?template_id=' . $template['id'],
                    ]
                ]
            ];
        } else if ($template['auth_source'] == Waybill::AUTH_SOURCE_JD) {
            $customData = [];
            foreach ($data['printNextItemBeans'] as $item) {
                $customData[$item['tip']] = $item['itemName'];
            }
            $province = isset($sender['province']) ? $sender['province'] : $template['sender_province'];
            $city = isset($sender['city']) ? $sender['city'] : $template['sender_city'];
            $district = isset($sender['district']) ? $sender['district'] : $template['sender_district'];
            $detail = isset($sender['address']) ? $sender['address'] : $template['sender_address'];
            return [
                'express_code' => 'YUNDA',
                'express_no' => '428059423056741',
                'customData' => [$customData],
                'documentID' => $orderIdStr,
                'customTempUrl' => 'http://jd.kuaidixia.net/api/jd/customer?template_id=' . $template['id'],
                'tempUrl' => $template['template_url'],
                'printData' => ["/Z9UqyNHS9fRUt44WYxOZ/7Fw/DsCal4IbY8ONal9hL+X9QSWMZ1CN5JZVU5nA2WxbofSDGDg5GIi7WlBpKXzhfL479foAXIIga/1SWwwHv/BiU3QkiBiOEHShYUi8yjhMoGcYGbcu3iB9r8NAIEUOUL4dPNizr7eAvvxiro8+M0Ez6l1HqcLB2chHkRXJ8JRUNkkqz8HKB/XUMRwAKWDZJ1MWONkzd0SHttUZFK42alKFZk8UV/UYTjZR5b3wizyhEDmqBauTFfpQTVrEkiBOnzP9u0a+BaBZmZKx0vv9Cx7bY/r2E1RblUCvVQi4B3kc5hGOmkJzAnzK34M0D5czH60Z8prCQdFA4nBPOfiB6TDYvlSOc+g8HLfvPf1NMs04EcoXIb9JvtG+Bdx9wnm9sOR1ORbO0SEF5qsGRDS8RUKYUVBr0K1UiUoZlZYM4s4PvLLn2JhFijVITpq7cLWfKq9cwA+BohB7iE+OSYTb8aRYNq+qNeroDm8UA3dDryHI0ipRU+3BejbmBd3J3MnxVpcATlHbPK7cIdP3CSnKjOPWl5wg5XRW6Jf3inldYEUr9syw4Gn0uy1S7tjVJmLfbV4+zGhSre8xWEkVbD8mSKkxb3Vr3DxzCnLa3TTZLbEU7mA5UkBVLMSieJbbFBrf2sObfe2zfeT8zD+0TFM7XMQTzd2U1aeFNB6SxX1DN1oWctY+rp+jtgsogODdgdz+KXrYDJWbiSLOtUPx45a0pCpUNooy25ofLm2ilD2GWA98blXcterC+NEU5DFH/P96pnIMgQDBSCCWwFqQstOk334WZS4OqEUsXvbcTW4LYDLiF2bThrDVrPzfwaldi7xZZQkqXcoHTYqorXHKtS4+zg+mkBSYFOKuqYQk0M0CHJJtKvovDJIvs1DCIZ8HnVXhDDlQ8c3djq0QAPM+G/vH9Ra4DOCU1gqvUottkL1rV0EeoX5TGCKCvccx7Echwk8i8bcp6MQh62YJ4bD1kpArncL0zMjs0JquEvFhDqDNLVreE0QkglD1c+wV+fhahjdXz5S5xRLo9BGsa6qoCzrz9g+1d13xZxN+u90A15iumE9fz+SS1TXOyV5as+D24Fzg=="
                ],
                'addData' => [
                    'sender' => [
                        'address' => $province . $city . $district . $detail,
                        'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                        'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                        'phone' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                    ]
                ],
            ];
        } else {
            return [
                'documentID' => $orderIdStr,
                'contents' => [
                    [
                        // 'encryptedData' => $encryptedData,
                        //'signature'           => '',
                        'templateURL' => $template['template_url'],
                        'data' => [
                            'sender' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['detail']) ? $sender['detail'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => isset($sender['sender_name']) ? $sender['sender_name'] : $template['sender_name'],
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ],
                            'recipient' => [
                                'address' => [
                                    'province' => isset($sender['province']) ? $sender['province'] : $template['sender_province'],
                                    'city' => isset($sender['city']) ? $sender['city'] : $template['sender_city'],
                                    'district' => isset($sender['district']) ? $sender['district'] : $template['sender_district'],
                                    'detail' => isset($sender['detail']) ? $sender['detail'] : $template['sender_address'],
                                    'town' => '',
                                ],
                                'name' => '测试',
                                'mobile' => isset($sender['mobile']) ? $sender['mobile'] : $template['sender_mobile'],
                                'phone' => '',
                            ],
                            'routingInfo' => [
                                'consolidation' => [
                                    'name' => '',
                                    'code' => '280104',
                                ],
                                'origin' => [
                                    'code' => '',
                                    'name' => '',
                                ],
                                'sortation' => [
                                    'name' => '830',
                                    'code' => '',
                                ],
                                'routeCode' => '830-120-00 012',
                            ],
                            'waybillCode' => $waybillCode,
                            'qrCode' => $waybillCode,
                            'wpCode' => $template['wp_code'],
                        ],
                        'signature' => '',
                    ],
                    [
                        'data' => $data,
                        'templateURL' => 'https://printer-static.oss-cn-zhangjiakou.aliyuncs.com/template/cn_extra.xml'
                    ]
                ],
            ];
        }
    }

    /**
     * 切了自定义字符串
     * @param string $str
     * @return bool|string
     */
    public static function getCustomKey(string $str)
    {
        $str = substr($str, stripos($str, '.') + 1);
        $key = substr($str, 0, stripos($str, '%'));

        return $key;
    }

    /**
     * 调用平台的接口获取标准模板的URL
     * @param int $userId
     * @param int $shopId
     * @param $templateArr
     * @return mixed
     * @throws ApiException
     * @throws BusinessException
     */

    private static function getTemplateUrl(int $userId, int $shopId, array &$templateArr)
    {
        $auth = WaybillUtil::findShopWaybillAuth($shopId, $templateArr['auth_source'], $templateArr['owner_id']);
//        if (!Environment::isWx()&&in_array($templateArr['auth_source'], [Waybill::AUTH_SOURCE_DY,
//                Waybill::AUTH_SOURCE_KS,
//            Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
//            $auth = Shop::where('id', $shopId)->first();
//        } else {
//            $auth = Waybill::where([
//                'user_id'     => $userId,
//                'shop_id'     => $shopId,
//                'owner_id'    => $templateArr['owner_id'],
//                'auth_source' => $templateArr['auth_source']
//            ])->first();
//        }

        //从接口拿官方模板
        $waybillService = WaybillServiceManager::init($templateArr['auth_source'], $auth->access_token);
        $wpCode = $templateArr['wp_code'];
        $obj = $waybillService->getCloudPrintStdTemplatesNew($wpCode, array_get($templateArr, 'extended_info'));


        if (in_array($templateArr['auth_source'], [Waybill::AUTH_SOURCE_TWC, Waybill::AUTH_SOURCE_LINK, Waybill::AUTH_SOURCE_TAOBAO])) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 6;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 1;
                    break;
                default:
                    $standardWaybillType = $templateArr['style'];
            }
            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
            //快运为4
            if (empty($sample) && $templateArr['size'] == Template::TEMPLATE_SIZE_100_180) {
                $sample = collect($obj)->where('standard_waybill_type', 4)->first();
            }
            if (in_array($wpCode, NewTBApi::ZIMUJIANMAP)) {
                $sample = collect($obj)->where('standard_waybill_type', 4)->first();
                //顺丰比较特殊
                if ($wpCode == 'SF') {
                    $sample = collect($obj)->where('standard_template_name', $templateArr['name'])->first();
                }
            }
            //对中铁智慧物流二联单进行特殊识别
            if (in_array($wpCode, ['LE32538030']) && $templateArr['size'] == Template::TEMPLATE_SIZE_100_180) {
                $sample = collect($obj)->where('standard_waybill_type', 10)->first();
            }
        } else if ($templateArr['auth_source'] == Waybill::AUTH_SOURCE_DY) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 1;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 2;
                    break;
                default:
                    $standardWaybillType = 1;
            }

            if ($wpCode == 'shunfeng') {
                if ($templateArr['size'] == Template::TEMPLATE_SIZE_76_130) {
                    $sample = collect($obj)->where('standard_template_name', '顺丰一联单(76*130)')->first();
                } else {
                    $sample = collect($obj)->where('standard_template_name', '顺丰一联单(100*150)')->first();
                }
            } else {
                Log::info("获取模板", ["standardWaybillType" => $standardWaybillType, "auth_source" => $templateArr['auth_source']]);
                $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
            }
        } else if ($templateArr['auth_source'] == Waybill::AUTH_SOURCE_KS) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 6;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 1;
                    break;
                default:
                    $standardWaybillType = 6;
            }

            if ($wpCode == 'FENGWANG') {
                if ($templateArr['size'] == Template::TEMPLATE_SIZE_76_130) {
                    $sample = collect($obj)->where('standard_template_name', '丰网速运一联单标准模板')->first();
                } else {
                    $sample = collect($obj)->where('standard_template_name', '丰网150面单')->first();
                }
            } else {
                $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
            }
        } else if ($templateArr['auth_source'] == Waybill::AUTH_SOURCE_JD) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 6;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 1;
                    break;
                default:
                    //默认一联单
                    $standardWaybillType = 6;
            }
            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
        } else if ($templateArr['auth_source'] == Waybill::AUTH_SOURCE_XHS) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 1;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 2;
                    break;
                default:
                    //默认一联单
                    $standardWaybillType = 1;
            }
            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
            $standardTemplateId = array_get($sample, 'standard_template_id');
            //小红书用平台返回的模板ID，替代掉传入的
            if (isset($standardTemplateId)) {
                //如果有返账平台的模板ID
                $templateArr['parent_template_id'] = $standardTemplateId;
            }
            Log::info("获取模板", ["templateArr" => $templateArr, "standardTemplateId" => $standardTemplateId]);
        } else {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 3;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 1;
                    break;
                default:
                    $standardWaybillType = $templateArr['style'];
            }
            //百世快运模板特殊
            if ($wpCode == 'BESTQJT') {
                $standardWaybillType = 5;
            }
            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
        }
        Log::info("模板创建", ["obj" => $obj, "sample" => $sample]);
        if (!$sample) {
            throw new BusinessException("没有匹配到标准模板");
        }

        $templateUrl = is_object($sample) ? $sample->standard_template_url : $sample['standard_template_url'];
        if ($templateArr['auth_source'] == Waybill::AUTH_SOURCE_DY && $wpCode == 'youshuwuliu') {
            $templateUrl = 'https://sf3-ttcdn-tos.pstatp.com/obj/logistics-davinci/template/template_100.xml';
        }

        return $templateUrl;
    }

}
