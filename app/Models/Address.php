<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/3/17
 * Time: 15:43
 */

namespace App\Models;

use App\Constants\RedisKeyConst;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class Address extends Model
{
    use SoftDeletes;

    static $addressListByDistrictIdxList = [];
    /**
     * 国家
     */
    const LEVEL_COUNTRY = 0;
    /**
     * 省
     */
    const LEVEL_PROVINCE = 1;
    /**
     * 市
     */
    const LEVEL_CITY = 2;
    /**
     * 区
     */
    const LEVEL_DISTRICT = 3;
    /**
     * 街道
     */
    const LEVEL_TOWN = 4;

    /**
     * 国家  中国（国内）编码
     */
    const COUNTRY_CHINA_CODE = 1;

    /**
     * 其他区
     */
    const OTHER_DISTRICT_NAME = '其他区';
    const OTHER_DISTRICT_CODE = 9999;

    protected $table = 'address';

    protected $guarded = ['id'];

    /**
     * @return Collection|null
     * <AUTHOR>
     */
    public static function getAddressByCache()
    {
        $key = RedisKeyConst::ADDRESS_LIST_BY_1_2_3;
        $list = Cache::remember($key, 60, function () {
            $arr = Address::query()
                ->whereIn('level', [
                    Address::LEVEL_PROVINCE,
                    Address::LEVEL_CITY,
                    Address::LEVEL_DISTRICT,
                ])->get(['code', 'parent_code', 'name', 'level'])->toArray();
            return $arr;
        });
        if (empty($list)) {
            Cache::delete($key);
        }
        return collect($list);
    }
    /**
     * @return Collection|null
     * <AUTHOR>
     */
    public static function getAddressLv4ByCache()
    {
        $key = RedisKeyConst::ADDRESS_LIST_BY_1_2_3_4;
        $list = Cache::remember($key, 60, function () {
            $arr = Address::query()
                ->whereIn('level', [
                    Address::LEVEL_PROVINCE,
                    Address::LEVEL_CITY,
                    Address::LEVEL_DISTRICT,
                    Address::LEVEL_TOWN,
                ])->get(['code', 'parent_code', 'name', 'level'])->toArray();
            return $arr;
        });
        if (empty($list)) {
            Cache::delete($key);
        }
        return collect($list);
    }

    public static function getAddressV2ByCache($level = 3)
    {
        if ($level == 4){
            $collection = static::getAddressLv4ByCache();
        }else{
            $collection = static::getAddressByCache();
        }
        $list = $collection->toArray();
        $newList = [];
        foreach ($list as $item) {
            $name = $item['name'];
            $code = $item['code'];
            $parent_code = $item['parent_code'];

            if (!isset($newList[$parent_code.$name])) {
                $newList[$parent_code.$name] = $item;
            } else {
                // 如果已经存在相同的 "name"，则将 "code" 拼接在一起
                $newList[$parent_code.$name]['code'] .= "," . $code;
            }
        }
        $newList = array_values($newList);
        return collect($newList);
    }

    /**
     * @return array
     * <AUTHOR>
     */
    public static function getAddressByDistrictIdxList()
    {
        $key = RedisKeyConst::ADDRESS_LIST_BY_DISTRICT_IDX;
        $list = Cache::remember($key, 60, function () {
            $arr = Address::query()
                ->whereIn('level', [
                    Address::LEVEL_PROVINCE,
                    Address::LEVEL_CITY,
                    Address::LEVEL_DISTRICT,
                ])->get(['code', 'parent_code', 'name', 'level'])->toArray();
            $arr = static::formatAddressToDistrictIdx($arr);
            return $arr;
        });
        if (empty($list)) {
            Cache::delete($key);
        }
        return $list;
    }

    protected static function formatAddressToDistrictIdx($list, $parentName = '', $parentCode = 1, $level = 1)
    {
        $resArr = [];
        foreach ($list as $index => $item) {
            if ($parentCode == $item['parent_code'] && $level == $item['level']) {
                if ($level == Address::LEVEL_DISTRICT) {
                    $item['full_name'] = $parentName . $item['name'];
                    $resArr[$item['full_name']] = $item;
                } else {
                    unset($list[$index]);
                    $resArr = array_merge($resArr, static::formatAddressToDistrictIdx($list, $parentName . $item['name'], $item['code'], $level + 1));
                }
            }
        }
        return $resArr;
    }

    /**
     * 获取区域 code 通过省市区
     * <AUTHOR>
     * @param $province
     * @param $city
     * @param $district
     * @return int|mixed
     */
    public static function getDistrictCode($province, $city, $district)
    {
        if (empty(static::$addressListByDistrictIdxList)) {
            static::$addressListByDistrictIdxList = static::getAddressByDistrictIdxList();
        }
        $list = static::$addressListByDistrictIdxList;
        return $list[$province.$city.$district]['code'] ?? 0;
    }

    /**
     * 获取区域 code 通过省市区
     * <AUTHOR>
     * @param $receiver_state
     * @param $receiver_city
     * @param $receiver_district
     * @return int|mixed
     */
    public static function getDistrictCodeAndOther($receiver_state, $receiver_city, $receiver_district)
    {
        if (empty($receiver_district)) {
            return 0;
        }
        $districtCode = Address::getDistrictCode($receiver_state, $receiver_city, $receiver_district);
        if (empty($districtCode)){
            if ($receiver_city == '市辖区') { // 北京市 市辖区 朝阳区
//                $receiver_city = $receiver_state;
                // 市辖区
                $districtCode = Address::getDistrictCode($receiver_state, $receiver_state, $receiver_district);
            }elseif ($receiver_state == $receiver_city) { // 北京市 北京市 朝阳区
                $districtCode = Address::getDistrictCode($receiver_state, '市辖区', $receiver_district);
            }
        }
        if (empty($districtCode)) {
            // 其他区
            $districtCode = Address::getDistrictCode($receiver_state, $receiver_city, Address::OTHER_DISTRICT_NAME);
        }
        return $districtCode;
    }

    /**
     * 获取偏远地区的区名
     * <AUTHOR>
     */
    public static function getRemoteDistrictName()
    {
        $str = '海南省|内蒙古自治区|青海省|西藏自治区|新疆维吾尔自治区';
        $arr = explode('|', $str);
        $list = static::getAddressByCache();
        // Province City District
        $provinceCodeArr = $list->whereIn('name', $arr)->pluck('code')->toArray();
        $cityCodeArr = $list->whereIn('parent_code',$provinceCodeArr)
            ->where('level',static::LEVEL_CITY)->pluck('code')->toArray();
        $districtNameArr = $list->whereIn('parent_code',$cityCodeArr)
            ->where('level',static::LEVEL_DISTRICT)->pluck('name')->filter(function ($item) {
                return $item != '其他区';
            })->values()->toArray();
        return $districtNameArr;
    }

    /**
     * 加载区域
     * @param array $levels
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public static  function loadAddressByLevel(array $levels=[]){
        if(!empty($levels)){
            return static::query()->whereIn('level',$levels)->get();
        }
        return static::query()->get();
    }
}
