<?php

namespace App\Models;
use App\Services\BusinessException;
use App\Services\Waybill\WaybillServiceManager;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
class WaybillShareAction extends Model
{
    const ACTION_STATUS_DEL    = 2; //删除
    const ACTION_STATUS_CLOSE  = 1; //冻结,停用
    const ACTION_STATUS_REGAIN = 0; //恢复
    const ACTION_STATUS_ADD    = 3; //追加
    const ACTION_STATUS_CREATE = 4; //新建
    const ACTION_STATUS_REMOVE = 5; //减少
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'shop_name',
        'name',
        'branch_name',
        'company_id',
        'identifier',
        'wp_code',
        'wp_name',
        'balanceLimit',
        'province',
        'city',
        'district',
        'detail',
        'action',
        'street',
    ];

    public static function search(array $condition, string $keyword, string $wpCode, int $offset, int $limit, string $orderBy = '')
    {
        $query   = self::query()->where($condition);
        if ($keyword) {
            $query = $query->where(function ($query) use ($keyword) {
            $query->where('shop_name', $keyword)
                      ->orWhere('name', $keyword)
                      ->orWhere('identifier', $keyword);
            });

        }
        if ($wpCode) {
            $wpCode = explode(',', $wpCode);
            $query->whereIn('wp_code', $wpCode);
        }
        $sortArr = explode(',', $orderBy);
        return $query->limit($limit)
                     ->offset($offset)
                     ->orderBy($sortArr[0], $sortArr[1])
                     ->get();
    }

    public static function getShareCount($companyId)
    {
        //新建初始化数量
        $initCount = self::query()->where(['company_id'=>$companyId,'action'=>self::ACTION_STATUS_CREATE])->get(['balanceLimit']);
        //-1按不限量处理
        $balanceLimit = $initCount[0]['balanceLimit'];
        if($balanceLimit==-1){
            return -1;
        }
        //追加总数量
        $addShareCount = self::query()->where(['company_id'=>$companyId,'action'=>self::ACTION_STATUS_ADD])->get([DB::raw('sum(balanceLimit) as count')]);
        //减少总数量
        $removeCount = self::query()->where(['company_id'=>$companyId,'action'=>self::ACTION_STATUS_REMOVE])->get([DB::raw('sum(balanceLimit) as count')]);

        return $balanceLimit + $addShareCount[0]->count - $removeCount[0]->count;
    }
}
