<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QueryArea extends Model
{
	use SoftDeletes;

	protected $fillable = [
		'user_id',
		'shop_id',
		'name',
		'province_str',
		'city_str',
		'district_str',
		'data',
		'template_id',
		'include',
		'union_wp_code',
		'custom_original_data',
        'custom_district_str'
	];

	/**
	 * 数据处理
	 * @param array $data
	 * @return array
	 */
	public static function handleStr(array $data)
	{
		$provinceStr = '';
		$cityStr = '';
		$districtStr = '';

		if (empty($data) && !is_array($data)) {
			return [
				'province_str' => $provinceStr,
				'city_str'     => $cityStr,
				'district_str' => $districtStr,
			];
		}
		foreach ($data as $p) {
			$provinceStr .= $p['code'] . ',';
			foreach ($p['children'] as $c) {
				$cityStr .= $c['code'] . ',';
                foreach ($c['children'] as $d) {
                    $districtStr .= $d['code'] . ',';
                }
			}
		}

		return [
			'data'         => json_encode($data, JSON_UNESCAPED_UNICODE),
			'province_str' => substr($provinceStr, 0, -1),
			'city_str'     => substr($cityStr, 0, -1),
			'district_str' => substr($districtStr, 0, -1)
		];
	}

}
