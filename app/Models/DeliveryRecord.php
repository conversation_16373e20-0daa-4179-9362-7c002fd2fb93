<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DeliveryRecord extends Model
{
    protected $fillable = [
        'user_id',
        'shop_id',
        'order_id',
        'history_id',
        'order_no',
        'waybill_code',
        'wp_code',
        'type',
        'result',
        'app_id'
    ];

    protected $appends = [
    	'info'
    ];

    public function order()
    {
        return $this->belongsTo(\App\Models\Fix\Order::class, 'order_id', 'id');
    }

	public function getInfoAttribute()
	{
		$info = '发货成功';
		if ($result = isJson($this->result)) {
			$info = $result->error_msg;
		}

		return $info;
    }

    const TYPE_NORMAL = 0; //普通发货
    const TYPE_IMPORT = 1; //导入发货

    public static function search(array $condition, array $shopIds, string $search, int $offset, int $limit, string $orderBy = '',$wpCode)
    {
        $query = self::query()->where($condition)->whereIn('shop_id',$shopIds);
        if ($search) {
            $query = $query->where(function ($query) use ($search) {
                $query->where('order_no', $search)
	                ->orWhere('order_no', $search . 'A')
                    ->orWhere('waybill_code', $search);
            });
        }

        if($wpCode){
            $codeArr = explode(',',$wpCode);
            $query->whereIn('wp_code',$codeArr);
        }

        $sortArr = explode(' ', $orderBy);
        $count = $query->count();
        $ret = $query->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get()
            ->toArray();

        return [$ret, $count];
    }

    public static function searchNew($shopIds, $orderIds,$offset, $limit, $orderBy,$wpCode)
    {
        $query = self::query()->whereIn('shop_id',$shopIds)->whereIn('order_id', $orderIds);

        if($wpCode){
            $codeArr = explode(',',$wpCode);
            $query->whereIn('wp_code',$codeArr);
        }

        $sortArr = explode(' ', $orderBy);
        $count = $query->count();
        $ret = $query->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get()
            ->toArray();

        return [$ret, $count];
    }
}
