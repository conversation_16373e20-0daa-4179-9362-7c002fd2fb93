<?php

namespace App\Models;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WaybillHistory extends Model
{
    const WAYBILL_RECOVERY_NO = 0; //未回收
    const WAYBILL_RECOVERY_YES = 1; //已回收
    const WAYBILL_SOURCE_YES = 1; //是虚拟分享电子面单
    const WAYBILL_SOURCE_NO = 0; //不是虚拟分享电子面单
    const ORDER_TYPE_GENERAL = 1; //普通订单
    const ORDER_TYPE_FREE = 2; //自由订单
    const ORDER_TYPE_FACTORY = 3; //代打订单

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'order_id',
        'package_id',
        'order_no',
        'template_id',
        'auth_source',
        'parent_waybill_code',
        'waybill_code',
        'wp_code',
        'waybill_status',
        'print_data',
        'receiver_province',
        'receiver_city',
        'receiver_district',
        'receiver_name',
        'receiver_phone',
        'receiver_address',
        'source',
        'source_shopid',
        'extra',
        'app_id',
        "print_data_items",
        "name_index",
        "phone_index",
        "batch_no",
        "to_shop_id",
        "outer_order_no",
        'platform_waybill_id',
        'version',
        'waybill_index',
        'waybill_count',
        'order_type',
        'sub_waybill_codes',
        'created_by',
        'updated_by',
        'soft_remark',
        'send_content',
        'company_id',
    ];
    protected $hidden = [
//        'print_data'
    ];

    public function shop()
    {
        return $this->belongsTo('App\Models\Shop', 'shop_id', 'id');
    }
    public function toShop()
    {
        return $this->belongsTo('App\Models\Fix\Shop', 'to_shop_id', 'id');
    }
    public function order()
    {
        return $this->belongsTo(\App\Models\Fix\Order::class, 'order_id', 'id');
    }
    public function customizeOrder()
    {
        return $this->belongsTo(\App\Models\CustomizeOrder::class, 'order_id', 'id');
    }

    public function orderItem()
    {
        return $this->hasMany('App\Models\Fix\OrderItem', 'order_id', 'order_id');
    }

    public function package(): BelongsTo
    {
        return $this->belongsTo('App\Models\Package', 'package_id', 'id');
    }

    public function packageOrders()
    {
        return $this->hasMany('App\Models\PackageOrder', 'package_id', 'package_id');
    }
    public function packageOrderItems()
    {
        return $this->belongsToMany('App\Models\Fix\OrderItem', 'package_orders', 'package_id',
            'order_item_id','package_id');
    }

    public function orderCipherInfo()
    {
        return $this->belongsTo('App\Models\OrderCipherInfo', 'order_id', 'order_id');
    }

    public function template()
    {
        return $this->belongsTo('App\Models\Template', 'template_id', 'id');
    }

    public function printRecords()
    {
        return $this->hasMany('App\Models\PrintRecord', 'history_id', 'id')->orderBy('id', 'desc');
    }
    public function lastPrintRecord()
    {
        return $this->hasOne('App\Models\PrintRecord', 'history_id', 'id')->orderBy('id', 'desc');
    }

//    /**
//     * list
//     * @param array $condition
//     * @param string $search
//     * @param int $offset
//     * @param int $limit
//     * @param string $orderBy
//     * @return array
//     */
//    public static function search(array $condition, $shopId, string $keyword, int $offset, int $limit, string $orderBy = '',
//                                        $waybillStatus, $wpCode, $batch_no = '')
//    {
//        $query = self::query()->where($condition)->whereIn('waybill_histories.shop_id', $shopId);
//        $query->selectRaw("waybill_histories.id,
//			waybill_histories.auth_source,
//			waybill_histories.package_id,
//			waybill_histories.order_id,
//			waybill_histories.shop_id,
//			waybill_histories.waybill_code,
//			waybill_histories.wp_code,
//			waybill_histories.waybill_status,
//			waybill_histories.receiver_name,
//			waybill_histories.receiver_phone,
//			waybill_histories.receiver_province,
//			waybill_histories.receiver_city,
//			waybill_histories.receiver_district,
//			waybill_histories.receiver_address,
//			waybill_histories.print_data_items,
//			waybill_histories.created_at,
//			waybill_histories.batch_no,
//			GROUP_CONCAT(waybill_histories.order_no) as order_no_str,
//			GROUP_CONCAT(waybill_histories.id) as id_str,
//			GROUP_CONCAT(waybill_histories.order_id) as order_id_str");
//
//        if ($waybillStatus >= 0) {
//            switch ($waybillStatus) {
//                case '0':
//                    //已发货单号
//                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
//                        ->where('package_id', '<>', 0)
//                        ->rightJoin('orders', 'orders.id', '=', 'waybill_histories.order_id')
//                        ->where('orders.order_status', Order::ORDER_STATUS_DELIVERED)
//                        ->whereIn('orders.shop_id', $shopId);
//                    break;
//                case '1':
//                    //已回收单号
//                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_YES);
//                    break;
//                case '2':
//                    //已占用单号且打印但未发货
//                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
//                        ->where('package_id', '<>', 0)
//                        ->rightJoin('orders', 'orders.id', '=', 'waybill_histories.order_id')
//                        ->where('orders.order_status', Order::ORDER_STATUS_PAYMENT)
//                        ->where('orders.print_status', Order::PRINT_STATUS_YES)
//                        ->whereIn('orders.shop_id', $shopId);
//                    break;
//                case '3':
//                    //已占用单号但未打印
//                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
//                        ->where('package_id', '<>', 0)
//                        ->rightJoin('orders', 'orders.id', '=', 'waybill_histories.order_id')
//                        ->where('orders.print_status', Order::PRINT_STATUS_NO)
//                        ->whereIn('orders.shop_id', $shopId);
//                    break;
//            }
//        }
//
//        if ($keyword) {
//            $orderService = OrderServiceManager::create(config('app.platform'));
//            $shops = Shop::query()->whereIn('id', $shopId)->get();
//            $idArr = [];
//            foreach ($shops as $shop) {
//                $orderService->setShop($shop);
//                $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_name', $keyword));
//                $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_phone', $keyword));
//            }
//
//            $customizeOrderIds = CustomizeOrder::selectByReturnIdList($shopId, $keyword);
//            \Log::info("查询自由订单", [$keyword, $customizeOrderIds]);
//            $idArr = array_merge($idArr, $customizeOrderIds);
//            //批次号
//            //$arr = explode('-', $keyword);
//            $packageIds = Package::query()->where('batch_no', $keyword)
//                ->select('id')->get();
//
//            $query = $query->where(function ($query) use ($keyword, $idArr, $packageIds) {
//                $query->where('waybill_histories.order_no', $keyword)
//	                ->orWhere('waybill_histories.order_no', $keyword . 'A')
//	                ->orWhere('waybill_histories.batch_no', $keyword)
//                    ->orWhere('waybill_histories.waybill_code', $keyword)
//                    ->orWhere('waybill_histories.order_id', $keyword);
//                if ($idArr) {
//                    $query->orWhereIn('order_id', $idArr);
//                }
//                if ($packageIds) {
//                    $query->orWhereIn('package_id', $packageIds);
//                }
//            });
//        }
//
//        if ($wpCode) {
//            $codeArr = explode(',', $wpCode);
//            $query->whereIn('wp_code', $codeArr);
//        }
//        if ($batch_no) {
//            $arr = explode('-', $batch_no);
//            $packageQuery = Package::query()->where('batch_no', 'like', $arr[0] . '-%')
//                ->select('id')->getQuery();
//            $query->whereIn('package_id', $packageQuery);
//        }
//
//        $sortArr = explode(' ', $orderBy);
//        $query->groupBy('waybill_histories.waybill_code');
//
//        //有group by不能直接count
//        $baseSql = getSqlByQuery($query);
//        $count = \DB::select("select count(*) as count from ($baseSql) as base");
//        $ret = $query->limit($limit)
//            ->offset($offset)
//            ->orderBy('waybill_histories.created_at', $sortArr[1])
//            ->get();
//        return array($ret, $count[0]->count);
//    }

    /**
     * 添加取号记录
     * @param array $waybillHistories
     * @param int $userId
     * @param int $shopId
     * @return bool
     */
    public static function generate(array $waybillHistories, int $userId, int $shopId)
    {
        $insert = [];
        foreach ($waybillHistories as $waybillHistory) {
            $insert[] = [
                'user_id' => $userId,
                'shop_id' => $shopId,
                'order_id' => $waybillHistory['order']['id'],
                'order_no' => isset($waybillHistory['order']['order_no']) ? $waybillHistory['order']['order_no'] : $waybillHistory['order']['no'],
                'template_id' => $waybillHistory['template_id'],
                'auth_source' => $waybillHistory['auth_source'],
                'parent_waybill_code' => $waybillHistory['parent_waybill_code'],
                'waybill_code' => $waybillHistory['waybill_code'],
                'wp_code' => $waybillHistory['wp_code'],
                'print_data' => $waybillHistory['print_data'],
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];
        }
        $ret = self::query()->insert($insert);

        return $ret;
    }


    //
//    public static function deep_in_array($value, $array)
//    {
//        foreach ($array as $item) {
//            if (!is_array($item)) {
//                if ($item == $value) {
//                    return true;
//                } else {
//                    continue;
//                }
//            }
//
//            if (in_array($value, $item)) {
//                return true;
//            } else if (self::deep_in_array($value, $item)) {
//                return true;
//            }
//        }
//        return false;
//    }

    //    /**
//     * 订单导入
//     * @param string $fileName
//     * @param string $path
//     * @return mixed
//     */
//    public static function import(string $fileName, string $path)
//    {
//        $ext = explode('.', $fileName);
//        $ext = $ext[count($ext) - 1];
//        $file = self::saveInLocal($path, $ext);
//        try {
//            $excel = Excel::toArray(null, $file);
//        } catch (\Exception $e) {
//            Log::error('Excel load failed !', ['file_name' => $file, 'file' => $file]);
//        }
//        if (empty($excel)) {
//            return [];
//        }
//        self::delInLocal($file);
//        //删除表头
//        unset($excel[0][0]);
//
//        return $excel[0];
//    }

//    /**
//     * 删除本地文件
//     *
//     * @param $path
//     * @return bool
//     */
//    private static function delInLocal($path)
//    {
//        return unlink($path);
//    }

    public static function shareWaybillsearch(array $condition, string $keyword, string $wpCode, int $offset,
                                              int $limit, string $orderBy = '', $waybillCodeArr = [], $shareMallName = '')
    {
        $query = self::query()->where($condition);
        if ($keyword) {
            $query = $query->where(function ($query) use ($keyword) {
                $query->where('waybill_code', $keyword)
                    ->orWhere('name', $keyword)
                    ->orWhere('shop_name', $keyword)
                    ->orWhere('identifier', $keyword);
            });
        }
        if ($shareMallName){
            $shop = Shop::query()->where('shop_name', $shareMallName)->first();
            if (empty($shop)){
                // 店铺不存在
                throw new ApiException(ErrorConst::SHOP_NOT_EXIST);
            }
            $query->where('shop_id', $shop->id);
        }
        if ($waybillCodeArr){
            $query->whereIn('waybill_code', $waybillCodeArr);
        }
        if ($wpCode) {
            $wpCode = explode(',', $wpCode);
            $query->whereIn('wp_code', $wpCode);
        }
        if ($orderBy == "id,desc") {
            $orderBy = "created_at,desc";
        }
        $sortArr = explode(',', $orderBy);
        $query->join('shops', 'waybill_histories.shop_id', '=', 'shops.id')
            ->select('waybill_histories.waybill_code', 'waybill_histories.wp_code', 'waybill_histories.parent_waybill_code', 'waybill_histories.waybill_status', DB::raw('min(waybill_histories.created_at) as created_at'), 'waybill_histories.updated_at', 'shops.shop_name', 'shops.name', 'shops.identifier')
            ->groupBy(['waybill_code']);
//            ->distinct();
        Log::info('共享面单使用查询', ['sql' => $query->toSql(), 'binds' => $query->getBindings(), 'orderBy' => $sortArr[0], 'orderType' => $sortArr[1]]);
        $count = \DB::query()->fromSub($query, 'q1')->count();
        $ret = $query->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get();

        return [$ret, $count];
    }

    public static function shareWaybillStatistic(array $condition, string $keyword, int $offset, int $limit)
    {
        $query = self::query()->where($condition);
        if ($keyword) {
            $query = $query->where(function ($query) use ($keyword) {
                $query->where('shop_name', $keyword)
                    ->orWhere('name', $keyword)
                    ->orWhere('identifier', $keyword);
            });

        }
        return $query->join('shops', 'waybill_histories.shop_id', '=', 'shops.id')
            ->select('shops.shop_name', 'shops.name', 'shops.identifier')
            ->selectRaw('DATE_FORMAT(waybill_histories.created_at,"%Y-%m-%d") as day')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('day', 'shop_id')
            ->limit($limit)
            ->offset($offset)
            ->orderBy('day', 'desc')
            ->get();
    }


    /**
     * @param WaybillHistory $waybillHistory
     * @return array
     * <AUTHOR>
     */
    public static function getWaybillDataByWaybillHistory(WaybillHistory $waybillHistory): array
    {
        $waybillData = [
            'express_code' => $waybillHistory->wp_code,
            'express_no' => $waybillHistory->waybill_code,
        ];
        //自由打印
        if ($waybillHistory->package_id == 0) {
            $order = CustomizeOrder::find($waybillHistory->order_id);
            //订单已删除无需拉取
            if (empty($order)) {
//                return true;
            }
            $waybillData = array_merge($waybillData, [
                'type' => 0,
                'tid' => $order->order_no ?? null,
                'receiver_province' => $order->receiver_province,
                'receiver_name' => $order->receiver_name,
                'send_at' => $order->printed_at,
            ]);
        } else {
            $order = Order::find($waybillHistory->order_id);
            //订单已删除无需拉取
            if (empty($order)) {
//                echo '订单已删除无需拉取'.PHP_EOL;
//		        return true;
            }
            $receiver_name = $order->receiver_name;
            if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD])) {
                $orderCipherInfo = $order->orderCipherInfo;
                if (!empty($orderCipherInfo) && !empty($orderCipherInfo->receiver_name_mask)) {
                    $receiver_name = $orderCipherInfo->receiver_name_mask;
                }
            }
            $waybillData = array_merge($waybillData, [
                'type' => $order->type,
                'tid' => $order->tid,
                'receiver_province' => $order->receiver_state,
                'receiver_name' => $receiver_name,
                'send_at' => $order->send_at,
            ]);
        }
        return array($waybillData, $order);
    }

    /**
     * 是否已经回收
     * @return bool
     */
    public function isRecycled():bool{
        return $this->waybill_status == self::WAYBILL_RECOVERY_YES;
    }


}
