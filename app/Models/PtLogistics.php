<?php

namespace App\Models;

use DB;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PtLogistics extends Model
{
    use SoftDeletes;

    // 1:内部发货
    const SOURCE_TYPE_INSIDE = 1;
    // 2:外部发货
    const SOURCE_TYPE_OUTSIDE = 2;

    protected $table = 'pt_logistics';

    protected $fillable = [
        'shop_id',
        'order_id',
        'package_id',
        'source_type',
        'waybill_code',
        'wp_code',
        'delivery_at',
        'delivery_id',
        'waybill_wp_index',
    ];

    public function ptLogisticsItems()
    {
        return $this->hasMany(PtLogisticsItem::class, 'pt_logistics_id', 'id');
    }
    public function mergePtLogisticsItems()
    {
        // waybill_wp_index 有非常多的 '-' 脏数据
        return $this->hasMany(PtLogisticsItem::class, 'waybill_wp_index', 'waybill_wp_index')
            ->where('waybill_wp_index','!=','-');
    }

    /**
     * 拆分的包裹
     */
    public function splitSubPtLogistics()
    {
        return $this->belongsToMany(PtLogistics::class, 'packages',
            'multi_package_main_waybill_code', 'waybill_code', 'waybill_code',
            'waybill_code');
    }
    public function package()
    {
        return $this->hasOne(Package::class, 'id', 'package_id');
    }
    public function order()
    {
        return $this->hasOne(\App\Models\Fix\Order::class, 'id', 'order_id');
    }

    public function orderItem()
    {
        $this->belongsToMany(\App\Models\Fix\OrderItem::class, 'packages',
            'multi_package_main_waybill_code', 'waybill_code', 'waybill_code',
            'waybill_code');
    }
    public function orderCipherInfo()
    {
        return $this->hasOne(OrderCipherInfo::class, 'order_id', 'order_id');
    }
    public function newPackages()
    {
        return $this->hasMany(Package::class, 'waybill_wp_index', 'waybill_wp_index')->from(DB::raw('packages' . ' force index(packages_waybill_wp_index_index)'));
    }

    public function subWaybillCodes()
    {
        return $this->hasMany(Package::class, 'multi_package_main_waybill_code', 'waybill_code')->from(DB::raw('packages' . ' force index(packages_multi_package_main_waybill_code_index)'));
    }
}
