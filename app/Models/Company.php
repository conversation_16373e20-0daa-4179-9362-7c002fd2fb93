<?php

namespace App\Models;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Models\Fix\Shop;
use App\Services\BusinessException;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use App\Utils\WaybillUtil;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Company extends Model
{
    use SoftDeletes;

    const SOURCE_STATUS_STOP = 1;
    const SOURCE_STATUS_OPEN = 0;

    /**
     * 面单状态(1，正常，0，失效)
     *
     * @var null|int $waybill_auth_status
     */
    public $waybill_auth_status=null;


    /**
     * 网点生效状态(1,正常，0，失效)
     * @var null
     */
    public $available_status=null;

    protected $appends = ['waybill_auth_status','available_status'];

    /**
     * 获取面单状态
     * @return int|null
     */
    public function getWaybillAuthStatusAttribute(): ?int
    {
        return $this->waybill_auth_status;
    }

    public function getAvailableStatusAttribute($value)
    {
        return $this->available_status;
    }



    const EXPRESS_COMPANY_STATUS_OPEN = 1; //开启
    const EXPRESS_COMPANY_STATUS_CLOSED = 2; //关闭

    const SOURCE_COMPANY_STATUS_NO = 0; //不是授权分享网点
    const SOURCE_COMPANY_STATUS_YES = 1; //是授权虚拟分享网点

    const SOURCE_COMPANY_STATUS_OPEN = 0; //授权分享网点正常
    const SOURCE_COMPANY_STATUS_CLOSED = 1; //授权分享网点冻结

    const INIT_QUANTITY = 0; //面单余额初始值
    const INIT_UNLIMITE_QUANTITY = -1; //面单余额初始值无限量
    //用于快递公司区分
    const TYPE_COMMON = 0; //公用
    const TYPE_PDD = 1;
    const TYPE_TB = 2;
    const TYPE_DY = 3;
    const TYPE_JD = 4;
    const TYPE_KS = 5;
    const TYPE_WX = 6;
    const TYPE_WXSP = 7;
    const TYPE_XHS = 8;
    //直营型快递公司列表
    const ZHI_YING_COMPANY_LIST = ['SF', 'EMS', 'HOAU', 'BESTQJT', 'ZJS', 'AIR', 'RRS', 'YZXB', 'KYE', 'DB', 'OTP', 'AXWL', 'SXJD', 'JD', 'DEBANGWULIU', 'debangkuaiyun', 'SFKY', 'JIUYE', 'GJ', 'POSTB', 'CN7000001003751', 'CP570969', 'CP468398', 'FAST', 'DBKD', 'CP471906', 'EYB', 'SNWL', '*********', 'FEDEX', '5000000007756', 'shunfeng', 'ems', 'jd', 'youzhengguonei'];

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'auth_source',
        'owner_id',
        'owner_name',
        'branch_name',
        'branch_code',
        'wp_code',
        'wp_name',
        'status',
        'source',
        'source_status',
        'source_shopid',
        'quantity',
        'cancel_quantity',
        'recycled_quantity',
        'allocated_quantity',
        'templates',
        'province',
        'city',
        'district',
        'detail',
        'street',
        'settlement_code',
        'platform_account_id',
        'platform_shop_id',
        'extended_info',
        'brand_code'
    ];

    /**
     * 快递公司下的电子面单模板
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function templates()
    {
        return $this->hasMany('App\Models\Template', 'company_id', 'id');
    }

    public function user()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id');
    }
    public function sourceShop()
    {
        return $this->belongsTo('App\Models\Shop', 'source_shopid', 'id');
    }

    /**
     * 添加快递公司
     * @param string $wpCode
     * @param string $branchCode
     * @param int $userId
     * @param int $shopId
     * @param string $ownerId
     * @param string $ownerName
     * @param int $authSource
     * @param array $addrArr
     * @param string|null $platformAccountId
     * @param string|null $platformShopId
     * @param string|null $extendInfo
     * @param string|null $brand_code
     * @return Builder|Model
     * @throws ApiException
     * @throws BusinessException
     */
    public static function generate(
        string $wpCode,
        string $branchCode,
        int    $userId,
        int    $shopId,
        string $ownerId,
        string $ownerName,
        int    $authSource,
        array  $addrArr = [], ?string $platformAccountId = null,?string $platformShopId=null,?string $extendInfo=null,?string $brand_code=null
    )
    {
        $where = array_merge([
            'user_id' => $userId,
            'shop_id' => $shopId,
            'wp_code' => $wpCode,
            'branch_code' => $branchCode,
            'owner_id' => $ownerId,
            'auth_source' => $authSource
        ], $addrArr);
        $express = Company::where($where)->first();
        $auth=WaybillUtil::findShopWaybillAuth($shopId, $authSource, $ownerId);
        Log::info("添加模板",["wpCode"=>$wpCode,'branchCode'=>$branchCode,'ownerId'=>$ownerId,'authSource'=>$authSource,"extendInfo"=>$extendInfo]);
        //如果存在，直接返回
        if ($express) {
            Log::info('匹配到已存在的网点信息',$where);
            if (Environment::isKs() && empty($branchCode) && empty($express->settlement_code)){
                // 订正 直营 settlement_code 值
                $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
                $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
                $express->settlement_code = ($waybill[0]['branch_account_cols'])[0]['settlement_code'] ?? "";
                $express->save();
            }
            return $express;
        }else{
            Log::info('没有匹配到已存在的网点信息',$where);
        }
        //不存在就更新
//        if (!Environment::isWx() && in_array($authSource, [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS,
//                Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD,Waybill::AUTH_SOURCE_WXSP])) {
//            $auth = Shop::where('id', $shopId)->first();
//        } else {
//            $auth = Waybill::where([
//                'user_id' => $userId,
//                'shop_id' => $shopId,
//                'owner_id' => $ownerId,
//                'auth_source' => $authSource
//            ])->first();
//        }
//        if (!$auth) {
//            throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
////                throw new \Exception('电子面单授权信息丢失!');
//        }
        //pdd站内
        if ($authSource == Waybill::AUTH_SOURCE_PDD || $authSource == Waybill::AUTH_SOURCE_TAOBAO) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);

        } else if ($authSource == Waybill::AUTH_SOURCE_DY) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else if ($authSource == Waybill::AUTH_SOURCE_KS) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else if ($authSource == Waybill::AUTH_SOURCE_JD) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        }else if($authSource == Waybill::AUTH_SOURCE_WXSP){
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillService->setShop($auth);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        }else if($authSource == Waybill::AUTH_SOURCE_XHS){
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else {
            $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
            $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        }
        if (empty($waybill)) {
            throw new BusinessException('未开通该公司电子面单服务!');
        }else{
            Log::info("创建网点的原始数据",$waybill);
        }

        $company = collect(config('express_company'))->where('wpCode', $wpCode)->first();
        if ($branchCode) {
            foreach ($waybill as $accountCol) {
                foreach ($accountCol['branch_account_cols'] as $branch) {
                    if ($branch['branch_code'] == $branchCode) {
                        $companyArr = array_merge([
                            'user_id' => $userId,
                            'shop_id' => $shopId,
                            'auth_source' => $authSource,
                            'wp_code' => $wpCode,
                            'owner_id' => $ownerId,
                            'owner_name' => $ownerName,
                            'branch_name' => $branch['branch_name'],
                            'branch_code' => $branch['branch_code'],
                            'wp_name' => array_get($company, 'name', ''),
                            'status' => self::EXPRESS_COMPANY_STATUS_OPEN,
                            'quantity' => $branch['quantity'],
                            'cancel_quantity' => $branch['cancel_quantity'],
                            'recycled_quantity' => $branch['recycled_quantity'],
                            'allocated_quantity' => $branch['allocated_quantity'],
                            'templates' => json_encode($waybillTemp),
                            'platform_account_id' => $platformAccountId,
                            'platform_shop_id' => $platformShopId,
                            'extended_info'=>$extendInfo
                        ], $addrArr);
                        Log::info("创建加盟网点", $companyArr);
                        $express = self::query()->create($companyArr);
                        if (!$express) {
                            Log::error('express_company add failed !',
                                ['wp_code' => $wpCode, 'user_id' => $userId]);
                            throw new BusinessException('快递公司添加失败！');
                        }
                    }
                }
            }
        } else {
            //直营快递公司
            $companyArr = array_merge([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'auth_source' => $authSource,
                'wp_code' => $wpCode,
                'owner_id' => $ownerId,
                'owner_name' => $ownerName,
                'branch_name' => ($waybill[0]['branch_account_cols'])[0]['branch_name'],
                'branch_code' => ($waybill[0]['branch_account_cols'])[0]['branch_code'],
                'wp_name' => array_get($company, 'name', ''),
                'status' => self::EXPRESS_COMPANY_STATUS_OPEN,
                'quantity' => ($waybill[0]['branch_account_cols'])[0]['quantity'],
                'cancel_quantity' => ($waybill[0]['branch_account_cols'])[0]['cancel_quantity'],
                'recycled_quantity' => ($waybill[0]['branch_account_cols'])[0]['recycled_quantity'],
                'allocated_quantity' => ($waybill[0]['branch_account_cols'])[0]['allocated_quantity'],
                'templates' => json_encode($waybillTemp),
                'settlement_code' => ($waybill[0]['branch_account_cols'])[0]['settlement_code'] ?? "",
                'platform_account_id' => $platformAccountId,
                'platform_shop_id' => $platformShopId,
                'brand_code'=>$brand_code,
                'extended_info'=>$extendInfo
            ], $addrArr);
            $express = self::create($companyArr);
            Log::info("创建直营网点", [$companyArr,$waybill[0]]);
            if (!$express) {
                Log::error('express_company add failed !', ['wp_code' => $wpCode, 'user_id' => $userId]);
                throw new BusinessException('快递公司添加失败！');
            }
        }


        return $express;
    }


    public static function search(array $condition, string $keyword, int $offset, int $limit, string $orderBy = '',
                                  $wpCode, $shopIdArr, $queryType)
    {
        $query = self::query()->with(['sourceShop'])->where($condition);
        if ($queryType == 'toOthers'){
            $query->whereIn('source_shopid', $shopIdArr);
        }else{
            $query->whereIn('shop_id', $shopIdArr);
        }
        if ($keyword) {
            $queryShop = Shop::query()->where('shop_name', 'like', '%' . $keyword . '%')->first();
            $query = $query->where(function ($query) use ($keyword,$queryShop) {
                $query->where('name', $keyword)
                    ->orWhere('shop_name', $keyword)
                    ->orWhere('identifier', $keyword);
                if ($queryShop) {
                    $query->orWhere('source_shopid', $queryShop->id);
                    $query->orWhere('shop_id', $queryShop->id);
                }
            });
        }

        if ($wpCode) {
            $codeArr = explode(',', $wpCode);
            $query->whereIn('wp_code', $codeArr);
        }
        $sortArr = explode(',', $orderBy);
        $query->join('shops', 'companies.shop_id', '=', 'shops.id');

        $count = $query->count();
        $list = $query->select('companies.*', 'shops.shop_name', 'shops.name', 'shops.identifier')
            ->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get();
        return [$count,$list];
    }


    /**
     * 处理使用数据
     * @return void
     * @throws Exception
     */
    public static function dealUseQuantity($id, $num)
    {
        Log::info("数据量减少", ["id" => $id, "num" => $num]);
        $company = Company::findOrFail($id);
        if ($company->source != Company::SOURCE_COMPANY_STATUS_YES) {
            return;
        }
        if ($company->quantity !== Company::INIT_UNLIMITE_QUANTITY) {
            Company::decrementQuantityAndIncrementAllocatedQuantity($id, $num);
        } else {
            //无限量的不用减少电子面单余额数量，已用面单数量增加
            Company::incrementAllocatedQuantity($id, $num);
        }
        Log::info("授权虚拟分享网点减少");

    }

    public static function incrementAllocatedQuantity($id, $num)
    {
        $query = Company::where('id', $id);
        $query->increment('allocated_quantity', $num);
    }

    /**
     * @throws Exception
     */
    public static function decrementQuantityAndIncrementAllocatedQuantity($id, $num)
    {
        $res = Company::query()
            ->where('id', $id)
            ->where('quantity', '>=', $num)
            ->update([
                'quantity' => DB::raw("`quantity` - $num"),
                'allocated_quantity' => DB::raw("`allocated_quantity` + $num")
            ]);
        if (!$res) {
            Log::info('decrementQuantityAndIncrementAllocatedQuantity failed !',['id'=>$id,'num'=>$num]);
            throw new Exception(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
        }
    }

    /**
     * 检查用量-只针对虚拟网点，实际网点的用量通过接口校验
     * @param $id
     * @param $num
     * @return bool
     */
    public static function hasEnoughQuantity($id, $num): bool
    {
        $company = Company::findOrFail($id);
        //如果是飞虚拟节点，跳过检查，用量通过接口实际校验
        if ($company->source != Company::SOURCE_COMPANY_STATUS_YES) {
            return true;
        }
        $quantity = $company->quantity;
        \Log::info('检查余额', ["quantity" => $quantity, "num" => $num]);
        return $quantity > $num;

    }

    /**
     * 是否是三方共享面单
     * @return bool
     */
    public  function isThirdShare(): bool
    {
        return $this->source == self::SOURCE_COMPANY_STATUS_YES;
    }

    /**
     * 网点地址是否匹配
     * @param array{province:string,city:string,district:string,detail:string,street_name:string} $address
     * @return bool
     */
    public function isAddressMatch(array $address): bool
    {
        if($this->province == $address['province'] &&
        $this->city == $address['city'] &&
        $this->district == $address['district'] &&
        $this->detail == $address['detail']) {
            //如果有街道信息，就判断街道信息
            if (!empty($address['street_name']) && !empty($company->street) && $company->street != $address['street_name']) {
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * @return string 网点地址描述
     */
    public function getCompanyAddressDesc(): string{
        return  PlatformConst::WAYBILL_AUTH_MAP[$this->auth_source].' '.   $this->province . $this->city . $this->district .$this->street??'' . $this->detail.$this->branch_name??''.$this->branch_code??'';
    }


}
