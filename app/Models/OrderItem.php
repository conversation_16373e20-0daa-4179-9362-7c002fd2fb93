<?php

namespace App\Models;

use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

/**
 * @property int $id ID
 * @property int $user_id 用户id
 * @property int $shop_id 店铺id
 * @property int $order_id 主订单主键
 * @property string $tid 主订单
 * @property string $oid 子订单号
 * @property int $type 订单类型
 * @property float $payment 实付金额
 * @property float $total_fee 总金额
 * @property float $discount_fee 优惠金额
 * @property int $goods_type 商品类型：1 普通，2 赠品
 * @property float $goods_price 商品单价
 * @property string|null $goods_pic 商品图片
 * @property string|null $goods_title 商品标题
 * @property int $goods_num 商品数量
 * @property string|null $num_iid 商品id
 * @property string|null $sku_id SKU id
 * @property string|null $sku_uuid 商品SKU UUID,目前JD需要用
 * @property string $sku_value SKU的值。如：手机套餐:官方标配
 * @property string $sku_value1 SKU值1
 * @property string $sku_value2 SKU值2
 * @property string|null $outer_iid 商家外部商品编码
 * @property string|null $outer_sku_iid 商家外部sku编码
 * @property string $refund_id 退款id
 * @property int $refund_status 退款状态
 * @property int $refund_sub_status
 * @property int $is_comment 是否评价 (1:已评价)
 * @property int $print_status 打印状态
 * @property int $print_num 打印次数
 * @property \Carbon\Carbon|null $refund_created_at 退款创建时间
 * @property \Carbon\Carbon|null $refund_updated_at 退款修改时间
 * @property \Carbon\Carbon|null $order_created_at 订单创建时间
 * @property \Carbon\Carbon|null $order_updated_at 订单修改时间
 * @property \Carbon\Carbon|null $created_at
 * @property \Carbon\Carbon|null $updated_at
 * @property \Carbon\Carbon|null $deleted_at
 * @property int $status 订单状态
 * @property string|null $waybill_code 运单号
 * @property \Carbon\Carbon|null $send_at 发货时间
 * @property \Carbon\Carbon|null $first_send_at 首次发货时间
 * @property string|null $first_send_waybill_code 首次发货运单号
 * @property string|null $custom_order_sku_value 自定义sku内容
 * @property string $product_no 货号
 * @property int $send_remain_num 发货剩余数量
 * @property int $send_num 已发货数量
 * @property int $pre_send_num 预发货数量
 * @property string|null $author_id 带货人id
 * @property string|null $author_name 带货人姓名
 * @property \Carbon\Carbon|null $print_tag_at 打印标签时间
 * @property int $print_tag_num 打印标签次数
 * @property int $is_pre_sale 是否预售
 * @property \Carbon\Carbon|null $promise_ship_at 承诺发货时间
 */
class OrderItem extends Model
{
    protected $table = 'order_items';

    const ORDER_STATUS_UNKNOWN   = 0;  //未知状态
    const ORDER_STATUS_PADDING   = 10; //代付款
    const ORDER_STATUS_PAYMENT   = 30; //已付款
    const ORDER_STATUS_PART_DELIVERED = 35; //部分发货
    const ORDER_STATUS_DELIVERED = 40; //已发货
    const ORDER_STATUS_RECEIVED  = 50; //已签收
    const ORDER_STATUS_SUCCESS   = 70; //订单成功
    const ORDER_STATUS_FAILED    = 80; //订单失败
    const ORDER_STATUS_CLOSE     = 90; //交易关闭
    const ORDER_STATUS_ABNORMAL  = 100; //交易异常


    const REFUND_STATUS_NO = 0; // 无售后
    const REFUND_STATUS_WAIT_SELLER = 1; // 商家待处理
    const REFUND_STATUS_CLOSED = 2; // 售后关闭
    const REFUND_STATUS_PROCESSING = 3; // 售后处理中
    const REFUND_STATUS_REFUNDING = 4; // 退款中
    const REFUND_STATUS_SUCCESS = 5; // 退款成功
    const REFUND_STATUS_PLATFORM_IN = 6; // 平台介入
    const REFUND_STATUS_ABNORMAL = 7; // 异常

    const PRINT_STATUS_YES          = 1;         //已打印
    const PRINT_STATUS_NO           = 0;         //未打印
    const GOODS_TYPE_NORMAL = 1; // 正常
    const GOODS_TYPE_GIFT = 2; // 赠品

    public $statusDesc = [
        self::ORDER_STATUS_UNKNOWN => "未知状态",
        self::ORDER_STATUS_PADDING => "代付款",
        self::ORDER_STATUS_PAYMENT => "待发货",
        self::ORDER_STATUS_PART_DELIVERED => "部分发货",
        self::ORDER_STATUS_DELIVERED => "已发货",
        self::ORDER_STATUS_RECEIVED => "已签收",
        self::ORDER_STATUS_SUCCESS => "订单成功",
        self::ORDER_STATUS_FAILED => "订单失败",
        self::ORDER_STATUS_CLOSE  => "交易关闭",
        self::ORDER_STATUS_ABNORMAL => "交易异常",
    ];
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'order_id',
        'tid',
        'oid',
        'type',
        'user_id',
        'shop_id',
        'payment',
        'total_fee',
        'discount_fee',
        'goods_type',
        'goods_pic',
        'goods_title',
        'goods_price',
        'goods_num',
        'num_iid',
        'sku_id',
        'sku_uuid',
        'sku_value',
        'sku_value1',
        'sku_value2',
        'outer_iid',
        'outer_sku_iid',
        'refund_id',
        'refund_status',
        'is_comment',
        'refund_created_at',
        'refund_updated_at',
        'order_created_at',
        'order_updated_at',
        'status',
        'waybill_code',
        'is_deliver_goods',
        'send_at',
        'product_no',
        'aftersale_extra',
        'send_remain_num',
        'send_num',
        'pre_send_num',
        'author_id',
        'author_name',
        'print_tag_at',
        'print_tag_num',
        'is_pre_sale',
        'promise_ship_at',
        'refund_sub_status',
    ];

    protected $appends = ['sku_desc', 'custom_title','custom_sku_value','status_desc','refund_sub_status_desc'];

    public function getSkuDescAttribute()
    {
        //淘宝简化规格
        if (config('app.platform') == PlatformConst::TAOBAO) {
            if (!empty($this->attributes['sku_value'])) {
                $skuArr = explode(';', $this->attributes['sku_value']);
                foreach ($skuArr as $value) {
                    $skuValue[] = explode(':', $value)[1];
                }

                return implode(';', $skuValue);
            }
        } else {
            return $this->attributes['sku_value']??'';
        }
    }

    public function getStatusDescAttribute(){
        return $this->statusDesc[$this->attributes['status']];
    }

    public function getCheckedAttribute(){
        if ($this->attributes['status'] > Order::ORDER_STATUS_PAYMENT) {
            return false;
        }
        return  true;
    }

    public function getCustomTitleAttribute()
    {
        $custom = $this->customGoods;
        if ($custom && $custom->custom_title) {
            return $custom->custom_title;
        }
        return $this->attributes['goods_title']??'';
    }
    public function getCustomSkuValueAttribute()
    {
       $custom = $this->customGoodsSkus;
       if ($custom && $custom->custom_sku_value) {
            return $custom->custom_sku_value;
       }

       return $this->attributes['sku_value']??'';
    }
    public function customGoods()
    {
        return $this->hasOne('App\Models\Goods', 'num_iid', 'num_iid');
    }

    public function customGoodsSkus()
    {
        return $this->hasOne('App\Models\GoodsSku', 'sku_id', 'sku_id');
    }
    public function order()
    {
        return $this->belongsTo('App\Models\Order', 'order_id', 'id');
    }

    public function orderItemExtra()
    {
        return $this->hasOne(OrderItemExtra::class, 'order_item_id', 'id');
    }
    public function shop()
    {
        return $this->belongsTo('App\Models\Shop', 'shop_id', 'id');
    }
    public function packages()
    {
        return $this->belongsToMany(Package::class, 'package_orders', 'order_item_id', 'package_id');
    }

    /**
     * 已发货包裹列表
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function shippedPackageOrders()
    {
        return $this->hasMany('App\Models\PackageOrder', 'order_item_id', 'id')
            ->where('source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY);
    }

    public static function handleSearch($query, string $search = '')
    {
        if (!$search) {
            return $query;
        }
        $query->where('tid', $search)
            ->orWhere('sku_value', $search)
            ->orWhere('sku_id', $search)
            ->orWhere('goods_title', $search)
            ->whereHas('order', function ($query) use ($search) {
                $query->orWhere('express_no', $search)
                    ->orWhere('receiver_phone', $search)
                    ->orWhere('seller_memo', $search)// 卖家备注
                    ->orWhere('receiver_name', $search);
            });

        return $query;
    }

    public function getRefundSubStatusDescAttribute(){
        $refund_sub_status = $this->attributes['refund_sub_status'];
        if (in_array($refund_sub_status,RefundSubStatusConst::REFUND_COMPLETE_ARRAY)){
            return 'REFUND_COMPLETE'; // 退款完成
        }
        if (in_array($refund_sub_status,RefundSubStatusConst::REFUND_CLOSE_ARRAY)){
            return 'REFUND_CLOSE'; // 退款关闭
        }
        if (in_array($refund_sub_status,RefundSubStatusConst::REFUND_PROCESSING_ARRAY)){
            return 'REFUND_PROCESSING'; // 退款中
        }
        return '';
    }

}
