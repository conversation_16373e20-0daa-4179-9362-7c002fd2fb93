<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/2/17
 * Time: 16:57
 */

namespace App\Models;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShopBind extends Model
{
    use SoftDeletes;

    const BIND_TYPE_BROTHER = 1;
    const BIND_TYPE_FATHER  = 2;

    const TYPE_MY_SHOP = 0; //本店铺
    const TYPE_ME_BIND = 1; //同级
    const TYPE_BIND_ME = 2; //主从

    const TYPE_AGENT_PRINT_SHOP_2_FACTORY = 3; //从我们应用内建立商家和厂家的关系
    const TYPE_AGENT_PRINT_FACTORY_2_SHOP = 4; //从外部APP建立代打关系
    const TYPE_AGENT_PRINT_FACTORY_SHOP   = 5; //平台代打关系


    //店铺场景
    const SCENE_SHOP = [self::TYPE_MY_SHOP, self::TYPE_ME_BIND, self::TYPE_BIND_ME];

    /**
     * 店铺拥有者
     */
    const SCENE_SELF_OWNER = [self::TYPE_MY_SHOP, self::TYPE_ME_BIND];
    //代打场景
    const SCENE_AGENT_PRINT = [self::TYPE_AGENT_PRINT_SHOP_2_FACTORY, self::TYPE_AGENT_PRINT_FACTORY_2_SHOP];
    public const BIND_ME_MASTER = 'master'; //主从关系里面的主
    public const BIND_ME_SLAVE = 'slave'; //主从管理里面的从

    protected $fillable = [
        'f_shop_id',
        'o_shop_id',
        'type',
        'remark',
        'created_at',
        'updated_at',
        'deleted_at',
    ];
    public function oshop()
    {
        return $this->belongsTo(\App\Models\Fix\Shop::class, 'o_shop_id', 'id');
    }
    public function fshop()
    {
        return $this->belongsTo(\App\Models\Fix\Shop::class, 'f_shop_id', 'id');
    }

    /**
     * 获取店铺关联列表 默认去店铺绑定关系的集合
     * @param int $shopId
     * @param int[] $types
     * @param bool $includeSelf
     * @param bool $isQueryDel
     * @return array
     */
    public static function getAllRelationShop(int $shopId, array $types = self::SCENE_SHOP,bool $includeSelf=true,
                                              bool $isQueryDel = false): array
    {
        $shopList = [];
        if (!$shopId) {
            return $shopList;
        }
        $shopIds = [];
        if ($includeSelf){
            $shopIds []= $shopId;
        }
        //查询绑定关系
        $builder = self::query()->whereIn('type', $types)->where(function ($query) use ($shopId) {
            $query->where('f_shop_id', $shopId)->orWhere('o_shop_id', $shopId);
        });
        if ($isQueryDel) {
            $builder->withTrashed();
            // 时间在昨天之后
//            $builder->where('deleted_at',  '>', date('Y-m-d', strtotime('-3 day')));
        }
        $bindShopList = $builder->get();
//        Log::info("查找绑定的店铺",[$bindShopList,$shopId]);
        foreach ($bindShopList as $value) {
            //发起绑定者是本店铺 都可以查看
            if ($value['f_shop_id'] == $shopId) {
                $shopIds[] = $value['o_shop_id'];
            }

            //绑定我的店铺 并且是兄弟关系
            if ($value['o_shop_id'] == $shopId && $value['type'] == self::BIND_TYPE_BROTHER) {
                $shopIds[] = $value['f_shop_id'];
            }
        }
        array_unique($shopIds);
        $shopList = Shop::query()->with('userExtra')->whereIn('id', $shopIds)->get()->toArray();
//        $shopList = \App\Models\Fix\Shop::query()->whereIn('id', $shopIds)->get()->toArray();
        $identifierSort = ShopBindSort::getShopBindSort($shopId);
        $extShopList = [];
        foreach ($shopList as $shop) {
            $shopBindSort = $identifierSort->get($shop['identifier']);
            $shop['sort']=isset($shopBindSort)?$shopBindSort->sort: 0;
            $extShopList[] = $shop;
//            \Log::info("查找绑定的店铺",[$shop]);
        }
        return  $extShopList;
    }


    /**
     * 绑定店铺场景的关系
     * @param $fShopId
     * @param $oShopId
     * @param $type
     * @param string $remark
     * @return bool
     */
    public static function bindShop($fShopId, $oShopId, $type, $remark = '')
    {
        if ($type == self::BIND_TYPE_FATHER) {
            //查询是否绑定过了
            $result = self::query()
                ->where(['f_shop_id' => $fShopId, 'o_shop_id' => $oShopId, 'type' => $type])
                ->first();
            if (!empty($result)) {
                return true;
            }
            //是否已经被另一方绑定过了 更改为兄弟关系
            $result = self::query()
                ->where(['f_shop_id' => $oShopId, 'o_shop_id' => $fShopId, 'type' => $type])
                ->first();
            if (!empty($result)) {
                $result->type = self::BIND_TYPE_BROTHER;
                $result->save();
                return true;
            }
        } elseif ($type == self::BIND_TYPE_FATHER) {
            //是否已经是兄弟关系了
            $result = self::query()
                ->where('type', self::BIND_TYPE_BROTHER)
                ->where(['f_shop_id' => $fShopId, 'o_shop_id' => $oShopId])
                ->where(function ($query) use ($fShopId, $oShopId) {
                    $query->orWhere(['f_shop_id' => $oShopId, 'o_shop_id' => $fShopId]);
                })
                ->first();
            if (!empty($result)) {
                return true;
            }
        }else{
            $exists = self::query()
                ->where('type', $type)
                ->where(['f_shop_id' => $fShopId, 'o_shop_id' => $oShopId])
                ->exists();
            if ($exists) {
                return true;
            }
        }

        $res = self::create([
            'f_shop_id' => $fShopId,
            'o_shop_id' => $oShopId,
            'type' => $type,
            'remark' => $remark,
        ]);

        return $res;
    }

    /**
     * 解除店铺场景绑定关系
     * @param $fShopId
     * @param $oShopId
     * @return bool
     */
    public static function unbindShop($fShopId, $oShopId)
    {
        $bindShopList = self::query()->whereIn('type', self::SCENE_SHOP)->where(function ($query) use ($oShopId) {
            $query->where('f_shop_id', $oShopId)->orWhere('o_shop_id', $oShopId);
        })->get();
        foreach ($bindShopList as $value) {
            //有关系的全都干掉
            if ($value['f_shop_id'] == $fShopId || $value['o_shop_id'] == $fShopId) {
                $value->delete();
            }
        }

        return true;
    }

    /**
     * 解除代打场景绑定关系
     * <AUTHOR>
     * @param $fShopId
     * @param $oShopId
     */
    public static function unbindFactoryShop($fShopId, $oShopId)
    {
        $bindShopList = self::query()->whereIn('type', self::SCENE_AGENT_PRINT)->where(function ($query) use ($oShopId) {
            $query->where('f_shop_id', $oShopId)->orWhere('o_shop_id', $oShopId);
        })->get();
        foreach ($bindShopList as $value) {
            //有关系的全都干掉
            if ($value['f_shop_id'] == $fShopId || $value['o_shop_id'] == $fShopId) {
                $value->delete();
            }
        }
    }

    /**
     * 检查店铺关系
     * @param int $currentShopId
     * <AUTHOR>
     */
    public static function checkShopRelation(int $currentShopId, int $targetShopId)
    {
        $shopList = self::getAllRelationShop($currentShopId);
        $shopIds = array_pluck($shopList, 'id');
        if (!in_array($targetShopId, $shopIds)) {
            throw new ApiException(ErrorConst::SHOP_NOT_BELONG_YOU);
        }
    }

    public static function getAllRelationShopIds(int $shopId,bool $isQueryDel = false,array $types=self::SCENE_SHOP): array
    {
        $shopList = self::getAllRelationShop($shopId,$types,true,$isQueryDel);
        return array_pluck($shopList, 'id');
    }

    /**
     * 获取有效的店铺 ids
     * @param int $shopId
     * @param array $identifierArray
     * @return array
     * @throws ApiException
     * <AUTHOR>
     */
    public static function getValidRelationShopIds(int $shopId, array $identifierArray)
    {
        $shopList = self::getAllRelationShop($shopId);
        $shopList = collect($shopList)->pluck(null, 'identifier')->toArray();
        $shopIds = [];
        foreach ($identifierArray as $index => $item) {
            if (empty($shopList[$item])) {
                throw new ApiException(ErrorConst::SHOP_NOT_BELONG_YOU);
            }
            $shopIds[] = $shopList[$item]['id'];
        }
        return $shopIds;
    }

    /**
     * 获取有效的店铺 identifier
     * @param int $shopId
     * @param array $identifierArray
     * @param $printMode
     * @return array
     * <AUTHOR>
     */
    public static function getValidIdentifierByRelation(int $shopId, array $identifierArray, $printMode): array
    {
        $types = self::SCENE_SHOP;
        if ($printMode == 2){
            $types = self::SCENE_AGENT_PRINT;
        }
        $shopList = self::getAllRelationShop($shopId, $types);
        $shopList = collect($shopList)->pluck(null, 'identifier')->toArray();
        $identifierArr = [];
        foreach ($identifierArray as $index => $item) {
            if (!empty($shopList[$item])) {
                $identifierArr[] = $shopList[$item]['identifier'];
            }
        }
        return $identifierArr;
    }

    /**
     * 绑定代打关系
     * @param int $factoryId
     * @param int $shopId
     * @return Model
     */
    public static function bindAgentPrintFromFactory2Shop(int $factoryId ,int $shopId ){
        $shopBind=[];
        $shopBind["f_shop_id"]=$factoryId;
        $shopBind["o_shop_id"]=$shopId;
        $shopBind["type"]=self::TYPE_AGENT_PRINT_FACTORY_2_SHOP;
        self::query()->updateOrCreate([
            'f_shop_id' =>$factoryId,
            'type' =>self::TYPE_AGENT_PRINT_FACTORY_2_SHOP,
            'o_shop_id' => $shopId,
        ], $shopBind);
        return $shopBind;

    }

    /**
     * 获取工厂的全部商家ID
     * @param $factoryIds
     * @return array
     */
    public static function getShopIdsOfFactory($factoryIds): array
    {
        return  ShopBind::query()->whereIn("f_shop_id", $factoryIds)->whereIn('type', ShopBind::SCENE_AGENT_PRINT)->get(['o_shop_id'])->map(function ($item){
            return   $item['o_shop_id'];
        })->unique()->all();
    }

    /**
     * 判断厂家是不是有商家
     * @param $factoryIds
     * @return void
     */
    public static function factoryHasShop($factoryIds):Collection{
        return  ShopBind::query()->whereIn("f_shop_id", $factoryIds)->whereIn('type', ShopBind::SCENE_AGENT_PRINT)->groupBy('f_shop_id')->get(['f_shop_id',DB::raw("min(`o_shop_id`)")])->keyBy('f_shop_id');
    }

    /**
     * 获取o_shop_id 列表 通过 $f_shop_ids 和 $type
     * @param array $f_shop_ids
     * @param $type
     * @return array
     * <AUTHOR>
     */
    public static function getOShopIdsByType(array $f_shop_ids, $type): array
    {
        return  ShopBind::query()->whereIn("f_shop_id", $f_shop_ids)->where('type', $type)->get(['o_shop_id'])
            ->map(function ($item){return $item['o_shop_id'];})->unique()->all();
    }

    /**
     * 获取o_shop_id 列表 通过 $f_shop_ids 和 $type
     * @param $f_shop_id
     * @param $o_shop_id
     * @param $type
     * @return \Illuminate\Database\Eloquent\Builder|Model|object|null
     * <AUTHOR>
     */
    public static function getBindByShopIdType($f_shop_id, $o_shop_id, $type)
    {
        return  ShopBind::query()->where("f_shop_id", $f_shop_id)->where('o_shop_id',$o_shop_id)
            ->where('type', $type)->first();
    }

    /**
     * 获取店铺跟从店铺绑定的店铺，包括了主从场景和兄弟场景
     *
     * @param $o_shop_id
     * @return array
     */
    public static function getShopSceneBindShopIds($o_shop_id): array
    {
        $binds = ShopBind::query()->where('o_shop_id',$o_shop_id)
            ->whereIn('type', self::SCENE_SHOP)->get();
        $binds=$binds->filter(function ($item) use ($o_shop_id){
            return $item->f_shop_id != $o_shop_id && $item->type!=0;
        });
        \Log::info('getShopSceneBindShopIds',[$o_shop_id,$binds->toArray()]);
        if ($binds->count()>=1){
           return array_column($binds->toArray(),'f_shop_id');
        }
        return [];
    }



}
