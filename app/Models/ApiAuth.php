<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use function foo\func;

/**
 * @property int $id 主键ID
 * @property string|null $name 机构名称
 * @property string|null $desc 机构备注
 * @property string|null $app_id app_id
 * @property string|null $app_key app_key
 * @property \Illuminate\Support\Carbon|null $created_at 创建时间
 * @property \Illuminate\Support\Carbon|null $updated_at 更新时间
 * @property \Illuminate\Support\Carbon|null $deleted_at 删除时间
 * @property string|null $shop_identifier 店铺唯一身份
 * @property int $bind_waybill_shop_limit 绑定运单店铺限制，默认为5
 * @property string|null $auth_to_app_id 授权appid
 * @property string $ip_whitelist ip白名单，默认为空字符串
 * @property int $request_freq_limit 请求速率限制，默认为0，0表示不限制
 * @property string|null $related_app_ids 关联应用ID，用逗号分割
 *
 * @package App\Models
 */
class ApiAuth extends Model
{

    use SoftDeletes;

    protected $connection = 'mysql';

    protected $table = 'api_auth';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'name',
        'app_id',
        'app_key',
        'desc',
        'shop_identifier',
        'updated_at',
        'deleted_at',
        'bind_waybill_shop_limit',
        'auth_to_app_id',
        'request_freq_limit',
        'ip_whitelist',
    ];

    public static function search(array $condition, int $offset, int $limit, string $orderBy = 'id desc')
    {
        $sortArr = explode(' ', $orderBy);

        $query   = ApiAuth::where($condition);

        return $query->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get();
    }

    public static function firstByAppId(string $appId)
    {
        return ApiAuth::where('app_id', $appId)->first();
    }

    public static function getAllAuthedAppId(string $appId){
        return ApiAuth::where('auth_to_app_id', $appId)->get()->map(function($item){
            return $item['app_id'];
        })->all();
    }

    /**
     * 获取appid授权的所有appid
     * @param array $appIds
     * @return array
     */
    public static function getAllAuthedAppIds(array $appIds): array
    {
        return ApiAuth::whereIn('app_id', $appIds)->get()->map(function($item){
            return $item['auth_to_app_id'];
        })->all();
    }

    /**
     * 获取关联的appId,这里没有使用递归，是为了避免使用递归导致的死循环问题
     * @param  string  $appId
     * @return array
     */
    public static function getRelatedAppIds(string $appId): array
    {
        $relatedAppIds = [];
        $queue         = [$appId];
        $visited       = [];

        while (!empty($queue)) {
            $currentAppId = array_shift($queue);
            if (isset($visited[$currentAppId])) {
                continue;
            }

            $visited[$currentAppId] = true;

            $apiAuth = self::where('app_id', $currentAppId)->first();
            if (!$apiAuth || empty($apiAuth->related_app_ids)) {
                continue;
            }

            $appIds = explode(',', $apiAuth->related_app_ids);
            foreach ($appIds as $id) {
                if (!isset($visited[$id])) {
                    $queue[] = $id;
                }
            }

            $relatedAppIds = array_merge($relatedAppIds, $appIds);
        }

        return array_unique($relatedAppIds);
    }
}
