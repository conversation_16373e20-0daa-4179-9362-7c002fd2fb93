<?php

namespace App\Models;

use App\Constants\PaymentConst;
use App\Exceptions\ErrorCodeException;
use App\Services\Order\Result\SubscriptionServiceInfo;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use App\Utils\RedisUtil;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Facades\Log;

/**
 * @property int $id
 * @property int $user_id 用户ID
 * @property int $platform_type 平台类型
 * @property int $shop_id 店铺ID
 * @property string $identifier 店铺唯一身份
 * @property string $order_id 订单ID
 * @property string $order_no 订单号
 * @property int $status 订单状态
 * @property string $service_id 服务ID
 * @property string $service_name 服务名称
 * @property int $fee 原价金额，单位为分
 * @property int $prom_fee 优惠金额，单位为分
 * @property int $refund_fee    退款金额，单位为分
 * @property int $pay_fee 实际支付金额，单位为分
 * @property string $pay_at 支付时间
 * @property int $pay_type 支付类型
 * @property string $order_created_at 订单创建时间
 * @property string $order_cycle 订单周期，例如：1个月、3个月等
 * @property string $cycle_start_at 周期开始时间
 * @property string $cycle_end_at 周期结束时间
 * @property int $source 来源渠道
 * @property string $sku_id SKU ID
 * @property string $sku_title SKU 名称
 * @property string $sku_spec SKU 规格
 * @property int $duration 持续时间
 * @property int $duration_unit 持续时间单位
 * @property int $auth_user_id 授权用户ID
 * @property string $created_at 创建时间
 * @property string $updated_at 更新时间
 * @property int is_push 是否推送
 * @property string $version 版本号
 *
 *
 */
class PlatformOrder extends Model
{
    protected $fillable = [
        'user_id',
        'shop_id',
        'platform_type',
        'identifier',
        'order_id',
        'order_no',
        'status',
        'service_id',
        'service_name',
        'fee',
        'prom_fee',
        'refund_fee',
        'pay_fee',
        'pay_at',
        'pay_type',
        'order_created_at',
        'order_cycle',
        'cycle_start_at',
        'cycle_end_at',
        'source',
        'sku_id',
        'sku_title',
        'sku_spec',
        'duration',
        'duration_unit',
        'auth_user_id',
        'is_push',
        'version',
    ];


    const STATUS_MAP = [
        self::STATUS_UNKNOWN => '未知状态',
        self::STATUS_PADDING => '待付款',
        self::STATUS_SUCCESS => '订单成功',
        self::STATUS_FAILED => '订单失败',
        self::STATUS_CLOSE => '交易关闭(退款或者未支付直接关闭)',
        self::STATUS_ABNORMAL => '交易异常'
    ];
    /**
     * 订单状态
     */
    const STATUS_UNKNOWN = 0; //未知状态
    const STATUS_PADDING = 1; //待付款
    const STATUS_SUCCESS = 2; //订单成功
    const STATUS_FAILED = 3; //订单失败(支付失败，或者订单超时)
    const STATUS_CLOSE = 4; //交易关闭(退款或者未支付直接关闭)
    const STATUS_ABNORMAL = 5; //交易异常

    public function shop()
    {
        return $this->belongsTo(\App\Models\Fix\Shop::class, 'shop_id', 'id');
    }

    public static function saveOrder(array $order)
    {
        return PlatformOrder::query()->updateOrCreate([
            'user_id' => $order['user_id'],
            'shop_id' => $order['shop_id'],
            'platform_type' => $order['platform_type'],
            'order_no' => $order['order_no'],
        ], $order);
    }

    public static function calcuMaxLevel($shop)
    {
        $all = PlatformOrder::query()->where('user_id', $shop->user_id)
            ->where('shop_id', $shop->id)
            ->where('status', PlatformOrder::STATUS_SUCCESS)
            //->where('cycle_start_at', '<=', date('Y-m-d H:i:s'))
            ->where('cycle_end_at', '>', date('Y-m-d H:i:s'))
            ->get();

        $version = null;
        if (count($all) == 1) {
            $version = $all->first();
        }
        if (count($all) > 1) {
            $version = collect($all)->sortByDesc('pay_fee')->first();

        }

        return $version;
    }

    public static function calcuMaxLevelByCycleEndAt($shop)
    {
        $all = PlatformOrder::query()->where('user_id', $shop->user_id)
            ->where('shop_id', $shop->id)
            ->where('status', PlatformOrder::STATUS_SUCCESS)
            //->where('cycle_start_at', '<=', date('Y-m-d H:i:s'))
            ->where('cycle_end_at', '>', date('Y-m-d H:i:s'))
            ->get();

        $version = null;
        if (count($all) == 1) {
            $version = $all->first();
        }
        if (count($all) > 1) {
            $version = collect($all)->sortByDesc('cycle_end_at')->first();

        }

        return $version;
    }

    /**
     * 获取有效时间内的高级版
     * @param $shop
     * @return mixed|null
     */
    public static function getHighLevelByLevel($shop)
    {
        $all = PlatformOrder::query()->where('user_id', $shop->user_id)
            ->where('shop_id', $shop->id)
            ->where('status', PlatformOrder::STATUS_SUCCESS)
            ->whereIn('sku_spec', [UserExtra::VERSION_SENIOR_NAME])
            //->where('cycle_start_at', '<=', date('Y-m-d H:i:s'))
            ->where('cycle_end_at', '>', date('Y-m-d H:i:s'))
            ->get()->toArray();

        if (empty($all)) {
            return null;
        }
        foreach ($all as $index => $item) {
            $all[$index]['version_level'] = UserExtra::getVersionLevel($item['sku_spec']);
        }
        $version = collect($all)->sortByDesc('version_level')->first();
        return $version;
    }

    /**
     * 获取服务信息
     * @param Shop $shop
     * @param string|null $serviceTime 服务时间，默认为当前时间
     * @return void
     */
    public static function getServiceInfo(Shop $shop, ?string $serviceTime = null): ?SubscriptionServiceInfo
    {
        $shopId = $shop->id;
        //把所以订购成功而且服务时间是当前时间以前的（还生效的）服务信息都查询出来，
        if (!$serviceTime) {
            $serviceTime = date('Y-m-d H:i:s');
        }
        $platformOrders = PlatformOrder::query()->where('shop_id', $shopId)->where('status', PlatformOrder::STATUS_SUCCESS)
            ->where('cycle_end_at', '>=', $serviceTime)
            ->get();
        Log::info('店铺订购服务信息，shopId:' . $shopId, ['platformOrders' => $platformOrders]);
        if ($platformOrders->isEmpty()) {
            Log::info('店铺没有生效订购服务，shopId:' . $shopId);
            //如果没有有效的订购订单，有两种情况，一种是店铺没有订购过服务，一种是店铺的服务已经过期
            //把以前过期的找出来
            $platformOrders = PlatformOrder::query()->where('shop_id', $shopId)->where('status', PlatformOrder::STATUS_SUCCESS)
                ->where('cycle_end_at', '<', $serviceTime)
                ->get();
            if ($platformOrders->isEmpty()) {
                //如果没有查到，说明店铺没有订购过服务
                return null;

            }

        }


        $subscriptionServiceInfo = new SubscriptionServiceInfo();
        //按服务开始时间从小到大排序
        $platformOrders = $platformOrders->sortBy('cycle_start_at');
        //找到第一条和最后一条记录
        /**
         * @var PlatformOrder $firstPlatformOrder
         */
        $firstPlatformOrder = $platformOrders->first();
        $lastPlatformOrder = $platformOrders->last();
        $version = $firstPlatformOrder->version;
        $versionDesc=$firstPlatformOrder->sku_spec;
        if($version==UserExtra::VERSION_FREE){
           //如果是免费版，直接返回订购的版本
            $version=$lastPlatformOrder->version;
            $versionDesc=$lastPlatformOrder->sku_spec;
        }
        //第一条记录的开始时间就是服务开始时间，最后一条的结束时间是服务结束时间
        $subscriptionServiceInfo->shopId = $shopId;
        $subscriptionServiceInfo->userId = $firstPlatformOrder->user_id;
        $subscriptionServiceInfo->platformType = $firstPlatformOrder->platform_type;
        $subscriptionServiceInfo->version = $version;
        $subscriptionServiceInfo->versionDesc =$versionDesc;
        $subscriptionServiceInfo->identifier = $shop->identifier;
        $subscriptionServiceInfo->payAmount = $firstPlatformOrder->pay_fee;
        $subscriptionServiceInfo->expiredAt = $lastPlatformOrder->cycle_end_at;
        return $subscriptionServiceInfo;

    }

    /**
     * 判断当前版本是否等于某个版本
     * @param string $version
     * @return bool
     */
    public function equalVersion(string $version): bool
    {
        //版本相同，再考虑如果没有$version根据名字转义到version进行比较
        return strcasecmp($this->version, $version) == 0 || strcasecmp(UserExtra::getVersionValueByName($this->sku_spec), $version) == 0;
    }

    /**
     * 判断当前版本是否免费版
     * @return bool
     */

    public function isFree(): bool
    {
        return $this->equalVersion(UserExtra::VERSION_FREE);
    }


    /**
     * 初始化免费版服务信息
     * @param int $shopId
     * @param int $userId
     * @param int $days
     * @return PlatformOrder
     * @throws ErrorCodeException
     */
    public static  function initFreeVersion(int $shopId,int $userId,int $days = 7): PlatformOrder{
        $platformOrder = new PlatformOrder();
        $platformOrder->shop_id = $shopId;
        $platformOrder->user_id = $userId;
        $platformOrder->version = UserExtra::VERSION_FREE;
        $platformOrder->status = PlatformOrder::STATUS_SUCCESS;
        $platformOrder->sku_spec =UserExtra::VERSION_FREE_NAME;
        $orderSn = RedisUtil::getOrderSn(strtoupper(Environment::platform()));
        $platformOrder->order_id = $orderSn;
        $platformOrder->order_no = $orderSn;
        $platformOrder->fee = 0;
        $platformOrder->prom_fee = 0;
        $platformOrder->refund_fee = 0;
        $platformOrder->platform_type=Environment::platformType();
        $platformOrder->pay_fee = 0;
        $platformOrder->pay_at =date('Y-m-d H:i:s');
        $platformOrder->pay_type = PaymentConst::PAYMENT_TYPE_ALIPAY;
        $platformOrder->order_cycle = 0;
        $platformOrder->order_created_at=DateTimeUtil::strNow();
        $platformOrder->cycle_start_at = date('Y-m-d H:i:s');
        $platformOrder->cycle_end_at = date('Y-m-d H:i:s', strtotime('+'.$days.' day'));
        $platformOrder->source = 0;
        $platformOrder->sku_title = '免费版';
        $platformOrder->duration =$days;
        $platformOrder->duration_unit = 0;
        $platformOrder->save();
        return $platformOrder;
    }


    /**
     * 查找店铺绑定的api授权
     * api_shop_binds 店铺绑定的api授权表
     * api_auth api授权表
     * @return HasManyThrough
     */

    public function bindApiAuth(): HasManyThrough
    {

        $hasManyThrough = $this->hasManyThrough(ApiAuth::class, ApiShopBind::class,
            'shop_id', 'app_id', 'shop_id', 'app_id');
        Log::info('店铺绑定的api授权', ['hasManyThrough' => $hasManyThrough->toSql()]);
        return $hasManyThrough;
    }
}
