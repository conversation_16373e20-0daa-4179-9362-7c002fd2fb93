<?php

namespace App\Models;

use App\Traits\QueryHelperTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Model
{
    use SoftDeletes;

    protected $connection = 'mysql';

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'phone',
        'nickname',
        'password',
        'invite_code',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $hidden = [
        'deleted_at',
        'password',
        'shops'
    ];

    /**
     * 获取授权信息
     */
    public function shops()
    {
        return $this->hasMany('App\Models\Shop', 'user_id', 'id');
    }

    /**
     * 获取原来的授权信息
     */
    public function originalShops()
    {
        return $this->hasMany('App\Models\Shop', 'original_user_id', 'id');
    }

    /**
     * 获取邀请的授权信息
     */
    public function inviteShops()
    {
        return $this->hasMany('App\Models\Shop', 'inviter', 'id');
    }

    public function userExtra()
    {
        return $this->hasMany('App\Models\UserExtra', 'user_id', 'id');
    }

    public static function firstById($id)
    {
        return self::query()->where('id',$id)->first();
    }
}
