<?php

namespace App\Models;

use App\Constants\PlatformConst;
use App\Services\Order\OrderServiceManager;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

class PrintRecord extends Model
{
    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'user_id',
        'shop_id',
        'order_id',
        'history_id',
        'package_id',
        'order_no',
        'waybill_code',
        'wp_code',
        'receiver_province',
        'receiver_city',
        'receiver_district',
        'receiver_town',
        'receiver_name',
        'receiver_phone',
        'receiver_zip',
        'receiver_address',
        'buyer_remark',
        'print_data',
        'app_id',
        'batch_no',
        'name_index',
        'phone_index',
        'print_index',
        'print_count',
        'outer_order_no',
        'to_shop_id',
        'template_id',
        'template_name',
        'version',
        'order_type',
        'created_by',
        'updated_by',
        'send_content',
        'company_id',
    ];
    protected $hidden = [
//        'print_data'
    ];

    //protected $appends = ['batch_no'];

	public function package()
	{
		return $this->belongsTo(Package::class, 'package_id', 'id');
    }

    /**
     * 返回关联的订单
     */
    public function order()
    {
        return $this->belongsTo(Order::class, 'order_no', 'tid');
    }

    public function waybillHistory()
    {
        return $this->belongsTo(WaybillHistory::class, 'history_id', 'id');
    }
    public function packageOrders()
    {
        return $this->hasMany('App\Models\PackageOrder', 'package_id', 'package_id');
    }
//	public function getBatchNoAttribute()
//	{
//		return is_null($this->package) ? '' :  $this->package->batch_no;
//	}

    /**
     * list
     * @param array  $condition
     * @param string $keyword
     * @param int    $offset
     * @param int    $limit
     * @param string $orderBy
     * @return array
     */
    public static function search(array $condition, array $shopIds, string $keyword, int $offset, int $limit, string $orderBy = '',$wpCode)
    {
        // 加 package 避免 n+1 查询
        $query   = self::query()->with('package')
            ->where($condition)
            ->whereIn('shop_id', $shopIds)
            ->whereNotNull('print_index');
        $query->selectRaw("*,GROUP_CONCAT(order_no) as order_no_str");
        if ($keyword) {
            $orderService = OrderServiceManager::create(config('app.platform'));
            $shops = Shop::query()->whereIn('id', $shopIds)->get();
            $idArr = [];
            foreach ($shops as $shop) {
                $orderService->setShop($shop);
                $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_name' ,$keyword));
                if (isPhoneNumber($keyword)){
                    $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_phone' ,$keyword));
                }
            }

            $query = $query->where(function ($query) use ($keyword, $idArr) {
                $query->where('order_no', $keyword)
	                ->orWhere('order_no', $keyword . 'A')
                    ->orWhere('waybill_code', $keyword)
                    ->orWhere('batch_no','like', '%' . $keyword . '%');
//                    });
                if ($idArr) {
                    $query->orWhereIn('order_id', $idArr);
                }
            });
        }

        if($wpCode){
            $codeArr = explode(',',$wpCode);
            $query->whereIn('wp_code',$codeArr);
        }

        $sortArr = explode(' ', $orderBy);

        $query->groupBy(['batch_no', 'waybill_code']);
        //有group by不能直接count
        $baseSql = getSqlByQuery($query);
        $count = \DB::select("select count(*) as count from ($baseSql) as base");
        $ret =  $query->limit($limit)
            ->offset($offset)
            ->orderBy('batch_no', $sortArr[1])
            ->orderBy('print_index', $sortArr[1])
            ->get();
        return array($ret, $count[0]->count);
    }

    /**
     * 添加打印记录
     * @param array $printRecords
     * @param int   $userId
     * @return bool
     */
    public static function generate($printRecords, $userId, $shopId)
    {
        $insert = [];
        foreach ($printRecords as $printRecord) {
            $insert[] = [
                'user_id'           => $userId,
                'shop_id'           => $shopId,
                'order_id'          => $printRecord['order']['id'],
                'history_id'        => $printRecord['history_id'],
                'package_id'        => $printRecord['package_id'],
                //'order_no'          => $printRecord['order']['tid'] ?? $printRecord['order']['order_no'] ?? null,
                'order_no'          => isset($printRecord['order']['tid']) ? $printRecord['order']['tid'] : (isset($printRecord['order']['order_no']) ? $printRecord['order']['order_no'] : null),
                'waybill_code'      => $printRecord['waybill_code'],
                'wp_code'           => $printRecord['wp_code'],
                'receiver_province' => $printRecord['order']['receiver_state'] ?? $printRecord['order']['receiver_province'],
                'receiver_city'     => $printRecord['order']['receiver_city'],
                'receiver_district' => $printRecord['order']['receiver_district'],
                'receiver_town'     => $printRecord['order']['receiver_town'],
                'receiver_name'     => isset($printRecord['order']['order_cipher_info']) ? $printRecord['order']['order_cipher_info']['receiver_name_mask'] : $printRecord['order']['receiver_name'],
                'receiver_phone'    => isset($printRecord['order']['order_cipher_info']) ? $printRecord['order']['order_cipher_info']['receiver_phone_mask'] : $printRecord['order']['receiver_phone'],
                'receiver_address'  => isset($printRecord['order']['order_cipher_info']) ? $printRecord['order']['order_cipher_info']['receiver_address_mask'] : $printRecord['order']['receiver_address'],
                'receiver_zip'      => $printRecord['order']['receiver_zip'],
                'buyer_remark'      => $printRecord['order']['buyer_message'] ?? null,
                'print_data'        => $printRecord['print_data'] ?? null,
                'app_id'            => $printRecord['app_id'] ?? '',
                'batch_no'          => $printRecord['batch_no'] ?? null,
                'name_index'        => $printRecord['name_index'] ?? null,
                'phone_index'       => $printRecord['phone_index'] ?? null,
                'to_shop_id'        => $printRecord['to_shop_id'] ?? null,
                'template_id'       => $printRecord['template_id'] ?? 0,
                'template_name'     => $printRecord['template_name'] ?? null,
                'version'           => $printRecord['version'] ?? 0,
                'print_count'       => $printRecord['print_count'] ?? 0,
                'print_index'       => $printRecord['print_index'] ?? 0,
                'created_at'        => Carbon::now(),
                'updated_at'        => Carbon::now(),
            ];
        }
        $ret = self::insert($insert);

        return $ret;
    }

}
