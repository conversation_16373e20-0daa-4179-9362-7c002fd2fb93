<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PlatformAddress extends Model
{
    use SoftDeletes;

    protected $table = 'platform_addresses';

    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'shop_id',
        'address_id',
        'name',
        'contact',
        'phone',
        'common_phone',
        'company_phone',
        'postal_code',
        'province',
        'city',
        'district',
        'town',
        'address',
        'is_default',
        'is_send_default',
        'link_type',
        'remark',
        'create_time',
        'update_time',
    ];

    /**
     * 关联到店铺
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function shop()
    {
        return $this->belongsTo('App\Models\Shop', 'shop_id', 'id');
    }

    /**
     * 搜索平台地址
     * @param array $condition 查询条件
     * @param string $search 搜索关键词
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @param string $orderBy 排序方式
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function search(array $condition, string $search, int $offset, int $limit, string $orderBy = 'id desc')
    {
        $query = self::query()->where($condition);
        if ($search) {
            $query = $query->where(function ($query) use ($search) {
                $query->where('name', 'like', '%' . $search . '%')
                    ->orWhere('contact', 'like', '%' . $search . '%')
                    ->orWhere('phone', 'like', '%' . $search . '%')
                    ->orWhere('address', 'like', '%' . $search . '%');
            });
        }
        $sortArr = explode(' ', $orderBy);

        return $query->limit($limit)
            ->offset($offset)
            ->orderBy($sortArr[0], $sortArr[1])
            ->get();
    }

}
