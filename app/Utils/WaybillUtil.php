<?php

namespace App\Utils;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Models\Company;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Waybill;
use App\Models\WaybillAuth;
use App\Services\Waybill\WaybillServiceManager;
use Illuminate\Support\Facades\Log;

/**
 * 电子面单相关的工具类
 */
class WaybillUtil
{


    /**
     * 获取当前平台的电子面单授权来源
     * @return string
     */
    public static function currentWaybillAuthSource(): string
    {
        $platform = Environment::platform();
        switch ($platform) {
            case PlatformConst::TAOBAO:
                $authSource = PlatformConst::WAYBILL_TB_TOP;
                break;
            case PlatformConst::PDD:
                $authSource = PlatformConst::WAYBILL_PDD;
                break;
            case PlatformConst::KS:
                $authSource = PlatformConst::WAYBILL_KS;
                break;
            case PlatformConst::JD:
                $authSource = PlatformConst::WAYBILL_JD;
                break;
            case PlatformConst::DY:
                $authSource = PlatformConst::WAYBILL_DY;
                break;
            case PlatformConst::WXSP:
                $authSource = PlatformConst::WAYBILL_WXSP;
                break;
            case PlatformConst::XHS:
                $authSource = PlatformConst::WAYBILL_XHS;
                break;
            default:
                $authSource = 0;
                break;
        }
        return $authSource;
    }

    /**
     *
     * @param  int  $authSource
     * @return bool
     */
    public static function matchCurrentAuthSource(int $authSource): bool
    {
        return $authSource == self::currentWaybillAuthSource();
    }

    /**
     * 因为没有电子面单授权来源这个参数，只能结合应用环境和授权来源来判断
     * @param  int  $shopId
     * @param  int  $authSource
     * @param  string  $ownerId
     * @param  bool  $safe
     * @return WaybillAuth|null
     * @throws ApiException
     */
    public static function findShopWaybillAuth(
        int $shopId,
        int $authSource,
        string $ownerId,
        bool $safe = false
    ): ?WaybillAuth {
//        \Log::info('findShopWaybillAuth', [$shopId, $authSource, $ownerId]);
        $currentWaybillAuthSource = self::currentWaybillAuthSource();
        //如果当前平台的电子面单授权来源和传入的授权来源一致，则通过店铺id查找，反之通过站外电子面单授权来源查找
        if ($currentWaybillAuthSource == $authSource) {
            $waybillAuth = Shop::find($shopId);
            $waybillAuth->auth_source = $authSource;
//            $waybillAuth->auth_Type = Waybill::AUTH_TYPE_SHOP;
        } else {
            $waybillAuth = Waybill::where([
                'owner_id' => $ownerId,
                'shop_id' => $shopId,
                'auth_source' => $authSource
            ])->orderBy('id', 'desc')->first();
//            $waybillAuth->auth_Type = Waybill::AUTH_TYPE_OUTSIDE_WAYBILL;
        }
        //如果没有找到电子面单授权信息，&&$safe为false时，抛出异常
        if (!$safe && !$waybillAuth) {
            throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
            //                        throw new \Exception('电子面单授权信息丢失!');

        }
        return $waybillAuth;
    }

    /**
     * 通过电子面单模板查找电子面单的授权
     * @param  array  $template
     * @return  Shop|Waybill|null
     */
    public static function findTemplateBindWaybill(array $template)
    {
        $templateShopId = $template['shop_id'];
        $authSource = $template['auth_source'];
        //是否共享面单
        $company = $template['company'];
        if (self::matchCurrentAuthSource($authSource)) {
            if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
                $shop = Shop::query()->where('identifier', $company['owner_id'])->first();
            } else {
                $shop = Shop::query()->where('id', $templateShopId)->first();
            }
            return $shop;
        } else {
            $company = Company::find($template['company_id']);
            $auth = Waybill::where([
                'shop_id' => $company['shop_id'],
                'owner_id' => $company['owner_id'],
                'auth_source' => $company['auth_source']
            ])->first();
            return $auth;
        }
    }

    /**
     * 电子面单授权来源是否有效
     * @param  int  $authSource
     * @return bool
     */
    public static function isValidateAuthSource(int $authSource): bool
    {
        return in_array($authSource, [
            Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD, Waybill::AUTH_SOURCE_KS,
            Waybill::AUTH_SOURCE_WXSP, Waybill::AUTH_SOURCE_XHS
        ]);
    }

    /**
     * 获取当前平台的电子面单授权类型
     * @return int
     */
    public static function getCurrentAuthSource(): int
    {
        switch (config('app.platform')) {
            case PlatformConst::TAOBAO:
                $source = Waybill::AUTH_SOURCE_TAOBAO;
                break;
            case PlatformConst::JD:
                $source = Waybill::AUTH_SOURCE_JD;
                break;
            case PlatformConst::DY:
                $source = Waybill::AUTH_SOURCE_DY;
                break;
            case PlatformConst::KS:
                $source = Waybill::AUTH_SOURCE_KS;
                break;
            case PlatformConst::WXSP:
                $source = Waybill::AUTH_SOURCE_WXSP;
                break;
            default:
                $source = 0;
                break;
        }
        return $source;
    }

    public function getAllWaybillSubscription(int $currentShopId, string $wpCode = "", array $scope = [1]): array
    {
        $shopIds = [];
        //scope=1 ,只包含当前店铺
        if (in_array(1, $scope)) {
            $shopIds[] = $currentShopId;
        }
        //scope=2 ，包含了同级店铺
        if (in_array(2, $scope)) {
            $shopIds = array_merge($shopIds,
                ShopBind::getAllRelationShopIds($currentShopId, false, [ShopBind::TYPE_ME_BIND]));
        }

        //获取店铺的面单授权
        Log::info("获取店铺电子面单授权", ["scope" => $scope, "shopIds" => $shopIds]);

        //站外电子面单
        $waybills = Waybill::whereIn('shop_id', $shopIds)->get();

        $result = [];
        foreach ($waybills as $key => $value) {
            try {
                $waybillService = WaybillServiceManager::init($value->auth_source, $value->access_token);
                $waybillService->checkTokenExpired($value, true);
                $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
                if (empty($waybill)) {
                    Log::error('未开通该公司电子面单服务', ['value' => $value->toArray()]);
                    throw new ApiException(ErrorConst::WAYBILL_SERVICE_NOT_OPEN);
//                    throw new \Exception('未开通该公司电子面单服务');
                }
                $result[] = [
                    'shop_id' => $value->shop_id,
                    'owner_id' => $value->owner_id,
                    'owner_name' => $value->owner_name,
                    'auth_source' => $value->auth_source,
                    'list' => $waybill
                ];
            } catch (\Exception $e) {
                Log::error('站外电子面单错误', ['msg' => $e->getMessage(), 'value' => $value]);
            }
        }

        $shops = Shop::query()->whereIn('id', $shopIds)->get();
        Log::info("获取平台店铺的面单授权，size=".count($shops));
        foreach ($shops as $shop) {

            //平台电子面单
//        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::KS, PlatformConst::TAOBAO,
//            PlatformConst::JD, PlatformConst::WXSP, PlatformConst::XHS])) {
            if ($shop->access_token) {
                switch (config('app.platform')) {
                    case PlatformConst::TAOBAO:
                        $source = Waybill::AUTH_SOURCE_TAOBAO;
                        break;
                    case PlatformConst::JD:
                        $source = Waybill::AUTH_SOURCE_JD;
                        break;
                    case PlatformConst::DY:
                        $source = Waybill::AUTH_SOURCE_DY;
                        break;
                    case PlatformConst::KS:
                        $source = Waybill::AUTH_SOURCE_KS;
                        break;
                    case PlatformConst::WXSP:
                        $source = Waybill::AUTH_SOURCE_WXSP;
                        break;
                    case PlatformConst::XHS:
                        $source = Waybill::AUTH_SOURCE_XHS;
                        break;
                    case PlatformConst::PDD:
                        $source = Waybill::AUTH_SOURCE_PDD;
                        break;
                    case PlatformConst::CN:
                    case PlatformConst::ALBB:
                    case PlatformConst::ALC2M:
                        $source = Waybill::AUTH_SOURCE_LINK;
                        break;
                    default:
                        $source = 0;
                        break;
                }
                $waybillService = WaybillServiceManager::init($source, $shop->access_token);
                $waybillService->setShop($shop);
                try {
                    $waybills = $waybillService->waybillSubscriptionQuery($wpCode, $shop->service_id ?? '');
                    if (!empty($waybills)) {
                        $result[] = [
                            'shop_id' => $shop->id,
                            'owner_id' => $shop->identifier,
                            'owner_name' => $shop->shop_name,
                            'auth_source' => $source,
                            'list' => $waybills
                        ];
                    }
                } catch (\Exception $ex) {
                    Log::error('平台电子面单错误', [$ex]);
                }
            } else {
                Log::error('平台电子面单错误', ['msg' => 'access_token为空', 'value' => $shop]);
            }
        }

//        }

        return $result;
    }

    /**
     * 获取第三方共享面单的授权信息
     * @return Waybill[]|\stdClass[]
     */
    public function getWaybillAuth(
        int $currentShopId,
        array $scope = [1],
        ?int $withShopId = null,
        $includeThird = true
    ): array {
        $shopIds = [];
        //scope=1 ,只包含当前店铺
        if (in_array(1, $scope)) {
            $shopIds[] = $currentShopId;
        }
        //scope=2 ，包含了同级店铺
        if (in_array(2, $scope)) {
            $shopIds = array_merge($shopIds,
                ShopBind::getAllRelationShopIds($currentShopId, false, [ShopBind::TYPE_ME_BIND]));
        }
        Log::info("获取店铺电子面单授权", ["scope" => $scope]);
        //把这个店铺关联的全收的站外电子面单都找出来
        if ($includeThird) {
            $waybills = Waybill::whereIn('shop_id', $shopIds)->with('shop')->get()->toArray();
        } else {
            $waybills = [];
        }
        //增加抖音平台账号
        //  if (in_array(config('app.platform') ,[PlatformConst::DY,PlatformConst::TAOBAO,PlatformConst::KS,PlatformConst::WXSP])) {
        $waybillType = Environment::getWaybillType();
        if ($withShopId && isset($waybillType)) {
            $shop = Shop::query()->where('id', $withShopId)->first();
            $tempWaybill = new \stdClass();
            $tempWaybill->owner_id = $shop->identifier;
            $tempWaybill->owner_name = $shop->shop_name.'('.Environment::platformName().'自有面单)';
            $tempWaybill->auth_source = $waybillType;
            $waybills[] = $tempWaybill;
        }
        return $waybills;
    }

    /**
     * 判断是否顺丰
     * @param  string  $wpCode
     * @return bool
     */
    public static function isSF(string $wpCode): bool
    {
        return in_array($wpCode, ['SF']);
    }

    /**
     * 判断是否JD
     */
    public static function isJD(string $wpCode): bool
    {
        return in_array($wpCode, ['JD', 'jd']);
    }
}
