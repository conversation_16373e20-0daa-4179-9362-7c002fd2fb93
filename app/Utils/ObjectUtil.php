<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/5/23
 * Time: 16:26
 */

namespace App\Utils;

use Illuminate\Support\Str;

class ObjectUtil
{
    /**
     * 把 map 转成 对象
     * @param array $data
     * @param string|object $class
     * @param bool $toCamel 字段转小驼峰
     * @return mixed
     * <AUTHOR>
     */
    public static function mapToObject(array $data, $class, bool $toCamel = false)
    {
        if(is_string($class)){
            $class = new $class();
        }
        foreach ($data as $key => $value) {
            $toCamel && $key = Str::camel($key);
            if (property_exists($class, $key)) {
                $class->$key = $value;
            }
        }
        return $class;
    }

    /**
     * 批量把 map 转成对象
     * <AUTHOR>
     * @param array $listData
     * @param string|object $class
     * @return array
     */
    public static function batchMapToObject(array $listData, $class): array
    {
        $resultList = [];
        foreach ($listData as $item) {
            $resultList[] = self::mapToObject($item, $class);
        }
        return $resultList;
    }
}
