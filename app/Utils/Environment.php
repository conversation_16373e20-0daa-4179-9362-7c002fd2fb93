<?php

namespace App\Utils;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ErrorCodeException;
use App\Models\Shop;

/**
 * 环境
 */
class Environment
{

    /**
     * 淘宝:授权有效期是就是服务有效期无需刷新
     * 是否需要刷新token
     * @return bool
     */
    public static function isNeedRefreshToken(): bool
    {

        return !(self::isTaoBao()||self::isJd());
    }

    /**
     * 判断平台是不是taobao
     * @return bool
     */
    public static function isTaoBao(): bool
    {
        return self::isPlatform(PlatformConst::TAOBAO);
    }

    /**
     * 判断平台是不是 albb
     * @return bool
     */
    public static function isAlbb(): bool
    {
        return self::isPlatform(PlatformConst::ALBB);
    }

    /**
     * 判断平台是不是taobao
     * @return bool
     */
    public static function isKs(): bool
    {
        return self::isPlatform(PlatformConst::KS);
    }
    /**
     * 判断平台是不是jd
     * @return bool
     */
    public static function isJd(): bool
    {
        return self::isPlatform(PlatformConst::JD);
    }

    /**
     * 判断平台是不是WX
     * @return bool
     */
    public static function isWx(): bool
    {
        return self::isPlatform(PlatformConst::WX);
    }
    public static function isWxsp(): bool
    {
        return self::isPlatform(PlatformConst::WXSP);
    }

    public static function isWxOrWxsp(): bool
    {
        return self::isPlatform(PlatformConst::WX)||self::isPlatform(PlatformConst::WXSP);
    }

    /**
     * 判断平台是不是DY
     * @return bool
     */
    public static function isDy(): bool
    {
        return self::isPlatform(PlatformConst::DY);
    }

    /**
     * 判断是不是特定的平台
     * @param $platform
     * @return bool
     */
    public static function isPlatform($platform): bool
    {
        return config('app.platform') == $platform;
    }

    /**
     * 平台的名称
     * @return string
     */
    public static function platform(): string
    {
        return config('app.platform');
    }

    /**
     *
     * 平台的中文名称
     * @return string
     */
    public static function platformName(): string
    {
        return PlatformConst::PLATFORM_NAME[self::platform()];
    }

    /**
     * 返回面单类型
     * @return int|null
     */

    public static function getWaybillType(): ?int
    {
        return PlatformConst::WAYBILL_MAPPING[self::platform()]??null;
    }

    public static function isAnyPlatform(array $platforms):bool{

        return in_array(self::platform(),$platforms);
    }
    /**
     * 支持追加接口的平台
     */
    public static function isSupportAppendPlatform(): bool
    {
        return self::isAnyPlatform([PlatformConst::TAOBAO,PlatformConst::DY,PlatformConst::KS,PlatformConst::JD]);
    }

    /**
     * 是否缺省就关掉了api绑定的同步
     * @return bool
     */
    public static function isDefaultTurnOffSync4Api(): bool
    {
        return self::isAnyPlatform([PlatformConst::TAOBAO,PlatformConst::JD]);
    }

    public static function envIsProduction():bool
    {
        return config('app.env') == 'production';
    }

    public static function isXhs()
    {
        return self::isPlatform(PlatformConst::XHS);
    }

    public static function isBeiKe(): bool

    {
        return  strtolower(config('app.name')) == strtolower('{dy-beike}');
    }

    /**
     * 是否支持商品标题搜索
     * @return bool
     */
    public static function isSupportGoodsTitleSearch(): bool{
        //只有抖音 1688 支持商品标题搜索
        return Environment::isDy()||Environment::isAlbb()||Environment::isAlC2M();
    }

    public static function isPdd()

    {
        return self::isPlatform(PlatformConst::PDD);
    }

    public static function isTb()
    {
    }

    public static function isArray(array $array)
    {
        return self::isAnyPlatform($array);
    }

    public static function isAlC2M()
    {
        return self::isPlatform(PlatformConst::ALC2M);
    }


    /**
     * @throws ErrorCodeException
     */
    public static function platformType():int{
        if(self::isWx()){
            return Shop::PLATFORM_TYPE_WX;
        }
        if(self::isWxsp()){
            return Shop::PLATFORM_TYPE_WXSP;
        }
        if(self::isJd()){
            return Shop::PLATFORM_TYPE_JD;
        }
        if(self::isTaoBao()){
            return Shop::PLATFORM_TYPE_TAOBAO;
        }
        if(self::isDy()){
            return Shop::PLATFORM_TYPE_DY;
        }
        if(self::isKs()){
            return Shop::PLATFORM_TYPE_KS;
        }
        if(self::isAlbb()){
            return Shop::PLATFORM_TYPE_ALBB;
        }
        if(self::isXhs()){
            return Shop::PLATFORM_TYPE_XHS;
        }
        if(self::isPdd()){
            return Shop::PLATFORM_TYPE_PDD;
        }
        if(self::isAlc2m()){
            return Shop::PLATFORM_TYPE_ALC2M;
        }
        throw_error_code_exception(ErrorConst::PARAM_ERROR,null, "不支持的平台类型");
    }


}
