<?php

namespace App\Utils;

use App\Constants\PlatformConst;
use App\Models\Order;
use App\Models\OrderItem;

/**
 * 订单Util
 */
class OrderUtil
{
    /**
     * 抖音的批量订单号添加结尾A
     * @param $orderIds
     * @return array|mixed
     */
    public static function batchAppendOrderSuffix($orderIds)
    {
        $orderList = [];
        if (PlatformConst::DY != config('app.platform')) {
            $orderList = $orderIds;
        } else {
            foreach ($orderIds as $orderId) {
                $cleanOrderId=trimBlankChars($orderId);
                if (!StrUtil::endsWith($orderId, 'A')) {
                    $orderList[] = $cleanOrderId . 'A';
                } else {
                    $orderList[] = $cleanOrderId;
                }
            }
        }
        return $orderList;
    }

    /**
     * 抖音的订单号添加结尾A
     * @param $orderId
     * @return mixed|string
     */
    public static function appendOrderSuffix($orderId)
    {
        if (PlatformConst::DY != config('app.platform')) {
            return $orderId;
        }
        if (!StrUtil::endsWith($orderId, 'A')) {
            return $orderId . 'A';
        } else {
            return $orderId;
        }
    }

    /**
     * 抖音订单号移除A
     * @param $orderId
     * @return false|mixed|string
     */
    public static function removeOrderSuffix($orderId)
    {
        if (PlatformConst::DY != config('app.platform')) {
            return $orderId;
        }
        if (StrUtil::endsWith($orderId, 'A')) {
            return substr($orderId,0,strlen($orderId)-1);
        } else {
            return $orderId;
        }
    }

    /**
     * 验证快递公司编码
     * @param string $expressCode
     * @return bool
     */
    public static function validateExpressCode(string $expressCode): bool
    {
        return  !empty($expressCode)&& preg_match('/^[_0-9a-z]*$/i', $expressCode);
    }

    /**
     * 把订单的退款状态转换到子订单的退款状态
     * @param $refundStatusList
     * @return array
     */
    public static function mappingOrderRefundStatus2OrderItemStatus($refundStatusList): array
    {
        $result=[];
        foreach ($refundStatusList as $refundStatus) {
            $itemRefundStatus=-1;
            switch ($refundStatus) {
                case Order::REFUND_STATUS_PART:
                    $itemRefundStatus = OrderItem::REFUND_STATUS_NO;
                    break;
                case Order::REFUND_STATUS_NO:
                    $itemRefundStatus=Order::REFUND_STATUS_NO;
                    break;

            }
            if($itemRefundStatus>=0){
                $result[]=$itemRefundStatus;
            }
        }
        return array_unique($result);
    }

    /**
     * 把订单状态转换成子订单的状态
     * @param $orderStatusList
     * @return array
     */
    public static function mappingOrderStatus2OrderItemStatus($orderStatusList)
    {
        $result=[];
        foreach ($orderStatusList as $orderStatus) {

            $itemOrderStatus=-1;
            switch ($orderStatus) {
                case Order::ORDER_STATUS_PART_DELIVERED:
                    $itemOrderStatus=OrderItem::ORDER_STATUS_PAYMENT;
                    break;
                default:
                    $itemOrderStatus = $orderStatus;
            }
            if($itemOrderStatus>=0){
                $result[]=$itemOrderStatus;
            }
        }
        return array_unique($result);
    }

    /**
     * 把尾部的[4个数字]去掉
     * @param string $input
     * @return array|string|string[]|null
     */
    public static  function removeVirtualPhoneSuffix(string $input){
        return preg_replace('/\[\d{4}\]/','',$input);
    }

    /**
     * 把尾部的-和四个数字例如-1234  去掉
     * @param string $input
     * @return array|string|string[]|null
     */
    public static  function removeVirtualPhoneDashSuffix(string $input){

        // 使用正则表达式匹配从最后一个 "_" 到字符串末尾的部分 例如：广西壮族自治区_玉林市_玉州区_落_18664645354-1165 变成 18664645354-1165
        $input = preg_replace('/^(.*_)/', '', $input);
//        您好！您可以使用以下正则表达式来实现您的需求：``。这个正则表达式会将字符串中的“-”和后面的4位数字都去掉，返回结果为“13241114473”。¹
        return preg_replace('/-\d{4}/', '', $input);
//        return preg_replace('/\-[\d{4}\]/','',$input);

    }
}
