<?php

namespace App\Utils;

use App\Models\Company;

class ExpressCompanyUtil
{

    const THIRD_PART_COMPANY_TYPE = [Company::TYPE_TB,
        Company::TYPE_PDD, Company::TYPE_DY,];


    /**
     * 根据快递公司编码找到名称
     * @param string $wpCode
     * @param string $default
     * @return mixed|string
     */
    public static function findExpressCompanyName(string $wpCode,string $default = ""){
        $expressCompany = collect(config('express_company'))->where('wpCode', $wpCode)->first();
        if(isset($expressCompany)){
            return $expressCompany['name'];
        }else{
            return $default;
        }
    }

    /**
     * 返回支持的快递公司当前平台的CompanyType以及共享的平台CompanyType
     */

    public static function currentExpressCompanyType():?int{
        if (Environment::isWxOrWxsp()) {
          return Company::TYPE_WXSP;
        } elseif (Environment::isPdd()) {
          return Company::TYPE_PDD;
        } elseif (Environment::isTaoBao()) {
          return Company::TYPE_TB;
        } elseif (Environment::isDy()) {
          return Company::TYPE_DY;
        } elseif (Environment::isJd()) {
          return Company::TYPE_JD;
        } elseif (Environment::isKs()) {
            return Company::TYPE_KS;
        } elseif(Environment::isXhs()) {
            return Company::TYPE_XHS;
        }
        else {
            return null;
        }
    }

    /**
     * 返回支持的快递公司
     * 第一要返回当前平台支持的指定的快递公司，第二要支持三方面单的快递公司
     * @return array
     */
    public static function supportExpressCompany(): array
    {
        $expressCompanyTypes=self::THIRD_PART_COMPANY_TYPE;
        if(self::currentExpressCompanyType()!=null){
            $expressCompanyTypes[]=self::currentExpressCompanyType();
        }
        $expressCompany = collect(config('express_company'))->whereIn('type',$expressCompanyTypes)->values()->all();
        if (Environment::isJd()) {
            //jd没有共用的百世和天地华宇 申通
            foreach ($expressCompany as $key => $item) {
                if (in_array($item['wpCode'], ['HOAU', 'BESTQJT', 'STO'])) {
                    unset($expressCompany[$key]);
                }
            }
            $expressCompany = array_values($expressCompany);
        }
        return $expressCompany;
    }

    /**
     * 返回支持的快递工地的模板
     * @return array
     */
    public static function getCommonExpressTemplates():array{

        $expressCompanyTypes=self::THIRD_PART_COMPANY_TYPE;
        if(self::currentExpressCompanyType()!=null){
            $expressCompanyTypes[]=self::currentExpressCompanyType();
        }
        return collect(config('common_templates'))->whereIn('type', $expressCompanyTypes)->values()->all();
    }
}
