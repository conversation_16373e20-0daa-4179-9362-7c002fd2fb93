<?php

namespace App\Utils;

use Illuminate\Support\Facades\Log;
use stdClass;

/**
 * Oauth相关的Util
 */
class OauthUtil
{

    /**
     * 从数组生成state字符串
     * @param $stateRaws
     * @return stirng
     */
    public static function encodeState($stateRaw): string
    {
        return base64_encode(json_encode($stateRaw));
    }

    /**
     * 解析state
     * @param string $state
     * @return stdClass
     */
    public static function decodeState(string $state=null,$safe=true):?stdClass
    {
        try {
            if (empty($state)) {
                return null;
            }
            $urldecode = urldecode($state);
            $base64_decode = base64_decode($urldecode);
            $json_decode = json_decode($base64_decode);
            if (!empty($state) && $json_decode == null) {
                throw new \InvalidArgumentException("state解析失败");
            }
            return $json_decode;
        }catch (\Throwable $throwable){
            Log::error($throwable);
            if($safe) {
                return null;
            }else{
                throw $throwable;
            }
        }
    }

}
