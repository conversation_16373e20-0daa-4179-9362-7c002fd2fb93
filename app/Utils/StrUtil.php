<?php

namespace App\Utils;

use DateTime;

class StrUtil{

    /**
     * 提取数字或者数字范围 1 or 1-10
     * @param string|null $input
     * @return int|float|string[]|null
     */
    public static function extractedNumOrNumArr(?string $input)
    {
        $value = null;
        if(empty($input)){
            return null;
        }
        if (is_numeric($input)) {
            $value = $input;
        } else {
            $searchArr = explode('-', $input);
            if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                $value = [$searchArr[0], $searchArr[1]];
            }
        }
        return $value;
    }


    private static $default_split_regex = "/[\s,]+/";

    /**
     * 分隔字符串，支持
     * @param $input
     * @return array|false|string[]
     */
    public static  function splitString($input){
        return preg_split(self::$default_split_regex,$input);
    }



    public static function  splitStringWithRegex($input,$regex){
        return preg_split($regex,$input);
    }


    /**
     * 判断字符串以什么开头
     * @param string $string
     * @param string $subString
     * @return bool
     */
    public static function startsWith(string $string, string $subString) : bool{
        return substr($string, 0, strlen($subString)) === $subString;
        // 或者 strpos($s2, $s1) === 0
    }

    /**
     * 判断字符串以什么结尾
     * @param string $string
     * @param String $subString
     * @return bool
     */
    public static function endsWith(string $string, String $subString) : bool{
        return substr($string, strpos($string, $subString)) === $subString;
    }

    /**
     * 判断字符串是否是json格式
     * @param string $string
     * @return bool
     */
    public static function isJson(string $string): bool
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }

    /**
     *
     *
     * 把字符串按分割器以及长度分割
     * 测试商品勿拍】白色春夏时尚百褶设计感休闲气质女士新杉杉T恤1 红色; 1件\n
     * 按分隔符\n进行分割，如果分割后的字符串长度大于指定长度，则按再按指定长度进行分割
     */
    public static function splitStringWithLengthAndSeparator(string $input,int $length,string $separator):array{
        $result = [];
        $split = explode($separator,$input);
        foreach ($split as $s){
            if(strlen($s) > $length){
                $result = array_merge($result,mb_str_split($s,$length));
            }else{
                if(!empty($s)) {
                    $result[] = $s;
                }
            }
        }
        return $result;

    }


    /**
     * 格式化字符串
     * format("Hello, {}! Lorem {}. Number: {}", "World", "ipsum", mt_rand());
     * @param string $format
     * @return string|null
     */
    public static function format(string $format): ?string {
        $args = func_get_args();

        for ($i = 1; $i < func_num_args(); ++$i) {
            $arg = $args[$i];
            if ($arg instanceof DateTime){
                $arg = $arg->format("Y-m-d H:i:s");
            }
            if(is_bool($arg)){
                $arg = $arg ? 'true' : 'false';
            }
            if(is_array($arg)){
                $arg = json_encode($arg,JSON_UNESCAPED_UNICODE);
            }
            if (!is_string($arg))
                $arg = strval($arg);

            $pos = strpos($format, "{}");

            if ($pos != 0 && !$pos) {
                return null;
            }

            $format = substr_replace($format, $arg, $pos, 2);
        }

        return $format;
    }

    /**
     * 判断字符串是否是加密的,如果长度大于指定长度则认为是加密的
     * @param string $str
     * @param int $maxSize
     * @return bool
     */
    public static function isEncrypted(string $str,int $maxSize=30): bool{
        return mb_strlen($str) > $maxSize;
    }


}
