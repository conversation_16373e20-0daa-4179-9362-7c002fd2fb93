<?php

namespace App\Utils\xhprof;

use XHProfRuns_Default;

/**
 * Xhprof的集成
 */
trait XhprofTrait
{


    /**
     * @return void开启
     */
    function startCommandProfile(){

        $isProfile = config("xhprof.CommandEnable") && $this->isSampling( config("xhprof.CommandRate"));
        if($isProfile){
            xhprof_enable(XHPROF_FLAGS_CPU + XHPROF_FLAGS_MEMORY, []);
            \Log::info('触发CommandProfile');
        }
        return $isProfile;
    }

    function  stopCommandProfile($isProfile){
        if($isProfile){
            $xhprofData = xhprof_disable();
            $xhprofRuns = new XHProfRuns_Default();
            $xhprofRuns->save_run($xhprofData, 'xhprof_laravel');
            \Log::info('关闭CommandProfile');
        }
    }


    function startJobProfile(){

        $isProfile = config("xhprof.QueueEnable") && $this->isSampling(config("xhprof.QueueRate"));
        if($isProfile){
            xhprof_enable(XHPROF_FLAGS_CPU + XHPROF_FLAGS_MEMORY, []);
            \Log::info('触发JobProfile');
        }
        return $isProfile;
    }

    function  stopJobProfile($isProfile){
        if($isProfile){
            $xhprofData = xhprof_disable();
            $xhprofRuns = new XHProfRuns_Default();
            $xhprofRuns->save_run($xhprofData, 'xhprof_laravel');
            \Log::info('关闭JobProfile');
        }
    }

    function isSampling($rate):bool{
        $rand = rand(0, 100);
        return $rand<$rate;
    }
}
