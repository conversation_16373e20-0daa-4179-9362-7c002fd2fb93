<?php

namespace App\Jobs\DoudianMsg;

use App\Models\Order;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Facades\Log;

/**
 * 抖音更新订单的消息任务
 */
class DySaveOrUpdateOrderMessageJob extends ShopMessageJob
{
    protected $msg;

    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function execute($shop, $data)
    {
//        return ;
        $tid =self::getTid($data);
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($shop->user_id);
        $orderService->setShop($shop);
        $order = $orderService->getOrderInfo($tid);
        if ($order) {
            // @doc https://op.jinritemai.com/docs/message-docs/30/7403
            // @doc https://bytedance.larkoffice.com/docx/VWoidD6qlo2UlHxZ5wecTVgVnVf
            if (!empty($data->tag_key) && $data->tag_key == 'shop_priority_delivery') { // 标签表示订单是否需要优先发货
//                    $order['urge_shipment_at'] = date('Y-m-d H:i:s');
                $dataInfo = json_decode($data->data, true);
                $order['urge_shipment_at'] = date('Y-m-d H:i:s', $dataInfo['create_time']);

            }
            $shopId = $shop->id;
            Order::batchSave([$order], $shop->user_id, $shopId);
            Log::info("更新订单",["shopId"=>$shopId,"tid"=>$tid,"tag"=>$this->msg['tag']??'']);
        }
    }


}
