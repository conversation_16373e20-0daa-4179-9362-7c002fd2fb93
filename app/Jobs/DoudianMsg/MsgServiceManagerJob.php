<?php

namespace App\Jobs\DoudianMsg;

use App\Constants\DoudianMsgTag;
use App\Jobs\Job;

class MsgServiceManagerJob
{
	protected $msg;

	public function __construct(array $msg = [])
	{
		$this->msg = $msg;
	}

	protected $initMsgMap = [
			DoudianMsgTag::TAG_TRADE_PAID        => DySaveOrUpdateOrderMessageJob::class,
			DoudianMsgTag::TAG_TRADE_SELLER_SHIP => DySaveOrUpdateOrderMessageJob::class,
			DoudianMsgTag::TAG_TRADE_SUCCESS     => DySaveOrUpdateOrderMessageJob::class,
			DoudianMsgTag::TAG_TRADE_CANCELED    => OrderCancelJob::class,
			DoudianMsgTag::TAG_TRADE_ADDRESS_CHANGED    => DySaveOrUpdateOrderMessageJob::class,
            DoudianMsgTag::TAG_TRADE_PARTLY_SELLER_SHIP => DySaveOrUpdateOrderMessageJob::class,
            DoudianMsgTag::TAG_TRADE_LOGISTICS_CHANGED=>OrderLogisticsChangedJob::class,
            DoudianMsgTag::TAG_REFUND_AGREED     => DySaveOrUpdateOrderMessageJob::class,
			DoudianMsgTag::TAG_REFUND_SUCCESS     => DySaveOrUpdateOrderMessageJob::class,
			DoudianMsgTag::TAG_REFUND_CLOSED     => RefundCloseJob::class,
			DoudianMsgTag::TAG_TRADE_ORDER_TAG_PUSH  => DySaveOrUpdateOrderMessageJob::class,
    ];

	protected function getInstance($msg)
	{
		if (isset($this->initMsgMap[$msg['tag']])) {
			dispatch(new $this->initMsgMap[$msg['tag']]($msg));
		}
	}

	public function handle()
	{
		$this->getInstance($this->msg);
	}
}
