<?php
namespace App\Jobs\KsMsg;

use App\Jobs\Job;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use App\Services\PlatformOrder\PlatformOrderServiceManager;
use Illuminate\Support\Facades\Log;

class KsServiceOrderJob
{
    protected $msg;

    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function handle()
    {
        Log::info('服务市场新增已支付订单消息', $this->msg);

        $msg        = $this->msg;
        $data       = json_decode($msg['info'], true);

//        $shop = Shop::query()->where('identifier', $msg['userId'])->firstOrFail();
        $shop = Shop::firstByIdentifier($msg['userId']);
        if ($shop->auth_status == Shop::AUTH_STATUS_SUCCESS) {
            $orderService = OrderServiceManager::create(config('app.platform'));
            $orderService->setShop($shop);
            $result = $orderService->sendMarketOrderDetail($data['oid']);
            if (isset($result['code'])) {
                Log::error('ks 推送订单消息 获取订单详情失败，result：', [$result]);
            } else {
                // 组装数据
                $platformOrderService = PlatformOrderServiceManager::create(config('app.platform'));
                $data = $platformOrderService->formatToOrder($result);

                $platformOrder = PlatformOrder::query()->create($data);
                if (!$platformOrder) {
                    Log::error('ks 推送订单消息 创建订购订单失败, params:', [$data]);
                }
            }
        }
    }
}
