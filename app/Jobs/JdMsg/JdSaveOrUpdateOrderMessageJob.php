<?php

namespace App\Jobs\JdMsg;

use App\Jobs\DoudianMsg\ShopMessageJob;
use App\Jobs\Job;
use App\Models\Order;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use App\Utils\LogUtil;
use Illuminate\Support\Facades\Log;

/**
 * JD更新订单的消息任务
 */
class JdSaveOrUpdateOrderMessageJob
{
    protected $msg;


    public function __construct(array $msg)
    {
        $this->msg = $msg;
    }

    public function handle()
    {
        $logger = jdJcqLog();
        try {
            $orderService = OrderServiceManager::create(config('app.platform'));
            $tid = self::getTid($this->msg);
            $shop = $this->getShop();
            $orderService->setShop($shop);
            $orders = $orderService->batchGetOrderInfo([['tid' => $tid]]);

            $logger->info("获取订单", ["shopId" => $shop->id, "tid" => $tid,"size" => count($orders)]);
            if (!empty($orders)) {
                $shopId = $shop->id;
                Order::batchSave($orders, $shop->user_id, $shopId);
                $logger->info("更新订单", ["shopId" => $shopId, "tid" => $tid]);
            } else {
                $logger->warning("订单没获取到订单", ["shopId" => $shop->id, "tid" => $tid]);
            }
        }catch (\Throwable $e) {
            $logger->error("获取订单失败", ["shopId" => $this->getShop()->id, "tid" => self::getTid($this->msg), "message"=>$e->getTraceAsString()]);
        }
    }

    public function getShop(){
        $serviceId = array_get($this->msg, 'serviceId');
        return Shop::firstByServiceId($serviceId);
    }
    public static function getTid($data): string
    {
       return array_get($data, 'tid');
    }



}
