<?php

namespace App\Jobs\Orders;

use App\Exceptions\OrderException;
use App\Jobs\Job;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Log;

/**
 * @deprecated
 *  订单同步
 */
class SyncOrdersI<PERSON><PERSON><PERSON><PERSON> extends Job
{
    protected $userId;
    protected $shopId;
    protected $accessToken;
    protected $beginAt;
    protected $endAt;
    const PAGE_SIZE = 100;      //每次请求最大100条
    const LIMIT_COUNT = 300;     //授权首次同步1000条
    const DELAY_TIME = 60;      //超过100条延迟2分钟发放
    public $timeout = 3600;
//    public $queue = 'auth_syn_orders';
    /**
     * @var Shop
     */
    private $auth;

    /**
     *   constructor.
     * @param Shop   $auth
     * @param string $beginAt
     * @param string $endAt
     */
    public function __construct(Shop $auth, string $beginAt, string $endAt)
    {
        $this->auth = $auth;
        $this->userId = $this->auth->user_id;
        $this->shopId = $this->auth->id;
        $this->accessToken = $this->auth->access_token;
        $this->beginAt = $beginAt;
        $this->endAt = $endAt;
    }

    /**
     * 授权后订单处理
     * @throws OrderException
     */
    public function handle()
    {

        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($this->userId);

        // 根据时间间隔 计算循环次数
        $len = intval((strtotime($this->endAt) - strtotime($this->beginAt)) / 60 / $orderService->orderIncrTimeInterval + 1);
        $endTime = strtotime($this->beginAt);
        $totalCount = 0;
        $delayCounter = 0;
        \Log::info('SyncOrdersIncrJob:time:' . $this->endAt . '  ' . $this->beginAt);
        Shop::updateLastSyncIfNewest($this->auth->id, $this->endAt);
        for ($i = 0; $i < $len; $i++) {
            $orderTimeInterval = $orderService->orderIncrTimeInterval;
            $startTime = $endTime;
            $endTime = strtotime("+$orderTimeInterval minute", $startTime);
            do {
                $orders = $orderService->getTradesOrderByIncr($startTime, $endTime);
                \Log::info('SyncOrdersIncrJob:count:' . count($orders));
                if (empty($orders)) {
                    continue;
                }
                // 翻页
                $orderService->pageTurning();
                if ($totalCount < self::LIMIT_COUNT) {
                    $delay = 0;
                } else {
                    $delayCounter++;
                    $delay = self::DELAY_TIME * $delayCounter;
                }
//                \Log::info('SyncOrdersJob', $orders);
                \Log::info(class_basename($this).':newSyncSaveOrders');
                dispatch((new SyncSaveOrders(
                    $this->userId,
                    $this->shopId,
                    $orders
                ))->delay($delay));
            } while ($orderService->hasNext);
        }
    }
}
