<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/19
 * Time: 16:10
 */

namespace App\Jobs\Orders;


use App\Jobs\Job;
use App\Models\Shop;
use App\Services\Order\OderLogicService;
use App\Services\Order\OrderServiceManager;
use GuzzleHttp\DefaultHandler;
use <PERSON><PERSON>\Util\Swoole\Guzzle\SwooleHandler;

class SyncFactoryOrderByScopeJob extends Job
{
    public $queue = 'swoole';
    private $shopId;
    private $beginTime;
    private $endTime;
    public $timeout = 600;
    protected $isFirstPull = true;
    /**
     * @var string
     */
    private $action;

    /**
     * SyncOrderByScopeJob constructor.
     * @param $shopId
     * @param string $beginTime
     * @param string $endTime
     * @param string $action
     */
    public function __construct($shopId, string $beginTime, string $endTime, string $action = '')
    {
        $this->shopId = $shopId;
        $this->beginTime = $beginTime;
        $this->endTime = $endTime;
        $this->action = $action;
    }

    public function handle()
    {
//        $shopId = $this->argument('shop_id');
//        $beginTime = $this->argument('begin_time');
//        $endTime = $this->argument('end_time');
        $orderService = OrderServiceManager::create();
        $oderLogicService = new OderLogicService();
        $shop = Shop::query()->findOrFail($this->shopId);

        $oderLogicService->setOrderService($orderService);
        $oderLogicService->setAction($this->action);
        $oderLogicService->setIsFirstPull($this->isFirstPull);
        $oderLogicService->setCallClassName(class_basename($this));
        $oderLogicService->setIsFactory(true);

        $oderLogicService->handleSyncOrders($shop, $this->beginTime, $this->endTime);
//        $orderCount = $oderLogicService->getOrderCount();

    }

    /**
     * @param bool $isFirstPull
     */
    public function setIsFirstPull(bool $isFirstPull): void
    {
        $this->isFirstPull = $isFirstPull;
    }
}
