<?php

namespace App\Jobs\Orders;

use App\Exceptions\OrderException;
use App\Jobs\Job;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Log;

/**
 *  订单同步
 */
class SyncOrdersJob extends Job
{
    protected $userId;
    protected $shopId;
    protected $accessToken;
    protected $beginAt;
    protected $endAt;
    protected $isFirstPull;
    const PAGE_SIZE = 100;      //每次请求最大100条
    const LIMIT_COUNT = 300;     //授权首次同步1000条
    const DELAY_TIME = 60;      //超过100条延迟2分钟发放
    public $timeout = 3600;
//    public $queue = 'auth_syn_orders';
    /**
     * @var Shop
     */
    private $auth;

    /**
     *   constructor.
     * @param Shop   $auth
     * @param string $beginAt
     * @param string $endAt
     */
    public function __construct(Shop $auth, string $beginAt, string $endAt, bool $isFirstPull = false)
    {
        $this->auth = $auth;
        $this->userId = $this->auth->user_id;
        $this->shopId = $this->auth->id;
        $this->accessToken = $this->auth->access_token;
        $this->beginAt = $beginAt;
        $this->endAt = $endAt;
        $this->isFirstPull = $isFirstPull;
    }

    /**
     * 授权后订单处理
     * @throws OrderException
     */
    public function handle()
    {
        $shop = $this->auth;

        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($this->userId);
        $orderService->setShop($shop);

        // 根据时间间隔 计算循环次数
        $len = intval((strtotime($this->endAt) - strtotime($this->beginAt)) / 60 / $orderService->orderTimeInterval + 1);
        $endTime = strtotime($this->beginAt);
        $totalCount = 0;
        $delayCounter = 0;

        $currentBeginAt = $this->beginAt;
        \Log::info('SyncOrdersJob:time:' . $this->endAt . '  ' . $this->beginAt. ' shopId='.$shop->id??'');
        try {
            for ($i = 0; $i < $len; $i++) {
                $orderService->initPage();
                $orderTimeInterval = $orderService->orderTimeInterval;
                $startTime = $endTime;
                $currentBeginAt = date('Y-m-d H:i:s',$startTime);
                $endTime = strtotime("+$orderTimeInterval minute", $startTime);
                \Log::info('SyncOrdersJob_'.$shop->id. ' start:' . date('Y-m-d H:i:s',$startTime).':end:' .
                    date('Y-m-d H:i:s',$endTime));
                do {
                    $orders = $orderService->getTradesOrder($startTime, $endTime, $this->isFirstPull);
                    \Log::info('SyncOrdersJob:'.$shop->id. ' start:' . date('Y-m-d H:i:s',$startTime).':end:'
                        . date('Y-m-d H:i:s',$endTime) .':count:' . count($orders));
                    if (empty($orders)) {
                        continue;
                    }

                    // 翻页
                    $orderService->pageTurning();
                    if ($totalCount < self::LIMIT_COUNT) {
                        $delay = 0;
                    } else {
                        $delayCounter++;
                        $delay = self::DELAY_TIME * $delayCounter;
                    }

                    \Log::info(class_basename($this).':newSyncSaveOrders');
                    dispatch((new SyncSaveOrders(
                        $this->userId,
                        $this->shopId,
                        $orders
                    ))->delay($delay));
                } while ($orderService->hasNext);
            }
            // 跑完再修改最后同步时间
            Shop::updateLastSync($shop->id, $this->endAt);
        }catch (\Exception $e) {
            //还原最后拉取时间&修改授权状态
            Shop::updateLastSync($shop->id, $currentBeginAt,false);
//            Shop::query()->where('id', $shop->id)
//                ->update([
//                    'last_sync_at' => $currentBeginAt,
////                        'auth_status'  => Shop::AUTH_STATUS_EXPIRE,
//                ]);
        }

    }
}
