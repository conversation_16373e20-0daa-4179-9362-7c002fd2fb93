<?php

namespace App\Jobs\OrderTraces;

use App\Constants\PlatformConst;
use App\Jobs\Job;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\OrderTraceList;
use App\Models\Package;
use App\Models\Shop;
use App\Models\Template;
use App\Models\UserExtra;
use App\Models\WaybillHistory;
use App\Services\Logistic\LogisticDealService;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\DateTimeUtil;
use App\Utils\ShopUtil;
use Illuminate\Support\Facades\Log;

class SyncOrderTracesJob
{
//    public $queue = 'low'; // 改成同步的方式

    protected $packageInfo;

    public function __construct(PackageInfo $packageInfo)
    {
        $this->packageInfo = $packageInfo;
    }

    public function handle(LogisticDealService $LogisticDealService): bool
    {
        $waybillCode = $this->packageInfo->waybillCode;
        $wpCode = $this->packageInfo->wpCode;

        Log::info('同步物流轨迹', ["waybillCode"=> $waybillCode,"updatedAt"=>$this->packageInfo->updatedAt]);


        $package = Package::findByWaybillCode($waybillCode, $wpCode);


        if (!isset($package) || $package->version < 3) {
            OrderTraceList::discard($waybillCode, $wpCode);
            Log::warning('没有匹配的包裹,处理成废弃', ["waybillCode" => $waybillCode, "wp_code" => $wpCode]);

            return true;
        }
        $operationShopId = $package->operation_shop_id;
        if($operationShopId){
            $userExtra = ShopUtil::firstByIdWithCache($operationShopId)->userExtra;
            if($userExtra&&$userExtra->version!=UserExtra::VERSION_SENIOR){
                OrderTraceList::discard($waybillCode, $wpCode);
                Log::info('操作店铺版本太低，废弃订单轨迹',["waybillCode"=> $waybillCode,"wp_code"=> $wpCode] );
                return true ;
            }

        }


        foreach ($package->orders as $order) {
           if(in_array($order->status,Order::ORDER_STATUS_ERROR_ARRAY)){
               OrderTraceList::discard($waybillCode, $wpCode);
               Log::warning('订单状态错误,处理成废弃', ["waybillCode" => $waybillCode, "wp_code" => $wpCode]);
               return true;
           }
        }


        $waybillHistory = $package->waybillHistory;


        if (empty($waybillHistory)) {

            OrderTraceList::discard($waybillCode, $wpCode);
            Log::info('没有匹配的物流信息,处理成废弃', ["waybillCode" => $waybillCode, "wp_code" => $wpCode]);
            return true;
        }

        $waybillShopId = (isset($waybillHistory->source_shopid) && $waybillHistory->source_shopid > 0) ? $waybillHistory->source_shopid : $waybillHistory->shop_id;
//        list($waybillData, $order) = WaybillHistory::getWaybillDataByWaybillHistory($waybillHistory);

        $shop = Shop::query()->where('id', $waybillShopId)->first();
        Log::info('店铺信息', ['shop' => $shop]);
//        if ($order->template_id > 0){
//            $template = Template::query()->where('id', $order->template_id)->first();
//            $shop = Shop::query()->where('id', $template->shop_id)->first();
//        }else{
//            $shop = Shop::query()->where('id', $order->shop_id)->first();
//        }
        //把包裹里面的tid都提取出来,并去重

     //   array_unique(array_pluck($package->packageOrders, 'tid'));
        $waybillData = [
            'express_code' => $wpCode,
            'express_no' => $waybillCode,
            'tid'=>$this->packageInfo->tid,
        ];

        if (empty($shop) || empty($shop->access_token) || $shop->auth_status != SHOP::AUTH_STATUS_SUCCESS) {
//            echo '店铺未授权或店铺不存在'.PHP_EOL;
            $waybillData['status'] = OrderTraceList::STATUS_DISCARD;
            Log::warning('店铺未授权或店铺不存在,面单标识作废', ['waybillCode' => $waybillCode, 'shop' => $shop]);
            OrderTraceList::discard($waybillCode, $wpCode);
            return false;
//            OrderTraceList::batchSave([$waybillData], 0, $this->packageInfo->shopId, false);
        }
        $waybillService = WaybillServiceManager::init($package->auth_source, $shop->access_token);
        try {


            $traceData = $waybillService->getOrderTraceList($waybillData);
            Log::info('物流轨迹', ['traceData' => $traceData]);
            if (empty($traceData)) {
                Log::info('物流轨迹为空', ['waybillCode' => $waybillCode]);
                return true;
            }
            $orderTraceArr = array_map(function ($tid) use ($package, $traceData) {
                return [
                    'tid' => $tid,
                    'user_id' => $package->user_id,
                    'shop_id' => $package->shop_id,
                    'express_code' => $package->wp_code,
                    'express_no' => $package->waybill_code,
                    'send_at' => $package->send_at,
                    'status' => $traceData['status'],
                    'latest_updated_at' => $traceData['latest_updated_at'],
                    'action' => $traceData['action'],
                    'latest_trace' => $traceData['latest_trace'],
                    'trace_list' => $traceData['trace_list'],

                ];
            }, array_unique(array_pluck($package->packageOrders, 'tid')));

            Log::info('同步物流轨迹', $orderTraceArr);
            OrderTraceList::batchSave($orderTraceArr, 0, $package->shop_id, false);
            $LogisticDealService->updateLogistic($traceData);

//            $traceData['waybill_code'] = $waybillCode;

//            Log::info('同步物流轨迹', $traceData);
        } catch (\Exception $e) {
            $message = $e->getMessage();
            Log::error('同步物流轨迹失败', [$message, $waybillCode,$e->getTraceAsString()]);
            //如果有一些错误信息进行特殊处理，例如：订单已取消
            if(mb_strpos($message, '订单已取消') >=0){
                OrderTraceList::discard($waybillCode, $wpCode);
                return true;
            }


            return true;
        }

        return true;
    }
}

