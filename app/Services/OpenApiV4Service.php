<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/10/12
 * Time: 18:52
 */

namespace App\Services;

use App\Constants\AuthStateTypeConst;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\Order;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Models\Waybill;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Order\Impl\JdOrderImpl;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use Log;

class OpenApiV4Service
{
    /**
     * @var OpenApiV3Service
     */
    protected $v3Service;

    public function __construct()
    {
        $this->v3Service = new OpenApiV3Service();
    }

    /**
     * @return string
     */
    public function getAppId()
    {
        return \request('appId');
    }

    public function genOauthUrl(int $isBindWaybillShop): array
    {
        $appId = $this->getAppId();
        $auth_code = session_create_id();
        $data = json_encode(['app_id' => $appId]);
        $redisKey = 'auth_code:' . $auth_code;
        $bool = redis('cache')->set($redisKey, $data, 'nx', 'ex', 60 * 30);
        if (!$bool) {
            throw new ApiException(StatusCode::OPERATION_FAILED_RETRY);
        }

        $type = AuthStateTypeConst::OPEN_BIND_SHOP;
        if ($isBindWaybillShop) {
            $type = AuthStateTypeConst::OPEN_BIND_WAYBILL_SHOP;
            $count = ApiShopBind::countByAppId($appId, ApiShopBind::IS_WAYBILL_SHOP_YES);
            $apiAuth = ApiAuth::firstByAppId($appId);
            if ($count >= $apiAuth['bind_waybill_shop_limit'] + 1) {
                throw new ApiException(StatusCode::SHOP_WAYBILL_BIND_EXCEEDED);
            }
        }
        $stateArr = [
            'code' => $auth_code,
            'type' => $type,
            'created_at' => date('Y-m-d H:i:s')
        ];
        $targetUrl = socialite()->driver(config('app.platform'))->redirect()->getTargetUrl();
        $parse_url = parse_url($targetUrl);
        $queryArr = [];
        parse_str($parse_url['query'], $queryArr);
        // 替换 url 里的 state
        $state = base64_encode(json_encode($stateArr, JSON_UNESCAPED_UNICODE));
        $queryArr['state'] = $state;
        $route = explode('?', $targetUrl)[0];
        $configPlatform = config('app.platform');
        $openapiRedirectUrl = config("socialite.$configPlatform.openapi_redirect_url");
        if (!empty($openapiRedirectUrl)) {
            $queryArr['redirect_uri'] = $openapiRedirectUrl;
        }
        if (Environment::isJd()) {
            $queryArr['scope'] = 'snsapi_base';
        } elseif (Environment::isWxOrWxsp()) {
            $url = env('APP_DOMAIN') . '/redirect-auth' . '?' . http_build_query(['state' => $state]);
            return [$url, $state];
        }
        $url = $route . '?' . http_build_query($queryArr);
        return [$url, $state];
    }

    public function getShopInfo($shopCode, $state, string $shopName = null)
    {
        /**
         * @var $shop Shop
         */
        $shop = null;
        if (empty($shopCode) && empty($state) && empty($shopName)) {
            $arr = StatusCode::PARAMS_ERROR;
            $arr[1] .= ':shopCode和state不能都为空';
            throw new ApiException($arr);
        }
        if (!empty($state)) {
            $stateArr = json_decode(base64_decode($state), true);
            if (!isset($stateArr['code'])) {
                $arr = StatusCode::PARAMS_ERROR;
                $arr[1] = 'State 不完整';
                throw new ApiException($arr);
            }
            if ($stateArr['created_at'] < date('Y-m-d H:i:s', time() - 60 * 10)) {
                $arr = StatusCode::PARAMS_ERROR;
                $arr[1] = 'State 已过期';
                throw new ApiException($arr);
            }
            $redisKey = 'open_auth_shop_id_by_code:' . $stateArr['code'];
            $shopId = redis('cache')->get($redisKey);

            if (empty($shopId)) {
                \Log::info("redisKey not Found, redisKey=" . $redisKey);
                throw new ApiException(StatusCode::STATE_UNAUTHORIZED);

            }
            $shop = Shop::query()->where('id', $shopId)->first();
        }
        if (!empty($shopCode)) {
            $shop = Shop::firstByShopCode($shopCode);
        }

        if (!empty($shopName)) {
            $shop = Shop::firstByShopName($shopName);
        }

        if (empty($shop)) {
            throw new ApiException(StatusCode::SHOP_NOT_FOUND);
        }
        $userExtraInfo = UserExtra::query()->where('shop_id', $shop->id)->orderByDesc('expire_at')->first();
        $appId = $this->getAppId();
        $apiShopBind = ApiShopBind::firstByAppIdShopId($appId, $shop->id);
        $data = [
            'id' => $shop->id,
            'shopName' => $shop->shop_name,
            'name' => $shop->name,
            'shopExpireTime' => $shop->expire_at,
            'serviceExpireTime' => $userExtraInfo->expire_at ?? null,
            'shopCode' => $shop->shop_code,
            'agentCode' => $apiShopBind->agent_code ?? '',
            'isBindWaybillShop' => $apiShopBind->is_waybill_shop ?? '',
            'serviceId' => $shop->service_id ?? null,
            'openShopId' => $shop->identifier
        ];
        return $data;
    }


    /**
     * @param $validateData
     * @return array
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function getPrintDataList($validateData): array
    {
        $platform = Waybill::OPEN_API_DEFAULT;
        $senderInfo = array_get($validateData, 'senderInfo');
        $waybillType = array_get($validateData, 'waybillType');
        $wpCode = array_get($validateData, 'wpCode');
        $shopCode = array_get($validateData, 'shopCode');
        $wpShopCode = array_get($validateData, 'wpShopCode');
        $orderPrintParams = array_get($validateData, 'orderPrintParams', []);

        $batchNo = time() . rand(00, 99);
        $appId = $this->getAppId();
        $wpShop = Shop::firstByShopCode($wpShopCode);
        $shop = Shop::firstByShopCode($shopCode);

        if (empty($wpShop) && empty($shop)) {
            throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }

        $waybill = $this->v3Service->getWaybill($platform, $wpShop, null);
        \Log::info("waybill=" . $waybill);
        $branchCodeArr = [
            $senderInfo['senderProvince'],
            $senderInfo['senderCity'],
            $senderInfo['senderTown'] ?? '',
            $senderInfo['senderStreet'] ?? '',
            $senderInfo['senderDetail'],
        ];

        $branchCode = implode(',', $branchCodeArr);
        \Log::info("处理订单取号");

        $orderSnList = array_pluck($orderPrintParams, 'orderSn');

        $orderArray = array_map(function ($tid) {
            return ['tid' => $tid, 'id' => $tid]; // 这个 id 是索引用
        }, $orderSnList);
        $orderService = OrderServiceManager::create();
        $orderService->setShop($shop);
        $tradesOrder = $orderService->batchGetOrderInfo($orderArray);
        Order::batchSave($tradesOrder, $shop->user_id, $shop->id);

        // 查询网点信息
        $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $waybillData = $waybillService->waybillSubscriptionQuery();
//        \Log::info("waybillData=", [$waybillData]);

        $userId = $wpShop->user_id;
        $shopId = $wpShop->id;

//        $printDataList = $this->getPrintDataAndWaybill($userId, $shopId, $wpCode, $platform, $branchCode,
//            $senderInfo['senderName'], $senderInfo['senderMobile'], $orderSnList, $waybillType, $batchNo, $packageNum,
//            $waybillData, $waybill, $appId, $orderPrintParams);
//        return $printDataList;

        // 查询发件人信息
        $addressInfo = Order::getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $wpCode);
        if (empty($addressInfo)) {
            return throw_error_code_exception(StatusCode::COMPANY_ERROR);
        }

        $addressInfo = array_merge($addressInfo, ['sender_name' => $senderInfo['senderName'], 'mobile' => $senderInfo['senderMobile']]);

        $newOrders = [];
        $orderList = Order::query()->with('OrderCipherInfo')->whereIn('tid', $orderSnList)->get();
        $orderList = array_pluck($orderList, null, 'tid');
        foreach ($orderSnList as $orderSn) {
            $mainOrder = $orderList[$orderSn] ?? null;
            if (empty($mainOrder)) {
                throw_error_code_exception(StatusCode::ORDER_NOT_FOUND);
            }
            $mainOrder->mergeOrders = null;
            $newOrders[] = $mainOrder;
        }
        $failedData = $printData = [];

        if (!empty($newOrders)) {
            $packageIdArr = array_pluck($orderPrintParams, 'packageId', 'orderSn');
            foreach ($newOrders as $index => $newOrder) {
                if (isset($packageIdArr[$newOrder->tid])) {
                    $newOrder->request_id = $packageIdArr[$newOrder->tid];
                }
            }
            //未取号取号处理
            $newWaybillHistories = Order::newWaybillHistoriesForOpenApi(
                $userId,
                $shopId,
                $platform,
                $newOrders,
                $addressInfo,
                $wpCode,
                $waybill,
                $batchNo,
                $waybillType,
                1,
                $appId
            );

            //取号失败信息
            if (count($newWaybillHistories['failed_data']) > 0) {
                $failedData = $newWaybillHistories['failed_data'];
            }
        }
        $printData = $newWaybillHistories['print_data'] ?? [];
        if (!$printData && !$failedData) {
            //throw new \Exception('订单获取打印数据异常！');
            return throw_error_code_exception(StatusCode::PRINT_ERROR);
        }
        $printDataList = array_merge($printData, $failedData);
        return $printDataList;

    }

    public function checkApiMethodWhiteList($apiMethod, $appId)
    {
        $platform = config('app.platform');
        $platform_api = config('platform_api');
        if (!isset($platform_api[$platform]) || !in_array($apiMethod, $platform_api[$platform])) {
            $platform_api_appid = config('platform_api_appid');
            if (isset($platform_api_appid[$platform][$appId]) && in_array($apiMethod, $platform_api_appid[$platform][$appId])) {
                return true;
            }
            throw new ApiException(StatusCode::NOT_IN_WHITE_LIST);
        }
    }

    public function filterApiData($apiMethod, $data)
    {
        $appid = $this->getAppId();
        $whitelist = explode(',', config('app.open_api_whitelist', ''));
        // 白名单不限制
        if (!empty($whitelist) && in_array($appid, $whitelist)) {
            return $data;
        }
        if (Environment::isTaoBao()) {
            $data = $this->unsetKey($data, 'oaid');
        } elseif (Environment::isDy()) {
            switch ($apiMethod) {
                case 'order.searchList':
                case 'order.orderDetail':
                    $data = $this->unsetKey($data, 'encrypt_post_tel');
                    $data = $this->unsetKey($data, 'encrypt_post_receiver');
                    $data = $this->unsetKey($data, 'encrypt_detail');
                    break;
            }
        } elseif (Environment::isJd()) {
            switch ($apiMethod) {
                case 'jingdong.pop.order.search':
                case 'jingdong.pop.order.get':
//                    $data['fullAddress']=addressDesensitization($data['fullAddress']);
//                    $data = $this->unsetKey($data, 'fullAddress');
//                    $data['fullname']=dataDesensitizationForOpenApi($data['fullname']);
//                    $data = $this->unsetKey($data, 'fullname');

                    $whitelist = explode(',', config('app.open_api_whitelist', ''));
                    // 白名单不限制
                    if (! in_array($appid, $whitelist)) {
                        $data = $this->unsetKey($data, 'desen_telephone');
                        $data = $this->unsetKey($data, 'desen_mobile');
                        $data = $this->maskOaid($data);
                    }


                    break;
            }
        } elseif (Environment::isKs()) {
            switch ($apiMethod) {
                case 'open.order.cursor.list':
                case 'open.order.detail':
                    $data = $this->unsetKey($data, 'encryptedMobile');
                    $data = $this->unsetKey($data, 'encryptedConsignee');
                    $data = $this->unsetKey($data, 'encryptedAddress');
                    break;
            }
        } elseif (Environment::isWxsp()) {
            switch ($apiMethod) {
                case '/channels/ec/order/get':
//                    $data = $this->unsetKey($data, 'tel_number');
//                    $data = $this->unsetKey($data, 'user_name');
//                    $data = $this->unsetKey($data, 'detail_info');
                    $data = $this->unsetKey($data, 'ewaybill_order_code');
                    break;
            }
        }
        return $data;
    }

    public function maskOaid($data){
        if (is_array($data)) {
            foreach ($data as $k => $v) {
                if (is_array($v)) {
                    $data[$k] = $this->maskOaid($v);
                } else {
                    if ($k == 'oaid') {
                        //对oaid进行脱敏  NDBhY2Q4ABT5Yz05DTH47QCKVJNOXZu1Yjk1MvRQ3QU71lix8TvEejumsIT4TXmkePWtSlX/smdnD+6O/Ex50jnZVX6Fi0V77ebkMqn2iL4lmLksxxman5ee/Mn+WtG4YuJCxkj34FbeO4TfxUUlEvpZugVLeKSfUUCs2A==
                        //对oaid进行脱敏，截断前面10位，后面10位，中间用10个*代替
                        $data[$k] = substr($v, 0, 10) . str_repeat('*', 10) . substr($v, -10);
                    }
                }
            }
        }
        return $data;
    }
    // 递归设置key 为空
    public function unsetKey($data, $key, $setVal = '')
    {
        if (is_array($data)) {
            foreach ($data as $k => $v) {
                if (is_array($v)) {
                    $data[$k] = $this->unsetKey($v, $key);
                } else {
                    if ($k == $key) {
                        $data[$k] = $setVal;
                    }
                }
            }
        }
        return $data;
    }

    /**
     * @throws ApiException
     * @throws ErrorCodeException
     */
    public function getSendByCustom($platform, $shopCode, $wpShopCode, string $requestMethod, $apiMethod, $apiParams, $orderSn)
    {
        $shop = $wpShop = null;
        if ($shopCode) {
            $shop = Shop::firstByShopCode($shopCode);
        }
        if ($wpShopCode) {
            $wpShop = Shop::firstByShopCode($wpShopCode);
        }
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        $orderService = OrderServiceManager::create($platform);
        if ($apiMethod == 'taobao.user.openuid.getbynick') { // 该接口无需token
            $shop->access_token = '';
            $orderService->setShop($shop);
        } else {
            $orderService->setShop($shop);
        }
//        $orderService->setShop($shop);

        if (!empty($orderSn)) {
            if (empty($wpShop)) {
                return throw_error_code_exception(StatusCode::WP_SHOP_UN_EXIST);
            }
            $tradesOrder = $orderService->batchGetOrderInfo([['tid' => $orderSn, 'id' => $orderSn]]);
            if (empty($tradesOrder) || empty($tradesOrder[0])) {
                Log::error('订单不存在:$tradesOrder', ['orderSn' => $orderSn]);
                return throw_error_code_exception(StatusCode::ORDER_NOT_FOUND);
            }
            Order::batchSave($tradesOrder, $shop->user_id, $shop->id);
            $orderInfo = \App\Models\Fix\Order::query()
                ->with(['orderCipherInfo', 'shop'])
                ->where('shop_id', $shop->id)
                ->where(function ($query) use ($orderSn) {
                    $query->where('tid', $orderSn)
                        ->orWhere('tid', $orderSn . 'A');
                })
                ->first();
            if (empty($orderInfo)) {
                Log::error('订单不存在:$orderInfo', ['orderSn' => $orderSn, 'shopId' => $shop->id]);
                throw new ApiException(StatusCode::ORDER_NOT_FOUND);
            }
            $orderService->setShop($wpShop);
            $apiParams = $orderService->fillApiParamByOrder($apiMethod, $apiParams, $orderInfo->toArray(), $shop);
            $data = $orderService->sendByCustom($requestMethod, $apiMethod, $apiParams, $orderInfo);
        } else {
            if (Environment::isKs() && $shopCode == $wpShopCode) {
                $apiParams = $orderService->fillApiParamByOrder($apiMethod, $apiParams, [], $wpShop);
            }
            $data = $orderService->sendByCustom($requestMethod, $apiMethod, $apiParams, null);
        }
        $this->recordRequestCounter($apiMethod, $data);
        $data = $this->handleApiData($apiMethod, $data);
        return $this->filterApiData($apiMethod, $data);
    }

    public function getJdMaskReceiverInfo(array $data): array
    {
        $appid = $this->getAppId();
        $whitelist = explode(',', config('app.open_api_whitelist', ''));
        if (!in_array($appid, $whitelist)) {
            throw new ApiException(StatusCode::NOT_IN_WHITE_LIST);
        }
        $fieldArr = [
//            'receiver_phone',
            'receiver_name',
            'receiver_address',
        ];
        $shopCode = $data['shopCode'];
        $orderService = new JdOrderImpl();
        $shop = Shop::firstByShopCode($shopCode);
        $orderService->setShop($shop);

        $maskArr = [];
        foreach ($data['list'] as $index => $datum) {
            foreach ($fieldArr as $field) {
                if (!isset($datum[$field])) {
                    continue;
                }
                $maskArr[$datum['tid']]['tid'] = $datum['tid'];
                $arr = [
                    'tid' => $datum['tid'],
                    'text' => $datum[$field],
                    'field' => $field
                ];
                $maskData = $orderService->cipherDecryptMaskBatch([$arr]);
                $maskArr[$datum['tid']][$field] = array_first($maskData)['text'] ?? '';
            }
        }

        return array_values($maskArr);
    }

    private function handleApiData($apiMethod, $data)
    {
        if (Environment::isDy()) {
            $dyOrderImpl = new DyOrderImpl();
            switch ($apiMethod) {
                case 'order.searchList':
                    if (!empty($data['data']['shop_order_list'])) {
                        foreach ($data['data']['shop_order_list'] as $index => &$datum) {
                            $receiver_phone = '$' . $dyOrderImpl->cipherExtractSearch($datum['encrypt_post_tel']) . '$';
                            $receiver_name = $dyOrderImpl->cipherExtractSearch($datum['encrypt_post_receiver']);
                            $receiver_address = $dyOrderImpl->cipherExtractSearch($datum['post_addr']['encrypt_detail']);
                            $datum['address_md5'] = md5($receiver_phone . $receiver_name . $receiver_address);
                        }
                    }
                    break;
                case 'order.orderDetail':
                    if (!empty($data['data']['shop_order_detail'])) {
                        $shop_order_detail = $data['data']['shop_order_detail'];
                        $receiver_phone = '$' . $dyOrderImpl->cipherExtractSearch($shop_order_detail['encrypt_post_tel']) . '$';
                        $receiver_name = $dyOrderImpl->cipherExtractSearch($shop_order_detail['encrypt_post_receiver']);
                        $receiver_address = $dyOrderImpl->cipherExtractSearch($shop_order_detail['post_addr']['encrypt_detail']);
                        $data['data']['shop_order_detail']['address_md5'] = md5($receiver_phone . $receiver_name . $receiver_address);
                    }
                    break;
            }

        }

        return $data;
    }

    private function recordRequestCounter($apiMethod, $data)
    {
        $appid = $this->getAppId();
        $ymd = date('m-d');
        $redisKey = "openApiV4:$appid:$ymd:doApi:$apiMethod";
        if (in_array($appid, ['16863066765337', '16884393554734'])) {
            redis('cache')->incr($redisKey);
            redis('cache')->expire($redisKey, 60 * 86400);
        }
    }


}
