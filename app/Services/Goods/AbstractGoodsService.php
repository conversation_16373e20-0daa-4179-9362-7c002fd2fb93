<?php

namespace App\Services\Goods;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Jobs\Goods\SyncGoodsJob;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Services\Order\AbstractOrderService;
use App\Utils\Environment;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Response;

/**
 * 商品相关Service
 */
abstract class AbstractGoodsService
{
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    /**
     * @var Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    private $shop;

    protected $accessToken;
    protected $dataType = 'JSON';
    protected $concurrency = 50;  //并发数
    /**
     * curl 并发池异常输出原始数据
     * @var bool
     */
    protected $poolCurlAbnormalOutputOriginalData = false;


    protected $serviceId;
    protected $specificationId;

    /**
     * 微信下一页的key
     * @var string
     */
    public $nextKey = '';


    /**
     * @param mixed $accessToken
     * @return AbstractOrderService
     */
    public function setAccessToken($accessToken): AbstractGoodsService
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * 获取用户授权
     * @return Shop
     * @throws ApiException
     * <AUTHOR>
     */
    public function getShop():Shop
    {
        if (empty($this->shop)) {
//            $this->shop = Shop::query()
//                ->where('type', $this->platformType)
//                ->where('user_id', $this->userId)
//                ->first();
            $shop = Shop::firstByUserId($this->userId);
        } else {
            $shop = Shop::firstById($this->shop->id);
        }
        if (empty($shop)) {
            throw new ApiException(ErrorConst::SHOP_NOT_FOUND);
        }
        return $shop;
    }


    /**
     * 获取token
     * @return mixed
     * @throws ApiException
     * <AUTHOR>
     */
    public function getAccessToken()
    {
//        if (!empty($this->accessToken)) {
//            return $this->accessToken;
//        }
        // 每次都重新获取token
        $shop = $this->getShop();
        return $this->accessToken = $shop->access_token;
    }


    /**
     * @param Builder|\Illuminate\Database\Eloquent\Model|object|null $shop
     */
    public function setShop($shop): void
    {
        $this->shop = $shop;
    }


    /**
     * 用户id
     * @var
     */
    protected $userId;

    /**
     * @param mixed $userId
     */
    public function setUserId($userId): void
    {
        $this->userId = $userId;
    }

    /**
     * 商品总数
     * @var int
     */
    public $goodsTotalCount = -1;

    /**
     * 批量格式化成商品表结构
     * @param array $goods
     * @return array
     * <AUTHOR>
     */
    abstract public function formatToGoods(array $goods): array;


    /**
     * 格式化成商品结构
     * @param array $goods 商品信息
     * @param null $skuPropertyName sku的属性,有些平台商品信息的批量接口和单个接口返回的sku属性信息是不同属性字段
     * @return array
     */
    abstract function formatToGood(array $goods): array;

//    /**
//     * 授权后的同步商品
//     * @return bool
//     */
//    public function syncGoodsByAuth()
//    {
//        $shop = $this->getShop();
//        $redis = redis('cache');
//        $redisKey = 'sync_goods_run:' . $shop->user_id;
//        $bool = $redis->exists($redisKey);
//        if ($bool) {
//            return false;
//        }
//        dispatch(new SyncGoodsJob($shop));
//        $redis->setex($redisKey, 1 * 60, time());
//        return true;
//    }

    /**
     * 发送获取商品列表请求
     * @param int $pageSize
     * @param int $currentPage
     * @return mixed
     */
    abstract protected function sendGetGoods(int $pageSize, int $currentPage);

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     */
    abstract protected function sendGetGoodsByGoodsId(array $goodsId);

    /**
     * 调用平台接口获取单个商品信息
     * @param $String
     * @return mixe
     */
    //abstract protected function sendGetSingleGoods(string $numIid);

    /**
     * 根据平台的商品ID获取数据
     * @param String $numIid
     * @return array
     */
    public function getGoodsByNumIid(string $numIid): array
    {
        if (empty($numIid)) {
            throw new InvalidArgumentException('商品ID不能为空');
        }

        $res = $this->sendGetGoodsByGoodsId([$numIid]);

        if (empty($res)) {
            return [];
        }
        Log::info("getGoodsByNumIid", [$res]);
        return $this->formatToGoods($res);
    }

    /**
     * 获取商品列表
     * @param int $pageSize
     * @param int $currentPage
     * @return array
     */
    public function getGoodsList(int $pageSize, int $currentPage = 1)
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }
        $this->hasNext = false;
        $res = $this->sendGetGoods($pageSize, $currentPage);
        return $this->formatToGoods($res);
    }

    /**
     * 获取商品列表
     * @param array $goodsIds
     * @return array
     */
    public function getGoodsListByGoodsId(array $goodsIds): array
    {
        if (empty($this->platformType)) {
            throw new InvalidArgumentException('未定义的字段：authType');
        }
        $res = $this->sendGetGoodsByGoodsId($goodsIds);
        if(Environment::isXhs()){
            return $this->formatItems($res);
        }else {
            return $this->formatToGoods($res);
        }
    }

    /**
     * 从缓存中读取Remark
     * @param $numIid
     * @return mixed
     */
    public static function readCacheRemark($numIid)
    {
        $redis = \redis('cache');
        $key = 'goods_remark_' . $numIid;
        $redis->get($key);
    }


    /**
     * 设置缓存中的备注
     * @param $numIid
     * @param $remark
     * @return void
     */
    public static function setCacheRemark($numIid, $remark)
    {
        $redis = \redis('cache');
        $key = 'goods_remark_' . $numIid;
        $redis->setex($key, 60 * 60 * 24 * 60, $remark);
    }

    /**
     * 最大同步量 null不限制
     * @return mixed
     */
    public function getMaxSyncGoodsCount()
    {
        return null;
    }

    /**
     * 编辑SKU简称
     * 1. 先检查GoodSku是否已经存在，如果存在就直接编辑
     * 2. 如果不存在，就是先同步商品
     *  2.1 商品同步成功，而且有匹配的sku，则编辑
     *  2.2 商品同步失败，就需要通过订单查找匹配的子订单，利用找到的子订单信息，新建商品和Sku
     * @param $shop
     * @param $numIid
     * @param $skuId
     * @param $customSkuValue
     * @return bool
     */
    public function skuEditBySkuId($shop, $numIid, $skuId, $customSkuValue):bool
    {
        $sku = GoodsSku::query()->where('sku_id', $skuId)->first();
        if ($sku) {
            $sku->custom_sku_value = $customSkuValue;
            $sku->save();
            return true;
        }
        $userId = $shop['user_id'];
        $shopId = $shop['id'];

        try {
            $goods = $this->getGoodsByNumIid($numIid);
            Goods::batchSave($goods, $userId, $shopId);
            $goods = Goods::query()->where('num_iid', $numIid)->where('shop_id', $shopId)->first();
            if (!$goods) {
                $goodsId= $this->createGoodsFromOrderItem($numIid,$shopId,$userId, null);
            }else{
                $goodsId = $goods['id'];
            }

            $sku = GoodsSku::query()->where('sku_id', $skuId)->first();
            if ($sku) {
                $sku->custom_sku_value = $customSkuValue;
                $sku->save();
                return true;
            }else{
               return $this->createGoodsSkuFormOrderItem($goodsId,$shopId,$userId,$skuId, $customSkuValue);
            }

        } catch (Exception $e) {
            Log::error("获取商品信息失败", [$numIid, $shopId]);
            return false;
        }


    }


    /**
     * 更新商品信息
     * 1. 先检查Goods是否已经存在，如果存在就直接编辑
     * 2. 如果不存在，就是先同步商品
     *   2.1 商品同步成功，而且有匹配的商品，则编辑，否则就基于子订单的信息创建
     *
     * @param $userId
     * @param $shop
     * @param $numIid
     * @param null $customerTitle
     * @param null $remark
     * @param null $flag
     * @return mixed
     * @throws ErrorCodeException
     */
    public
    function goodsEditByNumiid($userId, $shop, $numIid, $customerTitle = null, $remark = null, $flag = null)
    {
        $updates = [];
        if (isset($customerTitle)) {
            $updates ['custom_title'] = $customerTitle;
        }
        if (isset($remark)) {
            $updates['remark'] = $remark;
        }
        if (isset($flag)) {
            $updates['flag'] = $flag;
        }
        Log::info("Goods update", $updates);
        $shopId = $shop['id'];
        $goods = Goods::query()->where('num_iid', $numIid)->where('shop_id', $shopId)->update($updates);
        if ($goods) {
            return true;
        }
        try {
            $goods = $this->getGoodsByNumIid($numIid);
            Goods::batchSave($goods, $userId, $shopId);
            $goods = Goods::query()->where('num_iid', $numIid)->where('shop_id', $shopId)->update($updates);
            if ($goods == 0) {
                $this->createGoodsFromOrderItem($numIid, $shopId, $shop['user_id'], $customerTitle);
            }
            return true;
        } catch (Exception $e) {
            Log::error("获取商品信息失败", [$numIid, $shopId]);
            //从订单中行中获取商品信息
            $this->createGoodsFromOrderItem($numIid, $shopId, $shop['user_id'], $customerTitle);
            return true;
        }


    }

    /** 从订单中行中获取商品信息
     * @param $numIid
     * @param $shopId
     * @param $user_id
     * @param $customerTitle
     * @return int
     * @throws ErrorCodeException
     */
    function createGoodsFromOrderItem($numIid, $shopId, $user_id, $customerTitle): int
    {
        $goodsOrderItem = OrderItem::query()->where('num_iid', $numIid)->orderBy('id', 'desc')->first();
        if (!$goodsOrderItem) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR, null, '找不到商品订单信息');
        }
        $goods = Goods::create([
            'shop_id' => $shopId,
            'user_id' => $user_id,
            'type' => 5,
            'num_iid' => $numIid,
            'custom_title' => $customerTitle,
            'is_onsale' => 1,
            'title' => $goodsOrderItem['item_name'],
            'price' => $goodsOrderItem['item_price'],
            'num' => 1,
        ]);
        return $goods->id;
    }

    function createGoodsSkuFormOrderItem($goodsId, $shopId,  $user_id,$skuId,$customSkuValue): int{
        $goodsOrderItem = OrderItem::query()->where('sku_id', $skuId)->orderBy('id', 'desc')->first();
        if (!$goodsOrderItem) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR, null, '找不到SKU订单信息');
        }
        $goodsSku = GoodsSku::create([
            'goods_id'=>$goodsId,
            'sku_id'=>$skuId,
            'shop_id'=>$shopId,
            'user_id'=>$user_id,
            'type'=>5,
            'sku_value'=>$goodsOrderItem['sku_value'],
            'custom_sku_value'=>$customSkuValue,
            'sku_pic'=>$goodsOrderItem['sku_pic'],
            'is_onsale'=>1
        ]);
        return $goodsSku->id;


    }



    /**
     * 搜索商品
     * @param GoodsSearchRequest $request
     * @return GoodsSearchResponse
     * @throws ErrorCodeException
     */
    public function searchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $res = $this->sendSearchGoods($request);
        if (!empty($res->data)) {
            if(!Environment::isXhs()) {
                $res->data = $this->formatToGoods($res->data);
            }else{
                $res->data=$this->formatItems($res->data);
            }
        }
        return $res;
    }

    /**
     * 调用平台的接口查询
     * @param GoodsSearchRequest $request
     * @return GoodsSearchResponse
     * @throws ErrorCodeException
     */

    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        throw_error_code_exception(ErrorConst::UN_SUPPORT_OPERATION);
    }

    /**
     * 通过SkuId获取商品以及全部的SKU信息
     * @param array $skuIds
     * @return void
     */
    function getGoodsBySkuIds(array $skuIds){
        $skus=$this->sendGetGoodsBySkuIds($skuIds);
        if(!empty($skus)){
            $this->formatToGoods($skus);
        }

    }

    function sendGetGoodsBySkuIds(array $skuIds):array
    {
        throw_error_code_exception(ErrorConst::UN_SUPPORT_OPERATION);
    }

    public function formatItems(array $data):array
    {
        throw_error_code_exception(ErrorConst::UN_SUPPORT_OPERATION);
    }



}
