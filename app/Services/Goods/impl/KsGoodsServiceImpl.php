<?php

namespace App\Services\Goods\impl;

use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Client\KsClient;
use Illuminate\Support\Facades\Log;

/**
 * 快手的商品服务
 */
class KsGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    protected $platformType = Shop::PLATFORM_TYPE_KS;
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods['items'] as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['skuList'] as $index => $item) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['kwaiSkuId'],
                "sku_value" => $item['specification'],
                "outer_id" => $item['relSkuId'],
                "outer_goods_id" => $good['relItemId'],
                "sku_pic" => $item['imageUrl'],
                "is_onsale" => 0,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $good['kwaiItemId'],
            'outer_goods_id' => $good['relItemId'],
            'goods_title' => $good['title'],
            'goods_pic' => $good['mainImageUrl'],
            'is_onsale' => $good['shelfStatus'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => date('Y-m-d H:i:s', $good['createTime'] / 1000),
            'goods_updated_at' => date('Y-m-d H:i:s', $good['updateTime'] / 1000),
            'skus' => $skus
        ];
    }

    /**
     * 获取商品列表
     * @param int int $pageSize,
     * @param int $currentPage
     * @return array
     * @throws ClientException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        return $this->callOpenItemListGet($pageSize, $currentPage);
    }

//    function sendGetSingleGoods(string $numIid)
//    {
//        $goods = $this->callOpenItemListGet(10, 1, $numIid);
//        if (empty($goods['items'])) {
//            return [];
//        }
//        return $goods['items'][0];
//    }

    /**
     * @param int $pageSize
     * @param int $currentPage
     * @param null $numIid
     * @return array|mixed
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function callOpenItemListGet(int $pageSize, int $currentPage, $numIid = null)
    {
        $client = KsClient::newInstance($this->getAccessToken());
        $params = [
//            'sellerId' => $this->getShopId(),
            'pageSize' => $pageSize,
            'itemStatus' => 1,
            'itemType' => 1,
            'pageNumber' => $currentPage,
        ];
        if (isset($numIid)) {
            $params['kwaiItemId'] = $numIid;
        }
        $goods = $client->execute('get', '/open/item/list/get', $params);

        if (isset($goods['data']['totalItemCount'])) {
            $this->goodsTotalCount = $goods['data']['totalItemCount'];
        } else {
            $this->goodsTotalCount = 0;
        }

        if (isset($goods['data']['totalItemCount'])) {
            $this->goodsTotalCount = $goods['data']['totalItemCount'];
        } else {
            $this->goodsTotalCount = 0;
        }
        KsClient::handleErrorCode($goods);

//        \Log::info('ks_goods_result', [$goods]);
        if ($goods['result'] != 1 || !isset($goods['data'])) {
            if ($goods['result'] == 100026 || $goods['error_msg'] == '页数不合法') {
                return [];
            }
            Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
            return [];
        } elseif ($goods['data']['currentPageNumber'] < $goods['data']['totalPage']) {
            $this->hasNext = true;
        }
        return $goods['data'];
    }


    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     * @throws ApiException
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $client = KsClient::newInstance($this->getAccessToken());

        foreach ($goodsId as $datum) {
            $params = [
                'kwaiItemId' => $datum
            ];
            $paramsArr[] = $params;
        }
        $arr = $client->executeAsync('/open/item/list/get', $paramsArr);

        $result = [];
        foreach ($arr as $item) {
            if (isset($item['items']) && !empty($item['items'])) {
                $result[] = $item['items'][0];
            }
        }

        $res['items'] = $result;
        return $res;
    }

    /**
     * @throws OrderException
     * @throws ClientException
     * @throws ApiException
     */
    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $response = new GoodsSearchResponse();
        $pageSize = $request->pageSize;
        $currentPage = $request->currentPage;
        $numIid = $request->numIid;
        $client = KsClient::newInstance($this->getAccessToken());
        $params = [
//            'sellerId' => $this->getShopId(),
            'pageSize' => $pageSize,
            'itemStatus' => 1,
            'itemType' => 1,
            'pageNumber' => $currentPage,
        ];
        if (isset($numIid)) {
            $params['kwaiItemId'] = $numIid;
        }
        $goods = $client->execute('get', '/open/item/list/get', $params);

        if (isset($goods['data']['totalItemCount'])) {
            $response->total = $goods['data']['totalItemCount'];
        } else {
           $response->total = 0;
        }


        KsClient::handleErrorCode($goods);

//        \Log::info('ks_goods_result', [$goods]);
        if ($goods['result'] != 1 || !isset($goods['data'])) {
            if ($goods['result'] == 100026 || $goods['error_msg'] == '页数不合法') {
                $response->hasNext = false;
                $response->data = [];
            }
            Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
            $response->data = [];
            $response->hasNext = false;
            return $response;
        } elseif ($goods['data']['currentPageNumber'] < $goods['data']['totalPage']) {
            $response->hasNext = true;
        }
        $response->data = $goods['data'];
//        return $goods['data'];

        return $response;
    }
}
