<?php

namespace App\Services\Goods\impl;

use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Shop;
use App\Services\Client\WxClient;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

/**
 * 微信商品服务
 */
class WxspGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    protected $platformType = Shop::PLATFORM_TYPE_WX;

    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;




    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['skus'] as $index => $item) {
            $sku_value = '';
            foreach ($item['sku_attrs'] as $attr) {
                $sku_value .= $attr['attr_value'] . ';'; // 属性值
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['sku_id'],
                "sku_value" => $sku_value,
                "outer_id" => $item['out_sku_id']??'',
                "outer_goods_id" => $good['out_product_id'],
                "sku_pic" => $item['thumb_img'],
                "is_onsale" => $item['status'] == 5 ? GoodsSku::IS_ONSALE_YES : GoodsSku::IS_ONSALE_NO,
            ];
        }
        return [
            "type" => $this->platformType,
            'num_iid' => $good['product_id'],
            'outer_goods_id' => $good['out_product_id'],
            'goods_title' => $good['title'],
            'goods_pic' => $good['head_imgs'][0],
            'is_onsale' => $good['status'] == 5 ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'skus' => $skus
        ];
    }

    /**
     * 获取商品列表
     * @param int $pageSize
     * @param int $currentPage
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        $client =  WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());
        $params = [
            'page_size' => $pageSize,
            'status' => 5,
            'page' => $currentPage,
        ];
        $goods = $client->execute('post', '/channels/ec/product/list/get', $params);
        WxClient::handleErrorCode($goods);
        if (($currentPage * $pageSize) < $goods['total_num']) {
            $this->hasNext = true;
            $this->nextKey = $goods['next_key'];
        }

        $result = [];
        foreach ($goods['product_ids'] as $productId) {
            try {
                $params = [
                    'product_id' => $productId,
                ];
                $goods = $client->execute('post', '/channels/ec/product/get', $params);
                Log::info('wx_goods_result', [$goods]);
                $result[] = $goods['product'];
            } catch (\Exception $e) {
                Log::info('微信同步商品请求出错 sync goods error:'.$e->getMessage());
            }
        }
        return $result;
    }

    /**
     * 获取service_id
     * @return mixed
     * <AUTHOR>
     */
    public function getServiceId()
    {
        if (!empty($this->serviceId)) {
            return $this->serviceId;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权serviceId');
        }
        return $this->serviceId = $shop->service_id;
    }


    /**
     * 获取specification_id
     * @return mixed
     * <AUTHOR>
     */
    public function getSpecificationId()
    {
        if (!empty($this->specificationId)) {
            return $this->specificationId;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权serviceId');
        }
        return $this->specificationId = $shop->specification_id;
    }

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return array
     * @throws ApiException
     * @throws GuzzleException
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $result = [];
        $client =  WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());

        foreach ($goodsId as $numIid) {
            try {
                $params = [
                    'product_id' => $numIid,
                ];
                $goods = $client->execute('post', '/channels/ec/product/get', $params);
                Log::info('wx_goods_result', [$goods]);
                $result[] = $goods['product'];
            } catch (\Exception $e) {
                Log::info('微信同步商品请求出错 sync goods error:'.$e->getMessage());
            }
        }

        return $result;
    }

    function sendSearchGoods(GoodsSearchRequest $request):GoodsSearchResponse{
        $goodsSearchResponse=new GoodsSearchResponse();
        $client =  WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());
        $pageSize = $request->pageSize;
        $currentPage = $request->currentPage;
        $params = [
            'page_size' => $pageSize,
            'status' => 5,
            'page' => $currentPage,
        ];
        $goods = $client->execute('post', '/channels/ec/product/list/get', $params);
        WxClient::handleErrorCode($goods);
        if (($currentPage * $pageSize) < $goods['total_num']) {
            $goodsSearchResponse->hasNext = true;
        }

        $result = [];
        foreach ($goods['product_ids'] as $productId) {
            try {
                $params = [
                    'product_id' => $productId,
                ];
                $goods = $client->execute('post', '/channels/ec/product/get', $params);
                Log::info('wx_goods_result', [$goods]);
                $result[] = $goods['product'];
            } catch (\Exception $e) {
                Log::info('微信同步商品请求出错 sync goods error:'.$e->getMessage());
            }
        }
        $goodsSearchResponse->data=$result;

        return $goodsSearchResponse;
    }
}
