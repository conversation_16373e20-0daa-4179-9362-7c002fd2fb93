<?php

namespace App\Services\Goods\impl;

use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Shop;
use App\Services\Client\WxClient;
use App\Services\Goods\mixe;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

/**
 * 微信商品服务
 */
class WxGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    protected $platformType = Shop::PLATFORM_TYPE_WX;

    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;




    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        $client = WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());
        $list = [];
        foreach ($goods as $index => $good) {
            $good['skus'] = $this->fillSku2Spu($good, $client);
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['skus'] as $index => $item) {
            $sku_value = '';
            foreach ($item['sku_attrs'] as $attr) {
                //				$sku_value .= $attr['attr_key'] . ':'. $attr['attr_value'] . ';'; //属性键key + 属性值
                $sku_value .= $attr['attr_value'] . ';'; // 属性值
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['sku_id'],
                "sku_value" => $sku_value,
                "outer_id" => $item['out_sku_id']??'',
                "outer_goods_id" => $item['out_product_id'],
                "sku_pic" => $item['thumb_img'],
                "is_onsale" => $item['status'] == 5 ? GoodsSku::IS_ONSALE_YES : GoodsSku::IS_ONSALE_NO,
            ];
        }
        return [
            "type" => $this->platformType,
            'num_iid' => $good['product_id'],
            'outer_goods_id' => $good['out_product_id'],
            'goods_title' => $good['title'],
            'goods_pic' => $good['head_img'][0],
            'is_onsale' => $good['status'] == 5 ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            //			'goods_created_at' => date('Y-m-d H:i:s', $good['createTime'] / 1000),
            //			'goods_updated_at' => date('Y-m-d H:i:s', $good['updateTime'] / 1000),
            'skus' => $skus
        ];
    }

    /**
     * 获取商品列表
     * @param int int $pageSize,
     * @param int $currentPage
     * @return array
     * @throws ClientException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        $client =  WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());
        $params = [
            'page_size' => $pageSize,
            'status' => 5,
            'page' => $currentPage,
        ];
        $goods = $client->execute('post', '/product/spu/get_list', $params);
        WxClient::handleErrorCode($goods);
        if (($currentPage * $pageSize) < $goods['total_num']) {
            $this->hasNext = true;
        }
        return $goods['spus'];
    }

    /**
     * 获取service_id
     * @return mixed
     * @throws ApiException
     * <AUTHOR>
     */
    public function getServiceId()
    {
        if (!empty($this->serviceId)) {
            return $this->serviceId;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权serviceId');
        }
        return $this->serviceId = $shop->service_id;
    }


    /**
     * 获取specification_id
     * @return mixed
     * <AUTHOR>
     */
    public function getSpecificationId()
    {
        if (!empty($this->specificationId)) {
            return $this->specificationId;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权serviceId');
        }
        return $this->specificationId = $shop->specification_id;
    }

//    protected function sendGetSingleGoods(string $numIid)
//    {
//        $client =  WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());
//        $params = [
//            'product_id' => $numIid,
//        ];
//        $goods = $client->execute('post', '/product/spu/get', $params);
//        \Log::info('wx_goods_result', [$goods]);
//        WxClient::handleErrorCode($goods);
//        $spu = $goods['data']['spu'];
//        $skus = $this->fillSku2Spu($spu, $client);
//        $spu['skus'] = $skus;
//        return $spu;
//    }

    /**
     * 填充SPU的SKU信息
     * @param $spu
     * @param WxClient $client
     * @return array
     * @throws ClientException
     */
    protected function fillSku2Spu($spu, WxClient $client): array
    {
        foreach ((array)$spu['skus'] as $index2 => $datum) {
            $params = [
                'product_id' => $spu['product_id'],
                'sku_id' => $datum['sku_id'],
            ];
            $data = $client->execute('post', '/product/sku/get', $params);
            $skus[] = $data['data'];
        }
        return $skus;
    }

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     * @throws ApiException
     * @throws GuzzleException
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $result = [];
        $client =  WxClient::newInstance($this->getAccessToken(),$this->getServiceId(),$this->getSpecificationId());

        foreach ($goodsId as $numIid) {
            try {
                $params = [
                    'product_id' => $numIid,
                ];
                $goods = $client->execute('post', '/product/spu/get', $params);
                Log::info('wx_goods_result', [$goods]);
                WxClient::handleErrorCode($goods);
                $spu = $goods['data']['spu'];
                $skus = $this->fillSku2Spu($spu, $client);
                $spu['skus'] = $skus;
                $result[] = $spu;
            } catch (\Exception $e) {
                Log::info('微信同步商品请求出错 sync goods error:'.$e->getMessage());
            }
        }

        return $result;
    }
}
