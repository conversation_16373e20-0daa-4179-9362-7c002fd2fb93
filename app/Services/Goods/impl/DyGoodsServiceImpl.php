<?php

namespace App\Services\Goods\impl;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\ErrorCodeException;
use App\Exceptions\OrderException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Client\DyClient;
use Illuminate\Support\Facades\Log;

/**
 * 抖音的Goods 服务
 */
class DyGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    protected $platformType = Shop::PLATFORM_TYPE_DY;

    /**
     * 获取商品列表
     * @param int $pageSize
     * @param int $currentPage
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1): array
    {
        $this->hasNext = false;
        $client = DyClient::newInstance($this->accessToken);
        $params = [
            'page' => $currentPage,
            'size' => $pageSize,
        ];
        $goods = $client->execute('product/listV2', $params);
        DyClient::handleErrorCode($goods);
        if (isset($goods['data']['total'])) {
            $this->goodsTotalCount = $goods['data']['total'];
        } else {
            $this->goodsTotalCount = 0;
        }
        if (count($goods['data']['data']) == $goods['data']['size']) {
            $this->hasNext = true;
        }
        $paramsArr = [];
        foreach ((array)$goods['data']['data'] as $index => $datum) {
            $params = [
                'product_id' => $datum['product_id']
            ];
//            $data = $client->execute('product/detail', $params);
            $paramsArr[] = $params;
//            $arr[] = $data['data'];
        }
        return $client->executeAsync('product/detail', $paramsArr);
    }


    /**
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException|ErrorCodeException
     */
    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $goodsSearchResponse=new GoodsSearchResponse();

        $this->hasNext = false;
        $client = DyClient::newInstance($this->accessToken);
        $params = [
            'page' => $request->currentPage,
            'size' => $request->pageSize,
        ];
        if($request->goodsTitle){
            $params['name'] = $request->goodsTitle;
        }
        if($request->numIid){
            $params['product_id']=[$request->numIid];
        }

        if(isset($request->isOnSale)){
            //商品的状态是一个组合状态 参考 https://op.jinritemai.com/docs/question-docs/92/2070
            if ($request->isOnSale == Goods::IS_ONSALE_YES) {
                $params['status'] = 0;
                $params['check_status']=3;
            }
            elseif($request->isOnSale == Goods::IS_ONSALE_NO){
                $params['status'] = 1;
            }else{
                throw_error_code_exception(ErrorConst::PARAM_ERROR);
            }


        }
        Log::info("获取平台商品列表", [$request,$params]);
        $goods = $client->execute('product/listV2', $params);
        DyClient::handleErrorCode($goods);
        if (isset($goods['data']['total'])) {
            $goodsSearchResponse->total = $goods['data']['total'];
        } else {
            $goodsSearchResponse->total = 0;
        }
        if (count($goods['data']['data']) == $goods['data']['size']) {
            $goodsSearchResponse->hasNext = true;
        }
        $paramsArr = [];
        foreach ((array)$goods['data']['data'] as $index => $datum) {
            $params = [
                'product_id' => $datum['product_id']
            ];
//            $data = $client->execute('product/detail', $params);
            $paramsArr[] = $params;
//            $arr[] = $data['data'];
        }
        $date=$client->executeAsync('product/detail', $paramsArr);
        $goodsSearchResponse->data=$date;
        return $goodsSearchResponse;

    }

    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            if (!empty($good)) {
                $list[] = $this->formatToGood($good);
            }
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $goods
     * @return array
     */
    public function formatToGood(array $goods): array
    {
//        Log::info("获取商品详情", $goods);
        $skus = [];
        $specValues = [];
        if (is_array($goods['specs'])) {
            foreach ($goods['specs'] as $index => $item) {
                foreach ($item['values'] as $value) {
                    $specValues[$value['id']] = [
                        'spec_name' => $item['name'], //规格名称：尺寸，颜色
                        'value' => $value,            //规格值：array name 黄 ，XL
                    ];
                }
            }
        }
        foreach ($goods['spec_prices'] as $index => $item) {
            $specIds = array_get($item, 'spec_detail_ids', []);
            $skuValue = '';
            foreach ($specIds as $specId) {
                //带规格名：尺寸：XL;颜色:红色;
//				$skuValue  .= $specValues[$specId]['spec_name'] . ':'. $specValues[$specId]['value']['name'] . ';';
                //不带规格名：XL;红色;
                $skuValue .= $specValues[$specId]['value']['name'] . ';';
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => strval($item['sku_id']),
                "sku_value" => $skuValue,
                "outer_id" => $item['code']??'',
                "outer_goods_id" => $goods['out_product_id']??'',
                "sku_pic" => $item['sku_pic'] ?? null,
                "is_onsale" => 0,
            ];
        }

        $onsaleArr = [Goods::IS_ONSALE_YES, Goods::IS_ONSALE_NO, Goods::IS_ONSALE_NO];
        return [
            "type" => $this->platformType,
            'num_iid' =>strval( $goods['product_id']),
            'outer_goods_id' => $goods['out_product_id'],
            'goods_title' => $goods['name'],
            'goods_pic' => $goods['img'],
            'is_onsale' => $goods['status'] == 0 && $goods['check_status'] == 3 ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => $goods['create_time'],
            'goods_updated_at' => $goods['update_time'],
            'skus' => $skus
        ];
    }

    /**
     * 获取单个商品
     * @param String $numIid
     * @param String $outProductId
     * @param String $showDraft
     * @return \App\Services\Goods\mixe|array|mixed|\Psr\Http\Message\StreamInterface
     * @throws ClientException
     */
//    function  sendGetSingleGoods(String $numIid,String $outProductId="",String $showDraft="false"){
//        $client = DyClient::newInstance($this->accessToken);
//        $params=['show_draft'=>$showDraft];
//        if(!empty($numIid)){
//            $params[ 'product_id'] = $numIid;
//        }
//        if(!empty($outProductId)){
//            $params[ 'out_product_id'] = $outProductId;
//        }
//        $res = $client->execute('product/detail', $params);
//        DyClient::handleErrorCode($res);
//        return $res['data'];
//    }


    /**
     * DY限制同步最多10000数据
     * @return int
     */
    public function getMaxSyncGoodsCount(): int
    {
        return 10000;
    }

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $client = DyClient::newInstance($this->accessToken);
        foreach ($goodsId as $datum) {
            $params = [
                'product_id' => $datum
            ];
            $paramsArr[] = $params;
        }
        //        Log::info("获取商品详情",$result);
        return $client->executeAsync('product/detail', $paramsArr);
    }
}
