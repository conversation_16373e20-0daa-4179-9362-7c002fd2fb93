<?php

namespace App\Services\Goods\impl;

use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Facades\Log;
use WareReadFindWareByIdRequest;
use App\Services\Client\JdClient;
use SkuReadSearchSkuListRequest;
use WareReadSearchWare4ValidRequest;

/**
 * JD的Goods 服务
 */
class JdGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    protected $platformType = Shop::PLATFORM_TYPE_JD;


    /**
     * @inheritDoc
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $goods
     * @return array
     */
    public function formatToGood(array $goods): array
    {
        $skus = [];
        $skuList = $this->getSkuList($goods['wareId']);
        foreach ($skuList as $index => $item) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => strval($item['skuId']),
                "sku_value" => $item['multiCateProps'][0]['attrValueAlias'][0] ?? 0,
                "outer_id" => $item['outerId'] ?? '',
                "outer_goods_id" => $goods['outerId'] ?? '',
                "sku_pic" => $item['jdPrice'] ?? null,
                "is_onsale" => 1,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => strval($goods['wareId'] ?? ''),
            'outer_goods_id' => $goods['outerId'] ?? '',
            'goods_title' => $goods['title'],
            'goods_pic' => isset($goods['logo']) ? 'https://img10.360buyimg.com/n0/' . $goods['logo'] : '',
            'is_onsale' => $goods['wareStatus'] == 8 ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => $goods['created'],
            'goods_updated_at' => $goods['modified'],
            'skus' => $skus
        ];
    }

    public function getSkuList($goodId)
    {
        $client = JdClient::newSdkClient($this->accessToken);
        $req = new SkuReadSearchSkuListRequest();

        $req->setWareId($goodId);
        $req->setField('wareId,skuId,status,jdPrice,outerId,logo,skuName,wareTitle,modified,created,multiCateProps');


        $resp = $client->execute($req, $client->accessToken);
        JdClient::handleErrorCode($resp);
        $resp = json_decode(json_encode($resp), true);

        return $resp['jingdong_sku_read_searchSkuList_responce']['page']['data'] ?? [];
    }


    /**
     * @inheritDoc
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        $client = JdClient::newSdkClient($this->accessToken);
        $req = new \WareReadSearchWare4ValidRequest();
        $req->setPageNo($currentPage);
        $req->setPageSize($pageSize);
        $req->setWareStatusValue('8');
        $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'])) {
            $this->goodsTotalCount = $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'];
        } else {
            $this->goodsTotalCount = 0;
        }

        $arr = [];
        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page'])) {
            if ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageNo'] <
                ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'] / $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageSize'])) {
                $this->hasNext = true;
//                \Log::info("set hasnext=true",$resp);
            }

            return $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['data'];
        }

        return $arr;

    }

//    protected function sendGetSingleGoods(string $numIid)
//    {
//        $client = JdClient::newSdkClient($this->accessToken);
//
//        $req = new WareReadFindWareByIdRequest();
//        $req->setWareId($numIid);
//        $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');
//
//        $resp = $client->execute($req, $client->accessToken);
//        $resp = json_decode(json_encode($resp), true);
//        JdClient::handleErrorCode($resp);
//        return $resp['jingdong_ware_read_findWareById_responce']['ware'];
//    }

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return mixed
     */
    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $client = $this->getJdClient();
        $this->setAccessToken($this->accessToken);

        $result = $data = [];
        foreach ($goodsId as $index => $wareId) {
            $req = new WareReadFindWareByIdRequest();
            $req->setWareId($wareId);
            $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');
            list($requestUrl, $apiParams) = $client->getApiParamsAndUrl($req);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }

        $orderService = OrderServiceManager::create();
        $orderService->setAccessToken($this->accessToken);
        $responses = $orderService->poolCurl($data, 'post');

        foreach ($responses as $index => $response) {
            try {
                $resp = $orderService->handleResp($response);
                Log::info('jdOrderImpl',['response'=>$resp]);
                if (isset($resp['jingdong_ware_read_findWareById_responce']['ware'])) {
                    $result[$index] = $resp['jingdong_ware_read_findWareById_responce']['ware'];
                }
            }catch (\Exception $e){
                Log::info('京东同步商品请求出错 sync goods error:'.$e->getMessage());
            }
        }

        return $result;
    }

    protected function getJdClient()
    {
        $client = new JdClient(config('socialite.jd.client_id'), config('socialite.jd.client_secret'));
        $client->setAccessToken($this->getAccessToken());
        return $client;
    }

    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        Log::info('搜索商品',['request'=>$request]);
        $goodsSearchResponse=new GoodsSearchResponse();

        $this->hasNext = false;

        $client = JdClient::newSdkClient($this->accessToken);
        $req = new WareReadSearchWare4ValidRequest();
        $req->setPageNo($request->currentPage);
        $req->setPageSize($request->pageSize);
        if($request->isOnSale){
            if($request->isOnSale==Goods::IS_ONSALE_YES){
                $req->setWareStatusValue([8]);
            }
            if($request->isOnSale==Goods::IS_ONSALE_NO){
                $req->setWareStatusValue([2, 4,514, 516,1028]);
            }
        }
        if(!empty($request->goodsTitle)){
            $req->setSearchKey($request->goodsTitle);
            $req->setSearchField("[title]");
            Log::info('搜索商品标题',['searchKey'=>$request->goodsTitle,'searchField'=>"title"]);
        }
        if(!empty($request->numIid)){
            $req->setWareId([intval($request->numIid)]);
            Log::info('搜索商品numIid',['wareId'=>$request->numIid]);
        }
        $req->setField('wareId,title,categoryId,brandId,templateId,wareStatus,outerId,itemNum,modified,created,weight,width,height,length,images,logo,marketPrice,jdPrice,multiCateProps');

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);

        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'])) {
            $goodsSearchResponse->total = $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'];
        } else {
            $goodsSearchResponse->total = 0;
        }

        $arr = [];
        if (isset($resp['jingdong_ware_read_searchWare4Valid_responce']['page'])) {
            if ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageNo'] <
                ($resp['jingdong_ware_read_searchWare4Valid_responce']['page']['totalItem'] / $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['pageSize'])) {
                $goodsSearchResponse->hasNext = true;
//                \Log::info("set hasnext=true",$resp);
            }

            $goodsSearchResponse->data= $resp['jingdong_ware_read_searchWare4Valid_responce']['page']['data'];
        }

        return $goodsSearchResponse;
    }


}
