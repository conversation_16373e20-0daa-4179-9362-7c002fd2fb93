<?php

namespace App\Services\Goods\impl;

use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Client\Alc2mClient;
use App\Services\Goods\AbstractGoodsService;
use App\Utils\DateTimeUtil;
use Illuminate\Support\Facades\Log;

/**
 * 淘工厂商品服务实现类
 */
class Alc2mGoodsServiceImpl extends AbstractGoodsService
{

    protected $platformType = Shop::PLATFORM_TYPE_ALC2M;

    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods as $index => $good) {
            if (!empty($good)) {
                $list[] = $this->formatToGood($good);
            }
        }

        return $list;
    }

    function formatToGood(array $goods): array
    {
        $skus = [];
        //商品可能没有SKU
        foreach ($goods['skuInfos']??[] as $index => $item) {
            $skuImage = '';
            foreach ($item['skuImageUrl'] as $imageUrl) {

                if(empty($skuImage)){
                    $skuImage =$imageUrl??'';
                }
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => strval($item['skuId']),
                "sku_value" =>  $item['specificationDesc'],
                "sku_pic" =>self::fullImageUrl($skuImage),
                "is_onsale" => 0,
            ];
        }

//        $onsaleArr = [Goods::IS_ONSALE_YES, Goods::IS_ONSALE_NO, Goods::IS_ONSALE_NO];
        $images = array_get($goods, 'imageUrl');
        return [
            "type" => $this->platformType,
            'num_iid' =>strval( $goods['itemId']),
            'goods_title' => $goods['title'],
            'goods_pic' => self::fullImageUrl(array_shift($images)),
            'is_onsale' => $goods['status'] == 1? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => null,
            'goods_updated_at' => null,
            'skus' => $skus
        ];
    }

    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        // TODO: Implement sendGetGoods() method.
    }

    protected function sendGetGoodsByGoodsId(array $goodsId)
    {
        $params = [
            'pageIndex' => 1,
            'pageSize' => sizeof($goodsId),
            'bizCode'=>'C2M_TJ',
        ];
        $params['itemIdList']=json_encode($goodsId);
        $client = $this->getClient();
        $method = 'com.alibaba.product:alibaba.ascp.product.getList';
        $result = $client->execute($method, $params);
        $client::handleResp($result);
        return array_get($result,'data',[]);
    }

    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $goodsSearchResponse=new GoodsSearchResponse();

        $params = [
            'pageIndex' => $request->currentPage,
            'pageSize' => $request->pageSize,
            'bizCode'=>'C2M_TJ',
        ];
        if($request->goodsTitle){
            $params['title'] = $request->goodsTitle;
        }
        if($request->numIid){
            $params['itemIdList']=json_encode([$request->numIid]);
        }

        $client = $this->getClient();
        $method = 'com.alibaba.product:alibaba.ascp.product.getList';
        $result = $client->execute($method, $params);
        Log::info('淘工厂商品查询结果', $result);
        $client::handleResp($result);

        $goodsSearchResponse->total=array_get($result,'totalCount',0);
        $goodsSearchResponse->data=array_get($result,'data',[]);
        if(sizeof($goodsSearchResponse->data) == $request->pageSize){
            $goodsSearchResponse->hasNext=true;
        }

        return $goodsSearchResponse;
    }


    protected function getClient():Alc2mClient
    {
        return Alc2mClient::newInstance($this->accessToken);
    }


    public static function fullImageUrl(string $imageUrl): string{
        return 'https://img.alicdn.com/imgextra/'.$imageUrl;
    }


}
