<?php

namespace App\Services\Goods\impl;

use App\Exceptions\ApiException;
use App\Exceptions\OrderException;
use App\Http\Controllers\Goods\GoodsSearchRequest;
use App\Http\Controllers\Goods\GoodsSearchResponse;
use App\Models\Goods;
use App\Models\Shop;
use App\Services\Client\TaoBaoClient;
use Exception;
use Illuminate\Support\Facades\Log;
use ItemSkusGetRequest;
use ItemsSellerListGetRequest;
use TopClient\request\CustomRequest;
use \ItemSellerGetRequest;

/**
 * 淘宝商品服务
 */
class TaobaoGoodsServiceImpl extends \App\Services\Goods\AbstractGoodsService
{
    /**
     * SKU的字段信息
     */
    const SKU_FIELDS = "sku_id,
			properties,
			quantity,
			price,
			created,
			modified,
			status,
			properties_name,
			sku_spec_id,
			outer_id,
			barcode,
			num_iid,";
    /**
     * 商品字段信息
     */
    const GOODS_FIELDS = "approve_status,
	        num_iid,
	        title,
	        nick,
	        type,cid,
	        pic_url,
	        num,props,
	        valid_thru,
	        list_time,
	        price,
	        has_discount,
	        has_invoice,
	        has_warranty,
	        has_showcase,
	        modified,
	        delist_time,
	        postage_id,
	        seller_cids,
	        outer_id,
	        sku.sku_id,
	        sku.properties,
	        sku.out_id,
	        sku.properties_name,
	        sold_quantity";
    /**
     * 是否存在下一页
     * @var bool
     */
    public $hasNext = false;


    protected $platformType = Shop::PLATFORM_TYPE_WX;

    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        if (empty($goods['items']['item'])) {
            Log::warning("没有获取到商品");
            return [];
        }
        $list = [];
        foreach ($goods['items']['item'] as $index => $item) {
            //淘宝是可能会出现没有sku的情况
//            if (isset($item['skus']['sku']) && is_array($item['skus']['sku'])) {
            $list[] = $this->formatToGood($item);
//            }
        }

        return $list;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        if (isset($good['skus']['sku'])) {
            foreach ($good['skus']['sku'] as $index => $item) {

                $propertiesArr = explode(';', $item['properties']);
                foreach ($propertiesArr as $value) {
                    $item['properties_name'] = str_replace($value . ':', '', $item['properties_name']);
                }

                $skus[] = [
                    "type" => $this->platformType,
                    "sku_id" => $item['sku_id'],
                    "sku_value" => $item['properties_name'],
                    "outer_id" => $item['outer_id'] ?? null,
                    "outer_goods_id" => $good['outer_id'] ?? null,
                    "sku_pic" => $item['imageUrl'] ?? null,
                    "is_onsale" => 0,
                ];
            }
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $good['num_iid'],
            'outer_goods_id' => $good['outer_id'] ?? null,
            'goods_title' => $good['title'],
            'goods_pic' => $good['pic_url'],
            'is_onsale' => $good['approve_status'] == 'onsale' ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => $good['modified'],
            'goods_updated_at' => $good['modified'],
            'skus' => $skus
        ];
    }


//    protected function sendGetSingleGoods(string $numIid)
//    {
//        $topClient = TaoBaoClient::newInstance();
//        $token = $this->getAccessToken();
//        $req = new ItemSellerGetRequest();
//        $req->setNumIid($numIid);
//        $req->setFields(self::GOODS_FIELDS);
//
//        $resp = $topClient->execute($req, $token);
//        $resp = TaoBaoClient::handleResp($resp);
//        \Log::info("taobao goods", [$resp]);
////        if (isset($resp['item']) && isset($resp['item']['skus'])) {
////            $skus = [];
////            foreach ($resp['item']['skus']['sku'] as $skuIdx) {
////                $skus[]=$this->sendGetSingleSkuInfo($skuIdx['sku_id']);
////            }
////            $resp['item']['skus']=$skus;
////        }
//        return $resp['item'];
//    }

    /**
     * 获取单个的SKU信息
     * @param $skuId
     * @return void
     * @throws ApiException
     * @throws OrderException
     */
    protected function sendGetSingleSkuInfo($skuId)
    {
        $topClient = TaoBaoClient::newInstance();
        $token = $this->getAccessToken();
        $req = new \ItemSkuGetRequest();
        $req->setSkuId($skuId);
        $req->setFields(self::SKU_FIELDS);
        $resp = $topClient->execute($req, $token);
        $resp = TaoBaoClient::handleResp($resp);
        return $resp['sku'];
    }

    /**
     * 批量获取SKU信息
     * @param string $numIid
     * @return mixed
     * @throws ApiException
     * @throws OrderException
     */
    protected function sendGetSkusInfo(string $numIid)
    {
        $topClient = TaoBaoClient::newInstance();
        $token = $this->getAccessToken();
        $req = new ItemSkusGetRequest();
        $req->setNumIids($numIid);
        $req->setFields(self::SKU_FIELDS);
        $req->putOtherTextParam('num_iids', $numIid);
        $resp = $topClient->execute($req, $token);
        $resp = TaoBaoClient::handleResp($resp);
        return $resp['skus'];
    }


    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        return [];
        //没权限先注释掉
//        $topClient = $this->getClient();
//        $token = $this->getAccessToken();
//        $req = new CustomRequest();
//        $req->setApiMethodName('taobao.items.onsale.get');
//        $req->setFields("approve_status,
//	        num_iid,
//	        title,
//	        nick,
//	        type,cid,
//	        pic_url,
//	        num,props,
//	        valid_thru,
//	        list_time,
//	        price,
//	        has_discount,
//	        has_invoice,
//	        has_warranty,
//	        has_showcase,
//	        modified,
//	        delist_time,
//	        postage_id,
//	        seller_cids,
//	        outer_id,
//	        sold_quantity");
//        $req->putOtherTextParam('page_size', $pageSize);
//        $req->putOtherTextParam('page_no', $currentPage);
//
//        $resp = $topClient->execute($req, $token);
//        $resp = $this->handleResponse($resp);
//
//        if (empty($resp['items']['item'])) {
//            return [];
//        }
//        $resp['items']['item'] = array_map(function ($item) {
//            // 额外拉取备注
//            $tmp = $this->sendGetSkuInfo($item['num_iid']);
//            $item = $item + $tmp;
//            return $item;
//        }, $resp['items']['item']);
//        return $resp;
    }

    /**
     * 发送获取商品列表请求
     * @param array $goodsId
     * @return array
     */
    protected function sendGetGoodsByGoodsId(array $goodsId): array
    {
        $result = [];
        //最大请求20个
        $goodsIdArr = array_chunk($goodsId, 20);
        foreach ($goodsIdArr as $item) {
            try {
                $topClient = TaoBaoClient::newInstance();
                $token = $this->getAccessToken();
                $req = new ItemsSellerListGetRequest;
                $req->setFields(self::GOODS_FIELDS);
                $req->setNumIids(implode(',', $item));
                $resp = $topClient->execute($req, $token);
                $resp = TaoBaoClient::handleResp($resp);

                $result = array_merge($result, $resp);
            } catch (Exception $e) {
                Log::info('淘宝同步商品请求出错 sync goods error:' . json_encode($item));
            }
        }
        Log::info('淘宝同步商品' . json_encode($result));

        return $result;
    }

    /**
     * @throws ApiException
     * @throws OrderException
     */
    function sendSearchGoods(GoodsSearchRequest $request): GoodsSearchResponse
    {
        $pageSize = $request->pageSize;
        $currentPage = $request->currentPage;

        $goodSearchResponse = new GoodsSearchResponse();
        $topClient = TaoBaoClient::newInstance();
        $token = $this->getAccessToken();
        $req = new CustomRequest();
        $req->setApiMethodName('taobao.items.onsale.get');
        $req->setFields("approve_status,
	        num_iid,
	        title,
	        nick,
	        type,cid,
	        pic_url,
	        num,props,
	        valid_thru,
	        list_time,
	        price,
	        has_discount,
	        has_invoice,
	        has_warranty,
	        has_showcase,
	        modified,
	        delist_time,
	        postage_id,
	        seller_cids,
	        outer_id,
	        sold_quantity");
        $req->putOtherTextParam('page_size', $pageSize);
        $req->putOtherTextParam('page_no', $currentPage);

        $resp = $topClient->execute($req, $token);
        $resp = TaoBaoClient::handleResp($resp);
        //平台的参考文档有错误，实际的值items.item
        $items=array_get($resp,'items.item',[]);
        if (empty($items)) {
            return $goodSearchResponse;
        }
        $items= array_map(function ($item) {
            // 额外拉取备注
            $tmp = $this->sendGetSkusInfo($item['num_iid']);
            $item = $item + $tmp;  //数组合并
            return $item;
        }, $items);
        $goodSearchResponse->data =$items;
        if(sizeof($goodSearchResponse->data)==$pageSize){
            $goodSearchResponse->hasNext = true;
        }else{
            $goodSearchResponse->hasNext = false;
        }


        return $goodSearchResponse;
    }
}
