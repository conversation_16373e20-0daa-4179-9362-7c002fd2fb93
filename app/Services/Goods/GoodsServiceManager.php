<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 20:32
 */

namespace App\Services\Goods;


use App\Constants\PlatformConst;
use App\Models\Shop;
use App\Services\Goods\AbstractGoodsService;
use App\Services\Goods\impl\AlbbGoodsServiceImpl;
use App\Services\Goods\impl\Alc2mGoodsServiceImpl;
use App\Services\Goods\impl\DyGoodsServiceImpl;
use App\Services\Goods\impl\JdGoodsServiceImpl;
use App\Services\Goods\impl\KsGoodsServiceImpl;
use App\Services\Goods\impl\TaobaoGoodsSearchServiceImpl;
use App\Services\Goods\impl\TaobaoGoodsServiceImpl;
use App\Services\Goods\impl\WxGoodsServiceImpl;
use App\Services\Goods\impl\WxspGoodsServiceImpl;
use App\Services\Goods\impl\XhsGoodsServiceImpl;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Order\Impl\JdOrderImpl;
use App\Services\Order\Impl\KsOrderImpl;
use App\Services\Order\Impl\PddOrderImpl;
use App\Services\Order\Impl\TaobaoOrderImpl;
use App\Services\Order\Impl\WxOrderImpl;
use App\Utils\ArrayUtil;
use App\Utils\Environment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

/**
 * 商品服务的管理器
 */
class GoodsServiceManager
{
    protected static $initMap = [
        PlatformConst::TAOBAO => TaobaoGoodsServiceImpl::class,
        PlatformConst::KS => KsGoodsServiceImpl::class,
        PlatformConst::WX => WxGoodsServiceImpl::class,
        PlatformConst::DY => DyGoodsServiceImpl::class,
        PlatformConst::JD => JdGoodsServiceImpl::class,
        PlatformConst::WXSP => WxspGoodsServiceImpl::class,
        PlatformConst::XHS => XhsGoodsServiceImpl::class,
        PlatformConst::ALBB => AlbbGoodsServiceImpl::class,
        PlatformConst::ALC2M => Alc2MGoodsServiceImpl::class,
    ];

    /**
     * 创建一个订单server
     * @param $name
     * @return AbstractGoodsService
     * <AUTHOR>
     */
    public static function create($name = ''): AbstractGoodsService
    {
        if (empty($name)) {
            $name = config('app.platform');
        }
        if (isset(self::$initMap[$name])) {
            return new self::$initMap[$name]();
        }
        throw new InvalidArgumentException('不存在的 GoodsServiceManager:' . $name);
    }


    public static function newInstance($accessToken, $shop, $platform = ''): AbstractGoodsService
    {
        $goodsService = GoodsServiceManager::create($platform);
        $goodsService->setAccessToken($accessToken);
        $goodsService->setShop($shop);
        return $goodsService;
    }

    /**
     *
     * @param $accessToken
     * @param $shop
     * @param $platform
     * @return \App\Services\Goods\AbstractGoodsService
     */
    public static function newSearchInstance($accessToken, $shop, $platform = ''): AbstractGoodsService
    {
        $goodsService = null;
        if (Environment::isTaoBao()) {
            $goodsService = new TaobaoGoodsSearchServiceImpl();
        } else {
            $goodsService = GoodsServiceManager::create($platform);
        }
        $goodsService->setAccessToken($accessToken);
        $goodsService->setShop($shop);
        return $goodsService;

    }

    /**
     * @param GoodsDetailRequest[] $goodsDetailRequests
     * @return Collection
     */
    public static function batchGoodsInfo(array $goodsDetailRequests): Collection
    {
        $goodsCollection = new Collection();
        $goodsRequestCollection = new Collection($goodsDetailRequests);
        $goodsDetailRequestsGroupByShopId = $goodsRequestCollection->groupBy('shopId');
        foreach ($goodsDetailRequestsGroupByShopId as $shopId => $goodsDetailRequest) {
            try {
                $shop = Shop::firstById($shopId);
                $goodsService = GoodsServiceManager::newInstance($shop->access_token, $shop);
                $numIids = $goodsDetailRequest->pluck('numIid')->toArray();
                Log::info('获取商品信息', ["shopId" => $shopId, "numIid" => $numIids]);
                $goodsArray = $goodsService->getGoodsListByGoodsId($numIids);
                foreach ($goodsArray as $goods) {
                    $goodsCollection->put($goods['num_iid'], $goods);
                }

            } catch (\Throwable $e) {
                Log::error($e->getMessage(), [$e->getTraceAsString()]);
            }

        }

        return $goodsCollection;

    }
}
