<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/4/12
 * Time: 19:47
 */

namespace App\Services;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\Address;
use App\Models\QueryArea;
use App\Models\ShopExtra;
use Illuminate\Support\Collection;

class QueryAreaService
{
    /**
     * @var array|mixed
     */
    private $wpCodeDistrictList = null;
    /**
     * @var \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     */
    private $shopExtra = null;
    private $addressTempArr;

    public function getWpCodeDistrictList($shopId)
    {
        if (is_null($this->wpCodeDistrictList)) {
            $shopExtra = ShopExtra::firstByShopId($shopId);
            if (empty($shopExtra->preset_logistics_district_data) || $shopExtra->preset_logistics_district_switch == 0) {
                $this->wpCodeDistrictList = [];
                return $this->wpCodeDistrictList;
            }
            $query_area_id_list = array_column($shopExtra->preset_logistics_district_data, 'query_area_id');
            $queryAreaList = QueryArea::query()->where('shop_id', $shopId)->whereIn('id', $query_area_id_list)->get();
            if (empty($queryAreaList)) {
                $this->wpCodeDistrictList = [];
                return $this->wpCodeDistrictList;
            }
            $allDistrictArr = [];
            foreach ($queryAreaList as $queryArea) {
                $districtArr = $this->getDistrictListQueryArea($queryArea);
                $allDistrictArr = $allDistrictArr + $districtArr;
            }
            $this->wpCodeDistrictList = $allDistrictArr;
        }
        return $this->wpCodeDistrictList;
    }
    public function getUnionWpCodeByDistrictCode($shopId, $districtCode)
    {
        if (is_null($this->shopExtra)) {
            $this->shopExtra = ShopExtra::firstByShopId($shopId);
        }
        if (empty($this->shopExtra)) {
            return '';
        }
        $unionWpCode = $this->shopExtra->preset_logistics_union_wp_code;
        if ($this->shopExtra->preset_logistics_district_switch == 1) {
            $wpCodeDistrictList = $this->getWpCodeDistrictList($shopId);
            if (isset($wpCodeDistrictList[$districtCode]['union_wp_code'])) {
                $unionWpCode = $wpCodeDistrictList[$districtCode]['union_wp_code'];
            }
        }
        return $unionWpCode;
    }

    /**
     * 获取并计算区域列表
     * @return array [[name,code,union_wp_code]...]
     * <AUTHOR>
     */
    protected function getDistrictListQueryArea($queryArea): array
    {
        $include = $queryArea->include == 1;
        $union_wp_code = $queryArea->union_wp_code;
        if (empty($union_wp_code)) {
            return [];
        }
        $areaDataArr = json_decode($queryArea->data, true);

        $areaDataList = $this->formatTreeToList($areaDataArr);
        $areaDistrictArr = collect($areaDataList)->where('level', Address::LEVEL_DISTRICT)->pluck(null,'code')->toArray();
        $addressAll = Address::getAddressByCache();
        $addressDistrictArr = $addressAll->where('level', Address::LEVEL_DISTRICT)->pluck(null,'code')->toArray();
        if ($include) {
            $diffDistrictList = collect($addressDistrictArr)->intersectByKeys($areaDistrictArr)->toArray();
        }else{
            $diffDistrictList = collect($addressDistrictArr)->diffKeys($areaDistrictArr)->toArray();
        }
        $resultList = array_map(function ($item) use ($union_wp_code){
            return [
                'name' => $item['name'],
                'code' => $item['code'],
                'union_wp_code' => $union_wp_code,
            ];
        }, $diffDistrictList);
        $resultList = array_pluck($resultList, null, 'code');
        $customDistrictArr = explode(',', $queryArea->custom_district_str);
        if (!empty($customDistrictArr)) {
            $resultList = array_merge($resultList, $customDistrictArr);
        }
        return $resultList;
    }

    private  function formatTreeToList($areaDataArr,$level=1)
    {
        $list = [];
        foreach ($areaDataArr as $index => $item) {
            $item['level'] = $level;
            $list[] = $item;
            if (!empty($item['children'])) {
                $list = array_merge($list, $this->formatTreeToList($item['children'], $level + 1));
            }
        }
        return $list;
    }

    /**
     * @param string $customOriginalData
     * @return string
     * @throws BusinessException
     * <AUTHOR>
     */
    public function handleCustomData(string $customOriginalData)
    {
        if (empty($customOriginalData)) {
            return '';
        }
        $districtCodeAll = [];
        $addressAll = Address::getAddressByCache();
        // 替换"，"为","
        $customOriginalData = str_replace('，', ',', $customOriginalData);
        $dataList = explode(',', $customOriginalData);
        foreach ($dataList as $index => $item) {
            $provinceName = $cityName = $districtName = '';
            if (strpos($item, '|') !== false) {
                $arr1 = explode('|', $item);
                $provinceName = $arr1[0] ?? '';
                $cityName = $arr1[1] ?? '';
                $districtName = $arr1[2] ?? '';
            }else{
                $lastWord = mb_substr($item, -1, 1);
                $provinceName = $item;
                // 非直辖市且最后一个字是市
                if (!isAddressMunicipality($item) && $lastWord == '市') {
                    $provinceName = '';
                    $cityName = $item;
                }
            }

            if (empty($provinceName) && empty($cityName)) {
                throw new BusinessException(sprintf('匹配不到【%s】', $provinceName));
            }
            $provinceInfo = $this->findDistrict($addressAll, $provinceName, Address::LEVEL_PROVINCE);
            if (empty($provinceInfo) && empty($cityName)) {
                throw new BusinessException(sprintf('匹配不到省【%s】', $provinceName));
            }
//            \Log::info('自定义区域省份', [$provinceName,$provinceInfo]);
            // 只有省份，表示全省

            if (empty($cityName)) {
                $cityList = $addressAll->where('level', Address::LEVEL_CITY)->where('parent_code', $provinceInfo['code'])->all();
                $cityCodeArray = array_pluck($cityList,'code');

                $districtList = $addressAll->where('level', Address::LEVEL_DISTRICT)->whereIn('parent_code', $cityCodeArray)->all();
                $districtCodeArray = array_pluck($districtList,'code');
                $districtCodeAll = array_merge($districtCodeAll, $districtCodeArray);
                continue;
            }
            if (empty($provinceName)) { // 处理只有市的情况
                $provinceInfo = $this->findDistrict($addressAll, $cityName, Address::LEVEL_CITY);
                if (empty($provinceInfo)) {
                    throw new BusinessException(sprintf('匹配不到【%s】的省', $cityName));
                }
                $parentCode = $provinceInfo['parent_code'];
            }else{
                $parentCode = $provinceInfo['code'];
            }
//            \Log::info('自定义区域城市', [$cityName,$districtCodeAll]);
            $cityInfo = $this->findDistrict($addressAll, $cityName, Address::LEVEL_CITY, $parentCode);
            if (empty($cityInfo)) {
                throw new BusinessException(sprintf('匹配不到市【%s】', $provinceName.$cityName));
            }
            // 只有市，表示全市
            if (empty($districtName)) {
                $districtList = $addressAll->where('level', Address::LEVEL_DISTRICT)->where('parent_code', $cityInfo['code'])->all();
                $districtCodeArray = array_pluck($districtList,'code');
                $districtCodeAll = array_merge($districtCodeAll, $districtCodeArray);
                continue;
            }
//            \Log::info('自定义区域区县', [$districtName,$districtCodeAll]);
            $districtInfo = $this->findDistrict($addressAll, $districtName, Address::LEVEL_DISTRICT, $cityInfo['code']);
            if (empty($districtInfo)) {
                throw new BusinessException(sprintf('匹配不到区县【%s】', $districtName));
            }
            // 省市区都有
            $districtCodeAll = array_merge($districtCodeAll, [$districtInfo['code']]);
        }

        array_unique($districtCodeAll);
//        \Log::info('自定义区域', [$districtCodeAll]);
        return implode(',', $districtCodeAll);
    }

    /**
     * 查找地址
     * @param Collection $addressAll
     * @param $name
     * <AUTHOR>
     */
    protected function findDistrict(Collection $addressAll, $name, $level, $parentCode = null)
    {
        if (empty($name)) {
            return [];
        }
        $collection = $this->getAddressByLevel($addressAll, $level)->where('name', $name);
        if (!is_null($parentCode)) {
            $collection = $collection->where('parent_code', $parentCode);
        }
        $data = $collection->first();
        if (!empty($data)) {
            return $data;
        }
        // 模糊查找 暂时关闭性能太差
//        $data = $addressAll->where('level', $level)
//            ->filter(function ($item) use ($name) {
//                return strpos($item['name'], $name) !== false;
//            })->first();
        return $data;
    }

    private function getAddressByLevel(Collection $addressAll, $level)
    {
        if (empty($this->addressTempArr[$level])){
            $this->addressTempArr[$level] = $addressAll->where('level', $level);
        }
        return $this->addressTempArr[$level];
    }
}
