<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/3/15
 * Time: 15:06
 */
namespace App\Services\Export;

use App\Models\ExportTask;
use InvalidArgumentException;

class ExportTaskServiceManager
{
    protected static $initExportJobMap = [
        ExportTask::WAYBILL_HISTORY => WaybillHistoryTaskService::class,
        ExportTask::WAYBILL_HISTORY_PACKAGE => WaybillHistoryPackageTaskService::class,
        ExportTask::WAYBILL_DELIVERY => DeliveryTaskService::class,
        ExportTask::TYPE_PRINT_LOG => PrintLogTaskService::class,
        ExportTask::SHARE_WAYBILL_LIST => ShareWaybillTaskService::class,
        ExportTask::EXCEPTION_LOGISTIC => ExceptionLogisticTaskService::class,
        ExportTask::DELIVER_RECORD => DeliveryRecordTaskService::class,
        ExportTask::PLATFORM_ORDER=>PlatformOrderTaskService::class,

    ];

    public static function init($userId, $shopId, $data,$name,$exportType)
    {
        $action = $data['type'];
        if($action==ExportTask::DELIVER_RECORD||$action==ExportTask::WAYBILL_HISTORY||$action==ExportTask::WAYBILL_HISTORY_PACKAGE||$action==ExportTask::PLATFORM_ORDER){
            return new self::$initExportJobMap[$action]($userId, $shopId, $data,$name,$exportType);
        }
        if (isset(self::$initExportJobMap[$action])) {
            return new self::$initExportJobMap[$action]($userId, $shopId, $data);
        }
        throw new InvalidArgumentException('不存在的 ExportTaskServiceManager:' . $action);
    }
}
