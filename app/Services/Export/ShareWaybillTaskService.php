<?php
/**
 * Created by PhpStorm.
 * User: xu<PERSON><PERSON><PERSON>
 * Date: 2022/3/15
 * Time: 16:17
 */
namespace App\Services\Export;

use App\Models\WaybillHistory;

class ShareWaybillTaskService extends AbstractExportTaskService
{
    public function __construct($userId, $shopId, $data)
    {
        parent::__construct($userId, $shopId, $data);
    }

    public function getHead()
    {
        return ['店铺名称','快递公司', '面单号', '母单号', '取号时间', '回收时间', '运单状态'];
    }

    public function getExportData($fp)
    {
        $where = json_decode($this->data['condition'], true);
        $offset        = 0;
        $limit         = $this->limit;
        $keyword       = $where['keyword'] ?? '';
        $orderBy       = $where['order_by'] ?? '';
        $wpCode        = $where['wp_code']  ?? '';
        $condition     = $where['condition'];
        $waybillCodeArr = $where['waybillCodeArr'] ?? [];
        $shareMallName = $where['shareMallName'] ?? '';

        do {
            list($ret, $rowsFound) =WaybillHistory::shareWaybillsearch($condition, $keyword, $wpCode, $offset, $limit,
                $orderBy, $waybillCodeArr, $shareMallName);

            foreach ($ret as $index => $item) {
                $company = collect(config('express_company'))->where('wpCode', $item['wp_code'])->values()->first();
                $companyName = !empty($company) ? $company['name'] : ($item['wp_code'] ?? '');
                $list = [
                    'shop_name'        => $item['shop_name'] ?? '',
                    'wp_code'          => $companyName,
                    'waybill_code'     => $item['waybill_code'] ?? '',
                    'parent_waybill_code'    => $item['parent_waybill_code'] ?? '' ,
                    'created_at'       => $item['created_at'] ?? '',
                    'recycled_at'       => $item['recycled_at'] ?? '',
                    'waybill_status'   => $item['waybill_status'] == 0 ? '正常' : '已回收',
                ];

                $list = self::handleNumberNotFormat($list);
                fputcsv($fp, $list);
            }
            $offset += count($ret);
        } while($offset != $rowsFound);
    }
}
