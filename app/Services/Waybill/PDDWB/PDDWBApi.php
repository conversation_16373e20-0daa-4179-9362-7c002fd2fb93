<?php

namespace App\Services\Waybill\PDDWB;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Models\Order;
use App\Models\OrderTraceList;
use App\Models\Shop;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\BusinessException;
use App\Services\Client\PddClient;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\ArrayUtil;
use App\Utils\StrUtil;
use Illuminate\Support\Facades\Log;


/**
 * PDD站外电子面单服务
 */
class PDDWBApi extends AbstractWaybillService
{
    const EXCLUDE_VAS_TYPE = ['product_type'];


    /**
     * 保价服务对于的Code
     */
    const INSURE_VAS_TYPE = ["INSURE", "IN160", "IN159", "IN160", "IN159", "IN67", "VA002", "insuranceValue", "INSURE SERVICE", "ed-a-0002"];




    protected $baseUrl = 'http://gw-api.pinduoduo.com/api/router';
    protected $apiUrl = '';
    protected $dataType = 'JSON';
    protected $version = 'V1';
    private $authConfig;
    private $clientID;
    private $clientSecret;
    private $timestamp;
    protected $is_test = false;
    protected $waybillPlatformType = PlatformConst::WAYBILL_PDD_WB; // 电子面单平台类型

    public function __construct(string $accessToken = '')
    {
        $this->authConfig = config('waybill.pddwb');
        $this->clientID = $this->authConfig['client_id'];
        $this->clientSecret = $this->authConfig['client_secret'];
        $this->accessToken = $accessToken;
        $this->timestamp = time();
    }

    public function isTest()
    {
        $this->is_test = true;
    }

    //支持子母件的快递
    const ZIMUJIANMAP = [
        'BESTQJT',
        'DEBANGWULIU',
        'SFKY',
        'HOAU',
        'ANKY',
        'YDKY',
        'SXJD',
        'ZTOKY',
        'YDKY',
    ];

    //物流状态码
    const TRACE_ARR = [
        'GOT',
        'SEND',
        'SIGN',
        'ARRIVAL',
        'DEPARTURE',
        'FAIL',
        'REJECTION',
        'STAY_IN_WAREHOUSE',
        'SIGN_ON_BEHALF',
        'OTHER',
        'RETURN',
        'IN_CABINET',
        'OUT_CABINET',
    ];

    /**
     * pdd面单登录授权地址
     * @param $shopId
     * @return string
     */
    public function getLoginUrl($shopId)
    {
        $state = [
            'redirect_uri' => $this->authConfig['redirect_url'],
            'state' => $shopId,
        ];

        return $this->authConfig['code_url'] . '?response_type=code&client_id=' . $this->clientID . '&redirect_uri=' .
            urlencode($this->authConfig['code_redirect_url']) . '&state=' . base64_encode(json_encode($state));
    }

    /**
     * 获取access_token
     * @param string $code
     * @return array
     * @throws \Exception
     */
    public function getAccessToken(string $code)
    {
        $params = [
            'grant_type' => 'authorization_code',
            'client_id' => $this->clientID,
            'client_secret' => $this->clientSecret,
            'redirect_uri' => $this->authConfig['code_redirect_url'],
            'code' => $code,
            'state' => 'access_token',
        ];
        $client = new \GuzzleHttp\Client();
        $response = $client->post($this->authConfig['token_url'], [
                'json' => $params,
                'verify' => false,
                'headers' => [
                    'Content-type' => 'application/json',
                    "Accept" => "application/json"
                ],
            ]
        );
        $result = json_decode($response->getBody()->getContents(), true);
        if (!isset($result['access_token'])) {
            Log::error('pdd access_token get Failed', ['request' => $result]);
            throw new \Exception('获取授权码失败，请重试！');
        }

        return [
            'access_token' => $result['access_token'],
            'refresh_token' => $result['refresh_token'],
            'owner_id' => $result['owner_id'],
            'owner_name' => $result['owner_name'],
            'expires_in' => $result['expires_in'],
            'expires_at' => date('Y-m-d H:i:s', $result['expires_at']),
        ];
    }

    /**
     * 请求接口
     * @param $data
     * @return array|string
     */
    public function request($apiName, $data = array())
    {
        $param = $this->createRequestParam($apiName, $data);
        $response = $this->Curl($this->apiUrl, $param, 'POST');
//        $response = array (   'data' => '{"pdd_waybill_get_response":{"request_id":"15889218635055475","modules":[{"print_data":"{\\"encryptedData\\":\\"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\\",\\"signature\\":\\"l9IjyjWwVyrp7f+C+jN2nv/B1assIHHYOfPzCM8gfS0nMkBLL+Nfrd1UZo0LXsJRfRHIdTyc1lnvfw2T46ojoGUIP8TP3Bp2+hDaFmJOxvKb8kNbtN8Atu/MuZXZDJjAx1KIgH+6cqUE+CKoL7KyPYZAz1wWuzIlmK13V1pDAbg=\\",\\"templateUrl\\":\\"https://file-link.pinduoduo.com/sf_std\\",\\"ver\\":\\"3\\"}","waybill_code":"322143530760","object_id":"2012300003139696"}]}}',   'http_code' => 200, );

//	    Log::info(var_export($response, true));

        return $this->handleResponse($response);
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array $data
     * @return array
     */
    public function createRequestParam(string $apiName, array $data = array()): array
    {
        $params = [
            'type' => $apiName,
            'client_id' => $this->clientID,
            'data_type' => $this->dataType,
            'version' => $this->version,
//            'access_token' => $this->accessToken,
            'timestamp' => $this->timestamp,
        ];
        if ($accessToken = $this->accessToken) { // 无需授权的接口，该字段不参与sign签名运算
            $params['access_token'] = $accessToken;
        }
        $signature = $this->sign(array_merge($params, $data));
        $params['sign'] = $signature;
        $this->apiUrl = '';
        $this->apiUrl = $this->baseUrl . '?';
        foreach ($params as $k => $v) {
            $this->apiUrl .= "$k=" . urlencode($v) . '&';
        }
        $this->apiUrl = substr($this->apiUrl, 0, -1);
        $this->setRequestData($data);

        return $data;
    }

    /**
     * 签名
     * @param string $params
     * @return mixed
     */
    public function sign($params)
    {
        ksort($params);
//        Log::info('params', [$params]);
        $sign = $this->clientSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $this->clientSecret;

        return strtoupper(md5($sign));
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws \Exception
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        $type = 'pdd.waybill.search';
        $data = array();
        if ($wpCode) {
            $data['wp_code'] = $wpCode;
        }
        Log::info("拼多多电子面单查询请求参数", $data);
        $result = $this->request($type, $data);
        if (isset($result->error_msg)) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->waybill_apply_subscription_cols)) {
            return [];
        }
        $waybillsInfo = [];
        foreach ($result->waybill_apply_subscription_cols as $waybill_apply_subscription_col) {
            $branchAccounts = [];
            $wpType = $waybill_apply_subscription_col->wp_type;
            //1,"加盟型"2,"直营且月结账号必填"3,"直营且月结账号非必填" 4,"直营且无月结账号"
            $isJiaMeng = $wpType == 1;
            foreach ($waybill_apply_subscription_col->branch_account_cols as $key => $account_col) {
                $serviceInfoCols = [];
                foreach ($account_col->service_info_cols as $serviceInfo) {
                    $serviceAttributes = [];
                    foreach ($serviceInfo->service_attributes as $serviceDesc) {
                        $serviceAttributes[] = [
                            'attribute_name' => $serviceDesc->attribute_name,
                            'attribute_type' => $serviceDesc->attribute_type,
                            'attribute_code' => $serviceDesc->attribute_code,
                            //判断$serviceDesc->type_desc是不是json字符串，如果是json字符串就直接返回，如果不是，就变成json字符串，key是空字符串用“EMPTY_STRING”代替
//                            'type_desc'      =>  $serviceDesc->type_desc
                            'type_desc' => StrUtil::isJson($serviceDesc->type_desc) ? $serviceDesc->type_desc :
                                json_encode(["EMPTY_STRING" => $serviceDesc->type_desc])
                        ];
                    }
                    $serviceInfoCols[] = [
                        'required' => $serviceInfo->required,
                        'service_desc' => $serviceInfo->service_desc,
                        'service_name' => $serviceInfo->service_name,
                        'service_code' => $serviceInfo->service_code,
                        'service_attributes' => $serviceAttributes
                    ];
                }
                $branchAccounts[$key] = [
                    'branch_code' => $account_col->branch_code ?? 0,
                    'branch_name' => $account_col->branch_name ?? '',
                    'quantity' => $isJiaMeng ? $account_col->quantity : -1,
                    'cancel_quantity' => $account_col->cancel_quantity ?? 0,
                    'recycled_quantity' => $account_col->recycled_quantity ?? 0,
                    'allocated_quantity' => $account_col->allocated_quantity ?? 0,
                    'shipp_addresses' => $account_col->shipp_address_cols,
                    'service_info_cols' => $serviceInfoCols
                ];
            }

            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $waybill_apply_subscription_col->wp_code,
                'wp_type' => $wpType
            ];
        }

        return $waybillsInfo;
    }

    /**
     * 获取面单
     * @param     $sender
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        $type = 'pdd.waybill.get';
        $data = [];
        $errorArr = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            try {
                $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);
                foreach ($applyInfo as $index => $info) {
                    $tempIdStr = $idStr . '|' . $index;
                    $data[$tempIdStr] = [
                        'params' => $this->createRequestParam($type, $info), //只有一个包裹
                        'url' => $this->apiUrl,
                    ];
                }
            } catch (\Exception $e) {
                $errorArr[$idStr][] = $e->getMessage();
            }
        }

        $response = $this->poolCurl($data, 'POST');

        \Log::info('pdd_wb_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (isset($waybill->modules)) {
                foreach ($waybill->modules as $module) {
                    $printData = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => $printData['encryptedData'],
                        'signature'=> $printData['signature'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : '',
                        'templateUrl'=>$printData['templateUrl'],
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            } else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }
        foreach ($errorArr as $idStr => $msg) {
            $result[$idStr] = $msg;
        }

        return $result;
    }

    /**
     * 获取面单号
     * @param     $sender
     * @param     $order
     * @param     $template
     * @param int $packageNum
     * @return array|bool|string
     */
    public function waybillGet($sender, $order, $template, $packageNum = 1)
    {
        $type = 'pdd.waybill.get';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($type, $info);
                \Log::info('pdd response:' . json_encode($waybill));
                if (!isset($waybill->modules)) {
                    Log::error('API=>' . $type, [$waybill]);

                    return false;
                }
                $waybillsData = [];
                foreach ($waybill->modules as $module) {
                    $printData = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                Log::error("获取电子面单失败=>" . $type, [$e->getMessage(), $e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 组装面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        //抖音/快手平台需要转化收件人信息
        if ((in_array(config('app.platform'), [PlatformConst::DY]) || (config('app.platform') == PlatformConst::KS && ksEncryptSwitch($order['shop_id']))) &&
            empty($order['receiver_phone']) && isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
            $decryptData = [];
            $decryptField = ['tid', 'receiver_name', 'receiver_phone', 'receiver_address'];
            foreach ($decryptField as $column) {
                if ($column == 'tid') {
                    $decryptData[$column] = array_get($order, $column);
                } else {
                    //先置空收件人信息 防止解密失败取到有问题的面单
                    $order[$column] = '';
                    $decryptData[$column] = $order['order_cipher_info'][$column . '_ciphertext'];
                }
            }
            $orderService = OrderServiceManager::create();
            $orderService->setUserId($order['user_id']);
            $shop = Shop::query()->where('id', $order['shop_id'])->first();
            $orderService->setShop($shop);
            $result = $orderService->batchDecrypt($decryptData);
            \Log::info('抖音订单使用拼多多站外电子面单 解密数据：' . json_encode($result));
            if (!empty($result)) {
                foreach ($result as $key => $val) {
                    $order[$key] = $val;
                }
            }
        }
        $returnArr = [];
        $num = 1;
        //幂等性问题后面加上随机数
        $tradeOrderList = isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999);
        //取真实包裹数量
        if ($packageNum < 0) {
            $packageNum = $order['packageNum'];
        }
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile'] = $sender['mobile'];
            $senderInfo['phone'] = '';
            $senderInfo['name'] = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city'] = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town'] = '';
            $senderInfo['address']['detail'] = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //增值服务
            $tradeOrderInfoDto['logistics_services'] = '';
            if (!empty($template['service_list'])) {
                $tradeOrderInfoDto['logistics_services'] = $template['service_list'];
            }
            //订单信息
            $tradeOrderInfoDto['object_id'] = isset($order['request_id']) ? ($order['request_id'][$num] ?? $order['request_id'][1]) : $order['id'] . '_' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info'] = [
                'order_channels_type' => 'PinDuoDuo',
                'trade_order_list' => [
                    //子母件订单列表必须相同
                    in_array($template['wp_code'], self::ZIMUJIANMAP) ? $tradeOrderList :
                        (isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999))
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name'] = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[] = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id' => $num,
                'goods_description' => "",
                'items' => $items,
                'volume' => "",
                'weight' => "",
                'total_packages_count' => $packageNum,
            ];
            $tradeOrderInfoDto['recipient'] = [
                'address' => [
                    'city' => $order['receiver_city'],
                    'detail' => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town' => $order['receiver_town'],
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile' => $order['receiver_phone'],
                'name' => $order['receiver_name'],
                'phone' => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $template['template_url'];
            $tradeOrderInfoDto['user_id'] = $order['user_id'] ?? 0;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['wp_code'] = $template['wp_code'];
            $data['need_encrypt'] = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender'] = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[] = $temp;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * 取消面单
     * @param string $wpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return array|bool|mixed|string
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $wpCode, string $waybillCode, string $platformWaybillId = '')
    {
        $type = 'pdd.waybill.cancel';
        $data = array(
            'wp_code' => $wpCode,
            'waybill_code' => $waybillCode
        );
        $result = $this->request($type, $data);
        \Log::info('API=>' . $type, [$result]);
        if (!isset($result->cancel_result)) {
            if ($result->sub_msg == '面单已取消') {
                return true;
            }
            if (isset($result->sub_msg)) {
                throw new \Exception($result->sub_msg ?? "取消失败");
            }
        }

        return $result;
    }

    /**
     * 获取标准模板
     * @param string $wpCode
     * @return array
     * @throws \Exception
     */
    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $type = 'pdd.cloudprint.stdtemplates.get';
        $data = array(
            'wp_code' => $wpCode,
        );
        $result = $this->request($type, $data);
//        Log::info('result', [$result]);
        if (!isset($result->result)) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->result->datas['0'])) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception('面单服务查询失败!');
        }

        return array_column($result->result->datas['0']->standard_templates, null, 'standard_waybill_type');
    }

    /**
     * 获取标准模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return array
     * @throws \Exception
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $type = 'pdd.cloudprint.stdtemplates.get';
        $data = array(
            'wp_code' => $wpCode,
        );
        $result = $this->request($type, $data);
//        Log::info('result', [$result]);
        if (!isset($result->result)) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->result->datas['0'])) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception('面单服务查询失败!');
        }

        return array_column($result->result->datas['0']->standard_templates, null);
    }

    /**
     * 物流轨迹订阅
     * @param string $receiverPhone
     * @param string $expressCode
     * @param string $expressNo
     * @return bool|mixed
     */
    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        $type = 'pdd.logistics.isv.trace.notify.sub';
        $data = array(
            'ship_code' => $expressCode,
            'tel' => $receiverPhone,
            'track_no' => $expressNo,
        );
        $result = $this->request($type, $data);

        \Log::debug('消息订阅返回值', ['express_no' => $expressNo, 'result' => $result]);

        if (isset($result->is_success)) {
            return true;
        }
        return false;
    }

    protected function getClient()
    {
        return new PddClient(config('waybill.pddwb.client_id'), config('waybill.pddwb.client_secret'));
    }

    /**
     * 获取订单物流详情
     * @param array $waybill
     * @return mixed
     * @throws ClientException
     */
    protected function sendGetOrderTraceList(array $waybill, $order = null)
    {
        $client = $this->getClient();
        $params = [
            'company_code' => $waybill['express_code'],
            'mail_no' => $waybill['express_no'],
            'cache' => true, //是否缓存
        ];
        $response = $client->execute('pdd.logistics.ordertrace.get', $params);
        Log::info('pdd.logistics.ordertrace.get', $response);

        $result = $this->formatToOrderTrace(array_merge($waybill, [
            'trace_list' => $response['logistics_ordertrace_get_resposne']['trace_list'],
        ]));

        return $result;
    }

    /**
     * 物流数据整理
     * @param array $orderTrace
     * @return array
     */
    public function formatToOrderTrace(array $orderTrace): array
    {
        $latest = collect($orderTrace['trace_list'])->sortByDesc('time')->first();
//		$orderTraceMap = array_combine(self::TRACE_ARR, OrderTraceList::STATUS_ARR);
        $status = self::TRACE_ARR[$latest['action']] ?? OrderTraceList::STATUS_OTHER;

        return [
            "type" => $orderTrace['type'],
            'tid' => $orderTrace['tid'],
            'express_code' => $orderTrace['express_code'],
            'express_no' => $orderTrace['express_no'],
            'status' => $status,
            'action' => $latest['action'],
            'receiver_province' => $orderTrace['receiver_province'],
            'receiver_name' => $orderTrace['receiver_name'],
            'send_at' => $orderTrace['send_at'],
            'latest_updated_at' => $latest['status_time'],
            'latest_trace' => $latest['desc'],
            'trace_list' => json_encode($orderTrace['trace_list'], JSON_UNESCAPED_UNICODE),
        ];
    }

    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        //并行异步请求
        $type = 'pdd.waybill.get';
        $data = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($platform, $sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url' => $this->apiUrl,
            ];
        }

        $response = $this->poolCurl($data, 'POST');

        Log::debug('pdd_wb_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (isset($waybill->modules)) {
                foreach ($waybill->modules as $module) {
                    $module = (object)$module;
                    $printData = json_decode($module->print_data, true);
                    $insertPrint = [
                        'encryptedData' => $printData['encryptedData'],
                        'templateUrl' => $printData['templateUrl'],
                        'signature' => $printData['signature'],
                        'ver' => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            } else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    private function getWayBillRequestDataForOpenApi($platform, $sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile'] = $sender['mobile'];
            $senderInfo['phone'] = '';
            $senderInfo['name'] = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city'] = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town'] = '';
            $senderInfo['address']['detail'] = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id'] = $order['package_id'] ?? $order['tid'] ?? $order['id'] . '-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info'] = [
                'order_channels_type' => 'PinDuoDuo',
                'trade_order_list' => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name'] = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[] = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => isset($order['num']) && is_int($order['num']) ? $order['num'] : 1,
                    'name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id' => "",
                'goods_description' => "",
                'items' => $items,
                'volume' => "",
                'weight' => "",
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient'] = [
                'address' => [
                    'city' => $order['receiver_city'],
                    'detail' => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town' => "",
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile' => $order['receiver_phone'],
                'name' => $order['receiver_name'],
                'phone' => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode);
            $tradeOrderInfoDto['user_id'] = $order['user_id'] ?? 0;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['wp_code'] = $wpCode;
            $data['need_encrypt'] = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender'] = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[] = $temp;
            ++$num;
        }

//        Log::info(var_export($returnArr, true));

        return $returnArr;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        $type = 'pdd.waybill.update';
        $applyInfo = $this->getWayBillUpdateRequestData($sender, $order->toArray(), $template, $waybillCode);
        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($type, $info);
                Log::info('PDDWBApi 更新电子面单result:', [$waybill]);
                if (!isset($waybill->print_data)) {
                    Log::error('API=>' . $type, [$waybill]);
                }
                $printData = json_decode($waybill->print_data, true);
                $result = [
                    'waybill_code' => $waybill->waybill_code,
                    'print_data' => $printData['encryptedData'],
                ];
            } catch (\Exception $e) {
                Log::error("更新电子面单失败=>" . $type, [$e->getTraceAsString()]);
            }
        }
        return $result;
    }

    /**
     * 组装面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillUpdateRequestData($sender, $order, $template, $waybillCode)
    {
        $returnArr = [];
        //发件人信息
        $senderInfo['mobile'] = $sender['mobile'];
        $senderInfo['phone'] = '';
        $senderInfo['name'] = $sender['sender_name'];
        $senderInfo['address']['province'] = $sender['province'];
        $senderInfo['address']['city'] = $sender['city'];
        $senderInfo['address']['district'] = $sender['district'];
        $senderInfo['address']['town'] = '';
        $senderInfo['address']['detail'] = $sender['address'];
        $items = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['count'] = $good['goods_num'];
                $temp['name'] = $good['goods_title'] ? $good['goods_title'] : '';
                $items[] = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $items[] = [
                'count' => is_int($order['num']) ? $order['num'] : 1,
                'name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
            ];
        }
        $packageInfo = [
            'items' => $items,
            'volume' => 1,
            'weight' => 1,
        ];
        $recipient = [
            'address' => [
                'city' => $order['receiver_city'],
                'detail' => $order['receiver_address'],
                'district' => $order['receiver_district'],
                'town' => "",
                'province' => $order['receiver_state'] ?? $order['receiver_province'],
            ],
            'mobile' => $order['receiver_phone'],
            'name' => $order['receiver_name'],
            'phone' => $order['receiver_tel'] ?? '',
        ];
        $tradeOrderInfoDto['user_id'] = $order['user_id'] ?? 0;//userId字段目前无意义，随便传个数字即可
        $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
        //设置主体信息
        $data = [];
        $data['wp_code'] = $template['wp_code'];
        $data['need_encrypt'] = true;
        $data['recipient'] = $recipient;
        $data['sender'] = $senderInfo;
        $data['package_info'] = $packageInfo;
        $data['waybill_code'] = $waybillCode;
        $data['template_url'] = $template['template_url'];
        $temp = [];
        $temp['param_waybill_cloud_print_update_request'] = json_encode($data);
        $returnArr[] = $temp;
        return $returnArr;
    }

    /**
     * 构建增值服务
     * @param string|null $serviceListStr
     * @param string $insureAmount
     * @return string
     */

    static function buildOrderVasList(?string $serviceListStr, string $insureAmount ): string
    {
        $orderVasList = [];
        if(empty($serviceListStr)){
            return "";
        }
        $serviceList = json_decode($serviceListStr, true);
        foreach ($serviceList as $key => $item) {
            //排除掉一些特殊的增值服务
            if(in_array($key, self::EXCLUDE_VAS_TYPE)){
                continue;
            }
            $value=ArrayUtil::getArrayValue($item,"value");
            $desc=get_array_value($item,"desc",null);
            //保价有两种
            if ( in_array( $key, self::INSURE_VAS_TYPE)) {
                \Log::info("处理保价",["insureAmount"=>$insureAmount,"item"=>$item]);
                //对保价金额进行处理，如果是-1，就是按订单金额保价，除此以外按设定金额保价
                if($value!=-1){
                    \Log::info("保价金额不是-1,按设定的金额处理",["item"=>$item]);

                }else{
                    if(bccomp($insureAmount,"0")>0) {
                        \Log::info("保价金额是-1,订单金额>0 按订单金额处理",["insureAmount"=>$insureAmount]);
                        $value = round_bcmul($insureAmount, "100",0); //  (string)($insureAmount * 100);
                    }else{
                        \Log::info("保价金额是-1,订单金额=0 不处理这个保价",["insureAmount"=>$insureAmount]);
                        continue;
                    }
                }

            }


            $orderVasList[$key]= ["value"=>strval($value)];
        }
        if(empty($orderVasList)) {
            $vasOrderListString = "";
        }else {
            $vasOrderListString = json_encode($orderVasList, JSON_UNESCAPED_UNICODE);
        }
        \Log::info("处理增值服务",[$vasOrderListString]);
        return $vasOrderListString;
    }



    /**
     * 获取电子面单请求数据
     * @param SenderAddressBo $sender
     * @param PrintPackBo $printPackBo
     * @param $template
     * @param int $packageNum
     * @return array
     * <AUTHOR>
     */
    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $sender, PrintPackBo $printPackBo, $template, int $packageNum): array
    {


        $list = [];
        //发件人信息
        $senderInfo = [
            'address' => [
                'province' => $sender->province,
                'city' => $sender->city,
                'district' => $sender->district,
                'town' => $sender->street ?? '',
                'detail' => $sender->address,
            ],
            'name' => $sender->sender_name,
            'mobile' => $sender->mobile,
            'phone' => ''

        ];
        $order_infos = [];
        $isDYOrder = true;
        $total_pack_count = 1;
        //子母件 目前支持shunfeng、jd、debangwuliu、shunfengkuaiyun、annengwuliu
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
//            if (!in_array($template['wp_code'], self::ZIMUJIANMAP)){
//                throw new BusinessException('该快递公司不支持子母件');
//            }
            $total_pack_count = $packageNum;
        }


        $items = [];
        $orderInfo = [];
        $isPlatformOrder = $printPackBo->isPlatformOrder();
        if ($isPlatformOrder) {
            $orderInfo['order_channels_type'] = 'PDD';
        } else {
            $orderInfo['order_channels_type'] = 'OTHERS';
        }
        $orderInfo['trade_order_list'] = [$printPackBo->request_id . ''];
        $tradeOrderInfoDto = [];
        $tradeOrderInfoDto['object_id'] = $printPackBo->request_id . '';
        $packageInfo = [];

        foreach ($printPackBo->print_order_item_bo_list as $printOrderItemBo) {
            $goodsName = $printOrderItemBo->order_item_info['sku_value'];
            if (empty($goodsName)) {
                $goodsName = $printOrderItemBo->order_item_info['goods_title'];
            } else {
                $goodsName = '货物';
            }
            $items[] = [
                'name' => $goodsName,
                'count' => $printOrderItemBo->num,
            ];
        }
        $packageInfo['items'] = $items;
        $order = $printPackBo->master_order_info;
        $tradeOrderInfoDto['order_info'] = $orderInfo;
        $tradeOrderInfoDto['package_info'] = $packageInfo;
        //增值服务
        $tradeOrderInfoDto['logistics_services'] = '';
        $serviceListStr = $template['service_list'];
        if (!empty($serviceListStr)) {
            $tradeOrderInfoDto['logistics_services'] =self::buildOrderVasList($serviceListStr,$printPackBo->getInsureAmount());
        }
        $order = $order->toArray();
        $tid = null;
        if (empty($order['tid'])) { // 自由打印
            if (!empty($order['order_no'])) {
                $tid = $order['order_no'];
            } else {
                $tid = $order['id'];
            }
        } else {
            $tid = $order['tid'];
        }

        $recipient = [
            'address' => [
                'city' => $order['receiver_city'],
                'detail' => $order['receiver_address'],
                'district' => $order['receiver_district'],
//                'town' => $order['receiver_town'],
                'province' => $order['receiver_state'] ?? $order['receiver_province'],
            ],
            'mobile' => $order['receiver_phone'],
            'name' => $order['receiver_name'],
            'phone' => $order['receiver_tel'] ?? '',
        ];
        $tradeOrderInfoDto['recipient'] = $recipient;
        $tradeOrderInfoDto['template_url'] = $template['template_url'];
        $tradeOrderInfoDto['user_id'] = $printPackBo->getUserId();//userId字段目前无意义，随便传个数字即可

        $requestItem = [
            "sender" => $senderInfo,
            "trade_order_info_dtos" => [$tradeOrderInfoDto],
            'wp_code' => $template['wp_code'],
        ];

        $list['param_waybill_cloud_print_apply_new_request'] = json_encode($requestItem);

        return $list;
    }

    /**
     * 请求电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return PrintDataPackBo[]
     * @throws BusinessException
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
//        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);

        $type = 'pdd.waybill.get';
        $data = [];
        foreach ($printPackBoList as $index => $printPackBo) {
            $requestItem = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBo, $template, $packageNum);
            $data[$index] = [
                'params' => $this->createRequestParam($type, $requestItem), //只有一个包裹
                'url' => $this->apiUrl,
            ];
        }
//        $errorArr = [];
//        foreach ($printPackBoList as $order) {
//            $idStr     = handleOrderIdStr($order);
//            try {
//                $applyInfo = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo,$printPackBoList, $template, $packageNum);
//                foreach ($applyInfo as $index=>$info) {
//                    $tempIdStr = $idStr .'|'. $index;
//                    $data[$tempIdStr] = [
//                        'params' => $this->createRequestParam($type, $info), //只有一个包裹
//                        'url'    => $this->apiUrl,
//                    ];
//                }
//            }catch (\Exception $e){
//                $errorArr[$idStr][] = $e->getMessage();
//            }
//        }

        $response = $this->poolCurl($data, 'POST');
        $waybillsDatas = [];
        $waybillErrors = [];
        //把所有取号结果都提取出来
        foreach ($response as $index => $waybill) {
            \Log::info('waybill', [$waybill]);
            if (isset($waybill->modules)) {
                foreach ($waybill->modules as $module) {
                    $printData = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => $printData['encryptedData'],
                        'signature'=> $printData['signature'],
                        'templateUrl'=>$printData['templateUrl'],
                        'version'=>$printData['ver'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                    $waybillsDatas[] = $waybillsData;
                }

            } else {
                $errMsg = $waybill->error_response->error_msg;
                $errCode = $waybill->error_resonse->eror_code;
                $waybillErrors[$index] = [$errCode, $errMsg];
            }
        }
        //按请求ID，其实就是打印的入参请求ID进行分组
        $waybillDataGroupByRequestId = ArrayUtil::array_group_by($waybillsDatas, 'object_id');


        \Log::info('pdd_wb_response', [$response]);
        //准备打印内容
        $printDataPackBoList = [];
        foreach ($printPackBoList as $index => $printPackBo) {

            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (!isset($waybillDataGroupByRequestId[$printPackBo->request_id])) {
                $printDataPackBo->setError(ErrorConst::PLATFORM_ERROR, $waybillErrors[$index]);
                continue;
            }
            $waybills = $waybillDataGroupByRequestId[$printPackBo->request_id];
            $waybill = $waybills[0];

            if (sizeof($waybills) == 1) {
                //费子母件的情况
                $waybillCode = $waybill['waybill_code'];
                $printDataPackBo->waybill_code = $waybillCode;
                $waybillsPrintData = new WaybillsPrintDataBo();
                $waybillsPrintData->package_id = $printDataPackBo->package_id;
                $waybillsPrintData->encrypted_data = $waybill['print_data'];
                $waybillsPrintData->sign=$waybill['signature'];
                $waybillsPrintData->waybill_code = $waybillCode;
                $waybillsPrintData->version = $waybill['version'];
                $waybillsPrintData->userId=$printPackBo->getUserId();
                $printDataPackBo->setWaybillsPrintData($waybillsPrintData);
            } else {

                //子母件的情况
                $parent_waybill_code = $waybill['parent_waybill_code'];
                $waybillCode = $waybill['waybill_code'];
                $printDataPackBo->waybill_code = $parent_waybill_code;
                $waybillsPrintData = new WaybillsPrintDataBo();
                $waybillsPrintData->package_id = $printDataPackBo->package_id;
                $waybillsPrintData->encrypted_data = $waybill['print_data'];
                $waybillsPrintData->waybill_code = $waybillCode;
                $waybillsPrintData->sign=$waybill['signature'];
                $waybillsPrintData->version=$waybill['version'];
                $waybillsPrintData->userId=$printPackBo->getUserId();
                $printDataPackBo->setWaybillsPrintData($waybillsPrintData);
                $subWaybills = [];
                for ($i = 1; $i < sizeof($waybills); $i++) {
                    $subWaybill = $waybills[$i];
                    $subWaybillsPrintData = new WaybillsPrintDataBo();
                    $subWaybillsPrintData->package_id = $printDataPackBo->package_id;
                    $subWaybillsPrintData->encrypted_data = $subWaybill['print_data'];
                    $subWaybillsPrintData->sign = $subWaybill['signature'];
                    $subWaybillsPrintData->waybill_code = $subWaybill['waybill_code'];
                    $subWaybillsPrintData->version=$subWaybill['version'];
                    $subWaybillsPrintData->userId=$printPackBo->getUserId();
                    $subWaybillsPrintData->parent_waybill_code = $parent_waybill_code;
                    $subWaybills[] = $subWaybillsPrintData;
                }
                $printDataPackBo->setSubWaybillsPrintDataArr($subWaybills);

            }
            $printDataPackBoList[] = $printDataPackBo;

        }

        return $printDataPackBoList;
    }

    /**
     * @inheritDoc
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        throw new ApiException(ErrorConst::WAYBILL_PDD_NOT_SUPPORT);
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}
