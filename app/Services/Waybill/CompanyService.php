<?php

namespace App\Services\Waybill;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Template\Request\CompanyUpdateRequest;
use App\Http\StatusCode\StatusCode;
use App\Models\Company;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Waybill;
use App\Services\BusinessException;
use App\Utils\Environment;
use App\Utils\WaybillUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 获取电子面单账号信息
 */
class CompanyService
{
    /**
     * 获取网点列表
     * @param int $shopId
     * @return Collection<Company>
     */
    public function findManyByShopId(int $shopId): Collection
    {
        return Company::query()
            ->where('shop_id', $shopId)
            ->get();
    }

    /**
     * 邀请关联店铺公司列表
     * @param int $userId
     * @param int $shopId
     * @param null|string[] $with
     * @return array
     */
    public static function getRelationCompanies(int $userId, int $shopId, ?array $with = ['templates', 'user']): array
    {
        $shopList = ShopBind::getAllRelationShop($shopId);
        $shopIds = collect($shopList)->pluck('id')->toArray();
        Log::info('获取关联店铺列表', ['shopIds' => $shopIds]);
        //所有关联店铺公司
        $query = Company::query();
        if (!empty($with)) {
            $query->with($with);
        }
        $companies = $query
//            ->whereIn('user_id', $userIds)
            ->whereIn('shop_id', $shopIds)
            ->get();
        //先把自己的公司加进去
        $myCompanies = collect($companies)->where('shop_id', $shopId)->values()->all();

        foreach ($companies as $company) {
//            Log::info('公司信息', ['company' => $company]);
            $count = collect($myCompanies)->where('id', $company->id)
                ->count();

            //如果不存在就加进去
            if ($count == 0) {
                array_push($myCompanies, $company);
            }else{
//                Log::info('公司信息重复', ['company' => $company]);
            }
            //            $sample          = collect(config('express_company'))->where('wpCode', $company->wp_code)->first();
            //            $company['logo'] = $sample['logo'];
        }

        return $myCompanies;
    }

    /**
     * 修改Company的信息。
     * 先用CompanyUpdateRequest去匹配
     * @param CompanyUpdateRequest $companyUpdateRequest
     * @return void
     */

    public function updateCompany(CompanyUpdateRequest $companyUpdateRequest)
    {


    }

    /** 生成物流账号
     * @param CompanyUpdateRequest $companyUpdateRequest
     * @return ?Company
     * @throws ApiException
     * @throws BusinessException
     * @throws ErrorCodeException
     */


    public function generate(CompanyUpdateRequest $companyUpdateRequest): ?Company
    {

        $historyCompanyId = $companyUpdateRequest->companyId;
        if ($historyCompanyId) {
            $historyCompany = Company::find($historyCompanyId);
            if (!$historyCompany) {
                throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '没有匹配到网点信息');
            }
        }

        $province = $companyUpdateRequest->province;
        $city = $companyUpdateRequest->city;
        $district = $companyUpdateRequest->district;
        $detail = $companyUpdateRequest->detail;
        $addrArr = [
            'province' => $province,
            'city' => $city,
            'district' => $district,
            'detail' => $detail
        ];
        $userId = $companyUpdateRequest->userId;
        $ownerName = $companyUpdateRequest->ownerName;
        $shopId = $companyUpdateRequest->shopId;
        $ownerId = $companyUpdateRequest->ownerId;
        $authSource = $companyUpdateRequest->authSource;
        $wpCode = $companyUpdateRequest->wpCode;
        $platformAccountId = $companyUpdateRequest->platformAccountId;
        $platformShopId = $companyUpdateRequest->platformShopId;
        $companyCondition = array_merge([
            'user_id' => $companyUpdateRequest->userId,
            'shop_id' => $shopId,
            'wp_code' => $wpCode,
            'owner_id' => $ownerId,
            'auth_source' => $authSource,
        ], $addrArr);
        $branchCode = $companyUpdateRequest->branchCode;
        if ($branchCode) {
            $companyCondition['branch_code'] = $branchCode;
        }


        $express = Company::where($companyCondition)->first();
        //如果存在，直接返回
        if ($express) {
            Log::info('匹配已有的网点信息', ["id" => $express->id]);
            $settlementCode = $companyUpdateRequest->settlementCode;
            if(!empty($settlementCode)){
                //更新结算账号
                Log::info('更新结算账号', ["id" => $express->id, "settlementCode" => $settlementCode]);
                $express->settlement_code = $settlementCode;
                $express->save();
            }
            $brandCode = $companyUpdateRequest->brandCode;
            if(!empty($brandCode)){
                //更新品牌代码
                Log::info('更新品牌代码', ["id" => $express->id, "brandCode" => $brandCode]);
                $express->brand_code = $brandCode;
                $express->save();
            }

            return $express;
        }
        $auth = WaybillUtil::findShopWaybillAuth($shopId, $authSource, $ownerId);
        //pdd站内
        if ($authSource == Waybill::AUTH_SOURCE_PDD || $authSource == Waybill::AUTH_SOURCE_TAOBAO) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);

        } else if ($authSource == Waybill::AUTH_SOURCE_DY) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else if ($authSource == Waybill::AUTH_SOURCE_KS) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else if ($authSource == Waybill::AUTH_SOURCE_JD) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else if ($authSource == Waybill::AUTH_SOURCE_WXSP) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillService->setShop($auth);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else if ($authSource == Waybill::AUTH_SOURCE_XHS) {
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            $waybillService = WaybillServiceManager::init($authSource, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        } else {
            $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode);
            $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);
        }
        Log::info('waybill', [$waybill]);
        if (empty($waybill)) {
            throw new BusinessException('未开通该公司电子面单服务!');
        }

        $expressCompany = collect(config('express_company'))->where('wpCode', $wpCode)->first();
        if (!$expressCompany) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR, null, "没有匹配到物流公司");
        }
        //如果有网点就需要根据网点信息Code进行匹配
        if ($branchCode) {

            foreach ($waybill as $accountCol) {
                foreach ($accountCol['branch_account_cols'] as $branch) {
                    if ($branch['branch_code'] == $branchCode) {
                        $express = Company::query()->create(array_merge([
                            'user_id' => $userId,
                            'shop_id' => $shopId,
                            'auth_source' => $authSource,
                            'wp_code' => $wpCode,
                            'owner_id' => $ownerId,
                            'owner_name' => $ownerName,
                            'branch_name' => $branch['branch_name'],
                            'branch_code' => $branch['branch_code'],
                            'wp_name' => array_get($expressCompany, 'name', ''),
                            'status' => Company::EXPRESS_COMPANY_STATUS_OPEN,
                            'quantity' => $branch['quantity'],
                            'cancel_quantity' => $branch['cancel_quantity'],
                            'recycled_quantity' => $branch['recycled_quantity'],
                            'allocated_quantity' => $branch['allocated_quantity'],
                            'templates' => json_encode($waybillTemp),
                            'platform_account_id' => $platformAccountId,
                            'platform_shop_id' => $platformShopId,
                        ], $addrArr));
                        if (!$express) {
                            Log::error('express_company add failed !',
                                ['wp_code' => $wpCode, 'user_id' => $userId]);
                            throw new BusinessException('快递公司添加失败！');
                        }
                    }
                }
            }
            Log::info('匹配网点', ["branchCode" => $branchCode, "express" => $express]);
        } else {
            //直营快递公司

            $branchAccountCols = $waybill[0]['branch_account_cols'];
            $branchAccountCol = null;
            /**
             * @var array{branch_code:string,
             *     branch_name:string,
             *     quantity:int,
             *     cancel_quantity:int,
             *     recycled_quantity:int,
             *     allocated_quantity:int,
             *     shipp_addresses:array{
             *      city:string,
             *      detail:string,
             *      province:string,
             *      district:string,
             *      street:string,
             *      street_name:string
             *     }
             *     } $accountCol
             */
            foreach ($branchAccountCols as $accountCol) {
                foreach ($accountCol['shipp_addresses'] as $addr) {
                    if (array_get($addr, 'city') == $city && array_get($addr, 'detail') == $detail && array_get($addr, 'province') == $province && array_get($addr, 'district') == $district) {
                        $branchAccountCol = $accountCol;
                        break 2;
                    }
                }
            }
            if (!$branchAccountCol) {
                throw new BusinessException('没有匹配到网点');
            }
            Log::info('匹配到直营网点', ["branchAccountCol" => $branchAccountCol]);
//            foreach ()
            $express = Company::create(array_merge([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'auth_source' => $authSource,
                'wp_code' => $wpCode,
                'owner_id' => $ownerId,
                'owner_name' => $ownerName,
                'branch_name' => $branchAccountCol['branch_name'],
                'branch_code' => $branchAccountCol['branch_code'],
                'wp_name' => array_get($expressCompany, 'name', ''),
                'status' => Company::EXPRESS_COMPANY_STATUS_OPEN,
                'quantity' => $branchAccountCol['quantity'],
                'cancel_quantity' => $branchAccountCol['cancel_quantity'],
                'recycled_quantity' => $branchAccountCol['recycled_quantity'],
                'allocated_quantity' => $branchAccountCol['allocated_quantity'],
                'templates' => json_encode($waybillTemp),
                'settlement_code' => $branchAccountCol['settlement_code'] ?? "",
                'platform_account_id' => $platformAccountId,
                'platform_shop_id' => $platformShopId,
            ], $addrArr));
            if (!$express) {
                Log::error('express_company add failed !', ['wp_code' => $wpCode, 'user_id' => $userId]);
                throw new BusinessException('快递公司添加失败！');
            }
        }


        return $express;
    }


    /**
     * 查询分享的网点
     * @return Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public function queryShareCompanies(?int $shopId):Collection{
        $query = Company::query()->where('source', Company::SOURCE_COMPANY_STATUS_CLOSED);
        if ($shopId) {
            $query->where('shop_id', $shopId);
        }
        return $query->get();

    }



}
