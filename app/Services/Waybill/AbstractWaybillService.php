<?php

namespace App\Services\Waybill;

use App\Constants\PlatformConst;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\Company;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use App\Models\Shop;
use App\Models\Waybill;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\BusinessException;
use App\Services\Order\OrderServiceManager;
use GuzzleHttp\Client;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

abstract class AbstractWaybillService
{
    protected $accessToken;

    /**
     * @var Shop $shop
     */
    protected $shop;

    /**
     * @param mixed $shop
     */
    public function setShop($shop): void
    {
        $this->shop = $shop;
    }

    protected $dataType = 'JSON';
    protected $concurrency = 50;  //并发数
    protected $clientTimeout = 10; // 客户端超时 时间 秒
    protected $clientConnectTimeout = 3; // 客户端连接超时
    protected $waybillPlatformType = 0; // 电子面单平台类型 PlatformConst::WAYBILL_
    private $requestData = []; // 请求的数据
    /**
     * curl 并发池异常输出原始数据
     * @var bool
     */
    protected $poolCurlAbnormalOutputOriginalData = false;

    protected $errorCodeMap = [
        '10019' => '电子面单需重新授权',
        '27' => '电子面单需重新授权',
    ];

    protected function __construct(string $accessToken = '')
    {
        $this->accessToken = $accessToken;
    }

    /**
     * 获取平台所有的快递公司
     * @param string $wpCode
     * @return array
     */
    abstract public function getAllCompany(string $wpCode = ''): array;

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return mixed
     */
    abstract public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array;

    /**
     * 单个订单获取面单
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return mixed
     */
    abstract public function waybillGet($sender, $order, $template, $packageNum);

    /**
     * 多个订单获取面单
     * @param $sender
     * @param $orders
     * @param $template
     * @param $packageNum
     * @return mixed
     */
    abstract public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1);

    /**
     * 多个订单获取面单
     * @param $company
     * @param $sender
     * @param $orders
     * @param $template
     * @param int $packageNum
     * @return void
     * @throws ApiException
     */
    public function assemCompanyWaybillPackages($company, $sender, $orders, $template, $packageNum = 1)
    {
        $packages = [];
        //每次取号判断是否是虚拟网点，且正常状态为0，面单余额不为0
        if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
//                    if($ordersCount > $company['quantity'] && $company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY){
//                        throw new ApiException(ErrorConst::WAYBILL_INSUFFICIENT_BALANCE);
//                      /  throw new \Exception('打印订单数大于可用单号余额!');
//                    }
            if ($company['source_status'] == Company::SOURCE_COMPANY_STATUS_OPEN && $company['quantity'] !== Company::INIT_QUANTITY) {
                $packages = $this->assemWaybillPackages($sender, $orders, $template, $packageNum);
                //单号余额非无限量，取号后，电子面单余额数量减少，已用面单数量增加
//                        if($company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY){
//                            $query = Company::where('id',$company['id']);
//                            $query->decrement('quantity');
//                            $query->increment('allocated_quantity');
//                        }else{
//                            //无限量的不用减少电子面单余额数量，已用面单数量增加
//                            Company::incrementAllocatedQuantity($company['id']);
//                        }
            } else {
                throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
//                        throw new \Exception('电子面单余额为0，请联系您的单号分享者!');
            }
        } else {
            //非虚拟网点，正常取号
            $packages = $this->assemWaybillPackages($sender, $orders, $template, $packageNum);
        }
        return $packages;

    }

    /**
     * 作废面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return mixed
     */
    abstract public function wayBillCancelDiscard(string $cpCode, string $waybillCode, string $platformWaybillId = '');

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @return mixed
     */
    abstract public function getCloudPrintStdTemplates(string $wpCode = '');

    /**
     * 商家自定义的电子面单模板，默认不提供，只有微信视频号提供
     * @param string $wpCode
     * @return PlatformTemplate[]
     */
    public function getCustomizeWaybillTemplates(string $wpCode = ''): array
    {
        Log::info('getCustomizeWaybillTemplates');
        return [];
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return mixed
     */
    abstract public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null);

    /**
     * auth link
     * @param $shopId
     * @return mixed
     */
    abstract public function getLoginUrl($shopId);

    /**
     * access token
     * @param string $code
     * @return mixed
     */
    abstract public function getAccessToken(string $code);

    /**
     * 订阅物流轨迹
     * @param string $receiverPhone
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    abstract public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo);

    /**
     * 组合物流订阅数据
     * @param Order $order
     * @return true
     */
    public function sendLogisticsTraceMsg(Order $order): bool
    {
        //手机号解密
//        if (config('app.platform') == 'dy') {
//            $orderCipherInfo = OrderCipherInfo::query()->where('order_id', $order->id)->first();
//            if (!empty($orderCipherInfo)) {
//                $decryptData['tid'] = $order->tid;
//                $decryptData['receiver_phone'] = $orderCipherInfo->receiver_phone_ciphertext;
//                $orderService = OrderServiceManager::create();
//                $orderService->setUserId($order['user_id']);
//                $shop = Shop::query()->where('id', $order->shop_id)->first();
//                $orderService->setShop($shop);
//                try {
//                    $result = $orderService->batchDecrypt($decryptData);
//                }catch (\Exception $e){
//                    return true;
//                }
//                //解密失败就不订阅了
//                if (empty($result)) {
//                    return true;
//                }
//                $order->receiver_phone = $result['receiver_phone'];
//            }
//        }
        foreach ($order->packages as $package) {
            try {
                if ($package->wp_code && $package->waybill_code) {
                    $this->sendOrderLogisticsTraceMsg($order->receiver_phone, $package->wp_code, $package->waybill_code);
                }
            } catch (\Exception $e) {
                return true;
            }
        }

        return true;
    }

    /**
     * 获取订单物流
     * @param array $waybill [
     *                     'express_code'   => 'SF',
     *                     'express_no'     => '1222121',
     *                     'tid'            => '202293020932',
     *                     'receiver_province' => '',
     *                     'receiver_name' => '',
     *                     'send_at' => '',
     *                     ]
     * @return mixed
     */
    public function getOrderTraceList(array $waybill)
    {
        return $this->sendGetOrderTraceList($waybill);
    }


    /**
     * 发送获取物流轨迹信息
     * @param array $waybill
     * @return mixed
     */
    abstract protected function sendGetOrderTraceList(array $waybill);

    /**
     * 并行异步请求面单使用
     * @param string $params
     * @param string $method
     * @param bool $isCNLink
     * @param bool $isDY
     * @return array
     */
    public function poolCurl($params = '', $method = 'GET', $isCNLink = false, $isDY = false)
    {
        $headers = array();
        $keyArr = array_keys($params);
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'form_params';
                $headers = [
                    'Content-Type' => 'application/x-www-form-urlencoded'
                ];
                break;
            case 'json':
                $method = 'post';
                $postKey = 'json';
                break;
            case 'post_form':
                $method = 'post';
                $postKey = 'form_params';
                break;
            default:
                $postKey = 'json';
                break;
        }

        $client = new Client([
            'timeout' => $this->clientTimeout, //秒
            'connect_timeout' => $this->clientConnectTimeout, //秒
        ]);
        $requests = function ($data) use ($client, $method, $headers, $postKey, $isCNLink) {
            foreach ($data as $index => $datum) {
                yield function () use ($client, $method, $postKey, $headers, $datum, $isCNLink) {
                    //菜鸟请求
                    if ($isCNLink) {
                        $datum['url'] = $datum['url'] . '?' . http_build_query($datum['params']);
                    }
                    if (PlatformConst::WAYBILL_DY == $this->waybillPlatformType && 'json' == $postKey) {
                        $param_json = $datum['params']['param_json'];
                        unset($datum['params']['param_json']);
                        $datum['url'] = $datum['url'] . '?' . http_build_query($datum['params']);
                        $datum['params'] = json_decode($param_json, true);
                    }

//					Log::info('poolCurl $datum', [$datum]);
                    return $client->requestAsync($method, $datum['url'], [
                        $postKey => $datum['params'],
                        'headers' => $headers
                    ]);
                };
            }
        };

        $result = [];
        $pool = new Pool($client, $requests($params), [
            'concurrency' => $this->concurrency, //并发数
//            'options' => [
//                'connect_timeout' => 15, // 超时时间 秒
//            ],
            'fulfilled' => function (Response $response, $index) use (&$result, $params, $keyArr, $isDY) {

                $orderIdStr = $keyArr[$index];
                $res = [
                    'http_code' => $response->getStatusCode(), // 200
                    'reason' => $response->getReasonPhrase(), // OK
                    'data' => $response->getBody()->getContents()
                ];

                Log::info('run=>' . $orderIdStr, [$params[$orderIdStr], $res]);
                $content = $this->handleResponse($res, true, $isDY);
                $result[$orderIdStr] = [
                    'sort' => $index,
                    'orderIdStr' => $orderIdStr,
                    'content' => $content,
                ];
                return $result;
            },
            'rejected' => function (RequestException $e, $index) use (&$result, $keyArr) {
                $orderIdStr = $keyArr[$index];
                $message = '系统繁忙，请稍后重试！附加信息：' . $e->getMessage();
                $errorCode = $e->getHandlerContext()['errno'] ?? 0;
                if ($errorCode == 28) {
                    $message = '请求平台接口超时，请稍后重试！';
                }
                $result[$orderIdStr] = [
                    'sort' => $index,
                    'orderIdStr' => $orderIdStr,
                    'content' => $message,
                ];
                Log::error('poolCurl rejected', [$e->getMessage(), $result[$orderIdStr], $e->getTrace()]);
                return $result;
//                throw new \Exception($e->getMessage());
            },
        ]);

        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();
        Log::info('result', [$result]);
        array_multisort(array_column($result, 'sort'), SORT_ASC, $result);
        $result = Arr::pluck($result, 'content', 'orderIdStr');

        return $result;
    }

    /**
     * 数据格式转换
     * @param array $response
     * @param bool $isPoolCurl
     * @param bool $isDY
     * @return bool|mixed|null
     * @throws \Exception
     */
    public function handleResponse(array $response, bool $isPoolCurl = false, bool $isDY = false)
    {
        if ($response['http_code'] != 200) {
            return false;
        }
        $result = null;
        if ($this->dataType == 'JSON') {
            $response_ = preg_replace('/id":(\\d{11,})/', 'id":"\\1"', $response['data']);
            $respObject = json_decode($response_);
            if (!$isDY) {
                if (null !== $respObject) {
                    foreach ($respObject as $propKey => $propValue) {
                        if ($propKey == 'request_id')
                            continue;
                        $respObject = $propValue;
                    }
                }
                //记录错误日志
                if (isset($respObject->code)) {
                    Log::error('waybill api请求错误====', ['request' => $this->requestData, 'response' => $response]);
                }
            }
            $result = $respObject;

            if ($this->poolCurlAbnormalOutputOriginalData) {
                return json_decode(json_encode($result), true);
            }
            //抛出异常
            if ((isset($result->sub_code) && $result->sub_code) && (isset($result->sub_msg) && $result->sub_msg && $result->sub_msg != "面单已取消")) {
                //自定义错误提示
                Log::error('接口异常完整数据:' . json_encode($result));
                //自定义错误提示
                $errorMsg = array_get($this->errorCodeMap, $result->sub_code, false);
                if ($errorMsg) {
                    throw new \Exception($errorMsg);
                }
                //异步池请求，防止进程退出
                if ($isPoolCurl) {
                    return $result->sub_msg;
                } else {
                    throw new \Exception($result->sub_msg);
                }
            }
            return $result;
        }

        return $response['data'];
    }

    /**
     * Curl  异步独立请求
     * @param        $url
     * @param array $vars
     * @param string $method
     * @param array $headers
     * @param bool $debug
     * @return mixed
     */
    public function Curl($url, $vars = [], $method = 'GET', $isCNLink = false)
    {
        $headers = array();
        $client = new Client();
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'form_params';
                $headers = [
                    'Content-type' => 'application/x-www-form-urlencoded',
                ];
                break;
            default:
                $postKey = 'json';
                break;
        }
        //菜鸟请求
        if ($isCNLink) {
            $url = $url . '?' . http_build_query($vars);
        }

        $promise = $client->requestAsync($method, $url, [
            $postKey => $vars,
            'headers' => $headers
        ]);

        $result = [];
        $onFulfilled = function (ResponseInterface $response) use (&$result) {
            $result = [
                'http_code' => $response->getStatusCode(), // 200
                'reason' => $response->getReasonPhrase(), // OK
                'data' => $response->getBody()->getContents()
            ];

//			Log::info('result', [$result]);
            return $result;
        };

        $onRejected = function (RequestException $e) {
            throw new \Exception($e->getMessage());
            Log::error(var_export($e->getRequest()->getMethod(), true));
        };

        $promise->then(
            $onFulfilled,
            $onRejected
        )->wait();

//		Log::info('result', [$result]);

        return $result;
    }

    abstract public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1, $productType = null);

    public function getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode = '')
    {
        \Log::info('getTemplateUrl', [$waybillType, $waybillTemp, $platform, $wpCode]);
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            if ($wpCode == 'youshuwuliu') {
                $templateUrl = ['standard_template_url' => 'https://sf3-ttcdn-tos.pstatp.com/obj/logistics-davinci/template/template_100.xml'];
            } else {
                //抖音1 二联单 2一联单
                if ($waybillType == 1) {
                    $templateUrl = array_key_exists(1, $waybillTemp) ?
                        (array)$waybillTemp[1] : (array_key_exists(6, $waybillTemp) ? (array)$waybillTemp[6] : '');
                } else {
                    $templateUrl = (array)$waybillTemp[2] ?? '';
                }
            }
            if (config('app.platform') == PlatformConst::JD) {
                //京东1 二联单 1一联单
                if ($waybillType == 1) {
                    $templateUrl = array_key_exists(6, $waybillTemp) ? (array)$waybillTemp[6] : "";
                } else {
                    $templateUrl = (array)$waybillTemp[1];
                }
            }
            if (config('app.platform') == PlatformConst::TAOBAO) {
                //淘宝1 二联单 6一联单
                if ($waybillType == 1) {
                    $templateUrl = (array)$waybillTemp[6];
                } else {
                    $templateUrl = (array)$waybillTemp[1];
                }
            }
        } else if ($platform == Waybill::OPEN_API_TB || $platform == Waybill::OPEN_API_CN) {
            // 淘宝菜鸟 1二联单 6一联单
            if ($waybillType == 1) {
                $templateUrl = (array)$waybillTemp[6];
            } else {
                $templateUrl = (array)$waybillTemp[1];
            }
        } else {
            // 拼多多 1二联单 3一联单
            if ($waybillType == 1) {
                $templateUrl = (array)$waybillTemp[3];
            } else {
                $templateUrl = (array)$waybillTemp[1];
            }
        }

        return $templateUrl['standard_template_url'] ?? '';
    }

    abstract public function updateWaybillData($sender, $order, $template, $waybillCode);

    /**
     * 请求厂家电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBos
     * @param $template
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    abstract public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template);

    /**
     * 请求电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return PrintDataPackBo[]
     * @throws BusinessException
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        // 需要继承实现
        throw new BusinessException('方法未实现:assemWaybillPackageByPrintPackBo');
    }

    /**
     * @param array $requestData
     */
    public function setRequestData(array $requestData): void
    {
        $this->requestData = $requestData;
    }


    /**
     * 计算获取单号的数量，如果是子母件按一个单号算
     * @param $packages
     * @return int
     */
    public static function countWaybillCode($packages)
    {
        $waybillCodes = [];
        foreach (array_flatten($packages, 1) as $package) {
            $waybillCode = "";
            $package = collect($package)->toArray();
            $parentWaybillCode = $package['parent_waybill_code'] ?? '';
            if (!empty($parentWaybillCode)) {
                $waybillCode = $parentWaybillCode;
            } else {
                $waybillCode = $package['waybill_code'] ?? '';
            }
            if (!empty($waybillCode)) {
                $waybillCodes[] = $waybillCode;
            }
        }
        return sizeof($waybillCodes);
    }

    /**
     * 检查token是否过期
     *
     * 此函数旨在验证提供的认证信息中的token是否已过期根据认证信息中的expire_at或expires_at字段判断
     * 如果当前时间大于这些字段表示的过期时间，则标记token为过期
     *
     * @param $auth
     * @param bool $isThrow
     * @return bool 返回一个布尔值，true表示token已过期，false表示未过期
     * @throws ApiException
     */
    public function checkTokenExpired($auth, bool $isThrow = false)
    {
        if (is_object($auth) && method_exists($auth, 'toArray')) {
            $auth = $auth->toArray();
        }
        $bool = false;
        // shop
        if (!empty($auth['expire_at'])){
            if (time() > strtotime($auth['expire_at'])) {
                $bool = true;
            }
        }
        // waybills
        if (!empty($auth['expires_at'])){
            if (time() > strtotime($auth['expires_at'])) {
                $bool = true;
            }
        }
        if ($isThrow && $bool){
            throw new ApiException(ErrorConst::WAYBILL_AUTH_EXPIRED);
        }
        return $bool;
    }

    /**
     * @param array $order
     * @param bool $isPtOrder
     * @return array
     */
    protected function getTidByOrder(array $order): string
    {
        $tid = null;
        if (empty($order['tid'])) { // 自由打印
            if (!empty($order['order_no'])) {
                $tid = $order['order_no'];
            } else {
                $tid = $order['id'];
            }
        } else {
            $tid = $order['tid'];
        }
        return $tid;
    }

    /**
     * @param array $waybillBoList
     * @return array
     * @throws BusinessException
     */
    public function getPrintDataByWaybillBos(array $waybillBoList): array
    {
        throw new BusinessException('方法未实现:assemWaybillPackageByPrintPackBo');
        return [];
    }


}
