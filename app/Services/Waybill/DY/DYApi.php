<?php

namespace App\Services\Waybill\DY;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\PrintConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Models\Company;
use App\Models\Order;
use App\Models\OrderTraceList;
use App\Models\Shop;
use App\Models\Template;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\WaybillBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\BusinessException;
use App\Services\Client\DyClient;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\WaybillUtil;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\Element;
use phpDocumentor\Reflection\Types\True_;

/**
 * Created by PhpStorm.
 * User: xujianwei
 * Date: 2021/4/23
 * Time: 13:40
 */
class DYApi extends AbstractWaybillService
{

    const EXCLUDE_VAS_TYPE = ['product_type'];
    const INSURE_VAS_TYPE=["SVC-INSURE"];
        /**
     * 抖音第三方使用的配置，例如微信小商店使用抖音店铺的电子面单
     */
    private $authConfig;
    private $orderTraceMap = [
        '1' => OrderTraceList::STATUS_GOT, //揽件
        '5' => OrderTraceList::STATUS_SEND, //派件
        '3' => OrderTraceList::STATUS_SIGN, //签收
        '6' => OrderTraceList::STATUS_SIGN, //签收 已退回签收 6
//            '?' => OrderTraceList::STATUS_ARRIVAL, //到件
        '0' => OrderTraceList::STATUS_DEPARTURE, //发件 运输中 0
//            '?' => OrderTraceList::STATUS_FAIL, //问题件
//            '?' => OrderTraceList::STATUS_REJECTION, //拒签
//            '?' => OrderTraceList::STATUS_STAY_IN_WAREHOUSE, //留仓
//            '?' => OrderTraceList::STATUS_SIGN_ON_BEHALF, //代收点代签
        '13' => OrderTraceList::STATUS_SHIPPED, //已发货 待揽收
        '9' => OrderTraceList::STATUS_RETURN, //退件 已退回在途 9
        '4' => OrderTraceList::STATUS_RETURN, //退件 退回中 4
        '12' => OrderTraceList::STATUS_IN_CABINET, //入柜/入代收点
//            '?' => OrderTraceList::STATUS_OUT_CABINET, //出柜/出代收点
    ];
    protected $waybillPlatformType = PlatformConst::WAYBILL_DY; // 电子面单平台类型

    //子母件
    const ZIMUJIANMAP = [
        'shunfeng',
//        'jd',
        'debangwuliu',
        'shunfengkuaiyun',
        'annengwuliu',
        'zhongtongkuaiyun',
        'yundakuaiyun'];
    //不支持取消
    const NORECOVERY = ['shunfeng', 'zhongtong', 'youzhengguonei', 'ems', 'jd', 'zhongyouex'];
    //不支持更新
    const NOUPDATE = ['shunfeng', 'youzhengguonei', 'ems', 'jd', 'fengwang', 'yuantong'];

    public function __construct(string $accessToken = '')
    {
        $this->accessToken = $accessToken;
        $this->authConfig = config('waybill.dy');
    }

    /**
     * @return DyClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        return (new DyClient($appKey, $secretKey))->setAccessToken($this->accessToken);
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws ClientException
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        $client = $this->getClient();
        $params = [
            "logistics_code" => $wpCode
        ];

        Log::info('抖音面单查询前');
        $response = $client->execute('logistics/listShopNetsite', $params);
//        Log::info('抖音面单查询：' . json_encode($response));
        if (!isset($response['code']) || $response['code'] != 10000) {
            return [];
        }
        if (!isset($response['data']['netsites']) || empty($response['data']['netsites'])) {
            return [];
        }

        $waybillsInfo = [];
        $serviceInfoColsMap = collect(config('dy_service_attributes'))->all();
        foreach ($response['data']['netsites'] as $value) {
            $branchAccounts = $addresses = [];
            foreach ($value['sender_address'] as $item) {
                $addresses[] = [
                    'city' => $item['city_name'] ?? '',
                    'detail' => $item['detail_address'] ?? '',
                    'province' => $item['province_name'] ?? '',
                    'district' => $item['district_name'] ?? '',
                    'street' => $item['street_name'] ?? '',// 兼容名字
                    'street_name' => $item['street_name'] ?? ''
                ];
            }

            $serviceInfoCols = [];
            if (array_key_exists($value['company'], $serviceInfoColsMap)) {
                $serviceInfoCols = $serviceInfoColsMap[$value['company']];
            }
            $branchAccounts[] = [
                'branch_code' => $value['netsite_code'] ?? 0,
                'branch_name' => $value['netsite_name'] ?? '',
                'quantity' => $value['amount'] ?? 0,
                'cancel_quantity' => $value['cancelled_quantity'] ?? 0,
                'recycled_quantity' => $value['recycled_quantity'] ?? 0,
                'allocated_quantity' => $value['allocated_quantity'] ?? 0,
                'shipp_addresses' => $addresses,
                'service_info_cols' => $serviceInfoCols,
            ];

            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $value['company'],
                'wp_type' => $value['company_type']
            ];
        }

        $data = [];
        foreach ($waybillsInfo as $item) {
            $data[$item['wp_code']]['branch_account_cols'][] = $item['branch_account_cols'][0];
            $data[$item['wp_code']]['wp_code'] = $item['wp_code'];
            $data[$item['wp_code']]['wp_type'] = $item['wp_type'];
        }

        return array_values($data);
    }

    /**
     * 单个订单获取面单
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return mixed
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        $msg_type = 'logistics/newCreateOrder';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $client = $this->getClient();

        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $client->execute($msg_type, $info);
                \Log::info('dy response:' . json_encode($waybill));
                if (!isset($waybill['code']) || $waybill['code'] != 10000) {
                    \Log::error('API=>' . $msg_type, [$waybill]);
                }

                $waybill_applies = [];
                //要对子母件进行特殊处理,顺丰、德邦母件本身就是一个子件，例如parentWaybillCode = 1234567890,subWaybillCodes = 1234567890,1234567891
                //而韵达快运的母件不是子件，例如parentWaybillCode = 860010161,subWaybillCodes = 8600101610001,8600101610002
                //所以这里要对韵达的母件做特殊处理
                if (isset($waybill['data']['ebill_infos'][0]['sub_waybill_codes']) && !empty($waybill['data']['ebill_infos'][0]['sub_waybill_codes'])) {
                    if ($template['wp_code'] != 'yundakuaiyun') {
                        $waybill_applies[] = [
                            'logistics_code' => $template['wp_code'],
                            'track_no' => $waybill['data']['ebill_infos'][0]['track_no']
                        ];
                    }
                } else {
                    //非子母件
                    $waybill_applies[] = [
                        'logistics_code' => $template['wp_code'],
                        'track_no' => $waybill['data']['ebill_infos'][0]['track_no']
                    ];
                }
                $parentWaybillCode = '';
                //处理子母件
                if (isset($waybill['data']['ebill_infos'][0]['sub_waybill_codes']) && !empty($waybill['data']['ebill_infos'][0]['sub_waybill_codes'])) {
                    $parentWaybillCode = $waybill['data']['ebill_infos'][0]['track_no'];
                    $subWaybillCodes = explode(',', $waybill['data']['ebill_infos'][0]['sub_waybill_codes']);
                    foreach ($subWaybillCodes as $item) {
                        $waybill_applies[] = [
                            'logistics_code' => $template['wp_code'],
                            'track_no' => $item
                        ];
                    }
                }

                $res = $client->execute('logistics/waybillApply', [
                    'waybill_applies' => $waybill_applies,
                ]);
//                \Log::info('抖音获取加密打印数据  result：',[$res]);
                foreach ($res['data']['waybill_infos'] as $data) {
                    $result[] = [
                        'object_id' => $waybill['data']['ebill_infos'][0]['pack_id'],
                        'waybill_code' => $data['track_no'],
                        'print_data' => $data['print_data'],
                        'sign' => $data['sign'],
                        'parent_waybill_code' => $parentWaybillCode
                    ];
                }
            } catch (\Exception $e) {
                \Log::error("获取电子面单失败" . $msg_type, [$e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 多个订单获取面单
     * @param $sender
     * @param $orders
     * @param $template
     * @param $packageNum
     * @return mixed
     * @throws ClientException
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        \Log::info('抖音多个订单获取面单', [$packageNum]);
        $data = $result = [];

        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);

            //其中可能会修改access_token 所以在这里创建
            $client = $this->getClient();
            $msg_type = 'logistics/newCreateOrder';
            foreach ($applyInfo as $index => $info) {
                $tempIdStr = $idStr . '|' . $index;
                $data[$tempIdStr] = [
                    'params' => $client->buildRequestData($info, $msg_type), //只有一个包裹
                    'url' => $client->getBaseUrlByApimethod($msg_type),
                ];
            }
        }

        $response = $this->poolCurl($data, 'get', false, true);
//        \Log::debug('dy_response', [$response]);
        StatisticsCost('调用取号接口返回');
        $waybill_applies = $orderIdStrMap = $hasParentWaybillCode = [];
//        \Log::info("取号结果",["response"=>$response,$data]);
        foreach ($response as $orderIdStr => $waybill) {
            $waybill = json_decode(json_encode($waybill), true);
            //正常取号数据
            if (isset($waybill['data']['ebill_infos']) && !empty($waybill['data']['ebill_infos'])) {
                foreach ($waybill['data']['ebill_infos'] as $ebillInfos) {
                    $waybillCode = $ebillInfos['track_no'];
                    $waybill_applies[] = [
                        'logistics_code' => $template['wp_code'],
                        'track_no' => $waybillCode
                    ];
                    $orderIdStrMap[$waybillCode] = [
                        'order_id_str' => $orderIdStr,
                        'object_id' => $ebillInfos['pack_id']
                    ];
                    if (isset($ebillInfos['sub_waybill_codes']) && !empty($ebillInfos['sub_waybill_codes'])) {
                        $subWaybillCodes = explode(',', $waybill['data']['ebill_infos'][0]['sub_waybill_codes']);
                        if ($template['wp_code'] == 'zhongtongkuaiyun') {
                            if (count($subWaybillCodes) > 1) {
                                foreach ($subWaybillCodes as $item) {
                                    $waybill_applies[] = [
                                        'logistics_code' => $template['wp_code'],
                                        'track_no' => $item
                                    ];
                                    $orderIdStrMap[$item] = [
                                        'order_id_str' => $orderIdStr,
                                        'object_id' => $waybill['data']['ebill_infos'][0]['pack_id']
                                    ];
                                    $hasParentWaybillCode[$item] = $waybillCode;
                                }
                            }
                        } else {
                            foreach ($subWaybillCodes as $item) {
                                $waybill_applies[] = [
                                    'logistics_code' => $template['wp_code'],
                                    'track_no' => $item
                                ];
                                $orderIdStrMap[$item] = [
                                    'order_id_str' => $orderIdStr,
                                    'object_id' => $waybill['data']['ebill_infos'][0]['pack_id']
                                ];
                                $hasParentWaybillCode[$item] = $waybillCode;
                            }
                        }
                    }
                }
            }

            //取号错误数据
            if (isset($waybill['data']['err_infos']) && !empty($waybill['data']['err_infos'])) {
                $result[$orderIdStr][] = $waybill;
            }
        }

        if (!empty($waybill_applies)) {
            $res = $client->execute('logistics/waybillApply', [
                'waybill_applies' => $waybill_applies,
            ]);

            $requestData = $client->buildRequestData([], 'logistics/getShopKey');
            $paramsStr = urldecode(http_build_query($requestData));
            //正常数据
            foreach ($res['data']['waybill_infos'] as $data) {
                $result[$orderIdStrMap[$data['track_no']]['order_id_str']][] = [
                    'object_id' => $orderIdStrMap[$data['track_no']]['object_id'],
                    'waybill_code' => $data['track_no'],
                    'print_data' => $data['print_data'],
                    'sign' => $data['sign'],
                    'parent_waybill_code' => $hasParentWaybillCode[$data['track_no']] ?? '',
                    'params_str' => $paramsStr,
                ];
            }
            //错误数据
            foreach ($res['data']['err_infos'] as $data) {
                \Log::info('抖音获取加密打印数据错误', [$data]);
                $result[$orderIdStrMap[$data['track_no']]['order_id_str']][] = '抖音获取打印数据错误';
            }
        }

//        foreach ($response as $orderIdStr => $waybill) {
//            try {
//                $waybill = json_decode(json_encode($waybill), true);
//                if (isset($waybill['err_no']) && $waybill['err_no'] == 0 && !empty($waybill['data']['ebill_infos']) && empty($waybill['data']['err_infos'])) {
//                    //根据获取到的运单号获取密文打印数据
//                    $waybill_applies = [];
//                    $waybill_applies[] = [
//                        'logistics_code' => $template['wp_code'],
//                        'track_no' => $waybill['data']['ebill_infos'][0]['track_no']
//                    ];
//                    $parentWaybillCode = '';
//                    if (isset($waybill['data']['ebill_infos'][0]['sub_waybill_codes']) && !empty($waybill['data']['ebill_infos'][0]['sub_waybill_codes'])) {
//                        $parentWaybillCode = $waybill['data']['ebill_infos'][0]['track_no'];
//                        $subWaybillCodes = explode(',', $waybill['data']['ebill_infos'][0]['sub_waybill_codes']);
//                        foreach ($subWaybillCodes as $item){
//                            $waybill_applies[] = [
//                                'logistics_code' => $template['wp_code'],
//                                'track_no' => $item
//                            ];
//                        }
//                    }
//
//                    $res = $client->execute('logistics/waybillApply', [
//                        'waybill_applies' => $waybill_applies,
//                    ]);
////                    \Log::info('抖音获取加密打印数据  result：',[$res]);
//                    foreach ($res['data']['waybill_infos'] as $data) {
//                        $result[$orderIdStr][] = [
//                            'object_id'           => $waybill['data']['ebill_infos'][0]['pack_id'],
//                            'waybill_code'        => $data['track_no'],
//                            'print_data'          => $data['print_data'],
//                            'sign'                => $data['sign'],
//                            'parent_waybill_code' => $parentWaybillCode
//                        ];
//                    }
//                } else {
//                    \Log::error($msg_type . '取号错误', [$orderIdStr => $waybill]);
//                    $result[$orderIdStr][] = $waybill;
//                }
//            }catch (ConnectException $exception){
//                \Log::error($msg_type . '取号错误，连接错误：'.$exception->getMessage(), [$exception]);
//                $result[$orderIdStr][] = '抖音获取打印数据超时，请稍后重试！';
//            } catch (\Exception $exception){
//                \Log::error($msg_type . '取号错误:'.$exception->getMessage(), [$orderIdStr => $waybill]);
//                $result[$orderIdStr][] = '抖音获取打印数据错误：'.$exception->getMessage();
//            }
//        }

        return $result;
    }

    /**
     * 作废面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return mixed
     * @throws ClientException
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode, string $platformWaybillId = '')
    {
        //顺丰不支持取消
        if (in_array($cpCode, self::NORECOVERY)) {
            return true;
        }
        $client = $this->getClient();
        $msg_type = 'logistics/cancelOrder';

        $data = [
            'track_no' => $waybillCode,
            'logistics_code' => $cpCode,
        ];
        $result = $client->execute($msg_type, $data);
        \Log::info($msg_type, [$result]);

        //取消过了 {"err_no":7,"log_id":"20210923110319010131126238154F5542","message":"已经取消过了，无需再次取消"}
        if (isset($result['sub_msg']) && ($result['sub_msg'] == '已经取消过了，无需再次取消' || $result['sub_msg'] == '单号已回收，不允许变更')) {
            return true;
        }
        if (!isset($result['data']['cancel_result']) || !$result['data']['cancel_result']) {
            throw new \Exception($result['sub_msg'] ?? "取消失败");
        }

        return true;
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @return mixed
     */
    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $client = $this->getClient();
        $params = ['wp_code' => $wpCode];
        $response = $client->execute('logistics/templateList', $params);

        $standardTemplates = [];
        if (!isset($response['data']) || empty($response['data'])) {
            return $standardTemplates;
        }
        foreach ($response['data']['template_data'] as $template) {
            if ($template['logistics_code'] == $wpCode) {
                foreach ($template['template_infos'] as $item) {
                    if ($item['template_name'] == '顺丰一联单(100*150)') {
                        $item['template_type'] = 2;
                    }
                    $standardTemplates[$item['template_type']] = [
                        'standard_waybill_type' => $item['template_type'],
                        'standard_template_name' => $item['template_name'],
                        'standard_template_url' => $item['template_url'],
                    ];
                }
            }
        }

        return $standardTemplates;
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return mixed
     * @throws ClientException
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $client = $this->getClient();
        $params = ['wp_code' => $wpCode];
        $response = $client->execute('logistics/templateList', $params);
        \Log::info('抖音获取模板列表', [$response]);
        $standardTemplates = [];
        if (!isset($response['data']) || empty($response['data'])) {
            return $standardTemplates;
        }
        foreach ($response['data']['template_data'] as $template) {
            if ($template['logistics_code'] == $wpCode) {
                foreach ($template['template_infos'] as $item) {
                    $standardTemplates[] = [
                        'standard_waybill_type' => $item['template_type'],
                        'standard_template_name' => $item['template_name'],
                        'standard_template_url' => $item['template_url'],
                    ];
                }
            }
        }

        return $standardTemplates;
    }

    /**
     * 获取抖音的电子面单的授权链接，微信小商店想使用抖音的电子面单就需要获取这个链接
     * @param int $shopId
     * @return mixed
     */
    public function getLoginUrl($shopId)
    {
        $state = [
            'jumpUrl' => $this->authConfig['jump_url'],
            'state' => $shopId
        ];
        return $this->authConfig['code_url'] . base64_encode(json_encode($state));

    }

    /**
     * 这个是通过Code获取AccessToken，
     * access token
     * @param string $code
     * @return mixed
     */
    public function getAccessToken(string $code)
    {
        $oauthUser = socialite()->driver("dy")->user();
        $original = $oauthUser->getOriginal();
        \Log::info("获取授权", [json_encode($oauthUser)]);
        return [
            'access_token' => $oauthUser->getAccessToken(),
            'refresh_token' => $oauthUser->getRefreshToken(),
            'owner_id' => $original['shop_id'],
            'owner_name' => $original['shop_name'],
            'expires_in' => $original['expires_in'],
            'expires_at' => date('Y-m-d H:i:s', time() + $original['expires_in']),
        ];
//        return $oauthUser->getAccessToken();
    }

    /**
     * 订阅物流轨迹
     * @param string $receiverPhone
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        // TODO: Implement sendOrderLogisticsTraceMsg() method.
    }

    /**
     * 获取电子面单打印数据的请求参数
     * @param array $response
     * @param string $msg_type
     * @param $template
     * @return WaybillBo[]
     * <AUTHOR>
     */
    public function getWaybillBosByGetWaybillResponse(array $response, string $msg_type, $template): array
    {
        $list = [];
        foreach ($response as $index => $item) {
            $item = json_decode(json_encode($item), true);
            if (isset($item['err_no']) || $item['err_no'] != 0 || empty($item['data']['ebill_infos']) ||
                !empty($item['data']['err_infos'])) {
                \Log::error($msg_type . '取号错误', [$index => $item]);
            } else {
                foreach ($item['data']['ebill_infos'] as $ebill_info) {
                    $waybillBo = new WaybillBo();
                    $waybillBo->wp_code = $template['wp_code'];
                    $waybillBo->waybill_code = $ebill_info['track_no'];
                    $list[] = $waybillBo;
                }
            }
        }
        return $list;
    }


    /**
     * 批量请求打印数据
     * @param WaybillBo[] $waybillBoList
     * @return WaybillsPrintDataBo[]
     * <AUTHOR>
     */
    public function getPrintDataByWaybillBos(array $waybillBoList): array
    {
        // 请求打印数据
        $msg_type = 'logistics/waybillApply';
        $waybill_applies = [];
        // 请求批次和面单号对应关系
        $reqWaybillCodeIndexMap = [];
        foreach ($waybillBoList as $index => $waybillBo) {
            $waybill_applies[] = [
                'logistics_code' => $waybillBo->wp_code,
                'track_no' => $waybillBo->waybill_code,
            ];
        }
        // 10个一批
        $waybill_applies = array_chunk($waybill_applies, 10);
        $data = [];
        $client = $this->getClient();
        foreach ($waybill_applies as $batchIndex => $waybill_apply) {
            //其中可能会修改access_token 所以在这里创建
            $params = ['waybill_applies' => $waybill_apply];
            foreach ($waybill_apply as $item) {
                $reqWaybillCodeIndexMap[$item['track_no']] = $batchIndex;
            }
            $data[$batchIndex] = [
                'params' => $client->buildRequestData($params, $msg_type), //只有一个包裹
                'url' => $client->getBaseUrlByApimethod($msg_type),
            ];
        }
        $response = $this->poolCurl($data, 'json', false, true);
        $response = json_decode(json_encode($response), true);

        $waybillInfoList = [];
        $errorInfoList = [];
        foreach ($response as $index => $res) {
            if (!empty($res['data']['waybill_infos'])) {
                $waybillInfoList = array_merge($waybillInfoList, $res['data']['waybill_infos']);
            }
            if (!empty($res['data']['err_infos'])) {
                $errorInfoList = array_merge($errorInfoList, $res['data']['err_infos']);
            }
        }
        $waybillInfoList = array_pluck($waybillInfoList, null, 'track_no');
        $errorInfoList = array_pluck($errorInfoList, null, 'track_no');
        $waybillsPrintDataBoList = [];
        foreach ($waybillBoList as $index => $waybillBo) {
            $waybillsPrintDataBo = new WaybillsPrintDataBo();
            $batchIndex = $reqWaybillCodeIndexMap[$waybillBo->waybill_code];
            // 请求就错误了
            if (is_string($response[$batchIndex])) {
                $waybillsPrintDataBo->waybill_code = $waybillBo->waybill_code;
                $err_msg = $response[$batchIndex];
                $err = ErrorConst::PLATFORM_DY_ERROR;
                $waybillsPrintDataBo->setError($err, $err_msg);
            } elseif (!empty($waybillInfoList[$waybillBo->waybill_code]) && !empty($waybillInfoList[$waybillBo->waybill_code]['print_data'])) {// 正确返回数据
                $info = $waybillInfoList[$waybillBo->waybill_code];
                $waybillsPrintDataBo->waybill_code = $info['track_no'];
                $waybillsPrintDataBo->encrypted_data = $info['print_data'];
                $waybillsPrintDataBo->sign = $info['sign'];
                $waybillsPrintDataBo->order_id = $info['order_id'];
            } elseif (!empty($errorInfoList[$waybillBo->waybill_code])) {// 有没有返回错误数据
                $info = $errorInfoList[$waybillBo->waybill_code];
                $waybillsPrintDataBo->waybill_code = $info['track_no'];
                $waybillsPrintDataBo->order_id = $info['order_id'];
                $err_msg = $info['err_msg'] . "({$info['err_code']})";
                $err = ErrorConst::PLATFORM_DY_ERROR;
                $waybillsPrintDataBo->setError($err, $err_msg);
            } else { // 啥也没有，出错了
                $waybillsPrintDataBo->waybill_code = $waybillBo->waybill_code;
                $err = ErrorConst::WAYBILL_GET_PRINT_DATA_ERROR;
                $waybillsPrintDataBo->setError($err);
            }
//            $waybillsPrintDataBo->copyByArr($arr);
            $requestData = $client->buildRequestData([], 'logistics/getShopKey');
            $paramsStr = urldecode(http_build_query($requestData));
            $waybillsPrintDataBo->param_str = $paramsStr;
            $waybillsPrintDataBoList[] = $waybillsPrintDataBo;
        }

        return $waybillsPrintDataBoList;
    }

    /**
     * 构建增值服务
     * @param string|null $service_list
     * @param PrintPackBo $printDataPackBo
     * @return array
     */
    public function buildOrderVasList(?string $service_list, PrintPackBo $printDataPackBo): array
    {
        $newServiceList = [];
        if(empty($service_list)){
            return $newServiceList;
        }
        $serviceList = json_decode($service_list, true);
//        Log::info("增值服务",["serviceList"=>$serviceList]);
        foreach ($serviceList as $key => $item) {
            if(in_array($key, self::EXCLUDE_VAS_TYPE)){
                continue;
            }

            //保价有两种
            if (in_array($key, self::INSURE_VAS_TYPE)) {
                $insureAmount = $printDataPackBo->getInsureAmount();
                Log::info("处理保价",["insureAmount"=>$insureAmount,"item"=>$item]);
                //-1 是订单金额保价
                if ($item['value'] != -1) {
                   Log::info("保价金额不是-1,按设定的金额处理",["item"=>$item]);
                }else{
                    if(bccomp($insureAmount,"0")>0) {
                        Log::info("保价金额是-1,订单金额>0 按订单金额处理",["insureAmount"=>$insureAmount]);
                        $item['value'] = round_bcmul($insureAmount, "100",0); //  (string)($insureAmount * 100);
                    }else{
                        Log::info("保价金额是-1,订单金额=0 不处理这个保价",["insureAmount"=>$insureAmount]);
                        continue;
                    }
                }
            }
            $newServiceList[] = [
                'service_code' => $key,
                'service_value' => json_encode($item)
            ];
        }
        return $newServiceList;
    }


    /**
     * 发送获取物流轨迹信息
     * @see https://op.jinritemai.com/docs/api-docs/16/784
     * @param array $waybill
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function sendGetOrderTraceList(array $waybill): array
    {
        $client = $this->getClient();
        $params = [
            'logistics_code' => $waybill['express_code'],
            'track_no' => $waybill['express_no'],
        ];
        $result = $client->execute('logistics/trackNoRouteDetail', $params);
        DyClient::handleErrorCode($result);
        if (empty($result['data']['route__node_list'])) {
            return [];
        }
        return $this->formatToOrderTrace($waybill, $result['data']['route__node_list']);
    }

    /**
     * 物流数据整理
     * @param $waybill
     * @param array $orderTraceList
     * @return array
     */
    public function formatToOrderTrace($waybill, array $orderTraceList): array
    {

        $latest = collect($orderTraceList)->sortByDesc('timestamp')->first();

        $trace_list = [];
        foreach ($orderTraceList as $index => $item) {
            $status = $this->orderTraceMap[$item['state']] ?? OrderTraceList::STATUS_OTHER;
            $trace_list[] = [
                "status" => $status, // 状态
                "status_desc" => OrderTraceList::STATUS_NAME_MAPPING[$status], //状态描述
                "action" => $item['state'], // 平台那边的状态
                "action_desc" => $item['state_description'], // 平台那边的状态描述
                "site_name" => $item['site_name'], // 站点名称
//                "node_description" => "GOT",
                "status_time" => date('Y-m-d H:i:s', $item['timestamp']), // 状态发生时间
                "time" => date('Y-m-d H:i:s', $item['timestamp']), // 数据创建时间
                "desc" => $item['content'], // 流转过程
            ];
        }
        return [
            "type" => $waybill['type'] ?? 0, //?
            'tid' => $waybill['tid'] ?? '',
            'express_code' => $waybill['express_code'],
            'express_no' => $waybill['express_no'],
            'status' => $this->orderTraceMap[$latest['state']] ?? OrderTraceList::STATUS_OTHER,
            'action' => $latest['state'],
            'receiver_province' => $waybill['receiver_province'] ?? '',
            'receiver_name' => $waybill['receiver_name'] ?? '',
            'send_at' => $waybill['send_at'] ?? null,
            'latest_updated_at' => date('Y-m-d H:i:s', $latest['timestamp']),
            'latest_trace' => $latest['content'],
            'status_time' =>  isset($latest['timestamp'])?date('Y-m-d H:i:s', $latest['timestamp']):DateTimeUtil::strNow(),
            'trace_list' => '[]'
        ];
    }

    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        $data = [];
        $result = [];

        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum,$productType);

            //其中可能会修改access_token 所以在这里创建
            $client = $this->getClient();
            $msg_type = 'logistics/newCreateOrder';
            $data[$idStr] = [
                'params' => $client->buildRequestData($applyInfo[0], $msg_type), //只有一个包裹
                'url' => $client->getBaseUrlByApimethod($msg_type),
            ];
        }

        $response = $this->poolCurl($data, 'get', false, true);
//        \Log::debug('dy_response', [$response]);

        foreach ($response as $orderIdStr => $waybill) {
            $waybill = json_decode(json_encode($waybill), true);
            if (isset($waybill['code']) && $waybill['code'] == 10000 && !empty($waybill['data']['ebill_infos']) && empty($waybill['data']['err_infos'])) {
                //根据获取到的运单号获取密文打印数据
                $waybill_applies = [];
                $waybill_applies[] = [
                    'logistics_code' => $wpCode,
                    'track_no' => $waybill['data']['ebill_infos'][0]['track_no']
                ];

                $res = $client->execute('logistics/waybillApply', [
                    'waybill_applies' => $waybill_applies,
                ]);
//                \Log::info('抖音获取加密打印数据  result：',[$res]);
                // "params": "access_token=值&app_key=值&method=logistics.getShopKey&param_json={}&timestamp=时间&v=2&sign=值"
                $requestData = $client->buildRequestData([], 'logistics/getShopKey');
                $paramsStr = urldecode(http_build_query($requestData));
                $templateUrl = $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode);

                $insertPrint = [
                    'encryptedData' => $res['data']['waybill_infos'][0]['print_data'],
                    'templateUrl' => $templateUrl,
                    'templateURL' => $templateUrl,
                    'signature' => $res['data']['waybill_infos'][0]['sign'],
                    'params' => $paramsStr,
                    'addData' => [
                        'senderInfo' => [
                            'address' => [
                                'provinceName' => $sender['province'],
                                'cityName' => $sender['city'],
                                'districtName' => $sender['district'],
                                'detailAddress' => $sender['address'],
                                'town' => '',
                                'countryCode' => 'CHN'
                            ],
                            'contact' => [
                                'name' => $sender['sender_name'],
                                'mobile' => $sender['mobile'],
                                'phone' => '',
                            ]
                        ]
                    ]
                ];

                $waybillsData = [
                    'object_id' => $waybill['data']['ebill_infos'][0]['pack_id'],
                    'waybill_code' => $res['data']['waybill_infos'][0]['track_no'],
                    'print_data' => json_encode($insertPrint),
                    'parent_waybill_code' => ''
                ];
                $result[$orderIdStr][] = $waybillsData;
            } else {
                \Log::error($msg_type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    private function getWayBillRequestDataForOpenApi($sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum,$productType=null)
    {
        $returnArr = [];
        $num = 1;

        //去除A 并判断是否是抖音平台订单
        $isNotDYOrder = false;
        if (isset($order['tid'])) {
            $orderId = str_replace('A', '', $order['tid']);
        } else {
            $isNotDYOrder = true;
            $orderId = $order['order_no'] ?? $order['id'];
        }
        //是否共享面单
        $isShare = false;
        $shop = Shop::query()->where('access_token', $this->accessToken)->first();
        if ($shop->id != $order['shop_id'] || !isset($order['tid'])) {
            $isShare = true;
            $this->accessToken = $shop->access_token;
        }

        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['address']['city_name'] = $sender['city'];
            $senderInfo['address']['country_code'] = 'CHN';
            $senderInfo['address']['detail_address'] = $sender['address'];
            $senderInfo['address']['district_name'] = $sender['district'];
            $senderInfo['address']['province_name'] = $sender['province'];
            $senderInfo['address']['street_name'] = $sender['street'] ?? '';
            $senderInfo['contact']['mobile'] = $sender['mobile'];
            $senderInfo['contact']['name'] = $sender['sender_name'];
            //面单信息
            $tradeOrderInfoDto['order_id'] = $orderId;
            $tradeOrderInfoDto['pack_id'] = $order['request_id'] ?? $order['id'] . '_' . rand(1111, 9999);

            //面单收件人信息
            $tradeOrderInfoDto['receiver_info'] = [
                'address' => [
                    'city_name' => $order['receiver_city'],
                    'country_code' => 'CHN',
                    'detail_address' => (isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) ? $order['order_cipher_info']['receiver_address_ciphertext'] : stripslashes($order['receiver_address']),
                    'district_name' => $order['receiver_district'],
                    'province_name' => $order['receiver_state'] ?? $order['receiver_province'],
                    'street_name' => $order['receiver_town'] ?? ""
                ],
                'contact' => [
                    'mobile' => (isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'],
                    'name' => (isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                    'phone' => $order['receiver_tel'] ?? '',
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                //只取一条
                foreach (array_slice($order['order_item'],0,1) as $good) {
                    $temp = [];
                    $temp['item_count'] = $good['goods_num'];
                    $temp['item_name'] = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[] = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'item_count' => isset($order['num']) && is_int($order['num']) ? $order['num'] : 1,
                    'item_name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }
            $tradeOrderInfoDto['items'] = $items;
            if ($wpCode == 'shunfeng') {
                //默认电商标快
                $tradeOrderInfoDto['product_type'] =  $productType??'285';
            }
            if ($wpCode == 'jd') {
                //抖音不支持JD的月结，所以需要写死现结
                //支付方式：1-寄付月结，2-寄付现结）若不传，默认为商家与物流商网点约定的支付方式
                $tradeOrderInfoDto['pay_method'] = 2;
            }
            ksort($tradeOrderInfoDto);
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['logistics_code'] = $wpCode;
            $data['order_infos'] = $tradeOrderInfoDtos;
            $data['sender_info'] = $senderInfo;
            if ($isShare) {
                //$data['user_id'] = $waybillShop->identifier;
                $data['user_id'] = '-1';
            }

            if ($isNotDYOrder || Environment::isWxOrWxsp()) {
                //下单渠道编码:https://bytedance.feishu.cn/sheets/shtcngIVwcJlgXLzWhEtKrmv7Af
                $data['order_channel'] = '54';
            }else{
                $data['order_channel'] = '1';
            }

            $returnArr[] = $data;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * 地址是否可达
     * @param $sender
     * @param $order
     * @return bool
     * @throws \App\Exceptions\ClientException
     */
    public function getAddressAccessible($wpCode, $sender, $order)
    {
        //https://op.jinritemai.com/docs/guide-docs/33/338
        //顺丰、中通、京东、众邮不支持
        //极兔、韵达、圆通、百世、只能传2
        //申通只能传0
        if (in_array($wpCode, ['shunfeng', 'zhongtong', 'youzhengguonei', 'ems', 'jd', 'zhongyouex'])) {
            return true;
        }
        $client = $this->getClient();
        $msg_type = 'logistics/getOutRange';

        $params = [
            "logistics_code" => $order['express_code'],
            "sender_address" => [
                "country_code" => "CHN",
                "province_name" => $sender['province'],
                "city_name" => $sender['city'],
                "district_name" => $sender['district'],
                "detail_address" => $sender['address'],
            ],
            "receiver_address" => [
                'country_code' => 'CHN',
                'city_name' => $order['receiver_city'],
                'detail_address' => $order['receiver_address'],
                'district_name' => $order['receiver_district'],
                'province_name' => $order['receiver_province'],
            ],
            "type" => $wpCode == 'shentong' ? 0 : 2//0揽派合1 1揽收区域 2派送区域
        ];

        $result = $client->execute($msg_type, $params);

        if (isset($result['data']['is_out_range'])) {
            return $result['data']['is_out_range'];
        }
        return true;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        $client = $this->getClient();
        $msg_type = 'logistics/updateOrder';
        $applyInfo = $this->getWayBillUpdateRequestData($sender, $order->toArray(), $template, $waybillCode);

        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $client->execute($msg_type, $info);
                \Log::info('dy_update_response', [$waybill]);
                if (!isset($waybill['code']) || $waybill['code'] != 10000) {
                    \Log::error('API=>' . 'logistics/updateOrder', [$waybill]);
                    return $result;
                }
                //根据获取到的运单号获取密文打印数据
                $waybill_applies = [];
                $waybill_applies[] = [
                    'logistics_code' => $template['wp_code'],
                    'track_no' => $waybill['data']['track_no']
                ];

                $res = $client->execute('logistics/waybillApply', [
                    'waybill_applies' => $waybill_applies,
                ]);
                $requestData = $client->buildRequestData([], 'logistics/getShopKey');
                $paramsStr = urldecode(http_build_query($requestData));
//                \Log::info('抖音获取加密打印数据  result：',[$res]);
                $waybillsData = [
                    'object_id' => '',
                    'paramsStr' => $paramsStr,
                    'waybill_code' => $res['data']['waybill_infos'][0]['track_no'],
                    'print_data' => $res['data']['waybill_infos'][0]['print_data'],
                    'sign' => $res['data']['waybill_infos'][0]['sign'],
                    'parent_waybill_code' => ''
                ];
                $result = $waybillsData;
            } catch (\Exception $e) {
                \Log::error("更新电子面单失败" . $msg_type, [$e->getTraceAsString()]);
            }
        }

        return $result;
    }

    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        $returnArr = [];
        $num = 1;
        //去除A 并判断是否是抖音平台订单
        $isNotDYOrder = false;
        if (isset($order['tid'])) {
            $orderId = str_replace('A', '', $order['tid']);
        } else {
            $isNotDYOrder = true;
            $orderId = $order['id'];
        }

        $isShare = false;
        //是否共享面单
        $company = $template['company'];
        if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
            $isShare = true;
        }
        //是否共用面单
        if ($template['shop_id'] != $order['shop_id']) {
            $isShare = true;
            if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
                $waybillShop = Shop::query()->where('identifier', $company['owner_id'])->first();
                $this->accessToken = $waybillShop->access_token;
            } else {
                $waybillShop = Shop::query()->where('id', $template['shop_id'])->first();
                $this->accessToken = $waybillShop->access_token;
            }
        }

        //是否有加密数据
        $isGrayscale = false;
        if (isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
            $isGrayscale = true;
//            if (isset($order['order_no']) && !empty($order['receiver_phone'])) {
//                $isGrayscale = false;
//            }
        }
        //取真实包裹数量
        if ($packageNum < 0) {
            $packageNum = $order['packageNum'];
        }
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['address']['city_name'] = $sender['city'];
            $senderInfo['address']['country_code'] = 'CHN';
            $senderInfo['address']['detail_address'] = $sender['address'];
            $senderInfo['address']['district_name'] = $sender['district'];
            $senderInfo['address']['province_name'] = $sender['province'];
            $senderInfo['address']['street_name'] = $sender['street'] ?? '';
            $senderInfo['contact']['mobile'] = $sender['mobile'];
            $senderInfo['contact']['name'] = $sender['sender_name'];
            //面单信息
            $tradeOrderInfoDto['order_id'] = $orderId;
            $tradeOrderInfoDto['pack_id'] = isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] . '_' . rand(1111, 9999);
            //面单收件人信息
            $mobile = $isGrayscale ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'];
            $mobile = Environment::isWxOrWxsp() ? OrderUtil::removeVirtualPhoneDashSuffix($mobile) : $mobile;
//            \Log::info('修改抖音取号的手机号',[$mobile]);
            $tradeOrderInfoDto['receiver_info'] = [
                'address' => [
                    'city_name' => $order['receiver_city'],
                    'country_code' => 'CHN',
                    'detail_address' => $isGrayscale ? $order['order_cipher_info']['receiver_address_ciphertext'] : stripslashes($order['receiver_address']),
                    'district_name' => $order['receiver_district'],
                    'province_name' => $order['receiver_state'] ?? $order['receiver_province'],
                    'street_name' => $order['receiver_town']
                ],
                'contact' => [
                    'mobile' => $mobile,
                    'name' => $isGrayscale ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                    'phone' => $order['receiver_tel'] ?? '',
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['item_count'] = $good['goods_num'];
                    $temp['item_name'] = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[] = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'item_count' => is_int($order['num']) ? $order['num'] : 1,
                    'item_name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }
            $tradeOrderInfoDto['items'] = $items;
            //产品类型
            if ($template['time_delivery']) {
                $tradeOrderInfoDto['product_type'] = $template['time_delivery'];
            }
            //增值服务
            if (!empty($template['service_list'])) {
                $serviceList = json_decode($template['service_list'], true);
                $orderAmountCount = isset($order['order_item']) ? collect($order['order_item'])->sum('payment') : 20;
                $newServiceList = [];
                foreach ($serviceList as $key => $item) {
                    //保价有两种
                    if ($key == "SVC-INSURE" || $key == "INSURE") {
                        //-1 是订单金额保价
                        if ($item['value'] == -1) {
                            $item['value'] = (string)($orderAmountCount * 100);
                        }
                    }
                    $newServiceList[] = [
                        'service_code' => $key,
                        'service_value' => json_encode($item)
                    ];
                }
                $tradeOrderInfoDto['service_list'] = $newServiceList;
            }
            //子母件 目前支持shunfeng、jd、debangwuliu、shunfengkuaiyun、annengwuliu
            if ($packageNum > 1 && in_array($template['wp_code'], self::ZIMUJIANMAP)) {
                $num = $packageNum;
                $tradeOrderInfoDto['total_pack_count'] = $packageNum;
            }
            ksort($tradeOrderInfoDto);
            $tradeOrderInfoDtos = [];
            $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
            //设置主体信息
            $data = [];
            $data['logistics_code'] = $template['wp_code'];
            $data['order_infos'] = $tradeOrderInfoDtos;
            $data['sender_info'] = $senderInfo;
            if ($isShare) {
                //$data['user_id'] = $waybillShop->identifier;
                $data['user_id'] = '-1';
            }

            if ($isNotDYOrder || Environment::isWxOrWxsp()) {
                //下单渠道编码:https://bytedance.feishu.cn/sheets/shtcngIVwcJlgXLzWhEtKrmv7Af
                $data['order_channel'] = '54';
            }else{
                $data['order_channel'] = '1';
            }

            $returnArr[] = $data;
            ++$num;
        }

        return $returnArr;
    }

    private function getWayBillUpdateRequestData($sender, $order, $template, $waybillCode)
    {
        $isGrayscale = false;
        //是否有加密数据
        if ($order['order_cipher_info']) {
            $isGrayscale = true;
        }
        $returnArr = [];
        //发件人信息
        $senderInfo['address']['city_name'] = $sender['city'];
        $senderInfo['address']['country_code'] = 'CHN';
        $senderInfo['address']['detail_address'] = $sender['address'];
        $senderInfo['address']['district_name'] = $sender['district'];
        $senderInfo['address']['province_name'] = $sender['province'];
        $senderInfo['contact']['mobile'] = $sender['mobile'];
        $senderInfo['contact']['name'] = $sender['sender_name'];
        //面单信息
        $tradeOrderInfoDto['order_id'] = $order['tid'];
        //面单收件人信息
        $tradeOrderInfoDto['receiver_info'] = [
            'address' => [
                'city_name' => $order['receiver_city'],
                'country_code' => 'CHN',
                'detail_address' => $isGrayscale ? $order['order_cipher_info']['receiver_address_ciphertext'] : $order['receiver_address'],
                'district_name' => $order['receiver_district'],
                'province_name' => $order['receiver_state'],
            ],
            'contact' => [
                'mobile' => $isGrayscale ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'],
                'name' => $isGrayscale ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                'phone' => $order['receiver_tel'] ?? ''
            ]
        ];
        $items = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['item_count'] = $good['goods_num'];
                $temp['item_name'] = $good['goods_title'] ? $good['goods_title'] : '';
                $items[] = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $items[] = [
                'item_count' => is_int($order['num']) ? $order['num'] : 1,
                'item_name' => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
            ];
        }
        $tradeOrderInfoDto['receiver_info']['items'] = $items;
        $tradeOrderInfoDtos[] = $tradeOrderInfoDto;
        //设置主体信息
        $data = [];
        $data['track_no'] = $waybillCode;
        $data['logistics_code'] = $template['wp_code'];
        $data['order_infos'] = $tradeOrderInfoDtos;
        $data['sender_info'] = $senderInfo;
        $returnArr[] = $data;

        return $returnArr;
    }

    public function getPrintData($waybillList)
    {
        $client = $this->getClient();
        $waybill_applies[] = [
            'logistics_code' => $waybillList['wp_code'],
            'track_no' => $waybillList['waybill_code']
        ];


        $res = $client->execute('logistics/waybillApply', [
            'waybill_applies' => $waybill_applies,
        ]);
        \Log::info('抖音对外接口获取加密打印数据  result：', [$res]);

        return $res['data']['waybill_infos'][0] ?? $res;
    }


    /**
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBos
     * @param $template
     * @return PrintDataPackBo[]|array
     * <AUTHOR>
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        //非一单多包情况，并行异步请求
        $requestDataList = $this->getFactoryWayBillRequestData($branchAddressBo, $printPackBos, $template);
        $msg_type = 'iop/waybillGet';
        $data = [];
        foreach ($requestDataList as $index => $item) {
            //其中可能会修改access_token 所以在这里创建
            $client = $this->getClient();
            $data[$index] = [
                'params' => $client->buildRequestData($item, $msg_type), //只有一个包裹
                'url' => $client->getBaseUrlByApimethod($msg_type),
            ];
        }
        $responseArr = $this->poolCurl($data, 'json', false, true);
        $responseArr = json_decode(json_encode($responseArr), true);
        //测试数据
//        $responseArr = json_decode('[{"code":10000,"data":{"ebill_infos":[{"distr_order_id":"7126450321504600328","extra":"","extra_resp":"","mark_destination_code":"","mark_destination_code_name":"","pack_id":"27871","package_code":"710088","package_code_name":"\u897f\u5b89\u5206\u62e8\u5305","sort_code":"902A S238-G9 14","sub_waybill_codes":null,"track_no":"318337330663832"}],"err_infos":[]},"err_no":0,"log_id":"202208111424320101511911000F7BF3CB","message":"success","msg":"success","sub_code":"","sub_msg":""}]', true);
//        \Log::debug('dy_response', [$responseArr]);
        $successInfos = [];
        $errorInfos = [];
        foreach ($responseArr as $index => $response) {
            if (!empty($response['data']['ebill_infos'])) {
                $successInfos = array_merge($successInfos, $response['data']['ebill_infos']);
            }
            if (!empty($response['data']['err_infos'])) {
                $errorInfos = array_merge($errorInfos, $response['data']['err_infos']);
            }
        }
        $successInfos = array_pluck($successInfos, null, 'pack_id');
        $errorInfos = array_pluck($errorInfos, null, 'pack_id');

        $printDataPackBoList = [];
        // 获取面单号
        foreach ($printPackBos as $index => $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['track_no'];
                $printDataPackBo->wp_code = $template['wp_code'];
            } elseif (isset($errorInfos[$printDataPackBo->request_id])) {
                $errInfo = $errorInfos[$printDataPackBo->request_id];
                $err_msg = $errInfo['err_msg'] . "({$errInfo['err_code']})";
                $printDataPackBo->setError(ErrorConst::PLATFORM_DY_ERROR, $err_msg);
            } else {
                $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
            }
            $printDataPackBoList[] = $printDataPackBo;
        }
        $waybillBoList = [];
        foreach ($successInfos as $index => $successInfo) {
            $waybillBo = new WaybillBo();
            $waybillBo->wp_code = $template['wp_code'];
            $waybillBo->waybill_code = $successInfo['track_no'];
            $waybillBoList[] = $waybillBo;
        }
//        $waybillBos = $this->getWaybillBosByGetWaybillResponse($responseArr, $msg_type, $template);
        $waybillsPrintDataBoList = [];
        if (!empty($waybillBoList)) {
            $waybillsPrintDataBoList = $this->getPrintDataByWaybillBos($waybillBoList);
            $waybillsPrintDataBoList = array_pluck($waybillsPrintDataBoList, null, 'waybill_code');
        }

        // 组装 print data
        foreach ($printDataPackBoList as $index => $printDataPackBo) {
            if (isset($waybillsPrintDataBoList[$printDataPackBo->waybill_code])) {
                $waybillsPrintDataBo = $waybillsPrintDataBoList[$printDataPackBo->waybill_code];
                $waybillsPrintDataBo->package_id = $printDataPackBo->request_id;
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
            } else {
                //$printDataPackBo->setError(ErrorConst::WAYBILL_GET_PRINT_DATA_ERROR);
            }
            $printDataPackBoList[$index] = $printDataPackBo;
        }
        return $printDataPackBoList;
    }

    /**
     * 请求电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList,array  $template, int $packageNum): array
    {
        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);
//        \Log::info('requestDataList', [$requestDataList]);
        $msg_type = 'logistics/newCreateOrder';
        $data = [];
        $client = $this->getClient();
        $requestBatchIndexArr = []; // 批量请求时，根据pack_id获取请求的index
        foreach ($requestDataList as $index => $item) {
            $data[$index] = [
                'params' => $client->buildRequestData($item, $msg_type),
                'url' => $client->getBaseUrlByApimethod($msg_type),
            ];
            foreach ($item['order_infos'] as $order_info) {
                $requestBatchIndexArr[$order_info['pack_id']] = $index;
            }
        }
        $responseArr = $this->poolCurl($data, 'json', false, true);
        $responseArr = json_decode(json_encode($responseArr), true);
        $successInfos = [];
        $errorInfos = [];
        foreach ($responseArr as $index => $response) {
            if (!empty($response['data']['ebill_infos'])) {
                $successInfos = array_merge($successInfos, $response['data']['ebill_infos']);
            }
            if (!empty($response['data']['err_infos'])) {
                $errorInfos = array_merge($errorInfos, $response['data']['err_infos']);
            }
        }
        $successInfos = array_pluck($successInfos, null, 'pack_id');
        $errorInfos = array_pluck($errorInfos, null, 'pack_id');

        $printDataPackBoList = [];
        // 获取面单号
        foreach ($printPackBoList as $index => $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['track_no'];
                $printDataPackBo->wp_code = $template['wp_code'];
                // 子母件
                if (!empty($successInfo['sub_waybill_codes'])) {
                    $subWaybillCodeArr = explode(',', $successInfo['sub_waybill_codes']);
                    $printDataPackBo->sub_waybill_code_arr = $subWaybillCodeArr;
                }
            } elseif (isset($errorInfos[$printDataPackBo->request_id])) {
                $errInfo = $errorInfos[$printDataPackBo->request_id];
                $err_msg = $errInfo['err_msg'] . "({$errInfo['err_code']})";
                $printDataPackBo->setError(ErrorConst::PLATFORM_DY_ERROR, $err_msg);
            } else {
                $requestIndex = $requestBatchIndexArr[$printPackBo->request_id];
                $thisResponse = $responseArr[$requestIndex];
                $msg = '';
                try {
                    DyClient::handleErrorCode($thisResponse);
                } catch (\Exception $e) {
                    $msg = $e->getMessage();
                }
                if (!empty($msg)) {
                    $printDataPackBo->setError(ErrorConst::PLATFORM_DY_ERROR, $msg);
                } else {
                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
                }
            }
            $printDataPackBoList[] = $printDataPackBo;
        }
        $waybillBoList = [];
        $printDataWaybillCodeArr = collect($printDataPackBoList)->pluck('sub_waybill_code_arr')->flatten()
            ->merge(array_column($printDataPackBoList, 'waybill_code'))->unique()->filter()->toArray();
//        \Log::info('dy_getPrintDataByWaybillBos:$successInfos', [$successInfos, objectToArray($printDataWaybillCodeArr)]);
        foreach ($printDataWaybillCodeArr as $printDataWaybillCode) {
            $waybillBo = new WaybillBo();
            $waybillBo->wp_code = $template['wp_code'];
            $waybillBo->waybill_code = $printDataWaybillCode;
            $waybillBoList[] = $waybillBo;
        }
//        $waybillBos = $this->getWaybillBosByGetWaybillResponse($responseArr, $msg_type, $template);
        // 根据运单号去取打印数据
        $waybillsPrintDataBoList = [];
        if (!empty($waybillBoList)) {
            // 获取打印数据
            $waybillsPrintDataBoList = $this->getPrintDataByWaybillBos($waybillBoList);
            $waybillsPrintDataBoList = array_pluck($waybillsPrintDataBoList, null, 'waybill_code');
        }

//        \Log::info('dy_getPrintDataByWaybillBos', [objectToArray($waybillsPrintDataBoList), objectToArray($printDataPackBoList)]);
        // 组装 print data
        foreach ($printDataPackBoList as $index => $printDataPackBo) {
            if ($printDataPackBo->hasError()){
                continue;
            } elseif (isset($waybillsPrintDataBoList[$printDataPackBo->waybill_code])) {
                $waybillsPrintDataBo = $waybillsPrintDataBoList[$printDataPackBo->waybill_code];
                if ($waybillsPrintDataBo->hasError()){
                    $printDataPackBo->setError([$waybillsPrintDataBo->getErrorCode(), $waybillsPrintDataBo->getErrorMessage()]);
                    continue;
                }
                $waybillsPrintDataBo->package_id = $printDataPackBo->package_id;
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
                $subArr = [];
                foreach ($printDataPackBo->sub_waybill_code_arr as $sub_waybill_code) {
                    if (isset($waybillsPrintDataBoList[$sub_waybill_code])) {
                        $waybillsPrintDataBo = $waybillsPrintDataBoList[$sub_waybill_code];
                        $waybillsPrintDataBo->package_id = $printDataPackBo->package_id;
                        $subArr[] = $waybillsPrintDataBo;
                    }
                }
                $printDataPackBo->setSubWaybillsPrintDataArr($subArr);
            } else {
                $printDataPackBo->setError(ErrorConst::WAYBILL_GET_PRINT_DATA_ERROR);
            }
            $printDataPackBoList[$index] = $printDataPackBo;
        }
        return $printDataPackBoList;
    }

    /**
     * 获取电子面单请求数据
     * @param SenderAddressBo $sender
     * @param PrintPackBo[] $packs
     * @param $template
     * @return array
     * <AUTHOR>
     */
    private function getFactoryWayBillRequestData(SenderAddressBo $sender, array $packs, $template)
    {
        $limit = 10;
        $packs_chunk = array_chunk($packs, $limit);

        $list = [];
        /** @var PrintPackBo[] $packs */
        foreach ($packs_chunk as $index => $packs) {
            //发件人信息
            $senderInfo = [];
            $senderInfo['address']['cityName'] = $sender->city;
            $senderInfo['address']['countryCode'] = 'CHN';
            $senderInfo['address']['detailAddress'] = $sender->address;
            $senderInfo['address']['districtName'] = $sender->district;
            $senderInfo['address']['provinceName'] = $sender->province;
            $senderInfo['contact']['mobile'] = $sender->mobile;
            $senderInfo['contact']['name'] = $sender->sender_name;

            $tradeOrderList = [];
            foreach ($packs as $pack) {
                $firstOrderInfo = $pack->order_infos[0];
                $tradeOrder = [];
                $tradeOrder['user_id'] = $firstOrderInfo['distr_shop_id'];
                $tradeOrder['company_code'] = $template['wp_code'];

                $orderInfo = [];
                foreach ($pack->order_infos as $order_info) {
                    $orderInfo = [
                        'distr_order_id' => $firstOrderInfo['distr_oid'], // 代打订单id
                        'pack_id' => $pack->request_id, // 代打订单id
                    ];
                    $items = [];
                    $items[] = [
                        'item_name' => $order_info['goods_title'],
                        'item_specs' => $order_info['sku_value'],
                        'item_count' => $order_info['goods_num'],
                    ];
                    $orderInfo['items'] = $items;
                }
                $tradeOrder['order_infos'] = $orderInfo;
                $tradeOrderList[] = $tradeOrder;
            }
            $list[] = [
                'sender' => $senderInfo,
                'trade_order_list' => $tradeOrderList,
            ];
        }
        return $list;
    }

    /**
     * 获取电子面单请求数据
     * @param SenderAddressBo $sender
     * @param PrintPackBo[] $printPackBoList
     * @param $template
     * @param int $packageNum
     * @return array
     * <AUTHOR>
     */
    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $sender, array $printPackBoList, $template, int $packageNum): array
    {


        $list = [];
        //发件人信息
        $senderInfo = [
            'address' => [
                'country_code' => 'CHN',
                'province_name' => $sender->province,
                'city_name' => $sender->city,
                'district_name' => $sender->district,
                'street_name' => $sender->street ?? '',
                'detail_address' => $sender->address,
            ],
            'contact' => [
                'name' => $sender->sender_name,
                'mobile' => $sender->mobile,
                'phone' => ''
            ]
        ];
        $order_infos = [];
        $isPtOrder = true;
        $total_pack_count = 1;
        $wpCode = $template['wp_code'];
        //子母件 目前支持shunfeng、jd、debangwuliu、shunfengkuaiyun、annengwuliu
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
//            if (!in_array($template['wp_code'], self::ZIMUJIANMAP)){
//                throw new BusinessException('该快递公司不支持子母件');
//            }
            $total_pack_count = $packageNum;
        }
        foreach ($printPackBoList as $printDataPackBo) {
            $items = [];
            foreach ($printDataPackBo->print_order_item_bo_list as $printOrderItemBo) {
                /** @var PrintOrderItemBo $printOrderItemBo */
                $items[] = [
                    'item_name' => $printOrderItemBo->order_item_info['sku_value'],
                    'item_count' => $printOrderItemBo->num,
                ];
            }
            $order = $printDataPackBo->master_order_info;
            $order = $order->toArray();
            $tid = $this->getTidByOrder($order);
            $isPtOrder = $printDataPackBo->isPlatformOrder();
            $tid = str_replace('A', '', $tid);

            $order_info = [
                'order_id' => $tid,
                'pack_id' => $printDataPackBo->request_id,
                'receiver_info' => [
                    'address' => [
                        'city_name' => $order['receiver_city'],
                        'country_code' => 'CHN',
                        'detail_address' => !empty($order['order_cipher_info']) ? $order['order_cipher_info']['receiver_address_ciphertext'] : stripslashes($order['receiver_address']),
                        'district_name' => $order['receiver_district'],
                        'province_name' => $order['receiver_state'] ?? $order['receiver_province'],
                        'street_name' => $order['receiver_town'] ?? ""
                    ],
                    'contact' => [
                        'mobile' => !empty($order['order_cipher_info']) ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'],
                        'name' => !empty($order['order_cipher_info']) ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                        'phone' => $order['receiver_tel'] ?? '',
                    ]
                ],
                'items' => $items,
            ];
            if ($total_pack_count > 1) {
                $order_info['total_pack_count'] = $total_pack_count;
            }
            //产品类型
            if (!empty($template['time_delivery'])) {
                $order_info['product_type'] = $template['time_delivery'];
            }
            //增值服务
            if (!empty($template['service_list'])) {
                $newServiceList = $this->buildOrderVasList($template['service_list'], $printDataPackBo);
                $order_info['service_list'] = $newServiceList;
            }
//            if ($packageNum > 1){
//                $order_infos[] = $order_info;
//            }
            $order_infos[] = $order_info;
        }
        $limit = 10; // 支持批量取号，一次最多50条。
        Log::info("orderInfo.size=".count($order_infos).",printDataPackBo.size=".count($printPackBoList));
        $order_infos_chunk = array_chunk($order_infos, $limit);
        foreach ($order_infos_chunk as $index => $this_order_infos) {
            $arr = [
                'sender_info' => $senderInfo,
                'order_infos' => $this_order_infos,
                'logistics_code' => $template['wp_code'],
                'user_id' => -1,

                'order_channel' => $isPtOrder ? '1' : '54',
            ];
            if(WaybillUtil::isJd($wpCode)){
                //支付方式：1-寄付月结，2-寄付现结）若不传，默认为商家与物流商网点约定的支付方式
                $arr['pay_method'] = 2;
            }
            $list[] = $arr;
        }
        return $list;
    }

    public function getPrintParamsStr()
    {
        $client = $this->getClient();
        $requestData = $client->buildRequestData([], 'logistics/getShopKey');
        $paramsStr = urldecode(http_build_query($requestData));
        return $paramsStr;
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}
