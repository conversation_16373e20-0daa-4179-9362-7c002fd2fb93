<?php

namespace App\Services\Waybill\Taobao;

use AddressDto;
use App\Constants\ErrorConst;
use App\Models\OrderTraceList;
use App\Models\Shop;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Client\TbClient;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\ArrayUtil;
use CainiaoCloudprintStdtemplatesGetRequest;
use CainiaoWaybillIiCancelRequest;
use CainiaoWaybillIiGetRequest;
use CainiaoWaybillIiSearchRequest;
use CainiaoWaybillIiUpdateRequest;
use Item;
use LogisticsCompaniesGetRequest;
use LogisticsOrdersDetailGetRequest;
use LogisticsTraceSearchRequest;
use OrderInfoDto;
use PackageInfoDto;
use Receiver;
use Sentry\Util\JSON;
use TopClient;
use TradeOrderInfoDto;
use UserInfoDto;
use WaybillApplyRequest;
use WaybillCloudPrintApplyNewRequest;
use WaybillCloudPrintUpdateRequest;
use WlbWaybillISearchRequest;
use function GuzzleHttp\Psr7\build_query;
use Illuminate\Support\Facades\Log;

/**
 * 淘宝电子面单接口（站内）
 */
class TaoBaoApi extends AbstractWaybillService
{

    const EXCLUDE_VAS_TYPE = ['product_type'];
    const INSURE_VAS_TYPE = ["SVC-INSURE", "INSURE"];

    const ZIMUJIAN = ['LE32538030'];
    protected $baseUrl = 'http://ddcn.mayiapps.cn/router/rest';
    protected $apiUrl = '';
    protected $version = '2.0';
    protected $dataFormat = 'JSON';
    protected $signFormat = 'md5';
    private $authConfig;
    private $clientID;
    private $clientSecret;
    private $timestamp;
    protected $is_test = false;
    protected $tbClient;

    //物流状态码 数据库1未揽件 2运输中 3已签收
    const STATUS_NO_COLLECT = 1; //未揽收
    const STATUS_IN_TRANSIT = 2; //运输中
    const STATUS_SIGNED_IN = 3; //已签收

    //物流状态码
    private $orderTraceMap = [
        'CONSIGN' => OrderTraceList::STATUS_SHIPPED,
        'GOT' => OrderTraceList::STATUS_GOT,
        'SEND' => OrderTraceList::STATUS_SEND,
        'SIGN' => OrderTraceList::STATUS_SIGN,
        'ARRIVAL' => OrderTraceList::STATUS_ARRIVAL,
        'DEPARTURE' => OrderTraceList::STATUS_DEPARTURE,
        'FAIL' => OrderTraceList::STATUS_FAIL,
        'REJECTION' => OrderTraceList::STATUS_REJECTION,
        'STAY_IN_WAREHOUSE' => OrderTraceList::STATUS_STAY_IN_WAREHOUSE,
        'SIGN_ON_BEHALF' => OrderTraceList::STATUS_SIGN_ON_BEHALF,
        'OTHER' => OrderTraceList::STATUS_OTHER,
        'RETURN' => OrderTraceList::STATUS_RETURN,
        'IN_CABINET' => OrderTraceList::STATUS_IN_CABINET,
        'OUT_CABINET' => OrderTraceList::STATUS_OUT_CABINET,
    ];

    public function __construct(string $accessToken = '')
    {
        $this->authConfig = config('waybill.taobao');
        $this->clientID = $this->authConfig['appkey'];
        $this->clientSecret = $this->authConfig['secret'];
        $this->accessToken = $accessToken;
        $this->timestamp = date('Y-m-d H:i:s');
    }

    public function isTest()
    {
        $this->is_test = true;
    }

    /**
     * 授权地址
     * @param int $shopId
     * @return string
     */
    public function getLoginUrl($shopId)
    {
        $state = [
            'redirect_uri' => $this->authConfig['redirect_url'],
            'state' => $shopId,
        ];

        return $this->authConfig['code_url'] . '?response_type=code&client_id=' . $this->clientID . '&redirect_uri=' . $this->authConfig['code_redirect_url'] . '&state=' . base64_encode(json_encode($state));
    }

    public function getAccessToken(string $code)
    {
        $params = [
            'grant_type' => 'authorization_code',
            'client_id' => $this->clientID,
            'client_secret' => $this->clientSecret,
            'redirect_uri' => $this->authConfig['code_redirect_url'],
            'code' => $code,
            'state' => 'access_token',
        ];

        $url = $this->authConfig['token_url'] . '?' . build_query($params);
        $client = new \GuzzleHttp\Client();
        $response = $client->post($url, [
//			    'form_params'    => $params,
                'verify' => false,
                'headers' => [
                    'Content-type' => 'application/json',
                    "Accept" => "application/json"
                ],
            ]
        );

        $result = json_decode($response->getBody()->getContents(), true);
        Log::debug('taobao', $result);

        if (!isset($result['access_token'])) {
            Log::error('pdd access_token get Failed', ['request' => $result]);
            throw new \Exception('获取授权码失败，请重试！');
        }

        return [
            'access_token' => $result['access_token'],
            'refresh_token' => $result['refresh_token'],
            'owner_id' => $result['taobao_user_id'],
            'owner_name' => urldecode($result['taobao_user_nick']),
            'expires_in' => floor($result['expires_in'] / 1000),
            'expires_at' => date('Y-m-d H:i:s', floor($result['expire_time'] / 1000)),
        ];
    }

    /**
     * 请求接口
     * @param $apiName
     * @param array $data
     * @return bool|mixed|null
     * @throws \Exception
     */
    public function request($apiName, $data = array())
    {
        $param = $this->createRequestParam($apiName, $data);
        $response = $this->Curl($this->apiUrl, $param, 'POST');

//	    Log::info(var_export($response, true));

        return $this->handleResponse($response);
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array $data
     * @return array
     */
    public function createRequestParam($apiName, $data)
    {
        $params = [
            'method' => $apiName,
            'app_key' => $this->clientID,
            'session' => $this->accessToken,
            'v' => $this->version,
            'sign_method' => $this->signFormat,
            'format' => $this->dataFormat,
            'timestamp' => $this->timestamp,
        ];

        $allParams = array_merge($params, $data);
        $signature = $this->sign($allParams);
        $allParams['sign'] = $signature;
        $this->apiUrl = '';
        $this->apiUrl = $this->baseUrl . '?';
        foreach ($allParams as $k => $v) {
            $this->apiUrl .= "$k=" . urlencode($v) . '&';
        }
        $this->apiUrl = substr($this->apiUrl, 0, -1);

        $this->setRequestData($data);
        return $data;
    }

    /**
     * 签名
     * @param string $content
     * @return mixed
     */
    public function sign($params)
    {
        ksort($params);

//        Log::info(var_export($params, true));

        $sign = $this->clientSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $this->clientSecret;

        return strtoupper(md5($sign));
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {

        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new CainiaoWaybillIiSearchRequest;
        if (!empty($wpCode)) {
            $req->setCpCode("");
        }
        $result = $c->execute($req, $this->accessToken);

//        \Log::info(var_export($result, true));
        if (!isset($result->waybill_apply_subscription_cols) || !isset($result->waybill_apply_subscription_cols->waybill_apply_subscription_info)) {
            return [];
        }
        $waybillsInfo = [];
//        Log::info("面单账号查询",[$result]);
        foreach ($result->waybill_apply_subscription_cols->waybill_apply_subscription_info as $value) {
            $branchAccounts = [];

//            \Log::info(var_export($value->branch_account_cols->waybill_branch_account, true));

            foreach ($value->branch_account_cols->waybill_branch_account as $key => $item) {
                $addresses = [];
                foreach ($item->shipp_address_cols->address_dto as $add) {
                    $addre = [];
                    $addre['province'] = $add->province ?? '';
                    $addre['city'] = $add->city ?? '';
                    $addre['district'] = $add->district ?? '';
                    $addre['detail'] = $add->detail ?? '';
                    $addresses[] = $addre;
                }

                $service_info_cols = [];
                if (isset($item->service_info_cols->service_info_dto)) {
                    foreach ($item->service_info_cols->service_info_dto as $service_info_col) {
                        $service_info_cols[] = [
                            'required' => $service_info_col->required,
                            'service_code' => $service_info_col->service_code,
                            'service_desc' => $service_info_col->service_desc,
                            'service_name' => $service_info_col->service_name,
                            'service_attributes' => isset($service_info_col->service_attributes->service_attribute_dto) ? $service_info_col->service_attributes->service_attribute_dto : []
                        ];
                    }
                }
                $branchAccounts[$key] = [
                    'branch_code' => $item->branch_code ?? 0,
                    'branch_name' => $item->branch_name ?? '',
                    'brand_code'=>$item->brand_code??null,
                    'quantity' => $value->cp_type == 1 ? '-1' : ($item->quantity ?? 0),
                    'cancel_quantity' => $item->cancel_quantity ?? 0,
                    'recycled_quantity' => $item->print_quantity ?? 0,
                    'allocated_quantity' => $item->allocated_quantity ?? 0,
                    'shipp_addresses' => $addresses,
                    'service_info_cols' => $service_info_cols,
                ];
            }
            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $value->cp_code,
                'wp_type' => $value->cp_type,
            ];
        }

        return $waybillsInfo;
    }

    /**
     * 获取面单
     * @param $senderAddress
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     * @throws \Exception
     */
    public function assemWaybillPackages($senderAddress, $orders, $template, $packageNum = 1)
    {
        $result = [];
        foreach ($orders as $order) {
            $num = 1;
            while ($num <= $packageNum) {
                $idStr = handleOrderIdStr($order);
                $c = new TopClient;
                $c->appkey = $this->clientID;
                $c->secretKey = $this->clientSecret;
                $req = new CainiaoWaybillIiGetRequest;
                $param_waybill_cloud_print_apply_new_request = new WaybillCloudPrintApplyNewRequest;
                //物流公司编码
                $wpCode = $template['wp_code'];
                $param_waybill_cloud_print_apply_new_request->cp_code = $wpCode;
                //品牌编码
                if (in_array($wpCode, ['SF'])) {
                    $cloudPrintStdTemplates = $this->getCloudPrintStdTemplatesNew($wpCode);
//                    $templatesArr = json_decode($template['company']['templates'], true);
                    $collection = collect($cloudPrintStdTemplates);
                    $platformTempalte = $collection->where('standard_template_name', $template['name'])->first();

                    \Log::info('顺丰模板', [$template['name'], $collection, $platformTempalte]);


                    $param_waybill_cloud_print_apply_new_request->brand_code = $platformTempalte->brand_code;
                }
                //发货人信息
                $sender = $this->setSender($senderAddress);
                $param_waybill_cloud_print_apply_new_request->sender = $sender;
                //面单信息
                $objectId = isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] . '_' . rand(1111, 9999);
                $trade_order_info_dtos = $this->setTradeOrderInfo($order->toArray(), $template, $objectId);

                $param_waybill_cloud_print_apply_new_request->trade_order_info_dtos = $trade_order_info_dtos;
                //打印报文是否加密
                $param_waybill_cloud_print_apply_new_request->need_encrypt = "true";
                $paramWaybillCloudPrintApplyNewRequest = json_encode($param_waybill_cloud_print_apply_new_request);
                $req->setParamWaybillCloudPrintApplyNewRequest($paramWaybillCloudPrintApplyNewRequest);
                $waybill = $c->execute($req, $this->accessToken);
                \Log::info('tb_result:', [$waybill, $paramWaybillCloudPrintApplyNewRequest]);

                $waybillsData = [];
                if (!isset($waybill->modules)) {
                    Log::error('取号错误：', [$idStr => $waybill]);
                    $result[$idStr][] = $waybill;
                } else {
                    foreach ($waybill->modules->waybill_cloud_print_response as $module) {
                        $printData = json_decode($module->print_data, true);
                        $waybillsData = [
                            'object_id' => $module->object_id,
                            'waybill_code' => $module->waybill_code,
                            'print_data' => $printData['encryptedData'],
                            //云打印内容（encryptedData表示加密结果，data表示非加密结果） 淘宝云打印需要传加密结果，1688云打印需要传非加密结果
                            'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                        ];
                    }
                    $result[$idStr][] = $waybillsData;
                }
                ++$num;
            }
        }

        return $result;
    }

    /**
     * 电子面单云打印取号
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array|bool|string
     */
    public function waybillGet($senderAddress, $order, $template, $packageNum)
    {
        $num = 1;
        while ($num <= $packageNum) {
            $c = new TopClient;
            $c->appkey = $this->clientID;
            $c->secretKey = $this->clientSecret;
            $req = new CainiaoWaybillIiGetRequest;
            $param_waybill_cloud_print_apply_new_request = new WaybillCloudPrintApplyNewRequest;
            //物流公司编码
            $param_waybill_cloud_print_apply_new_request->cp_code = $template['wp_code'];
            //发货人信息
            $sender = $this->setSender($senderAddress);
            $param_waybill_cloud_print_apply_new_request->sender = $sender;
            //面单信息
            $trade_order_info_dtos = $this->setTradeOrderInfo($order, $template);
            $param_waybill_cloud_print_apply_new_request->trade_order_info_dtos = $trade_order_info_dtos;
            //打印报文是否加密
            $param_waybill_cloud_print_apply_new_request->need_encrypt = "true";
            $req->setParamWaybillCloudPrintApplyNewRequest(json_encode($param_waybill_cloud_print_apply_new_request));
            $waybill = $c->execute($req, $this->accessToken);

            \Log::info('tb_result:', [$waybill]);
            if (!isset($waybill->modules)) {
                Log::error('API=>' . $req->getApiMethodName(), [$waybill]);
            }
            $waybillsData = [];
            foreach ($waybill->modules->waybill_cloud_print_response as $module) {
                $printData = json_decode($module->print_data, true);
                $waybillsData = [
                    'object_id' => $module->object_id,
                    'waybill_code' => $module->waybill_code,
                    'print_data' => $printData['encryptedData'],
                    'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                ];
            }
            $result[] = $waybillsData;
            ++$num;
        }

        return $result;
    }

    /**
     * 电子面单更新接口
     * @param array $order
     * @param Shipping $shipping
     * @return array
     * @throws CytException
     */
    public function waybillUpdate(array $order, Shipping $shipping)
    {
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new CainiaoWaybillIiUpdateRequest;
        $param_waybill_cloud_print_update_request = new WaybillCloudPrintUpdateRequest;
        $param_waybill_cloud_print_update_request->cp_code = $shipping->cn_code;
        $param_waybill_cloud_print_update_request->logistics_services = "";
        //包裹信息
        $package_info = $this->setPackageInfo($order, 1);
        $param_waybill_cloud_print_update_request->package_info = $package_info;
        //收件人信息
        $recipient = $this->setReceiver($order);
        $param_waybill_cloud_print_update_request->recipient = $recipient;
        //发件人信息
        $sender = $this->setUpdateSender($order);
        $param_waybill_cloud_print_update_request->sender = $sender;
        $param_waybill_cloud_print_update_request->template_url = $shipping->cn_url;
        $param_waybill_cloud_print_update_request->waybill_code = $order['tracking_number'];
        $param_waybill_cloud_print_update_request->object_id = "x";
        $req->setParamWaybillCloudPrintUpdateRequest(json_encode($param_waybill_cloud_print_update_request));
        $result = $c->execute($req, $this->accessToken);

        if (!isset($result->cainiao_waybill_ii_update_response)) {
            return false;
        }

        return [
            'labelData' => json_decode($result->cainiao_waybill_ii_update_response->print_data, true),
            'trackNumber' => $result->cainiao_waybill_ii_update_response->waybill_code
        ];
    }

    /**
     * 作废电子面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return bool
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode, string $platformWaybillId = '')
    {
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new CainiaoWaybillIiCancelRequest;
        $req->setCpCode($cpCode);
        $req->setWaybillCode($waybillCode);
        $result = $c->execute($req, $this->accessToken);
        Log::info('tb_cancel:', [$result]);

        if (!isset($result->cancel_result)) {
            throw new \Exception($result->sub_msg ?? "取消失败");
        }

        return true;
    }

    //获取标准电子模板
    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new CainiaoCloudprintStdtemplatesGetRequest;
        $result = $c->execute($req);

        if (!isset($result->result)) {
            throw new \Exception('面单服务查询失败!');
        }
        if (empty($result->result->datas->standard_template_result)) {
            throw new \Exception('面单服务查询失败!');
        }
        $standardTemplates = [];
        if ($wpCode) {
            foreach ($result->result->datas->standard_template_result as $value) {
                if ($wpCode == $value->cp_code) {
                    $standardTemplates = array_column($value->standard_templates->standard_template_do, null, 'standard_waybill_type');
                }
            }
        }
        Log::info("获取最新的模板", [$wpCode, $standardTemplates, $result]);
        return $standardTemplates;
    }

    //获取标准电子模板
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new CainiaoCloudprintStdtemplatesGetRequest;
        $result = $c->execute($req);

        if (!isset($result->result)) {
            throw new \Exception('面单服务查询失败!');
        }
        if (empty($result->result->datas->standard_template_result)) {
            throw new \Exception('面单服务查询失败!');
        }
        $standardTemplates = [];
        if ($wpCode) {
            foreach ($result->result->datas->standard_template_result as $value) {
                if ($wpCode == $value->cp_code) {
                    $standardTemplates = array_column($value->standard_templates->standard_template_do, null);
                }
            }
        }

        return $standardTemplates;
    }

    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        //todo 物流轨迹订阅
    }

    /**
     * @see https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.76fd669aCbokbi&source=search&docId=10463&docType=2
     * <AUTHOR>
     * @param array $waybill
     * @return array|mixed
     */
    public function sendGetOrderTraceList(array $waybill)
    {
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new LogisticsTraceSearchRequest;
        if (empty($waybill['tid'])) {
//            echo '订单号不能为空'.PHP_EOL;
            return [];
        }
        $req->setTid($waybill['tid']);
        $resp = $c->execute($req, $this->accessToken);
        $resp = json_decode(json_encode($resp), true);
        $this->handleResp($resp);
        $result = $this->formatToOrderTrace(array_merge($waybill, [
            'trace_list' => $resp,
        ]));

        return $result;
    }

    /**
     * 物流数据整理
     * @param array $orderTrace
     * @return array
     */
    public function formatToOrderTrace(array $orderTrace): array
    {
//        Log::info('$orderTrace:',[$orderTrace]);
//        $latest        = collect($orderTrace['trace_list']['trace_list']['transit_step_info'])->sortByDesc('status_time')->first();
        $trace_list = [];
        foreach ($orderTrace['trace_list']['trace_list']['transit_step_info'] as $index => $item) {
            $status = $this->orderTraceMap[$item['action']] ?? OrderTraceList::STATUS_OTHER;

            $trace_list[] = [
                "status" => $status, // 状态
                "status_desc" => OrderTraceList::STATUS_NAME_MAPPING[$status], //状态描述
                "action" => $item['action'], // 平台那边的状态
                "action_desc" => '', // 平台那边的状态描述
                "site_name" => '', // 站点名称
                "status_time" => $item['status_time'], // 状态发生时间
                "time" => $item['status_time'], // 数据创建时间
                "desc" => $item['status_desc'], // 流转过程
            ];
        }
        $latest = collect($trace_list)->sortByDesc('time')->first();

        return [
            "type" => $orderTrace['type'],
            'tid' => $orderTrace['tid'],
            'express_code' => $orderTrace['express_code'],
            'express_no' => $orderTrace['express_no'],
            'status' => $latest['status'],
            'action' => $latest['action'],
            'receiver_province' => $orderTrace['receiver_province'],
            'receiver_name' => $orderTrace['receiver_name'],
            'send_at' => $orderTrace['send_at'],
            'latest_updated_at' => $latest['status_time'],
            'latest_trace' => $latest['desc'],
            'trace_list' => json_encode($trace_list),
        ];
    }

    /**
     * @return TbClient
     * @throws \Exception
     */
    public function getTbClient()
    {
        $appKey = config('socialite.taobao_top.client_id');
        $secretKey = config('socialite.taobao_top.client_secret');
        $tbClient = new TbClient($appKey, $secretKey, $this->accessToken);
        $tbClient->setAccessToken($this->accessToken);
        $this->tbClient = $tbClient;
        return $this->tbClient;
    }

    /**
     * @param $resp
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\OrderException
     * <AUTHOR>
     */
    protected function handleResp($resp): void
    {
        $this->getTbClient()->handleResp($resp);
    }

    /**
     * 获取物流状态
     * @param $action
     * @return int
     */
    private function getTraceStatus($action)
    {
        //未揽收
        if (in_array($action, ['CREATE', 'X_TO_SYSTEM', 'X_OUT_WAREHOUSE', 'CONSIGN'])) {
            return OrderTraceList::STATUS_SHIPPED;
        }
        //运送中
        if (in_array($action, ['TMS_ACCEPT', 'WMS_ACCEPT', 'WMS_FAILED', 'TMS_DELIVERING', 'TMS_STATION_IN', 'TMS_STATION_OUT', 'TMS_CHANGE',
            'TMS_ERROR', 'TMS_FAILED', 'TMS_DELIVERY', 'TMS_FAILED_IN', 'TMS_RETURN'])) {
            return OrderTraceList::STATUS_ARRIVAL;
        }
        //已签收
        if (in_array($action, ['SH_INBOUND', 'SH_SIGNED', 'GTMS_SIGNED', 'TMS_SIGN', 'STA_SIGN'])) {
            return OrderTraceList::STATUS_SIGN;
        }

        return OrderTraceList::STATUS_OTHER;
    }

    private function setTradeOrderInfo($order, $template, $objectId)
    {
        $trade_order_info_dtos = new TradeOrderInfoDto;
        $trade_order_info_dtos->logistics_services = "";
        $trade_order_info_dtos->object_id = $objectId;
        $wpCode = $template['wp_code'];
        //订单信息
        $order_info = new OrderInfoDto;
        //订单渠道
        $order_info->order_channels_type = isset($order['order_no']) ? "OTHERS" : "TB";
        if (isset($order['tid'])) {
            $order_tid = $order['tid'];
        } elseif (!empty($order['order_no'])) {
            $order_tid = $order['order_no'];
        } else {
            $order_tid = $order['id'];
        }
        $order_info->trade_order_list = [
//            isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999)
            $order_tid
        ];
        $trade_order_info_dtos->order_info = $order_info;
        //包裹信息
        $package_info = $this->setPackageInfo($order, $objectId);
        $trade_order_info_dtos->package_info = $package_info;
        if (in_array($wpCode, self::ZIMUJIAN)) {
            //如果是子母件，总包裹数为1，实际应该需要调整
            $trade_order_info_dtos->package_info->total_packages_count = 1;
            \Log::info("子母件增加包裹数据");
        }
        //收件人信息
        $receiver = $this->setReceiver($order);
        $trade_order_info_dtos->recipient = $receiver;
        //模板url
        $trade_order_info_dtos->template_url = $template['template_url'];
        //实际取号店铺的id
        if (isset($template['shop_id'])) {
            $shop = Shop::query()->where('id', $template['shop_id'])->first();
        } else {
            $shop = Shop::query()->where('shop_code', \request()->input('wpShopCode'))->first();
        }
        $trade_order_info_dtos->user_id = $shop->identifier;

        return $trade_order_info_dtos;
    }

    //发件人信息
    private function setSender($senderAddress)
    {
        $address = new AddressDto;
        $address->city = $senderAddress['city'];
        $address->detail = $senderAddress['address'];
        $address->district = $senderAddress['district'];
        $address->province = $senderAddress['province'];
        $address->town = "";
        $sender = new UserInfoDto;
        @$sender->address = $address;
        $sender->mobile = $senderAddress['mobile'];
        $sender->name = $senderAddress['sender_name'];
        $sender->phone = "";

        return $sender;
    }

    private function setSender2($senderAddress)
    {
        $address = new AddressDto;
        $address->city = $senderAddress['city'];
        $address->detail = $senderAddress['address'];
        $address->district = $senderAddress['district'];
        $address->province = $senderAddress['province'];
        $address->town = "";
        return [
            'address' => $address,
            'mobile' => $senderAddress['mobile'],
            'name' => $senderAddress['sender_name'],
            'phone' => ''
        ];
    }

    //收件人信息
    public function setReceiver($order)
    {
        $oaid = $order['order_cipher_info']['oaid'] ?? '';
        $recipient = new UserInfoDto;
        $recipient->tid = $order['tid'] ?? ''; // 自由打印没有 tid
        $recipient->oaId = $oaid;
        $address = new AddressDto;
        $address->city = $order['receiver_city'];
        $address->detail = $order['receiver_address'];
        $address->district = $order['receiver_district'];
        $address->province = $order['receiver_state'] ?? $order['receiver_province'];
        $address->town = "";
        $recipient->address = $address;
        $recipient->mobile = $order['receiver_phone'];
        $recipient->name = $order['receiver_name'];
        $recipient->phone = $order['receiver_tel'] ?? '';
        $recipient->oaid = $oaid;

        return $recipient;
    }

    /**
     * 添加加密联系人
     * @param $order
     * @return UserInfoDto
     */
    public function setEncrypedReceiver($order)
    {
        $oaid = $order['order_cipher_info']['oaid'] ?? '';

        $recipient = new UserInfoDto;
        $recipient->tid = $order['tid'] ?? ''; // 自由打印没有 tid
        $recipient->oaid = $oaid;
        if (empty($oaid)) {
            $address = new AddressDto;
            $address->city = $order['receiver_city'];
            $address->detail = $order['receiver_address'];
            $address->district = $order['receiver_district'];
            $address->province = $order['receiver_state'] ?? $order['receiver_province'];
            $address->town = "";
            $recipient->address = $address;
            $recipient->mobile = $order['receiver_phone'];
            $recipient->name = $order['receiver_name'];
            $recipient->phone = $order['receiver_tel'] ?? '';
            $recipient->oaid = $oaid;
        }

        return $recipient;
    }

    //包裹信息
    private function setPackageInfo($order, $objectId): PackageInfoDto
    {
        $item = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['count'] = $good['goods_num'];
                $temp['name'] = $good['goods_title'] ? $good['goods_title'] : '';
                $item = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $item = [
                'count' => (!empty($order['num']) && is_int($order['num'])) ? $order['num'] : 1,
                'name' => !empty($order['goods_info']) ?
                    (json_decode($order['goods_info'], true)[0]['title'] ?? $order['production_type']) : $order['production_type'],
            ];
        }

        $package_info = new PackageInfoDto;
        $package_info->id = $objectId;//包裹id
        $package_info->items = [
            'count' => $item['count'],
            'name' => $item['name']
        ];
        $package_info->volume = "1";
        $package_info->weight = "1";
        if (isset($order['order_no'])) {
            $goodsDescription = json_decode($order['goods_info'], true)[0]['title'] ?? $order['production_type'];
        } else {
            $goodsDescription = $order['goods_title'] ?? "";
        }
        //商品描述
        //$package_info->goods_description=$goodsDescription;
        $package_info->goods_description = "";

        return $package_info;
    }

    private function setUpdateSender(array $order)
    {
        $sender = new UserInfoDto;
        $sender->mobile = $order['sender_info']['mobile'];
        $sender->name = $order['sender_info']['name'];
        $sender->phone = $order['sender_info']['phone'] ?? "";

        return $sender;
    }

    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1, $productType = null)
    {
        //一单多包，普通取号
        $template = [
            'wp_code' => $wpCode,
        ];
        $template['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode);

        $result = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $c = new TopClient;
            $c->appkey = $this->clientID;
            $c->secretKey = $this->clientSecret;
            $req = new CainiaoWaybillIiGetRequest;
            $param_waybill_cloud_print_apply_new_request = new WaybillCloudPrintApplyNewRequest;
            //物流公司编码
            $wp_code = $template['wp_code'];
            $param_waybill_cloud_print_apply_new_request->cp_code = $wp_code;
            if ($wp_code == 'SF') {
                $param_waybill_cloud_print_apply_new_request->product_code = '285';
            }
            //发货人信息
            $senderDto = $this->setSender($sender);
            $param_waybill_cloud_print_apply_new_request->sender = $senderDto;
            //面单信息
            $objectId = $order['request_id'] ?? $order['id'] . '_' . rand(1111, 9999);
            $trade_order_info_dtos = $this->setTradeOrderInfo($order->toArray(), $template, $objectId);
            $param_waybill_cloud_print_apply_new_request->trade_order_info_dtos = $trade_order_info_dtos;
            //打印报文是否加密
            $param_waybill_cloud_print_apply_new_request->need_encrypt = "true";
            $req->setParamWaybillCloudPrintApplyNewRequest(json_encode($param_waybill_cloud_print_apply_new_request));
            $waybill = $c->execute($req, $this->accessToken);
            \Log::info('tb_result:', [$waybill]);

            $waybillsData = [];
            if (!isset($waybill->modules)) {
                Log::error('取号错误：', [$idStr => $waybill]);
                $result[$idStr][] = $waybill;
            } else {
                foreach ($waybill->modules->waybill_cloud_print_response as $module) {
                    $printData = json_decode($module->print_data, true);
                    $insertPrint = [
                        'encryptedData' => $printData['encryptedData'],
                        'templateUrl' => $printData['templateURL'],
                        'templateURL' => $printData['templateURL'],
                        'signature' => $printData['signature'],
                        'ver' => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id' => $module->object_id,
                        'waybill_code' => $module->waybill_code,
                        'print_data' => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[$idStr][] = $waybillsData;
            }
        }
        return $result;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        \Log::info('updateWaybillData:', [$sender, $order, $template, $waybillCode]);
        $orderArray = $order->toArray();
        $updateRequest = new CainiaoWaybillIiUpdateRequest();
        $waybillCloudPrintUpdateRequest = new WaybillCloudPrintUpdateRequest();
        $updateRequest->setParamWaybillCloudPrintUpdateRequest($waybillCloudPrintUpdateRequest);
        $waybillCloudPrintUpdateRequest->cp_code = $template['wp_code'];
        $package_info = $this->setPackageInfo($orderArray, null);
        $waybillCloudPrintUpdateRequest->package_info = $package_info;
        $receiver = $this->setEncrypedReceiver($orderArray);
        $waybillCloudPrintUpdateRequest->waybill_code = $waybillCode;
        $waybillCloudPrintUpdateRequest->recipient = $receiver;
        $updateRequest->setParamWaybillCloudPrintUpdateRequest(json_encode($waybillCloudPrintUpdateRequest));
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $waybill = $c->execute($updateRequest, $this->accessToken);
        $waybill = json_decode(json_encode($waybill), true);
        \Log::info('tb_result:', $waybill);
        $printData = json_decode($waybill['print_data'], true);
        return [
            'object_id' => '',
            'waybill_code' => $waybill['waybill_code'],
            'print_data' => ArrayUtil::getArrayValue($printData, 'encryptedData', ''),
            'parent_waybill_code' => ''
        ];
    }

    /**
     * 请求厂家电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBos
     * @param $template
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        //先从redis中读取
        $redis = \redis('cache');
        $key = 'logistic-company-key';
        $listString = $redis->get($key);
        if (!empty($listString)) {
            $list = json_decode($listString, true);
            if (empty($wpCode)) {
                return $list;
            }
            $match = collect($list)->where('code', $wpCode)->first();
            if (!empty($match)) {
                return [$match];
            }
            return [];
        }
        //redis中不存在，直接去平台读取
        $c = new TopClient;
        $c->appkey = $this->clientID;
        $c->secretKey = $this->clientSecret;
        $req = new LogisticsCompaniesGetRequest;
        $req->setFields("id,code,name");
        $resp = $c->execute($req);
        $resp = json_decode(json_encode($resp), true);
        if (empty($resp['logistics_companies']['logistics_company'])){
            Log::error('物流公司列表为空',[$resp]);
        }
        $list = $resp['logistics_companies']['logistics_company'];
        if (empty($wpCode)) {
            \Log::info('存储数据：');
            $redis->set($key, jsonEncode($list));
        }
        if (empty($wpCode)) {
            return $list;
        }
        $match = collect($list)->where('code', $wpCode)->first();
        if (!empty($match)) {
            return [$match];
        }
        return [];
    }

    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        //组装数据
        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);
        $tbClient = $this->getTbClient();
        $requestBatchIndexArr = [];
        $data = [];
        foreach ($requestDataList as $index => $item) {
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($item);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
            $obj = json_decode($item->getParamWaybillCloudPrintApplyNewRequest(), true);
            $requestBatchIndexArr[$obj['trade_order_info_dtos'][0]['object_id']] = $index;
        }
        Log::info('请求淘宝取号的参数', [$data]);
        $responseArr = $this->poolCurl($data, 'post_form');
        $responseArr = json_decode(json_encode($responseArr), true);
        Log::info('取号返回的response', [$responseArr]);
        $successInfos = [];
        foreach ($responseArr as $response) {
            if (!empty($response['modules']['waybill_cloud_print_response'])) {
                $successInfos = array_merge($successInfos, $response['modules']['waybill_cloud_print_response']);
            }
        }
        $successInfos = array_pluck($successInfos, null, 'object_id');
        Log::info("淘宝新版取号获取的成功的结果", [$successInfos]);
        $printDataPackBoList = [];
        foreach ($printPackBoList as $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['parent_waybill_code'] ?? $successInfo['waybill_code'];
                $printDataPackBo->wp_code = $successInfo['cp_code'];
                // 子母件
                if (isset($successInfo['parent_waybill_code'])) {
                    $printDataPackBo->sub_waybill_code_arr = [$successInfo['waybill_code']];
                }
                $printData = json_decode($successInfo['print_data'], true);
                $waybillsPrintDataBo = new WaybillsPrintDataBo;
                $waybillsPrintDataBo->waybill_code = $successInfo['waybill_code'];
                $waybillsPrintDataBo->parent_waybill_code = $successInfo['parent_waybill_code'] ?? '';
                $waybillsPrintDataBo->package_id = $successInfo['object_id'];
                $waybillsPrintDataBo->encrypted_data = $printData['encryptedData'];
                $waybillsPrintDataBo->templateURL = $printData['templateURL'];
                $waybillsPrintDataBo->sign = $printData['signature'];
                $waybillsPrintDataBo->version = $printData['ver'];
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
            } else {
                $requestIndex = $requestBatchIndexArr[$printPackBo->request_id];
                $thisResponse = $responseArr[$requestIndex];
                $msg = '';
                if (gettype($thisResponse) == 'string') {
                    $msg = $thisResponse;
                } else {
                    try {
                        $this->handleResponse($thisResponse);
                    } catch (\Exception $e) {
                        $msg = $e->getMessage();
                    }
                }
                if (!empty($msg)) {
                    $printDataPackBo->setError(ErrorConst::PLATFORM_SERVER_ERROR, $msg);
                } else {
                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
                }
            }
            $printDataPackBoList[] = $printDataPackBo;
        }
        Log::info("淘宝取号初步得到的最终结果", [$printDataPackBoList]);
        //考虑子母件一单多包的情况,需要聚合
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
            $result = [];
            $parentCodeMap = collect($printDataPackBoList)->groupBy('waybill_code')->toArray();
            foreach ($parentCodeMap as $parentWaybillCode => $itemList) {
                $obj = $itemList[0];
                $obj->sub_waybill_code_arr = collect($itemList)->pluck("sub_waybill_code_arr")->flatten()->unique()->toArray();
                $printList = [];
                foreach ($itemList as $item) {
                    $printData = $item->getWaybillsPrintData();
                    if ($printData->waybill_code == $obj->getWaybillsPrintData()->waybill_code) {
                        //去除第一个，放到waybills_print_data里面
                        continue;
                    }
                    $printList[] = $printData;
                }
                $obj->setSubWaybillsPrintDataArr($printList);
                $result[] = $obj;
            }
            return $result;
        }
        return $printDataPackBoList;
    }

    /**
     * 获取电子面单请求数据
     * @param SenderAddressBo $sender
     * @param PrintPackBo[] $printPackBoList
     * @param $template
     * @param int $packageNum
     * @return array
     * <AUTHOR>
     */
    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $sender, array $printPackBoList, $template, int $packageNum): array
    {
        $list = [];
        $logList = [];
        $senderDto = $this->setSender2((array)$sender);
        $total_pack_count = 1;
        //子母件
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
            $total_pack_count = $packageNum;
        }
        //实际取号店铺的id
        $shop = Shop::query()->where('id', $template['shop_id'])->first();
        foreach ($printPackBoList as $item) {
            $req = new CainiaoWaybillIiGetRequest;
            $param_waybill_cloud_print_apply_new_request = [];
            $param_waybill_cloud_print_apply_new_request['cp_code'] = $template['wp_code'];
            $param_waybill_cloud_print_apply_new_request['brand_code'] = array_get($template,'company.brand_code');
//            if (in_array($template['wp_code'], ['SF'])) {
//                $cloudPrintStdTemplates = $this->getCloudPrintStdTemplatesNew($template['wp_code']);
//                $collection = collect($cloudPrintStdTemplates);
//                $platformTempalte = $collection->where('standard_template_name', $template['name'])->first();
//                $param_waybill_cloud_print_apply_new_request->brand_code = $platformTempalte->brand_code;
//            }
            //设定取号返回的云打印报文是否加密
            $param_waybill_cloud_print_apply_new_request['need_encrypt'] = "true";
            //组装发件人信息
            $param_waybill_cloud_print_apply_new_request['sender'] = $senderDto;
            //组装请求面单信息，数量限制为10 trade_order_info_dtos
            $items = [];
            foreach ($item->print_order_item_bo_list as $printOrderItemBo) {
                /** @var PrintOrderItemBo $printOrderItemBo */
                $items[] = [
                    'name' => isset($printOrderItemBo->order_item_info['sku_value']) && $printOrderItemBo->order_item_info['sku_value'] ? $printOrderItemBo->order_item_info['sku_value'] : $printOrderItemBo->order_item_info['goods_title'],
                    'count' => strval($printOrderItemBo->num),
                ];
            }
            $trade_order_info_dto = new TradeOrderInfoDto;
            $trade_order_info_dto->object_id = $item->request_id;
            //收件人信息
            $trade_order_info_dto->recipient = $this->setReceiver($item->master_order_info->toArray());
            //模板url
            $trade_order_info_dto->template_url = $template['template_url'];
            //userId
            $trade_order_info_dto->user_id = $shop->identifier;
//           //增值服务
            if (!empty($template['service_list']) && $template['service_list'] != '{}') {
                $newServiceList = $this->buildOrderVasList($template['service_list'], $item);
                $trade_order_info_dto->logistics_services = $newServiceList;
            } else {
                $trade_order_info_dto->logistics_services = '';
            }
            //组装订单信息
            $trade_order_info_dto->order_info = [];
            $order_info = new OrderInfoDto;
            $isPtOrder = $item->isPlatformOrder();
            //订单渠道
            $order_info->order_channels_type = $isPtOrder ? "TB" : "OTHERS";
            $order_info->trade_order_list = $isPtOrder ? collect($item->order_infos)->pluck("tid")->toArray() : collect($item->order_infos)->pluck("id")->toArray();
            $trade_order_info_dto->order_info = $order_info;
            //组装包裹信息
            $package_info = new PackageInfoDto;
            $package_info->id = $item->master_order_info['id'] . '_' . rand(1111, 9999);//包裹id
            $package_info->items = $items;
            $package_info->volume = '1';
            $package_info->weight = '1';
            $package_info->goods_description = "";
            $package_info->total_packages_count = strval($total_pack_count);
            $trade_order_info_dto->package_info = $package_info;

            $param_waybill_cloud_print_apply_new_request['trade_order_info_dtos'] = [$trade_order_info_dto];
            $req->setParamWaybillCloudPrintApplyNewRequest(json_encode($param_waybill_cloud_print_apply_new_request));
            $logList[] = $req->getParamWaybillCloudPrintApplyNewRequest();
            $list[] = $req;

            //如果是子母件
            if ($total_pack_count > 1) {
                //只有$trade_order_info_dto里面的package_info的id不相同，object_id不相同，订单号一样
                for ($i = 0; $i < $total_pack_count - 1; $i++) {
                    $reqCopy = clone $req;
                    $request = $reqCopy->getParamWaybillCloudPrintApplyNewRequest();
                    $request['trade_order_info_dtos'][0]['package_info']['id'] .= $i;
                    $request['trade_order_info_dtos'][0]['object_id'] .= $i;
                    $reqCopy->setParamWaybillCloudPrintApplyNewRequest($request);
                    $list[] = $reqCopy;
                    $logList[] = $request;
                }
            }
        }
        Log::info('淘宝新取号接口，请求的list参数：', [$logList]);
        return $list;
    }

    /**
     * 构建增值服务
     * @param string|null $service_list
     * @param PrintPackBo $printDataPackBo
     * @return array
     */
    private function buildOrderVasList(?string $service_list, PrintPackBo $printDataPackBo): array
    {
        $newServiceList = [];
        if (empty($service_list)) {
            return $newServiceList;
        }
        $serviceList = json_decode($service_list, true);
        \Log::info("增值服务", ["serviceList" => $serviceList]);
        foreach ($serviceList as $key => $item) {
            if (in_array($key, self::EXCLUDE_VAS_TYPE)) {
                continue;
            }

            //保价有两种
            if (in_array($key, self::INSURE_VAS_TYPE)) {
                $insureAmount = $printDataPackBo->getInsureAmount();
                \Log::info("处理保价", ["insureAmount" => $insureAmount, "item" => $item]);
                //-1 是订单金额保价
                if ($item['value'] != -1) {
                    \Log::info("保价金额不是-1,按设定的金额处理", ["item" => $item]);
                } else {
                    if (bccomp($insureAmount, "0") > 0) {
                        \Log::info("保价金额是-1,订单金额>0 按订单金额处理", ["insureAmount" => $insureAmount]);
                        $item['value'] = round_bcmul($insureAmount, "100", 0); //  (string)($insureAmount * 100);
                    } else {
                        \Log::info("保价金额是-1,订单金额=0 不处理这个保价", ["insureAmount" => $insureAmount]);
                        continue;
                    }
                }
            }
            $newServiceList[$key] = $item;
        }
        return $newServiceList;
    }
}
