<?php

namespace App\Services\Waybill\Taobao;

use App\Services\Bo\SenderAddressBo;
use App\Services\Waybill\AbstractWaybillService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * @deprecated 弃用
 */
class TBApi extends AbstractWaybillService
{
    protected $baseUrl    = 'http://khopen.souyousoft.com/topapi/getdata_out';
    protected $apiUrl     = '';
    protected $version    = '2.0';
    protected $dataFormat = 'JSON';
    protected $signFormat = 'md5';
    private   $authConfig;
    private   $clientID;
    private   $clientSecret;
    private   $timestamp;
    protected $is_test    = false;

    public function __construct(string $accessToken = '')
    {
        $this->authConfig   = config('waybill.twc');
        $this->clientID     = $this->authConfig['appkey'];
        $this->clientSecret = $this->authConfig['secret'];
        $this->accessToken  = $accessToken;
        $this->timestamp    = Carbon::now();
    }

    public function isTest()
    {
        $this->is_test = true;
    }

    /**
     * 授权地址
     * @param $shopId
     * @return string
     */
    public function getLoginUrl($shopId)
    {
        return $this->authConfig['token_url'] . '&state=OutPlatformName=menggouchaquan,UserName=mrlisf,outstate=' . $shopId . ',CallbackUrl=' . $this->authConfig['redirect_url'];
    }

    public function getAccessToken(string $code)
    {
        return [];
    }

	/**
	 * 请求接口
	 * @param $apiName
	 * @param array $data
	 * @return bool|mixed|null
	 * @throws \Exception
	 */
    public function request($apiName, $data = array())
    {
        $param    = $this->createRequestParam($apiName, $data);
        $response = $this->Curl($this->apiUrl, $param, 'POST');

//	    Log::info(var_export($response, true));

	    return $this->handleResponse($response);
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array  $data
     * @return array
     */
    public function createRequestParam($apiName, $data)
    {
        $params         = [
            'method'      => $apiName,
            'app_key'     => $this->clientID,
            'session'     => $this->accessToken,
            'v'           => $this->version,
            'sign_method' => $this->signFormat,
            'format'      => $this->dataFormat,
            'timestamp'   => $this->timestamp,
        ];
        $signature      = $this->sign(array_merge($params, $data));
        $params['sign'] = $signature;
        $this->apiUrl  = '';
        $this->apiUrl  = $this->baseUrl . '?';
        foreach ($params as $k => $v) {
            $this->apiUrl .= "$k=" . urlencode($v) . '&';
        }
        $this->apiUrl = substr($this->apiUrl, 0, -1);

        $this->setRequestData($data);
        return $data;
    }

    /**
     * 签名
     * @param string $content
     * @return mixed
     */
    public function sign($params)
    {
        ksort($params);
        $sign = $this->clientSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $this->clientSecret;

        return strtoupper(md5($sign));
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws \Exception
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        $type   = 'cainiao.waybill.ii.search';
        $data   = array(
            'cp_code' => $wpCode
        );
        $result = $this->request($type, $data);
        Log::info(var_export($result, true));
	    if (!isset($result->waybill_apply_subscription_cols) || empty($result->waybill_apply_subscription_cols)){
		    return [];
	    }
        $waybillsInfo = [];
        foreach ($result->waybill_apply_subscription_cols->waybill_apply_subscription_info as $value) {
            $branchAccounts = [];
            foreach ($value->branch_account_cols->waybill_branch_account as $key => $item) {
                $branchAccounts[$key] = [
                    'branch_code'        => $item->branch_code ?? 0,
                    'branch_name'        => $item->branch_name ?? '',
                    'quantity'           => $item->quantity ?? 0,
                    'cancel_quantity'    => $item->cancel_quantity ?? 0,
                    'recycled_quantity'  => $item->print_quantity ?? 0,
                    'allocated_quantity' => $item->allocated_quantity ?? 0,
                    'shipp_addresses'    => $item->shipp_address_cols->address_dto
                ];
            }
            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code'             => $value->cp_code
            ];
        }

        return $waybillsInfo;
    }

    /**
     * 获取面单
     * @param     $sender
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
	    //一单多包，普通取号
	    $result = [];
	    if ($packageNum > 1) {
		    foreach ($orders as $order) {
			    $idStr = handleOrderIdStr($order);
			    //单个订单获取面单的详情数组
			    $result[$idStr] = $this->waybillGet($sender, $order->toArray(), $template, $packageNum);
		    }

		    return $result;
	    }

	    //非一单多包情况，并行异步请求
	    $type   = 'cainiao.waybill.ii.get';
	    $data   = [];
	    foreach ($orders as $order) {
		    $idStr     = handleOrderIdStr($order);
		    $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);

		    $data[$idStr] = [
			    'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
			    'url'    => $this->apiUrl,
		    ];
	    }

	    $response = $this->poolCurl($data, 'POST');

	    Log::debug('old_tb_response', [$response]);
	    foreach ($response as $orderIdStr => $waybill) {
		    if (is_object($waybill)) {
			    foreach ($waybill->modules->waybill_cloud_print_response as $module) {
				    $printData    = json_decode($module->print_data, true);
				    $waybillsData = [
					    'object_id'           => $module->object_id,
					    'waybill_code'        => $module->waybill_code,
					    'print_data'          => $printData['encryptedData'],
					    'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
				    ];
			    }
			    $result[$orderIdStr][] = $waybillsData;
		    }else {
			    Log::error($type . '取号错误', [$orderIdStr => $waybill]);
			    $result[$orderIdStr][] = $waybill;
		    }
	    }

	    return $result;
    }

    /**
     * 电子面单云打印取号
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array|bool|string
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        $msg_type  = 'cainiao.waybill.ii.get';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($msg_type, $info);
                if (!isset($waybill->modules)) {
                    Log::error('API=>' . $msg_type, [$waybill]);
                }
                $waybillsData = [];
                foreach ($waybill->modules as $module) {
                    $printData      = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id'           => $module->object_id,
                        'waybill_code'        => $module->waybill_code,
                        'print_data'          => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                \Log::error("获取电子面单失败" . $msg_type, [$e->getMessage(), $e->getTraceAsString()]);
            }
        }

        return $result;
    }

    /**
     * 设置电子面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id']          = $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list'    => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];

            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
	                'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id'                   => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999),
                'items'                => $items,
                'volume'               => 1,
                'weight'               => 1,
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => '',
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $template['template_url'];
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                          = [];
            $data['cp_code']               = $template['wp_code'];
            $data['need_encrypt']          = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender']                = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[]                   = $temp;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * 电子面单更新接口
     * @param array    $order
     * @param Shipping $shipping
     * @return array
     * @throws CytException
     */
    public function waybillUpdate(Array $order, Shipping $shipping)
    {
        $msg_type = 'TMS_WAYBILL_UPDATE';
        $data     = $this->getWayBillUpdateData($order, $shipping);
        $result   = $this->request($msg_type, $data);
        if ($result === false) {
            self::$errMsg = '接口请求失败';

            return false;
        }
        $respone_data = json_decode($result, true);
        if (!$respone_data['success']) {
            self::$errMsg = '接口返回错误信息：' . $respone_data['errorMsg'];

            return false;
        }
        $labelData   = $respone_data['printData'];
        $waybillCode = $respone_data['waybillCode'];

        return ['labelData' => json_decode($labelData, true), 'trackNumber' => $waybillCode];
    }

    /**
     * 设置电子面单请求更新数据
     * @param array    $order
     * @param Shipping $shipping
     */
    private function getWayBillUpdateData(Array $order, Shipping $shipping)
    {
        $items = [];
        $data  = [];
        foreach ($order['goods'] as $key => $good) {
            $items[$key]['count'] = $good['goods_count'];
            $items[$key]['name']  = $good['goods_spec'];
        }
        $data['cpCode']            = $shipping->cn_code;
        $data['waybillCode']       = $order['tracking_number'];
        $data['objectId']          = 1;
        $data['logisticsServices'] = '';
        $data['packageInfo']       = [
            'items'  => $items,
            'volume' => 1,
            'weight' => 1,
        ];
        $data['sender']            = [
            'mobile' => $order['sender_info']['mobile'],
            'phone'  => $order['sender_info']['phone'],
            'name'   => $order['sender_info']['name'],
        ];
        $data['recipient']         = [
            'address' => [
                'city'     => $order['receiver_info']['city'],
                'detail'   => $order['receiver_info']['address'],
                'district' => $order['receiver_info']['town'],
                'province' => $order['receiver_info']['province'],
                'town'     => '',
            ],
            'mobile'  => $order['receiver_info']['mobile'],
            'name'    => $order['receiver_info']['name'],
            'phone'   => $order['receiver_info']['phone'],
        ];
        $data['templateUrl']       = $shipping->cn_url;

        return $data;
    }

    /**
     * 作废电子面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return bool
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode,string $platformWaybillId = '')
    {
        $msg_type = 'cainiao.waybill.ii.cancel';
        $data     = array(
            'cpCode'      => $cpCode,
            'waybillCode' => $waybillCode
        );
        $result   = $this->request($msg_type, $data);
        Log::info($msg_type, [$result]);
        if (!isset($result->cancel_result)) {
            return false;
        }

        return true;
    }

    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $msg_type = 'cainiao.cloudprint.stdtemplates.get';
        $data     = array();
        $result   = $this->request($msg_type, $data);
        if (!isset($result->result)) {
            throw new \Exception('面单服务查询失败!');
        }
        if (empty($result->result->datas->standard_template_result)) {
            throw new \Exception('面单服务查询失败!');
        }
        $standardTemplates = [];
        if ($wpCode) {
            foreach ($result->result->datas->standard_template_result as $value) {
                if ($wpCode == $value->cp_code) {
                    $standardTemplates = array_column($value->standard_templates->standard_template_do, null, 'standard_waybill_type');
                }
            }
        }

        return $standardTemplates;
    }

	public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
	{
		//todo 物流轨迹订阅
	}

	public function sendGetOrderTraceList(array $waybill)
	{
		//todo 物流轨迹订阅
	}

    public function assemWaybillPackagesForOpenApi($platform,$sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        //非一单多包情况，并行异步请求
        $type   = 'cainiao.waybill.ii.get';
        $data   = [];
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url'    => $this->apiUrl,
            ];
        }

        $response = $this->poolCurl($data, 'POST');

        Log::debug('old_tb_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (is_object($waybill)) {
                foreach ($waybill->modules->waybill_cloud_print_response as $module) {
                    $printData    = json_decode($module->print_data, true);
                    $insertPrint = [
                        'encryptedData' =>$printData['encryptedData'],
                        'templateUrl'   =>$printData['templateUrl'],
                        'signature'     => $printData['signature'],
                        'ver'           => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id'           => $module->object_id,
                        'waybill_code'        => $module->waybill_code,
                        'print_data'          => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            }else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    private function getWayBillRequestDataForOpenApi($sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id']          = $order['package_id']??$order['tid'] ?? $order['id'] .'-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list'    => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];

            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id'                   => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999),
                'items'                => $items,
                'volume'               => 1,
                'weight'               => 1,
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => '',
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp);
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                          = [];
            $data['cp_code']               = $wpCode;
            $data['need_encrypt']          = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender']                = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[]                   = $temp;
            ++$num;
        }

        return $returnArr;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        // TODO: Implement updateWaybillData() method.
    }

    /**
     * @inheritDoc
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return mixed
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        // TODO: Implement getCloudPrintStdTemplatesNew() method.
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}
