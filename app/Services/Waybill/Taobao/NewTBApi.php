<?php

namespace App\Services\Waybill\Taobao;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Models\Fix\Order;
use App\Models\Shop;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\ArrayUtil;
use function GuzzleHttp\Psr7\build_query;
use Illuminate\Support\Facades\Log;

/**
 * 淘宝电子面单接口（站外）
 */
class NewTBApi extends AbstractWaybillService
{
    const EXCLUDE_VAS_TYPE = ['product_type'];

    const INSURE_VAS_TYPE = ["INSURE", "IN160", "IN159", "IN160", "IN159", "IN67", "VA002", "insuranceValue", "INSURE SERVICE", "ed-a-0002","SVC-INSURE","VALUE_INSURED"];


    protected $baseUrl    = 'https://eco.taobao.com/router/rest';
    protected $apiUrl     = '';
    protected $version    = '2.0';
    protected $dataFormat = 'JSON';
    protected $signFormat = 'md5';
    private   $authConfig;
    private   $clientID;
    private   $clientSecret;
    private   $timestamp;
    protected $is_test    = false;
    protected $waybillPlatformType = PlatformConst::WAYBILL_TB; // 电子面单平台类型

    public function __construct(string $accessToken = '')
    {
        $this->authConfig   = config('waybill.newtwc');
        $this->clientID     = $this->authConfig['appkey'];
        $this->clientSecret = $this->authConfig['secret'];
        $this->accessToken  = $accessToken;
        $this->timestamp    = date('Y-m-d H:i:s');
    }

    //支持子母件的快递
    const ZIMUJIANMAP = [
        'SF',
        'CN7000001009020',
        'BESTQJT',
        'CN7000001000869',
        '2744832184_543',
        'CN7000001021040',
        '3108002701_1011',
        'SURE',
        'CN7000001017817',
        'CP457538',
        'HOAU',
        'CP471906',
        'CP446169',
        'ZTKY',
        'CN7000001028572',
    ];

    public function isTest()
    {
        $this->is_test = true;
    }

    /**
     * 授权地址
     * @param int $shopId
     * @return string
     */
    public function getLoginUrl($shopId)
    {
	    $state = [
		    'redirect_uri' => $this->authConfig['redirect_url'],
		    'state'        => $shopId,
	    ];

        return $this->authConfig['code_url'] . '?response_type=code&client_id=' . $this->clientID . '&redirect_uri=' . $this->authConfig['code_redirect_url'] . '&state=' . base64_encode(json_encode($state));
    }

    public function getAccessToken(string $code)
    {
	    $params   = [
		    'grant_type'    => 'authorization_code',
		    'client_id'     => $this->clientID,
		    'client_secret' => $this->clientSecret,
		    'redirect_uri'  => $this->authConfig['code_redirect_url'],
		    'code'          => $code,
		    'state'         => 'access_token',
	    ];

	    $url = $this->authConfig['token_url'] . '?' . build_query($params);
	    $client   = new \GuzzleHttp\Client();
	    $response = $client->post($url, [
//			    'form_params'    => $params,
			    'verify'  => false,
			    'headers' => [
				    'Content-type' => 'application/json',
				    "Accept"       => "application/json"
			    ],
		    ]
	    );

	    $result   = json_decode($response->getBody()->getContents(), true);
	    Log::debug('taobao', $result);

	    if (!isset($result['access_token'])) {
		    Log::error('pdd access_token get Failed', ['request' => $result]);
		    throw new \Exception('获取授权码失败，请重试！');
	    }

	    return [
		    'access_token'  => $result['access_token'],
		    'refresh_token' => $result['refresh_token'],
		    'owner_id'      => $result['taobao_user_id'],
		    'owner_name'    => urldecode($result['taobao_user_nick']),
		    'expires_in'    => floor($result['expires_in'] / 1000),
		    'expires_at'    => date('Y-m-d H:i:s', floor($result['expire_time'] / 1000)),
	    ];
    }

	/**
	 * 请求接口
	 * @param $apiName
	 * @param array $data
	 * @return bool|mixed|null
	 * @throws \Exception
	 */
    public function request($apiName, $data = array())
    {
        $param    = $this->createRequestParam($apiName, $data);
        $response = $this->Curl($this->baseUrl, $param, 'post');

	    Log::info(var_export($response, true));

	    return $this->handleResponse($response);
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array  $data
     * @return array
     */
    public function createRequestParam($apiName, $data)
    {
        $params         = [
            'method'      => $apiName,
            'app_key'     => $this->clientID,
            'session'     => $this->accessToken,
            'v'           => $this->version,
            'sign_method' => $this->signFormat,
            'format'      => $this->dataFormat,
            'timestamp'   => $this->timestamp,
        ];

        $allParams = array_merge($params, $data);
        $signature      = $this->sign($allParams);
	    $allParams['sign'] = $signature;
        /*$this->apiUrl  = '';
        $this->apiUrl  = $this->baseUrl . '?';
        foreach ($allParams as $k => $v) {
            $this->apiUrl .= "$k=" . urlencode($v) . '&';
        }
        $this->apiUrl = substr($this->apiUrl, 0, -1);

        return $data;*/
        $this->setRequestData($allParams);
        return $allParams;
    }

    /**
     * 签名
     * @param string $content
     * @return mixed
     */
    public function sign($params)
    {
        ksort($params);

        Log::info(var_export($params, true));

        $sign = $this->clientSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $this->clientSecret;

        return strtoupper(md5($sign));
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws \Exception
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''):array
    {
        $type   = 'cainiao.waybill.ii.search';
        $data = array();
        if ($wpCode) {
            $data   = array(
                'cp_code' => $wpCode
            );
        }

        $result = $this->request($type, $data);
        Log::info('面单查询：'.json_encode($result));
	    if (!isset($result->waybill_apply_subscription_cols) || !isset($result->waybill_apply_subscription_cols->waybill_apply_subscription_info)){
            return [];
        }
        $waybillsInfo = [];
        foreach ($result->waybill_apply_subscription_cols->waybill_apply_subscription_info as $value) {
            $branchAccounts = [];

            \Log::info(var_export($value->branch_account_cols->waybill_branch_account, true));

            foreach ($value->branch_account_cols->waybill_branch_account as $key => $item) {
	            $addresses = [];
	            foreach ($item->shipp_address_cols->address_dto as $add) {
		            $addre             = [];
		            $addre['province'] = $add->province ?? '';
		            $addre['city']     = $add->city ?? '';
		            $addre['district'] = $add->district ?? '';
		            $addre['detail']   = $add->detail ?? '';
		            $addresses[]       = $addre;
	            }
                $serviceInfoCols=[];
                $serviceMap = $item->service_info_cols->service_info_dto ?? [];
                foreach ($serviceMap as $serviceInfo) {
                    $serviceAttributes = [];
                    if (isset($serviceInfo->service_attributes->service_attribute_dto)) {
                        foreach ($serviceInfo->service_attributes->service_attribute_dto as $serviceDesc) {
                            //做一层处理保持数据一致
                            $serviceAttributes[] = [
                                'attribute_name' => $serviceDesc->attribute_name,
                                'attribute_type' => $serviceDesc->attribute_type,
                                'attribute_code' => $serviceDesc->attribute_code,
                                'type_desc'      => $serviceDesc->type_desc
                            ];
                        }
                        $serviceInfoCols[] = [
                            'required'     => $serviceInfo->required,
                            'service_desc' => $serviceInfo->service_desc,
                            'service_name' => $serviceInfo->service_name,
                            'service_code' => $serviceInfo->service_code,
                            'service_attributes' => $serviceAttributes
                        ];
                    } else {
                        $serviceInfoCols[] = [
                            'required'     => $serviceInfo->required,
                            'service_desc' => $serviceInfo->service_desc,
                            'service_name' => $serviceInfo->service_name,
                            'service_code' => $serviceInfo->service_code,
                            'service_attributes' => []
                        ];
                    }
                }

	            $branchAccounts[$key] = [
		            'branch_code'        => $item->branch_code ?? 0,
		            'branch_name'        => $item->branch_name ?? '',
		            'quantity'           => $value->cp_type == 1 ? '-1' : ($item->quantity ?? 0),
		            'cancel_quantity'    => $item->cancel_quantity ?? 0,
		            'recycled_quantity'  => $item->print_quantity ?? 0,
		            'allocated_quantity' => $item->allocated_quantity ?? 0,
		            'shipp_addresses'    => $addresses,
                    'service_info_cols'  => $serviceInfoCols
	            ];
            }
            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code'             => $value->cp_code,
                'wp_type'             => $value->cp_type
            ];
        }

        return $waybillsInfo;
    }

    /**
     * 获取面单
     * @param     $sender
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
	    $type   = 'cainiao.waybill.ii.get';
	    $data   = [];
        $errorArr = [];
        foreach ($orders as $order) {
		    $idStr     = handleOrderIdStr($order);
            try {
                $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);
                foreach ($applyInfo as $index=>$info) {
                    $tempIdStr = $idStr .'|'. $index;
                    $data[$tempIdStr] = [
                        'params' => $this->createRequestParam($type, $info), //只有一个包裹
                        'url'    => $this->baseUrl,
                    ];
                }
            }catch (\Exception $e){
                $errorArr[$idStr][] = $e->getMessage();
            }
	    }


	    $response = $this->poolCurl($data, 'post');

	    Log::debug('tb_response', [$response]);
	    foreach ($response as $orderIdStr => $waybill) {
	    	if (is_object($waybill)) {
			    foreach ($waybill->modules->waybill_cloud_print_response as $module) {
				    $printData      = json_decode($module->print_data, true);
				    $waybillsData = [
					    'object_id'           => $module->object_id,
					    'waybill_code'        => $module->waybill_code,
					    'print_data'          => $printData['encryptedData'],
					    'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
				    ];
			    }

			    $result[$orderIdStr][] = $waybillsData;
		    }else {
			    Log::error($type . '取号错误', [$orderIdStr => $waybill]);
			    $result[$orderIdStr][] = $waybill;
		    }
	    }
        foreach ($errorArr as $idStr => $msg) {
            $result[$idStr] = $msg;
        }

	    return $result;
    }

    /**
     * 电子面单云打印取号
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array|bool|string
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        $msg_type  = 'cainiao.waybill.ii.get';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($msg_type, $info);
                \Log::info('taobao response:'.json_encode($waybill));
                if (!isset($waybill->modules)) {
                    Log::error('API=>' . $msg_type, [$waybill]);
                }
                $waybillsData = [];
                foreach ($waybill->modules->waybill_cloud_print_response as $module) {
                    $printData      = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id'           => $module->object_id,
                        'waybill_code'        => $module->waybill_code,
                        'print_data'          => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                \Log::error("获取电子面单失败" . $msg_type, [$e->getMessage(), $e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 设置电子面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        //抖音平台需要转化收件人信息
        if ((in_array(config('app.platform'), [PlatformConst::DY]) || (config('app.platform')== PlatformConst::KS && ksEncryptSwitch($order['shop_id']))) &&
            empty($order['receiver_phone']) && isset($order['order_cipher_info']) && !empty($order['order_cipher_info'])) {
            $decryptData = [];
            $decryptField = ['tid', 'receiver_name', 'receiver_phone', 'receiver_address'];
            foreach ($decryptField as $column) {
                if ($column == 'tid') {
                    $decryptData[$column] = array_get($order, $column);
                } else {
                    //先置空收件人信息 防止解密失败取到有问题的面单
                    $order[$column] = '';
                    $decryptData[$column] = $order['order_cipher_info'][$column.'_ciphertext'];
                }
            }
            $orderService = OrderServiceManager::create();
            $orderService->setUserId($order['user_id']);
            $shop = Shop::query()->where('id', $order['shop_id'])->first();
            $orderService->setShop($shop);
            $result = $orderService->batchDecrypt($decryptData);
            \Log::info('抖音订单使用淘宝站外电子面单 解密数据：'.json_encode($result));
            if (!empty($result)) {
                foreach ($result as $key => $val) {
                    $order[$key] = $val;
                }
            }
        }
        $returnArr = [];
        $num       = 1;
        //幂等性问题后面加上随机数
        $tradeOrderList = isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999);
        //取真实包裹数量
        if ($packageNum < 0 ) {
            $packageNum = $order['packageNum'];
        }
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            //增值服务
            if (!empty($template['service_list'])) {
                $tradeOrderInfoDto['logistics_services'] = $template['service_list'];
            }
            $tradeOrderInfoDto['object_id']          = isset($order['request_id']) ? ($order['request_id'][$num] ?? $order['request_id'][1]) : $order['id'] .'_' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list'    => [
                    //子母件订单列表必须相同
                    in_array($template['wp_code'],self::ZIMUJIANMAP) ? $tradeOrderList :
                        (isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999))
                ]
            ];

            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
	                'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id'                   => $num,
                'items'                => $items,
                'volume'               => 1,
                'weight'               => 1,
                'total_packages_count' => $packageNum,
            ];
            //快运包裹必传参数不然会报错
            if (in_array($template['wp_code'], self::ZIMUJIANMAP)) {
                $tradeOrderInfoDto['package_info'] = array_merge($tradeOrderInfoDto['package_info'],[
                    'packaging_description' => '包装方式',//大件快运包装方式
                    'goods_description'     => '描述',//大件快运中的货品描述
                    'length'                => '100',//包裹长
                    'width'                 => '100',//包裹宽
                    'height'                => '50',//包裹高
                    'good_value'            => $order['payment']??'',//物品价值，单位元
                ]);
            }
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => $order['receiver_town'],
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $template['template_url'];
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                          = [];
            $data['cp_code']               = $template['wp_code'];
            $data['brand_code']            = $template['company']['branch_code'];
            $data['need_encrypt']          = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender']                = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[]                   = $temp;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * 电子面单更新接口
     * @param array    $order
     * @param Shipping $shipping
     * @return array
     * @throws CytException
     */
    public function waybillUpdate(Array $order, Shipping $shipping)
    {
        $msg_type = 'TMS_WAYBILL_UPDATE';
        $data     = $this->getWayBillUpdateData($order, $shipping);
        $result   = $this->request($msg_type, $data);
        if ($result === false) {
            self::$errMsg = '接口请求失败';

            return false;
        }
        $respone_data = json_decode($result, true);
        if (!$respone_data['success']) {
            self::$errMsg = '接口返回错误信息：' . $respone_data['errorMsg'];

            return false;
        }
        $labelData   = $respone_data['printData'];
        $waybillCode = $respone_data['waybillCode'];

        return ['labelData' => json_decode($labelData, true), 'trackNumber' => $waybillCode];
    }

    /**
     * 设置电子面单请求更新数据
     * @param array    $order
     * @param Shipping $shipping
     */
    private function getWayBillUpdateData(Array $order, Shipping $shipping)
    {
        $items = [];
        $data  = [];
        foreach ($order['goods'] as $key => $good) {
            $items[$key]['count'] = $good['goods_count'];
            $items[$key]['name']  = $good['goods_spec'];
        }
        $data['cpCode']            = $shipping->cn_code;
        $data['waybillCode']       = $order['tracking_number'];
        $data['objectId']          = 1;
        $data['logisticsServices'] = '';
        $data['packageInfo']       = [
            'items'  => $items,
            'volume' => 1,
            'weight' => 1,
        ];
        $data['sender']            = [
            'mobile' => $order['sender_info']['mobile'],
            'phone'  => $order['sender_info']['phone'],
            'name'   => $order['sender_info']['name'],
        ];
        $data['recipient']         = [
            'address' => [
                'city'     => $order['receiver_info']['city'],
                'detail'   => $order['receiver_info']['address'],
                'district' => $order['receiver_info']['town'],
                'province' => $order['receiver_info']['province'],
                'town'     => '',
            ],
            'mobile'  => $order['receiver_info']['mobile'],
            'name'    => $order['receiver_info']['name'],
            'phone'   => $order['receiver_info']['phone'],
        ];
        $data['templateUrl']       = $shipping->cn_url;

        return $data;
    }

    /**
     * 作废电子面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return bool
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode,string $platformWaybillId = '')
    {
        $msg_type = 'cainiao.waybill.ii.cancel';
        $data     = array(
            'cp_code'      => $cpCode,
            'waybill_code' => $waybillCode
        );
        $result   = $this->request($msg_type, $data);
        Log::info($msg_type, [$result]);
        if (!isset($result->cancel_result)) {
            throw new \Exception($result->sub_msg ?? "取消失败");
        }

        return true;
    }

    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $msg_type = 'cainiao.cloudprint.stdtemplates.get';
        $data     = array();
        $result   = $this->request($msg_type, $data);
        if (!isset($result->result)) {
            throw new \Exception('面单服务查询失败!');
        }
        if (empty($result->result->datas->standard_template_result)) {
            throw new \Exception('面单服务查询失败!');
        }
        $standardTemplates = [];
        if ($wpCode) {
            foreach ($result->result->datas->standard_template_result as $value) {
                if ($wpCode == $value->cp_code) {
                    $standardTemplates = array_column($value->standard_templates->standard_template_do, null, 'standard_waybill_type');
                }
            }
        }

        return $standardTemplates;
    }

    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $msg_type = 'cainiao.cloudprint.stdtemplates.get';
        $data     = array();
        $result   = $this->request($msg_type, $data);
        if (!isset($result->result)) {
            throw new \Exception('面单服务查询失败!');
        }
        if (empty($result->result->datas->standard_template_result)) {
            throw new \Exception('面单服务查询失败!');
        }
        $standardTemplates = [];
        if ($wpCode) {
            foreach ($result->result->datas->standard_template_result as $value) {
                if ($wpCode == $value->cp_code) {
                    $standardTemplates = array_column($value->standard_templates->standard_template_do, null);
                }
            }
        }

        return $standardTemplates;
    }

	public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
	{
		//todo 物流轨迹订阅
	}

	public function sendGetOrderTraceList(array $waybill)
	{
		//todo 物流轨迹订阅
	}

    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        //并行异步请求
        $type   = 'cainiao.waybill.ii.get';
        $data   = [];
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($platform, $sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url'    => $this->baseUrl, // 这个接口的 url 不能拼接参数 会造成 413 FULL head 错误
                //'url'    => $this->apiUrl,
            ];
        }

        $response = $this->poolCurl($data, 'post');

        Log::debug('tb_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (is_object($waybill)) {
                foreach ($waybill->modules->waybill_cloud_print_response as $module) {
                    $printData      = json_decode($module->print_data, true);
                    $insertPrint = [
                        'encryptedData' =>$printData['encryptedData'],
                        'templateUrl'   =>$printData['templateURL'],
                        'templateURL'   =>$printData['templateURL'],
                        'signature'     => $printData['signature'],
                        'ver'           => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id'           => $module->object_id,
                        'waybill_code'        => $module->waybill_code,
                        'print_data'          => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }

                $result[$orderIdStr][] = $waybillsData;
            }else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }
        return $result;
    }

    /**
     * 请求电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template, $packageNum);
        $requestDataArr = [];

        $type = 'cainiao.waybill.ii.get';
//        $requestBatchIndexArr = []; // 批量请求时，根据pack_id获取请求的index
        foreach ($requestDataList as $index => $item) {
            $requestDataArr[$index] = [
                'params' => $this->createRequestParam($type, $item),
                'url' => $this->baseUrl,
            ];
        }
        $responseArr = $this->poolCurl($requestDataArr,'post');
        $responseArr = json_decode(json_encode($responseArr), true);
        Log::debug('tb_response', $responseArr);
        $successInfos = [];
        $errorInfos = [];
        foreach ($responseArr as $index => $response) {
            if (!empty($response['modules']['waybill_cloud_print_response'])) {
                $successInfos = array_merge($successInfos, $response['modules']['waybill_cloud_print_response']);
            }
//            if (!empty($response['data']['err_infos'])) {
//                $errorInfos = array_merge($errorInfos, $response['data']['err_infos']);
//            }
        }
        $successInfos = array_pluck($successInfos, null, 'object_id');
        $errorInfos = array_pluck($errorInfos, null, 'object_id');

        $printDataPackBoList = [];
        // 获取面单号
        foreach ($printPackBoList as $index => $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['waybill_code'];
                $printDataPackBo->wp_code = $template['wp_code'];
                $printDataPackBo->package_id = $successInfo['object_id'];
                $waybillsPrintDataBo = new WaybillsPrintDataBo();
                $waybillsPrintDataBo->copyByPrintDataPackBo($printDataPackBo);
                $waybillsPrintDataBo->parent_waybill_code = $successInfo['parent_waybill_code'] ?? '';
                $successPrintData = json_decode($successInfo['print_data'],true);
                $waybillsPrintDataBo->encrypted_data = $successPrintData['encryptedData'];
                $waybillsPrintDataBo->sign = $successPrintData['signature'];
                $waybillsPrintDataBo->param_str = 'ver='.$successPrintData['ver'];
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
            } else {
//                $requestIndex = $requestBatchIndexArr[$printPackBo->request_id];
                $thisResponse = $responseArr[$index];
                $thisRequest = $requestDataList[$index];
                $msg = '';
                if (is_string($thisResponse)){
                    $msg = $thisResponse;
                }else{
                    try {
                        $this->setRequestData($thisRequest);
                        $this->handleResponse($thisResponse);
                    } catch (\Exception $e) {
                        $msg = $e->getMessage();
                    }
                }

                if (!empty($msg)) {
                    $printDataPackBo->setError([ErrorConst::PLATFORM_ERROR[0], $msg]);
                } else {
                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
                }
            }
            $printDataPackBoList[] = $printDataPackBo;
        }

        return $printDataPackBoList;
    }

    private function getWayBillRequestDataForOpenApi($platform, $sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id']          = $order['package_id']??$order['tid'] ?? $order['id'] .'-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list'    => [
                    isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999)
                ]
            ];

            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => isset($order['num']) && is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id'                   => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999) ,
                'items'                => $items,
                'volume'               => 1,
                'weight'               => 1,
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => '',
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode);
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                          = [];
            $data['cp_code']               = $wpCode;
            $data['need_encrypt']          = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender']                = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[]                   = $temp;
            ++$num;
        }

        return $returnArr;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        $msg_type  = 'cainiao.waybill.ii.update';
        $applyInfo = $this->getWayBillUpdateRequestData($sender, $order, $template, $waybillCode);
        $result = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($msg_type, $info);
                \Log::info('newTBApi 更新单子面单接口',[$waybill]);
                if (!isset($waybill->print_data)) {
                    Log::error('API=>' . $msg_type, [$waybill]);
                }
                $printData = json_decode($waybill->print_data, true);
                $result = [
                    'waybill_code'        => $waybill->waybill_code,
                    'print_data'          => $printData['encryptedData'],
                ];
            } catch (\Exception $e) {
                \Log::error("更新电子面单失败" . $msg_type, [$e->getTraceAsString()]);
            }
        }
        return $result;
    }
    /**
     * 设置电子面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillUpdateRequestData($sender, $order, $template, $waybillCode)
    {
        $returnArr = [];
        //发件人信息
        $senderInfo['mobile']              = $sender['mobile'];
        $senderInfo['phone']               = '';
        $senderInfo['name']                = $sender['sender_name'];
        $senderInfo['address']['province'] = $sender['province'];
        $senderInfo['address']['city']     = $sender['city'];
        $senderInfo['address']['district'] = $sender['district'];
        $senderInfo['address']['town']     = '';
        $senderInfo['address']['detail']   = $sender['address'];
        $items = [];
        if (array_key_exists('order_item', $order)) {
            foreach ($order['order_item'] as $good) {
                $temp = [];
                $temp['count'] = $good['goods_num'];
                $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                $items[]       = $temp;
            }
        }
        if (array_key_exists('production_type', $order)) {
            $items[] = [
                'count' => is_int($order['num']) ? $order['num'] : 1,
                'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
            ];
        }
        $packageInfo = [
            'id'                   => $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999) ,
            'items'                => $items,
            'volume'               => 1,
            'weight'               => 1,
            'total_packages_count' => 1,
        ];
        $recipient    = [
            'address' => [
                'city'     => $order['receiver_city'],
                'detail'   => $order['receiver_address'],
                'district' => $order['receiver_district'],
                'town'     => '',
                'province' => $order['receiver_state'] ?? $order['receiver_province'],
            ],
            'mobile'  => $order['receiver_phone'],
            'name'    => $order['receiver_name'],
            'phone'   => $order['receiver_tel'] ?? '',
        ];
        //设置主体信息
        $data                          = [];
        $data['cp_code']               = $template['wp_code'];
        $data['need_encrypt']          = true;
        $data['sender']                = $senderInfo;
        $data['package_info']          = $packageInfo;
        $data['recipient']             = $recipient;
        $data['template_url']          = $template['template_url'];
        $temp = [];
        $data['waybill_code'] = $waybillCode;
        $temp['param_waybill_cloud_print_update_request'] = json_encode($data);
        $returnArr[]                   = $temp;
        return $returnArr;
    }

    /**
     * @inheritDoc
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    static function buildOrderVasList(?string $serviceListStr, string $insureAmount ): string
    {
        $orderVasList = [];
        if(empty($serviceListStr)){
            return "";
        }
        $serviceList = json_decode($serviceListStr, true);
        foreach ($serviceList as $key => $item) {
            //排除掉一些特殊的增值服务
            if(in_array($key, self::EXCLUDE_VAS_TYPE)){
                continue;
            }
            $value=ArrayUtil::getArrayValue($item,"value");
            //保价有两种
            if ( in_array( $key, self::INSURE_VAS_TYPE)) {
                \Log::info("处理保价",["insureAmount"=>$insureAmount,"item"=>$item]);
                //对保价金额进行处理，如果是-1，就是按订单金额保价，除此以外按设定金额保价
                if($value!=-1){
                    \Log::info("保价金额不是-1,按设定的金额处理",["item"=>$item]);

                }else{
                    if(bccomp($insureAmount,"0")>0) {
                        \Log::info("保价金额是-1,订单金额>0 按订单金额处理",["insureAmount"=>$insureAmount]);
                        $value = round_bcmul($insureAmount, "100",0); //  (string)($insureAmount * 100);
                    }else{
                        \Log::info("保价金额是-1,订单金额=0 不处理这个保价",["insureAmount"=>$insureAmount]);
                        continue;
                    }
                }

            }


            $orderVasList[$key]= ["value"=>strval($value)];
        }
        if(empty($orderVasList)){
            $vasOrderListString = "";
        }else {
            $vasOrderListString = json_encode($orderVasList, JSON_UNESCAPED_UNICODE);
        }
        \Log::info("处理增值服务", [$vasOrderListString]);
        return $vasOrderListString;
    }


    /**
     * 获取电子面单请求数据
     * @param SenderAddressBo $sender
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return array
     * <AUTHOR>
     */
    private function getWayBillRequestDataByPrintPackBo(SenderAddressBo $sender, array $printPackBoList, array $template, int $packageNum): array
    {
        //发件人信息
        $senderInfo = [
            'address' => [
                'province' => $sender->province,
                'city' => $sender->city,
                'district' => $sender->district,
                'town' => $sender->street ?? '',
                'detail' => $sender->address,
            ],
            'name' => $sender->sender_name,
            'mobile' => $sender->mobile,
            'phone' => ''
        ];
        $returnArr = [];
        $num       = 1;
//        $packageNum = 1;
        //幂等性问题后面加上随机数
//        $tradeOrderList = isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999);
//        $isPtOrder = true;
        //取真实包裹数量
//        if ($packageNum < 0 ) {
//            $packageNum = $order['packageNum'];
//        }
        foreach ($printPackBoList as $printDataPackBo) {
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
//            //增值服务
//            if (!empty($template['service_list'])) {
//                $tradeOrderInfoDto['logistics_services'] = $template['service_list'];
//            }

            $serviceListStr = $template['service_list'];
            if (!empty($serviceListStr)) {
                $tradeOrderInfoDto['logistics_services'] =self::buildOrderVasList($serviceListStr,$printDataPackBo->getInsureAmount());
            }


            $order = $printDataPackBo->master_order_info;
            $order = $order->toArray();
            $tid = $this->getTidByOrder($order);
            $isPtOrder = $printDataPackBo->isPlatformOrder();

            $order_channels_type = 'OTHERS';
            $caid = '';
            $oaid = '';
            switch ($order['type']??'') {
                case Shop::PLATFORM_TYPE_ALC2M:
                case Shop::PLATFORM_TYPE_TAOBAO:
                    $oaid = $order['order_cipher_info']['oaid'] ?? '';
                    $order_channels_type = 'TB';
                    break;
                case Shop::PLATFORM_TYPE_ALBB:
                    $caid = $order['order_cipher_info']['oaid'] ?? '';
                    $order_channels_type = '1688';
                    break;
                default:
                    break;
            }


            $tradeOrderInfoDto['object_id']          = $printDataPackBo->request_id;
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => $order_channels_type,
                'trade_order_list'    => [
                    $tid
                    //子母件订单列表必须相同
//                    in_array($template['wp_code'],self::ZIMUJIANMAP) ? $tradeOrderList :
//                        (isset($order['tid']) ? $order['tid'] . rand(0000, 9999) : $order['id'] . rand(0000, 9999))
                ]
            ];
            $items = [];
            foreach ($printDataPackBo->print_order_item_bo_list as $printOrderItemBo) {
                $items[] = [
                    'name' => $printOrderItemBo->order_item_info['sku_value'],
                    'count' => $printOrderItemBo->num,
                ];
            }
//            if (array_key_exists('production_type', $order)) {
//                $items[] = [
//                    'count' => is_int($order['num']) ? $order['num'] : 1,
//                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
//                ];
//            }

            $tradeOrderInfoDto['package_info'] = [
//                'id'                   => $num,
                'id'                   => $printDataPackBo->request_id,
                'items'                => $items,
                'volume'               => 1,
                'weight'               => 1,
                'total_packages_count' => $packageNum,
            ];
            //快运包裹必传参数不然会报错
            if (in_array($template['wp_code'], self::ZIMUJIANMAP)) {
                $tradeOrderInfoDto['package_info'] = array_merge($tradeOrderInfoDto['package_info'],[
                    'packaging_description' => '包装方式',//大件快运包装方式
                    'goods_description'     => '描述',//大件快运中的货品描述
                    'length'                => '100',//包裹长
                    'width'                 => '100',//包裹宽
                    'height'                => '50',//包裹高
                    'good_value'            => $order['payment']??'',//物品价值，单位元
                ]);
            }
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => $order['receiver_town'],
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
                'oaid'   => $oaid,
                'caid'   => $caid,
            ];
            $tradeOrderInfoDto['template_url'] = $template['template_url'];
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                          = [];
            $data['cp_code']               = $template['wp_code'];
            $data['brand_code']            = $template['company']['branch_code'];
            $data['need_encrypt']          = true;
            $data['trade_order_info_dtos'] = $tradeOrderInfoDtos;
            $data['sender']                = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[]                   = $temp;
            ++$num;
        }

        return $returnArr;
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}
