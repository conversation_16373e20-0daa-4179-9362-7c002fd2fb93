<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2024/5/15
 * Time: 下午3:37
 */

namespace App\Services\Waybill\XHS;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Models\OrderTraceList;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Client\DyClient;
use App\Services\Client\XhsClient;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\DateTimeUtil;
use Illuminate\Support\Facades\Log;

class XHSApi extends AbstractWaybillService
{
    protected $waybillPlatformType = PlatformConst::WAYBILL_XHS; // 电子面单平台类型

    public function __construct(string $accessToken = '')
    {
        $this->accessToken = $accessToken;
        $this->authConfig = config('waybill.xhs');
    }

    const ORDER_TRACE_MAP = [
        "待付款" => OrderTraceList::STATUS_SHIPPED, //1010：订单已创建
        "已支付" => OrderTraceList::STATUS_SHIPPED, //1015：订单已支付
        "待配货" => OrderTraceList::STATUS_OTHER, //1019：信息待补充
        "配货中" => OrderTraceList::STATUS_SHIPPED, //3010：商家处理中
        "仓库作业中" => OrderTraceList::STATUS_SHIPPED, //3010：商家打包中
        "已发货" => OrderTraceList::STATUS_SHIPPED, //3020：商家已发货
        "待揽" => OrderTraceList::STATUS_GOT, //3050：仓库处理中
        "已揽件" => OrderTraceList::STATUS_DEPARTURE, //3055：an仓库分拣中
        "运输中" => OrderTraceList::STATUS_DEPARTURE, //3060：仓库打包中
        "已签收" => OrderTraceList::STATUS_SIGN, //3065：：仓库已发出
        "已完成" => OrderTraceList::STATUS_OTHER, //6020：：仓库已发出
        "已关闭" => OrderTraceList::STATUS_OTHER, //6020：：仓库已发出
        "取消发货" => OrderTraceList::STATUS_OTHER, //3050：仓库处理中
        //2010/2011/2012：海关申报中
        "海关通过" => OrderTraceList::STATUS_DEPARTURE, //发件 运输中 0
        "海关申报中" => OrderTraceList::STATUS_DEPARTURE, //发件 运输中 0

    ];

    /**
     * @return XhsClient
     * <AUTHOR>
     */
    protected function getClient(): XhsClient
    {
        return XhsClient::newInstance($this->accessToken);
    }

    /**
     * @inheritDoc
     * @param  string  $wpCode
     * @param  string  $serviceId
     * @return array
     * @throws ClientException
     * S* @throws ApiException
     * @throws OrderException*@throws ApiException
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        //新旧两种进行兼容，如果新的没有，就去旧的
        $waybillSubscriptionQuery = $this->subWaybillSubscriptionQuery($wpCode, 2);
//        if (empty($waybillSubscriptionQuery)) {
//            //如果新的没取到用老的
//            $waybillSubscriptionQuery = $this->subWaybillSubscriptionQuery($wpCode, 1);
//        }
        return $waybillSubscriptionQuery;
    }

    /**
     * @inheritDoc
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        // TODO: Implement waybillGet() method.
    }

    /**
     * @inheritDoc
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        // TODO: Implement assemWaybillPackages() method.
    }

    /**
     * @inheritDoc
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode, string $platformWaybillId = '')
    {
        $client = $this->getClient();
        $params = [
            "cpCode" => $cpCode,
            "waybillCode" => $waybillCode,
        ];

        $response = $client->execute('express.cancelEbillOrder', $params);
        return true;
    }

    /**
     * @inheritDoc
     */
    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $client = $this->getClient();
        $params = ['cpCode' => $wpCode];
        $response = $client->execute('express.queryEbillTemplates', $params);

        Log::debug('getCloudPrintStdTemplates', [$response]);
        $standardTemplates = [];
        if (empty($response['data']['templateList'])) {
            return $standardTemplates;
        }
        foreach ($response['data']['templateList'] as $template) {
            if ($template['cpCode'] == $wpCode) {
//                if ($template['templateName'] == '顺丰一联单(100*150)') {
//                    $template['template_type'] = 2;
//                }
                $standardTemplates[$template['templateType']] = [
                    'standard_waybill_type' => $template['templateType'],
                    'standard_template_name' => $template['templateName'],
                    'standard_template_url' => $template['standardTemplateUrl'],
//                    'standard_template_id' => $template['id'],
                ];
            }
        }

        return $standardTemplates;
    }

    /**
     * @inheritDoc
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '', ?string $extendedInfo = null)
    {
        $client = $this->getClient();
        $params = ['cpCode' => $wpCode];

        $params['billVersion'] = 2;

        Log::info('getCloudPrintStdTemplates', [$params]);
        $response = $client->execute('express.queryEbillTemplates', $params);

        Log::info('getCloudPrintStdTemplates', [$response]);
        $standardTemplates = [];
        if (empty($response['data']['templateList'])) {
            return $standardTemplates;
        }
        $typeMapping = [
            '76*130' => 1,
            '100*150' => 2,
            '100*180' => 3,
        ];
        foreach ($response['data']['templateList'] as $template) {
            if ($template['cpCode'] == $wpCode) {
//                if ($template['templateName'] == '顺丰一联单(100*150)') {
//                    $template['template_type'] = 2;
//                }
                $standardTemplates[$template['templateType']] = [
                    'standard_waybill_type' => $typeMapping[$template['templateType']],
                    'standard_template_name' => $template['templateName'],
                    'standard_template_url' => $template['standardTemplateUrl'],
                    'standard_template_id' => $template['id'],
                ];
            }
        }

        return $standardTemplates;
    }

    /**
     * @inheritDoc
     */
    public function getLoginUrl($shopId)
    {
        // TODO: Implement getLoginUrl() method.
    }

    /**
     * @inheritDoc
     */
    public function getAccessToken(string $code)
    {
        // TODO: Implement getAccessToken() method.
    }

    /**
     * @inheritDoc
     */
    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        // TODO: Implement sendOrderLogisticsTraceMsg() method.
    }

    /**
     * 小红书有新旧两种电子面单
     * @param  string  $wpCode
     * @param  int  $billVersion  1 老版 2 新版
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    public function subWaybillSubscriptionQuery(string $wpCode, int $billVersion = 1): array
    {
        $client = $this->getClient();
        $params = [];
        if (!empty($wpCode)) {
            $params ["cpCode"] = $wpCode;

        }
        $params ["needUsage"] = true;
        $params['billVersion'] = $billVersion;

        $response = $client->execute('express.queryEbillSubscribes', $params);
        Log::info('面单查询：'.json_encode($response), [$params]);

        $client->handleResponse($response);

        $waybillsInfo = [];
        foreach ($response['data']['subscribeList'] as $value) {
            $branchAccounts = $addresses = [];
            foreach ($value['senderAddressList'] as $item) {
                $addresses[] = [
                    'city' => $item['address']['city'] ?? '',
                    'detail' => $item['address']['detail'] ?? '',
                    'province' => $item['address']['province'] ?? '',
                    'district' => $item['address']['district'] ?? '',
                    'street' => $item['address']['town'] ?? '',
                    'street_name' => $item['address']['town'] ?? '',
                ];
            }

            //直营快递必须有客户编码
            $serviceInfoCols = [];
//            if ($value['cpType'] == 1) {
//                $serviceInfoCols = [
//                    [
//                        'required' => true,
//                        'service_desc' => '客户编码',
//                        'service_name' => '客户编码',
//                        'service_code' => 'settleAccount',
//                        'service_attributes' => [
//                            [
//                                'attribute_name' => '客户编码',
//                                'attribute_type' => 'string',
//                                'attribute_code' => 'value',
//                                'type_desc'      => '{"maxLength":128,"minLength":1,"required":true,"type":"string"}'
//                            ]
//                        ]
//                    ]
//                ];
//            }

            $expressCompanyType = $value['cpType'];
            $branchAccounts[] = [
                'branch_code' => $value['branchCode'] ?? 0,
                'branch_name' => $value['branchName'] ?? '',
                //直营返回的是-1，就是无限
                'quantity' => $expressCompanyType == 1 ? -1 : $value['usage']['quantity'] ?? 0,
                'cancel_quantity' => $value['usage']['cancelQuantity'] ?? 0,
                'recycled_quantity' => $value['usage']['recycledQuantity'] ?? 0,
                'allocated_quantity' => $value['usage']['allocatedQuantity'] ?? 0,
                'shipp_addresses' => $addresses,
                'service_info_cols' => $serviceInfoCols,
                'settlement_code' => $value['customerCode'] ?? '',
                'extended_info' => json_encode(["billVersion" => $billVersion])
            ];

            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $value['cpCode'],
                'wp_type' => $expressCompanyType
            ];
        }

        $data = [];
        foreach ($waybillsInfo as $item) {
            $data[$item['wp_code']]['branch_account_cols'][] = $item['branch_account_cols'][0];
            $data[$item['wp_code']]['wp_code'] = $item['wp_code'];
            $data[$item['wp_code']]['wp_type'] = $item['wp_type'];
        }

        return array_values($data);
    }

    /**
     * @inheritDoc
     */
    protected function sendGetOrderTraceList(array $waybill)
    {
        $client = $this->getClient();
        $params = [];
        $waybillCode = $waybill['express_no'];
        $params ["orderId"] = $waybill['tid'];
        $response = $client->execute('order.getOrderTracking', $params);
        Log::info('sendGetOrderTraceList', [$response]);
        $client->handleResponse($response);
        $orderTrackInfos = $response['data']['orderTrackInfos'];
        //把对于的快递单找出来
        $orderTrackInfos = array_filter($orderTrackInfos, function ($item) use ($waybillCode) {
            return $item['expressNo'] == $waybillCode;
        });
        if (empty($orderTrackInfos)) {
            return [];
        } else {
            //这个是把这个订单的物流都取出来
            return $this->formatToOrderTrace($waybill, $orderTrackInfos[0]);
        }

    }

    public function formatToOrderTrace($waybill, array $orderTraceList): array
    {
        Log::info('formatToOrderTrace', [$orderTraceList]);
        $currentStatusDesc = $orderTraceList['currentStatusDesc'];
        $latest = collect($orderTraceList['records'])->sortByDesc('eventAt')->first();
        Log::info('latest', [$latest]);

        return [
            "type" => $waybill['type'] ?? 0, //?
            'tid' => $waybill['tid'] ?? '',
            'express_code' => $waybill['express_code'],
            'express_no' => $waybill['express_no'],
            'status' => self::ORDER_TRACE_MAP[$currentStatusDesc] ?? OrderTraceList::STATUS_OTHER,
            'action' => $currentStatusDesc,
            'receiver_province' => $waybill['receiver_province'] ?? '',
            'receiver_name' => $waybill['receiver_name'] ?? '',
            'send_at' => $waybill['send_at'] ?? null,
            'latest_updated_at' => $latest['eventAt'],
            'latest_trace' => $latest['eventDesc'],
            'status_time' => $latest['eventAt'],
            'trace_list' => '[]'
        ];
    }

    public function assemWaybillPackagesForOpenApi(
        $platform,
        $sender,
        $orders,
        $wpCode,
        $waybillType,
        $waybillTemp,
        $packageNum = 1,
        $productType = null
    ) {
        // TODO: Implement assemWaybillPackagesForOpenApi() method.
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        // TODO: Implement updateWaybillData() method.
    }

    /**
     * @inheritDoc
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    /**
     * 请求电子面单
     * @param  SenderAddressBo  $branchAddressBo
     * @param  PrintPackBo[]  $printPackBoList
     * @param  array  $template
     * @param  int  $packageNum
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(
        SenderAddressBo $branchAddressBo,
        array $printPackBoList,
        array $template,
        int $packageNum
    ): array {
        $requestDataList = $this->getWayBillRequestDataByPrintPackBo($branchAddressBo, $printPackBoList, $template,
            $packageNum);
        $msg_type = 'express.createEbillOrders';
        $requestDataArr = [];
        $client = $this->getClient();
        $requestBatchIndexArr = []; // 批量请求时，根据pack_id获取请求的index
        foreach ($requestDataList as $index => $item) {
            $requestDataArr[$index] = [
                'params' => $client->buildRequestData($item, $msg_type),
                'url' => $client->getBaseUrlByApimethod($msg_type),
            ];
            foreach ($item['tradeOrderInfoList'] as $order_info) {
                $requestBatchIndexArr[$order_info['objectId']] = $index;
            }
        }
        $responseArr = $this->poolCurl($requestDataArr, 'json', false, true);
        $responseArr = json_decode(json_encode($responseArr), true);
        $successInfos = [];
        $errorInfos = [];
        foreach ($responseArr as $index => $response) {
            $wayBillList = $response['data']['wayBillList'] ?? [];

//            $successInfos = array_merge($successInfos, $response['data']['wayBillList']);
            $successInfos = array_merge($successInfos, array_filter($wayBillList, function ($wayBillItem) {
                return $wayBillItem['success'] ?? false;
            }));
            $errorInfos = array_merge($errorInfos, array_filter($wayBillList, function ($wayBillItem) {
                return !$wayBillItem['success'] ?? false;
            }));
//            if (!empty($response['data']['err_infos'])) {
//                $errorInfos = array_merge($errorInfos, $response['data']['err_infos']);
//            }
        }
        $successInfos = array_pluck($successInfos, null, 'objectId');
        $errorInfos = array_pluck($errorInfos, null, 'objectId');

        $printDataPackBoList = [];
        // 获取面单号
        foreach ($printPackBoList as $index => $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['waybillCode'];
                $printDataPackBo->wp_code = $template['wp_code'];
                $waybillsPrintDataBo = new WaybillsPrintDataBo();
                $waybillsPrintDataBo->copyByPrintDataPackBo($printDataPackBo);
                $successPrintData = json_decode($successInfo['printData'], true);
                $waybillsPrintDataBo->encrypted_data = $successPrintData['encryptedData'];
                $waybillsPrintDataBo->sign = array_get($successPrintData, 'signature');
                $waybillsPrintDataBo->param_str = 'ver='.$successPrintData['ver'];
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
                // 子母件
//                if (!empty($successInfo['sub_waybill_codes'])) {
//                    $subWaybillCodeArr = explode(',', $successInfo['sub_waybill_codes']);
//                    $printDataPackBo->sub_waybill_code_arr = $subWaybillCodeArr;
//                }
            } else {
                $requestIndex = $requestBatchIndexArr[$printPackBo->request_id];
                $thisResponse = $responseArr[$requestIndex];
                $thisRequest = $requestDataArr[$requestIndex];
                $msg = '';
                try {
                    XhsClient::handleErrorCode($thisResponse, $thisRequest);
                } catch (\Exception $e) {
                    $msg = $e->getMessage();
                }
                if (!empty($msg)) {
                    $printDataPackBo->setError([ErrorConst::PLATFORM_ERROR[0], $msg]);
                } else {
                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
                }
            }
            $printDataPackBoList[] = $printDataPackBo;
        }

        return $printDataPackBoList;
    }

    /**
     * 获取电子面单请求数据
     * @param  SenderAddressBo  $sender
     * @param  PrintPackBo[]  $printPackBoList
     * @param $template
     * @return array
     * <AUTHOR>
     */
    private function getWayBillRequestDataByPrintPackBo(
        SenderAddressBo $sender,
        array $printPackBoList,
        $template,
        int $packageNum
    ) {

        $list = [];
        //发件人信息
        $senderInfo = [
            'address' => [
                'province' => $sender->province,
                'city' => $sender->city,
                'district' => $sender->district,
                'town' => $sender->street ?? '',
                'detail' => $sender->address,
            ],
            'name' => $sender->sender_name,
            'mobile' => $sender->mobile,
            'phone' => ''
        ];
        $order_infos = [];
        $isPtOrder = true;
        $total_pack_count = 1;
        //子母件
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
            $total_pack_count = $packageNum;
        }
        foreach ($printPackBoList as $printDataPackBo) {
            $items = [];
            foreach ($printDataPackBo->print_order_item_bo_list as $printOrderItemBo) {
                /** @var PrintOrderItemBo $printOrderItemBo */
                $items[] = [
                    //兼容小红书sku规格名称没有的情况
                    'name' => empty($printOrderItemBo->order_item_info['sku_value'])?$printOrderItemBo->order_item_info['goods_title']:$printOrderItemBo->order_item_info['sku_value'],
                    'count' => $printOrderItemBo->num,
                ];
            }
            $order = $printDataPackBo->master_order_info;
            $order = $order->toArray();
            $tid = $this->getTidByOrder($order);
            $isPtOrder = $printDataPackBo->isPlatformOrder();
            if ($isPtOrder) {
                $recipient = ['openAddressId' => $order['order_cipher_info']['oaid'] ?? '',];
            } else {
                $recipient = [
                    'address' => [
                        'city' => $order['receiver_city'],
                        'detail' => !empty($order['order_cipher_info']) ? $order['order_cipher_info']['receiver_address_ciphertext'] : stripslashes($order['receiver_address']),
                        'district' => $order['receiver_district'],
                        'province' => $order['receiver_state'] ?? $order['receiver_province'],
                        'town' => $order['receiver_town'] ?? ""
                    ],
                    'mobile' => !empty($order['order_cipher_info']) ? $order['order_cipher_info']['receiver_phone_ciphertext'] : $order['receiver_phone'],
                    'name' => !empty($order['order_cipher_info']) ? $order['order_cipher_info']['receiver_name_ciphertext'] : $order['receiver_name'],
                    'phone' => $order['receiver_tel'] ?? '',
                ];
            }
            $order_info = [
                'objectId' => $printDataPackBo->request_id,
                'orderInfo' => [
                    'orderChannelsType' => $isPtOrder ? "XIAO_HONG_SHU" : "OTHERS",
                    'tradeOrderList' => [$tid],
                    'xhsOrderId' => $tid,
                ],
                'packageInfo' => [
                    'id' => $printDataPackBo->package_id,
                    'items' => $items,
                ],
                'recipient' => $recipient,
                'templateId' => $template['parent_template_id'],

            ];
//            if ($total_pack_count > 1) {
//                $order_info['total_pack_count'] = $total_pack_count;
//            }
            //产品类型
//            if (!empty($template['time_delivery'])) {
//                $order_info['product_type'] = $template['time_delivery'];
//            }
            //增值服务
//            if (!empty($template['service_list'])) {
//                $serviceList = json_decode($template['service_list'], true);
//                $orderAmountCount = isset($order['order_item']) ? collect($order['order_item'])->sum('payment') : 20;
//                $newServiceList = [];
//                foreach ($serviceList as $key => $item) {
//                    //保价有两种
//                    if ($key == "SVC-INSURE" || $key == "INSURE") {
//                        //-1 是订单金额保价
//                        if ($item['value'] == -1) {
//                            $item['value'] = (string)($orderAmountCount * 100);
//                        }
//                    }
//                    $newServiceList[] = [
//                        'service_code' => $key,
//                        'service_value' => json_encode($item)
//                    ];
//                }
//                $order_info['service_list'] = $newServiceList;
//            }
//            if ($packageNum > 1){
//                $order_infos[] = $order_info;
//            }
            $order_infos[] = $order_info;
        }
        $limit = 1; // 支持批量取号，一次最多10条。 小红书有毛病，10 个请求过去任意一个单号有问题比如区域不可达，就全部失败，所以改成 1 个。
        $order_infos_chunk = array_chunk($order_infos, $limit);
        foreach ($order_infos_chunk as $index => $this_order_infos) {
            $company = $template['company'];
            $billVersion = null;
            //小红书的网点扩展信息里面存了billVersion，
            if (!empty($company['extended_info'])) {
                $billVersion = json_decode($company['extended_info'], true)['billVersion'] ?? null;
            }
            $companyData = [
                'sender' => $senderInfo,
                'tradeOrderInfoList' => $this_order_infos,
                'cpCode' => $template['wp_code'],
                'customerCode' => $company['settlement_code'] ?? '',
                'branchCode' => $company['branch_code'] ?? '',
                'sellerName' => $printPackBoList[0]->master_order_info['shop_title'] ?: '默认店铺名',
            ];
//            if ($billVersion) {
            $companyData['billVersion'] = 2;
//            }
            $list[] = $companyData;
        }
        return $list;
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}
