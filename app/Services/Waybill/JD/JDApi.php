<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2021/8/16
 * Time: 10:33
 */

namespace App\Services\Waybill\JD;

use ACES\Common\Exception\ArgumentNullException;
use ACES\Common\Exception\JosGwException;
use ACES\Common\Exception\MalformedException;
use ACES\Common\Exception\NoValidKeyException;
use ACES\Common\Exception\VoucherInfoGetException;
use ACES\TDEClient;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\OrderException;
use App\Http\StatusCode\StatusCode;
use App\Models\Company;
use App\Models\OrderTraceList;
use App\Models\Shop;
use App\Models\WaybillAuth;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\BusinessException;
use App\Services\Waybill\AbstractWaybillService;
use App\Utils\ArrayUtil;
use App\Utils\StrUtil;
use App\Utils\WaybillUtil;
use GetThirdTraceRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;
use JdClient;
use LdopAlphaProviderSignSuccessRequest;
use LdopAlphaWaybillApiUnbindRequest;
use LdopAlphaWaybillReceiveRequest;
use PhpOffice\PhpSpreadsheet\Calculation\Web\Service;
use PrintingPrintDataPullData\Attribute1;
use PrintingPrintDataPullData\Param1 as PullDataParam1;
use PrintingPrintDataPullData\ParametersFixed;
use PrintingPrintDataPullDataRequest;
use PrintingTemplateGetTemplateList\Param1 as TemplateParam1;
use PrintingTemplateGetTemplateListRequest;

class JDApi extends AbstractWaybillService
{
    const WAYBILL_ERROR_CODE = StatusCode::PRINT_ERROR[0];
    private $orderTraceMap = [
        'GOT' => OrderTraceList::STATUS_GOT, //揽件
        'SENT_SCAN' => OrderTraceList::STATUS_SEND, //派件
        'SIGNED' => OrderTraceList::STATUS_SIGN, //签收
        'ARRIVAL_S' => OrderTraceList::STATUS_ARRIVAL, //到件
        'ARRIVAL_C' => OrderTraceList::STATUS_ARRIVAL, //到件
        'DEPARTURE_S' => OrderTraceList::STATUS_DEPARTURE, //发件
        'DEPARTURE_C' => OrderTraceList::STATUS_DEPARTURE, //发件
//            '?' => OrderTraceList::STATUS_FAIL, //问题件
//            '?' => OrderTraceList::STATUS_REJECTION, //拒签
//            '?' => OrderTraceList::STATUS_STAY_IN_WAREHOUSE, //留仓
//            '?' => OrderTraceList::STATUS_SIGN_ON_BEHALF, //代收点代签
//        '?' => OrderTraceList::STATUS_OTHER, //其他
//        '?' => OrderTraceList::STATUS_RETURN, //退件
//        '?' => OrderTraceList::STATUS_RETURN, //退件
//        '?' => OrderTraceList::STATUS_IN_CABINET, //入柜/入代收点
//            '?' => OrderTraceList::STATUS_OUT_CABINET, //出柜/出代收点
    ];

    public function __construct(string $accessToken = '')
    {
        $this->accessToken = $accessToken;
    }

    protected function getClient()
    {
        $client = new JdClient();
        $client->appKey = config('socialite.jd.client_id');
        $client->appSecret = config('socialite.jd.client_secret');
        $client->accessToken = $this->accessToken;

        return $client;
    }

    /**
     * @param array $template
     * @param int $packageNum
     * @param PrintDataPackBo $printPackBo
     * @param WaybillAuth $waybillAuth
     * @param SenderAddressBo $branchAddressBo
     * @param $company
     * @param JdClient $client
     * @return array{url:string,params:array}
     * @throws BusinessException
     */
    public function buildWaybillCodeRequest(array $template, int $packageNum, PrintPackBo $printPackBo, WaybillAuth $waybillAuth, SenderAddressBo $branchAddressBo, $company, JdClient $client): array
    {
        $childMotherOrder = false;
        $total_pack_count = 1;
        $wpCode = $template['wp_code'];
        if (!empty($template['parent_part']) && $template['parent_part'] == 1 && $packageNum > 1) {
//            if (!in_array($template['wp_code'], self::ZIMUJIANMAP)){
//                throw new BusinessException('该快递公司不支持子母件');
//            }
            $total_pack_count = $packageNum;
            $childMotherOrder = true;
        }
        $req = new LdopAlphaWaybillReceiveRequest();
        $order = $printPackBo->master_order_info->toArray();

        $isPlatformOrder = $printPackBo->isPlatformOrder();
        $tid = $printPackBo->getMasterOrderId();

        $toAddress = [];
        if ($isPlatformOrder) {
            //平台订单直接用oaid取号
            $masterOaid = $printPackBo->getMasterOaid();
            $orderShop = Shop::query()->find($order['shop_id']);
            $isRemoteTransit = array_get($order, 'is_remote_transit', false);
            //判断使用的是不是JD的电子面单
            Log::info("判断是否是集运订单", [$isRemoteTransit, $wpCode]);
            if ($isRemoteTransit && !in_array($wpCode, ['jd'])) {
                throw new BusinessException('集运订单请使用JD快递');
            }
            $tde = TDEClient::getInstance($orderShop->access_token,$client->appKey,$client->appSecret);
            $isRemoteTransit = $order['is_remote_transit'];
            $cipherInfo = $order['order_cipher_info'];
            $isEncrypt=StrUtil::isEncrypted($cipherInfo['receiver_name_ciphertext'],20);
            $receiverName = $isEncrypt ? $tde->decrypt($cipherInfo['receiver_name_ciphertext']) : $cipherInfo['receiver_name_mask'];
            $receiverAddress=$isEncrypt ? $tde->decrypt($cipherInfo['receiver_address_ciphertext']) : $cipherInfo['receiver_address_mask'];
            $mobile =$order['receiver_phone'];
            $phone = $cipherInfo['receiver_telephone_ciphertext'];
            $toAddress = [
                'provinceName' => $order['receiver_state'] ?? $order['receiver_province'],
                'cityName' => $order['receiver_city'],
                'countryName' => $order['receiver_district'],
                //'countrysideName' => '不知道什么街道',
                'address' => $receiverAddress,
                'contact' =>$receiverName,
                'mobile' =>$mobile,
                'phone' => $phone
            ];

            if (!$isRemoteTransit&&!$isEncrypt) {
                //不是集运订单，用oaid（这个是原始的收件人的）,也不是加密的情况
                Log::info("不是集运订单，用oaid（这个是原始的收件人的）", [$masterOaid,$isEncrypt]);
                $toAddress['oaid'] = $masterOaid;
            }

        } else {
            //自由打印订单
            $toAddress = [
                'provinceName' => $order['receiver_state'] ?? $order['receiver_province'],
                'cityName' => $order['receiver_city'],
                'countryName' => $order['receiver_district'],
                //'countrysideName' => '不知道什么街道',
                'address' => $order['receiver_address'],
                'contact' => $order['receiver_name'],
                'mobile' => $order['receiver_phone'],
                'phone' => $order['receiver_phone'],
            ];
        }
        //处理增值服务
        /**
         * $template['service_list'] 包含的json字符串格式参考，{"SVC-TEM":{"value":"7"}}
         */
        $newServiceList = [];
        $serviceList = [];
        if (!empty($template['service_list'])) {
            $serviceList = json_decode($template['service_list'], true);
            $insureAmount = $printPackBo->getInsureAmount();
            foreach ($serviceList as $key => $item) {
                if($key=='product_type'&&WaybillUtil::isSF($wpCode)){
                    //产品类型不作为增值服务的内容进行传递
                    continue;
                }
                $service = ["name" => $key];
                $value = null;
                //保价有两种
                if ($key == "INSURE") {
                    $value = $insureAmount;
                } else {
                    $value = ArrayUtil::getArrayValue($item, "value");
                }
                if (!$value) {
                    $service["value"] = $item;
                }
                $newServiceList[] = $service;
            }
        }


        $content = [
            'waybillType' => 1, //运单类型：1普通运单
            'waybillCount' => $total_pack_count, //所需运单的数量
            'providerCode' => $wpCode, //物流编码
            'salePlatform' => isset($order['tid']) ? '0010001' : '0030001', //销售平台
            'platformOrderNo' => $tid, //平台订单号
            'vendorCode' => $waybillAuth->getServiceId(),
            'vendorName' => $waybillAuth->getShopName(), //商家名称
            'vendorOrderCode' => $printPackBo->request_id,
            'fromAddress' => [
                'provinceName' => $branchAddressBo->province,
                'cityName' => $branchAddressBo->city,
                'countryName' => $branchAddressBo->district,
                'countrysideName' => $branchAddressBo->street,
                'address' => $branchAddressBo->address,
                'contact' => $branchAddressBo->sender_name,
                'mobile' => $branchAddressBo->mobile,
                'phone' => $branchAddressBo->mobile,
            ],
            "toAddress" => $toAddress,
            'weight' => 0,
            'volume' => 0,
            'promiseTimeType' => 0,
            'payType' => 0,
            'goodsMoney' => 0.0,
            'shouldPayMoney' => 0.0,
            'needGuarantee' => false,
            'guaranteeMoney' => 0.0,
            'receiveTimeType' => 0,
            'childMotherOrder' => $childMotherOrder
        ];

        //顺丰快递设置快递类型
        if(WaybillUtil::isSF($wpCode)){
            //把增值服务$newServiceList 例如[{"product_type":{"value":"325","desc":"温控包裹"}}
            $content['expressType'] =strval($serviceList['product_type']['value']);
        }
        if (!empty($newServiceList)) {
            $content['serviceList'] = $newServiceList;

        }
        if (in_array($wpCode, ['EMS', 'EMSBZ', 'ZJS', 'ZGYZZHDD', 'KYE', 'SF', 'DBKD'])) {
            $content['settlementCode'] = $company->settlement_code;
        } else {//非直营
            $content['branchCode'] = $company->branch_code;
        }
        $content['expressPayMethod']=1; //快递费付款方式（运费付款方式）1:寄方付 2:收方付 3:第三方付
        $req->setContent(json_encode($content));
        return $client->buildRequest($req, $this->accessToken);
    }

    /**
     * @param array $waybillCodes 运单号
     * @param $orderId
     * @param $wp_code
     * @param JdClient $client
     * @return array
     */
    public function buildWaybillPrintDataRequest(array $waybillCodes, $orderId, $wp_code, JdClient $client): array
    {

        $req = new PrintingPrintDataPullDataRequest();
        $param1 = new PullDataParam1;
        $param1->setObjectId($orderId . '_' . rand(1111, 9999));
        $parameters = new ParametersFixed();
        $parameters->setKeyValue('eCustomerCode', $this->shop->getServiceId());
        $param1->setParameters($parameters);
        $wayBillInfos = [];
        foreach ($waybillCodes as $waybillCode) {
            $wayBillInfo = new Attribute1();
            $wayBillInfo->setOrderNo((string)$orderId);
            $wayBillInfo->setPopFlag(1);
            $wayBillInfo->setWayBillCode($waybillCode);
            $wayBillInfos[] = $wayBillInfo;
        }
        $param1->setWayBillInfos($wayBillInfos);
        $param1->setCpCode($wp_code);
        $req->setParam1($param1->getInstance());
        return $client->buildRequest($req, $this->accessToken);
    }

    protected function getJdClient()
    {
        $client = new \App\Services\Client\JdClient(config('socialite.jd.client_id'), config('socialite.jd.client_secret'));
        $client->setAccessToken($this->accessToken);
        return $client;
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        $client = $this->getClient();
        $req = new LdopAlphaProviderSignSuccessRequest();
        if (empty($serviceId)) {
            $shop = Shop::query()->where('access_token', $this->accessToken)->first();
            $serviceId = $shop->service_id;
        }
        $req->setVendorCode($serviceId);
        $resp = $client->execute($req, $client->accessToken);

        if (!isset($resp->jingdong_ldop_alpha_provider_sign_success_responce->resultInfo->data)) {
            return [];
        }

//        Log::info('面单查询',["wpCode"=>$wpCode,"result"=>$resp->jingdong_ldop_alpha_provider_sign_success_responce->resultInfo->data]);
        $waybillsInfo = [];
        $serviceInfoColsMap = collect(config('jd_service_attributes'))->all();
//        Log::info('增值服务配置信息', $serviceInfoColsMap);
        foreach ($resp->jingdong_ldop_alpha_provider_sign_success_responce->resultInfo->data as $value) {
            if(!empty($wpCode)){
                if($value->providerCode!=$wpCode){
                    //JD的接口不支持物流公司编码，所以这里需要判断物流编码是否一致
                    continue;
                }
            }
            $providerCode = $value->providerCode;
            $operationType=$value->operationType;// 1-直营 2-加盟

            $serviceInfoCols = ArrayUtil::getArrayValue($serviceInfoColsMap, $providerCode, []);

            $branchAccounts = $addresses = [];

            $addresses[] = [
                'city' => $value->address->cityName ?? '',
                'detail' => $value->address->address ?? '',
                'province' => $value->address->provinceName ?? '',
                'district' => $value->address->countryName ?? '',
                'street_name' => $value->address->countrysideName ?? '',
            ];

            $branchAccounts[] = [
                'branch_code' => $value->branchCode ?? 0,
                'branch_name' => $value->branchName ?? '',
                'quantity' => $operationType==1?-1:$value->amount ?? 0,
                'cancel_quantity' => 0,
                'recycled_quantity' => 0,
                'allocated_quantity' => 0,
                'shipp_addresses' => $addresses,
                'settlement_code' => $value->settlementCode ?? null,
                'service_info_cols' => $serviceInfoCols
            ];

            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code' => $providerCode,
                'wp_type' => $value->providerType
            ];
        }

        $data = [];
        //重新排列组合  相同快递组合到一起
        foreach ($waybillsInfo as $item) {
            $data[$item['wp_code']]['branch_account_cols'][] = $item['branch_account_cols'][0];
            $data[$item['wp_code']]['wp_code'] = $item['wp_code'];
            $data[$item['wp_code']]['wp_type'] = $item['wp_type'];
        }
//        Log::info('面单查询', $data);

        return array_values($data);
    }

    /**
     * 单个订单获取面单
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return mixed
     * @throws ArgumentNullException
     * @throws JosGwException
     * @throws MalformedException
     * @throws NoValidKeyException
     * @throws VoucherInfoGetException
     * @throws \JsonMapper_Exception
     * @throws \Throwable
     */
    public function waybillGet($sender, $order, $template, $packageNum)
    {
        $num = 1;
        $result = [];
        $client = $this->getClient();
        //查询授权信息
        $company = Company::query()->where('id', $template['company_id'])->first();
        //解密姓名以及详细地址
        $appKey = config('socialite.jd.client_id');
        $appSecret = config('socialite.jd.client_secret');
        $orderShop = Shop::query()->find($order['shop_id']);
        $tde = TDEClient::getInstance($orderShop->access_token, $appKey, $appSecret);
        $receiverName = isset($order['order_cipher_info']) ? $tde->decrypt($order['order_cipher_info']['receiver_name_ciphertext']) : $order['receiver_name'];
        $receiverAddresss = isset($order['order_cipher_info']) ? $tde->decrypt($order['order_cipher_info']['receiver_address_ciphertext']) : $order['receiver_address'];

        $orderId = isset($order['tid']) ? $order['tid'] : $order['id'];
        $templateShop = Shop::query()->find($template['shop_id']);
        //先取运单号
        $req = new LdopAlphaWaybillReceiveRequest();
        while ($num <= $packageNum) {
            $request_id = isset($order['request_id']) ? $order['request_id'][$num] : $order['id'] . '_' . rand(1111, 9999);
            $payment = isset($order['payment']) ? (float)$order['payment'] : (float)1;
            $payment = sprintf("%01.2f", $payment);
            $content = [
                'waybillType' => 1, //运单类型：1普通运单
                'waybillCount' => 1, //所需运单的数量
                'providerCode' => $template['wp_code'], //物流编码
                'salePlatform' => isset($order['tid']) ? '0010001' : '0030001', //销售平台
                'platformOrderNo' => $orderId, //平台订单号
                'vendorCode' => $templateShop->service_id,//商家编码
                'vendorName' => $templateShop->shop_name, //商家名称
                'vendorOrderCode' => $request_id, //商家自有订单号
                'fromAddress' => [
                    'provinceName' => $sender['province'],
                    'cityName' => $sender['city'],
                    'countryName' => $sender['district'],
                    'countrysideName' => $sender['street'],
                    'address' => $sender['address'],
                    'contact' => $sender['sender_name'],
                    'mobile' => $sender['mobile'],
                    'phone' => $sender['mobile'],
                ],
                'toAddress' => [
                    'provinceName' => $order['receiver_state'] ?? $order['receiver_province'],
                    'cityName' => $order['receiver_city'],
                    'countryName' => $order['receiver_district'],
                    //'countrysideName' => '不知道什么街道',
                    'address' => $receiverAddresss,//需解密
                    'contact' => $receiverName,//需解密
                    'mobile' => $order['receiver_phone'],
                    'phone' => $order['receiver_phone'],
                ],
                'weight' => 0,
                'volume' => 0,
                'promiseTimeType' => 0,
                'payType' => 0,
                'goodsMoney' => $payment,
                'shouldPayMoney' => 0.0,
                'needGuarantee' => false,
                'guaranteeMoney' => 0.0,
                'receiveTimeType' => 0,
            ];
            if (in_array($template['wp_code'], ['EMS', 'EMSBZ', 'ZJS', 'ZGYZZHDD', 'KYE', 'SF', 'DBKD'])) {
                $content['settlementCode'] = $company->settlement_code;
            } else {//非直营
                $content['branchCode'] = $company->branch_code;
            }
            $req->setContent(json_encode($content));
            $resp = $client->execute($req, $client->accessToken);
            $resp = json_decode(json_encode($resp), true);
            //$resp = ['data' => ['platformOrderNo' => '216579384730','waybillCodeList' => ['4280256622638']],'statusMessage' => '调用成功','statusCode' => 0];
            Log::info('京东获取运单号  result:' . json_encode($resp));

            if (isset($resp['statusCode']) && $resp['statusCode'] == 0) {
                //取号成功去获取加密打印
                $req = new PrintingPrintDataPullDataRequest();
                $param1 = new PullDataParam1;
                $param1->setObjectId(isset($order['request_id']) ? (string)$order['request_id'][$num] : $order['id'] . '_' . rand(1111, 9999));
                $parameters = new ParametersFixed();
                $parameters->setKeyValue('eCustomerCode', $templateShop->service_id);
                $param1->setParameters($parameters);

                $wayBillInfo = new Attribute1();
                $wayBillInfo->setOrderNo((string)$orderId);
                $wayBillInfo->setPopFlag(1);
                $wayBillInfo->setWayBillCode($resp['data']['waybillCodeList'][0]);
                $param1->setWayBillInfos([$wayBillInfo]);
                $param1->setCpCode($template['wp_code']);
                $req->setParam1($param1->getInstance());
                $ret = $client->execute($req, $client->accessToken);
                $ret = json_decode(json_encode($ret), true);
                Log::info('京东获取加密打印数据  result：' . json_encode($ret));
                if (isset($ret['jingdong_printing_printData_pullData_responce']['returnType']) &&
                    $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['code'] == 1) {

                    $waybillsData = [
                        'err_no' => 0,
                        'parent_waybill_code' => '',
                        'object_id' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['objectId'],
                        'waybill_code' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['wayBillNo'],
                        'print_data' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['perPrintData'],
                    ];
                    $result[] = $waybillsData;
                } else {
                    $ret['err_no'] = -1;
                    $result[] = $ret;
                }
            } else {
                $resp['err_no'] = -1;
                $result[] = $resp;
            }

            ++$num;
        }

        return $result;
    }

    /**
     * 多个订单获取面单
     * @param $sender
     * @param $orders
     * @param $template
     * @param int $packageNum
     * @return mixed
     * @throws ArgumentNullException
     * @throws JosGwException
     * @throws MalformedException
     * @throws NoValidKeyException
     * @throws VoucherInfoGetException
     * @throws \JsonMapper_Exception
     * @throws \Throwable
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
        $result = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            //单个订单获取面单的详情数组
            $result[$idStr] = $this->waybillGet($sender, $order->toArray(), $template, $packageNum);
        }

        return $result;
    }

    /**
     * 作废面单
     * @param string $cpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return mixed
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $cpCode, string $waybillCode, string $platformWaybillId = '')
    {
        $client = $this->getClient();
        $req = new LdopAlphaWaybillApiUnbindRequest();
        $req->setProviderCode($cpCode);
        $req->setWaybillCode($waybillCode);

        $resp = $client->execute($req, $client->accessToken);
        $resp = json_decode(json_encode($resp), true);
        Log::info('京东取消运单号  result::' . json_encode($resp));

        if (!isset($resp['jingdong_ldop_alpha_waybill_api_unbind_responce']['resultInfo']['statusCode'])
            || $resp['jingdong_ldop_alpha_waybill_api_unbind_responce']['resultInfo']['statusCode'] != 0) {
            throw new \Exception($resp['jingdong_ldop_alpha_waybill_api_unbind_responce']['resultInfo']['statusMessage'] ?? "取消失败");
        }

        return true;
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @return mixed
     */
    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $client = $this->getClient();
        $req = new PrintingTemplateGetTemplateListRequest();
        $param1 = new TemplateParam1;
        $param1->setCpCode($wpCode);
        $req->setParam1($param1->getInstance());
        $resp = $client->execute($req, $client->accessToken);

        $standardTemplates = [];
        if (!isset($resp->jingdong_printing_template_getTemplateList_responce->returnType) || $resp->jingdong_printing_template_getTemplateList_responce->returnType->code != '1') {
            return $standardTemplates;
        }
        foreach ($resp->jingdong_printing_template_getTemplateList_responce->returnType->datas->sDatas as $template) {
            foreach ($template->standardTemplates as $item) {
                if (!array_key_exists($item->standardWaybillType, $standardTemplates)) {
                    $standardTemplates[$item->standardWaybillType] = [
                        'standard_waybill_type' => $item->standardWaybillType,
                        'standard_template_name' => $item->standardTemplateName,
                        'standard_template_url' => $item->standardTemplateUrl,
                        'standard_template_id' => $item->standardTemplateId,
                    ];
                }
            }
        }

        return $standardTemplates;
    }

    /**
     * 官方自定义模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return mixed
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $client = $this->getClient();
        $req = new PrintingTemplateGetTemplateListRequest();
        $param1 = new TemplateParam1;
        $param1->setCpCode($wpCode);
        $req->setParam1($param1->getInstance());
        $resp = $client->execute($req, $client->accessToken);

        $standardTemplates = [];
        if (!isset($resp->jingdong_printing_template_getTemplateList_responce->returnType) || $resp->jingdong_printing_template_getTemplateList_responce->returnType->code != '1') {
            return $standardTemplates;
        }
        foreach ($resp->jingdong_printing_template_getTemplateList_responce->returnType->datas->sDatas as $template) {
            foreach ($template->standardTemplates as $item) {
                if (!array_key_exists($item->standardWaybillType, $standardTemplates)) {
                    $standardTemplates[] = [
                        'standard_waybill_type' => $item->standardWaybillType,
                        'standard_template_name' => $item->standardTemplateName,
                        'standard_template_url' => $item->standardTemplateUrl,
                        'standard_template_id' => $item->standardTemplateId,
                    ];
                }
            }
        }

        return $standardTemplates;
    }

    /**
     * auth link
     * @param $shopId
     * @return mixed
     */
    public function getLoginUrl($shopId)
    {
        // TODO: Implement getLoginUrl() method.
    }

    /**
     * access token
     * @param string $code
     * @return mixed
     */
    public function getAccessToken(string $code)
    {
        return $this->accessToken;
    }

    /**
     * 订阅物流轨迹
     * @param string $receiverPhone
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
    {
        // TODO: Implement sendOrderLogisticsTraceMsg() method.
    }

    /**
     * 发送获取物流轨迹信息
     * @see https://open.jd.com/home/<USER>/#/doc/apiAuthPackage?apiCateId=667&apiId=2087&apiName=jingdong.getThirdTrace
     * @param array $waybill
     * @return mixed
     * @throws ApiException
     * @throws OrderException
     */
    protected function sendGetOrderTraceList(array $waybill)
    {
        $client = $this->getClient();
        $req = new GetThirdTraceRequest();

        if (empty($waybill['tid'])) {
            return [];
        }
        $req->setOrderId($waybill['tid']);

        $ret = $client->execute($req, $client->accessToken);
        $ret = json_decode(json_encode($ret), true);
        $this->handleResp($ret);
        if (empty($ret['jingdong_getThirdTrace_responce']['result']['result'])) {
            return [];
        }
        $data = $this->formatToOrderTrace($waybill, $ret['jingdong_getThirdTrace_responce']['result']['result']);

        return $data;
    }

    public function assemWaybillPackagesForOpenApi($platform, $sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1, $productType = null)
    {
        $result = [];
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            //单个订单获取面单的详情数组
            $result[$idStr] = $this->waybillGetForOpenApi($platform, $sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum, $productType);
        }

        return $result;
    }

    public function waybillGetForOpenApi($platform, $sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum, $productType = null)
    {
        // 文档 https://ces.s3.cn-north-1.jdcloud-oss.com/%E9%98%BF%E5%B0%94%E6%B3%95-%E9%9D%92%E9%BE%99%E5%AF%B9%E6%8E%A5%E6%89%BF%E8%BF%90%E5%95%86%E8%AF%B4%E6%98%8E%E4%B9%A6%EF%BC%88ISV%E6%8E%A5%E5%8F%A3%EF%BC%893.2%281%29.pdf?AWSAccessKeyId=C4098DEBFDFA43577FDC514B2E54E966&Expires=**********&Signature=SKTUXSZWfq1jaxPUcmp5FUWVwRo%3D
        $num = 1;
        $result = [];
        $client = $this->getClient();
        //查询店铺信息
        $shop = Shop::query()->where('access_token', $this->accessToken)->first();
        //获取签约信息
        $resultInfo = $this->waybillSubscriptionQuery($wpCode, $shop->service_id);
        $settlementCode = $resultInfo[0]['branch_account_cols'][0]['settlement_code'];
        $branchCode = $resultInfo[0]['branch_account_cols'][0]['branch_code'];
        foreach ($resultInfo as $item) {
            if ($item['wp_code'] == $wpCode) {
                foreach ($item['branch_account_cols'] as $v) {
                    if ($sender['province'] == $v['shipp_addresses'][0]['province'] && $sender['city'] == $v['shipp_addresses'][0]['city'] &&
                        $sender['district'] == $v['shipp_addresses'][0]['district'] && $sender['address'] == $v['shipp_addresses'][0]['detail']) {
                        $branchCode = $v['branch_code'] ?? 0;
                        $settlementCode = $v['settlement_code'] ?? 0;
                    }
                }
            }
        }
        //解密姓名以及详细地址
        //$accessToken = $this->accessToken;
        $orderShop = Shop::query()->where('id', $order['shop_id'])->first();
        $accessToken = $orderShop->access_token;
        $appKey = config('socialite.jd.client_id');
        $appSecret = config('socialite.jd.client_secret');
        $oaid = ArrayUtil::getArrayValue($order, 'order_cipher_info.oaid');
        $receiverName = $order['receiver_name'];
        $receiverAddresss = $order['receiver_address'];

        $orderId = $order['tid'] ?? ($order['order_no'] ?? $order['id']);
        $orderIdSuffix = rand(1000, 9999);
        !empty($order['request_id']) && $orderIdSuffix = $order['request_id'];
        //先取运单号
        $req = new LdopAlphaWaybillReceiveRequest();
        while ($num <= $packageNum) {
            $toAddress = [
                'provinceName' => $order['receiver_state'] ?? $order['receiver_province'],
                'cityName' => $order['receiver_city'],
                'countryName' => $order['receiver_district'],
                //'countrysideName' => '不知道什么街道',
                'address' => $receiverAddresss,//需解密
                'contact' => $receiverName,//需解密
                'mobile' => $order['receiver_phone'],
                'phone' => $order['receiver_phone'],
                'oaid'=> $oaid
            ];


            $content = [
                'waybillType' => 1, //运单类型：1普通运单
                'waybillCount' => isset($order['child_parent_packages_count']) ? (int)$order['child_parent_packages_count'] : 1, //所需运单的数量
                'providerCode' => $wpCode, //物流编码
                'salePlatform' => isset($order['tid']) ? '0010001' : '0030001', //销售平台
                'platformOrderNo' => $orderId, //平台订单号
                'vendorCode' => $shop->service_id,//商家编码
                'vendorName' => $shop->shop_name, //商家名称
                'vendorOrderCode' => $orderIdSuffix, //商家自有订单号
                'fromAddress' => [
                    'provinceName' => $sender['province'],
                    'cityName' => $sender['city'],
                    'countryName' => $sender['district'],
                    //'countrysideName' => '不知道什么街道',
                    'address' => $sender['address'],
                    'contact' => $sender['sender_name'],
                    'mobile' => $sender['mobile'],
                    'phone' => $sender['mobile'],
                ],
                'toAddress' => $toAddress,
                'weight' => 0,
                'volume' => 0,
                'promiseTimeType' => 0,
                'payType' => 0,
                'goodsMoney' => (float)1,
                'shouldPayMoney' => 0.0,
                'needGuarantee' => false,
                'guaranteeMoney' => 0.0,
                'receiveTimeType' => 0,
                'childMotherOrder' => isset($order['is_child_parent_order']) && $order['is_child_parent_order'] == 1,
            ];

            if (in_array($wpCode, ['EMS', 'EMSBZ', 'ZJS', 'ZGYZZHDD', 'KYE', 'SF', 'DBKD'])) {
                $content['settlementCode'] = $settlementCode;
            } else {//非直营
                $content['branchCode'] = $branchCode;
            }
            if (in_array($wpCode, ['SF'])) {
                $content['expressPayMethod'] = "1";
                $content['expressType'] = $productType ?? "285";
            }
            $req->setContent(json_encode($content));
            Log::info('京东获取运单号  $content:', $content);
            $resp = $client->execute($req, $client->accessToken);
            $resp = json_decode(json_encode($resp), true);
            //$resp = ['data' => ['platformOrderNo' => '216579384730', 'waybillCodeList' => ['4280256622638']], 'statusMessage' => '调用成功', 'statusCode' => 0];
            Log::info('京东获取运单号  result:' . json_encode($resp));

            if (isset($resp['statusCode']) && $resp['statusCode'] == 0) {
                //取号成功去获取加密打印
                $req = new PrintingPrintDataPullDataRequest();
                $param1 = new PullDataParam1;
                $param1->setObjectId($order['id'] . '_' . rand(1111, 9999));
                $parameters = new ParametersFixed();
                $parameters->setKeyValue('eCustomerCode', $shop->service_id);
                $param1->setParameters($parameters);

                $wayBillInfo = new Attribute1();
                $wayBillInfo->setOrderNo((string)$orderId);
                $wayBillInfo->setPopFlag(1);
                $wayBillInfo->setWayBillCode($resp['data']['waybillCodeList'][0]);
                $param1->setWayBillInfos([$wayBillInfo]);
                $param1->setCpCode($wpCode);
                $req->setParam1($param1->getInstance());
                $ret = $client->execute($req, $client->accessToken);
                $ret = json_decode(json_encode($ret), true);
                Log::info('京东获取加密打印数据  result：' . json_encode($ret));
                if (isset($ret['jingdong_printing_printData_pullData_responce']['returnType']) && ArrayUtil::array_key_exists('jingdong_printing_printData_pullData_responce.returnType.prePrintDatas', $ret) &&
                    $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['code'] == 1) {

                    $insertPrint = [
                        'waybill_code' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['wayBillNo'],
                        'print_data' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['perPrintData'],
                        'templateUrl' => $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode),
                    ];

                    $waybillsData = [
                        'err_no' => 0,
                        'object_id' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['objectId'],
                        'print_data' => json_encode($insertPrint),
                        'waybill_code' => $ret['jingdong_printing_printData_pullData_responce']['returnType']['prePrintDatas'][0]['wayBillNo'],
                        'templateUrl' => $this->getTemplateUrl($waybillType, $waybillTemp, $platform, $wpCode),
                    ];

                    $result[] = $waybillsData;
                } else {
                    $ret['err_no'] = -1;
                    $result[] = $ret;
                }
            } else {
                $resp['err_no'] = -1;
                $result[] = $resp;
            }
            ++$num;
        }

        return $result;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        // TODO: Implement updateWaybillData() method.
    }

    private function formatToOrderTrace(array $waybill, $orderTrace)
    {
        $orderTraceList = $orderTrace[0]['listTrace'];

        $trace_list = [];
        foreach ($orderTraceList as $index => $item) {
            $status = $this->orderTraceMap[$item['scanType']] ?? OrderTraceList::STATUS_OTHER;
            $trace_list[] = [
                "status" => $status, // 状态
                "status_desc" => OrderTraceList::STATUS_NAME_MAPPING[$status], //状态描述
                "action" => $item['scanType'], // 平台那边的状态
                "action_desc" => '', // 平台那边的状态描述
                "site_name" => '', // 站点名称
                "status_time" => date('Y-m-d H:i:s', intval($item['processDate'] / 1000)), // 状态发生时间
                "time" => date('Y-m-d H:i:s', intval($item['processDate'] / 1000)), // 数据创建时间
                "desc" => $item['processInfo'], // 流转过程
            ];
        }
        $latest = collect($trace_list)->sortByDesc('time')->first();
        if (empty($latest)) {
            // 没有轨迹，补充已发货待揽收
            $latest = [
                "status_desc" => OrderTraceList::STATUS_NAME_MAPPING[OrderTraceList::STATUS_SHIPPED],
                "status" => OrderTraceList::STATUS_SHIPPED,
                "action" => 0,
                "status_time" => $waybill['send_at'] ?? null,
                "time" => $waybill['send_at'] ?? null,
                "desc" => "已发货，等待揽收",
            ];
        }
        $data = [
            "type" => $waybill['type'] ?? 0,
            'tid' => $waybill['tid'] ?? '',
            'express_code' => $waybill['express_code'],
            'express_no' => $waybill['express_no'],
            'status' => $latest['status'],
            'action' => $latest['action'],
            'receiver_province' => $waybill['receiver_province'] ?? '',
            'receiver_name' => $waybill['receiver_name'] ?? '',
            'send_at' => $waybill['send_at'] ?? null,
            'latest_updated_at' => $latest['time'],
            'latest_trace' => $latest['desc'],
            'trace_list' => json_encode($trace_list, JSON_UNESCAPED_UNICODE),
        ];
        return $data;
    }

    /**
     * @param $ret
     * @throws ApiException
     * @throws OrderException
     * <AUTHOR>
     */
    protected function handleResp($ret): void
    {
        $this->getJdClient()->handleResp($ret);
    }

    /**
     * 请求厂家电子面单
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBos
     * @param $template
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemFactoryWaybillPackages(SenderAddressBo $branchAddressBo, array $printPackBos, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }


    /**
     * 请求电子面单
     * JD的电子面单分两部一部分是先取号，然后再取打印内容
     * @param SenderAddressBo $branchAddressBo
     * @param PrintPackBo[] $printPackBoList
     * @param array $template
     * @param int $packageNum
     * @return PrintDataPackBo[]
     * <AUTHOR>
     */
    public function assemWaybillPackageByPrintPackBo(SenderAddressBo $branchAddressBo, array $printPackBoList, array $template, int $packageNum): array
    {
        $client = $this->getClient();
        $company = Company::query()->where('id', $template['company_id'])->first();

        /**
         * @var array{orderId:string,errorCode:string,errorMsg:string} $errorInfos
         */
        $errorInfos = [];

        /**
         * @var  WaybillAuth $waybillAuth
         */
        $waybillAuth = $this->shop;
        $data = [];
        foreach ($printPackBoList as $printPackBo) {
            $orderId = $printPackBo->getMasterOrderId();
            try {
                $urlAndParams = $this->buildWaybillCodeRequest($template, $packageNum, $printPackBo, $waybillAuth, $branchAddressBo, $company, $client);
                Log::info("先取号请求参数", $urlAndParams);
                $data[$orderId] = [
                    'url' => $urlAndParams['url'],
                    'params' => $urlAndParams['params'],
                ];
            } catch (\Exception $exception) {
                Log::error("先取号异常", [$exception->getMessage(), $exception->getTraceAsString()]);
                $errorInfos[] = [
                    "orderId" => $orderId,
                    "errorCode" => ErrorConst::PARAM_ERROR_CODE,
                    "errorMsg" => $exception->getMessage()
                ];
            }

        }
        Log::info("先取号请求参数", $data);
        $responseArr = $this->poolCurl($data, 'json', false, false);
//        \Log::info("先取号返回结果：" . json_encode($responseArr));
        $responseArr = json_decode(json_encode($responseArr), true);
        /**
         * orderId：订单号,waybillCode : 运单号,sub_waybill_codes:子单号
         * @var  array{orderId:string,waybillCode:string,subWaybillCodes:array} $successInfos
         */
        $successInfos = [];


        foreach ($responseArr as $orderId => $response) {
            Log::info("订单：{$orderId}，返回结果：" . json_encode($response));
            try {
                $statusCode = ArrayUtil::getArrayValue($response, 'statusCode');

//                $returnTypeCode = ArrayUtil::getArrayValue($response, 'jingdong_ldop_alpha_waybill_receive_responce.resultInfo.statusCode');
                if ($statusCode == 0) {
                    /**
                     * @var string $platformOrderNo
                     */
                    $platformOrderNo = ArrayUtil::getArrayValue($response, 'data.platformOrderNo');
                    /**
                     * @var string[] $waybillCodeList
                     */
                    $waybillCodeList = ArrayUtil::getArrayValue($response, 'data.waybillCodeList');
                    //把第一个单号当母单号处理
                    $successInfos[] = ["orderId" => $platformOrderNo, "waybillCode" => $waybillCodeList[0], 'subWaybillCodes' => array_slice($waybillCodeList, 1)];
                } else {
                    $statusMessage = ArrayUtil::getArrayValue($response, 'statusMessage');
                    $errorInfos[] = [
                        "orderId" => $orderId,
                        "errorCode" => $statusCode,
                        "errorMsg" => $statusMessage,
                    ];
                }

            } catch (\Throwable $throwable) {
                Log::error("JD取号异常", [$throwable->getMessage(), $throwable->getTraceAsString()]);
                $errorInfos[] = [
                    "orderId" => $orderId,
                    "errorMsg" => $throwable->getMessage(),
                    'errorCode' => self::WAYBILL_ERROR_CODE,
                ];
            }
        }

        Log::info("取号结果", ["success" => $successInfos, "error" => $errorInfos]);
        /**
         * 按成功取号的,取打印内容
         */
        $waybillPrintData = [];

        foreach ($successInfos as $successInfo) {
            $orderId = $successInfo['orderId'];
            //把success里面的运单号以及子单号拼起来一次取出来
            $waybillCode = $successInfo['waybillCode'];
            $waybillCodes = [$waybillCode];
            $waybillCodes = array_merge($waybillCodes, $successInfo['subWaybillCodes']);
            $urlAndParams = $this->buildWaybillPrintDataRequest($waybillCodes, $orderId, $template['wp_code'], $client);
            $waybillPrintData[$orderId] = [
                'url' => $urlAndParams['url'],
                'waybillCode' => $successInfo['waybillCode'],
                'params' => $urlAndParams['params'],
            ];
        }


        $responseArr = $this->poolCurl($waybillPrintData, 'json', false, false);
        Log::info("打印内容返回结果" . json_encode($responseArr));
        $responseArr = json_decode(json_encode($responseArr), true);
        /**
         * @var  array{waybillCode:string,printData:string,orderId:string} $successPrintData
         */
        $successPrintData = [];
        /**
         * @var array{waybillCode:string,errorMsg:string,orderId:string} $errorPrintData
         */
        $errorPrintData = [];
        foreach ($responseArr as $orderId => $response) {
            Log::info("打印内容", [$orderId, $response]);
            try {
                $this->handleResp($response);
                $returnType = ArrayUtil::getArrayValue($response, 'jingdong_printing_printData_pullData_responce.returnType',[]);

                $returnTypeCode = ArrayUtil::getArrayValue($returnType, 'code');
                $prePrintDatas = ArrayUtil::getArrayValue($returnType, 'prePrintDatas');
                //因为是子母件一起去取的，所以可能返回多个，先返回第一个做成功与否的判断
                $prePrintDataFirst = $prePrintDatas[0] ?? null;
                if ($returnTypeCode == "1" && $prePrintDataFirst && $prePrintDataFirst['code'] == "1") {
                    foreach ($prePrintDatas as $prePrintData) {
                        $successPrintData[] = [
                            'waybillCode' => $prePrintData['wayBillNo'],
                            'printData' => $prePrintData['perPrintData'],

                        ];
                    }

                } else {
                    $errorPrintData[] = [
                        'waybillCode' => $prePrintDataFirst['wayBillNo'],
                        'errorMsg' => $prePrintDataFirst['msg'],
                        'errorCode' => $prePrintDataFirst['code'],

                    ];
                }

            } catch (\Throwable $throwable) {
                $errorPrintData[] = [
                    'waybillCode' => $waybillPrintData[$orderId]['waybillCode'],
                    "errorMsg" => $throwable->getMessage(),
                    "errorCode" => self::WAYBILL_ERROR_CODE,
                ];
            }
        }
        Log::info("返回成功和失败的打印内容", ["success" => $successPrintData, "errors" => $errorPrintData]);
        //把成功信息和错误信息都按订单ID进行排序
        $successInfosKeyByOrderId = array_pluck($successInfos, null, 'orderId');
        $errorInfosKeyByOrderId = array_pluck($errorInfos, null, 'orderId');
        $successPrintDataKeyByWaybillCode = array_pluck($successPrintData, null, 'waybillCode');
        $errorPrintDataKeyByWaybillCode = array_pluck($errorPrintData, null, 'waybillCode');

        //准备打印内容
        $printDataPackBoList = [];

        foreach ($printPackBoList as $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->copyByPrintPackBo($printPackBo);
            $orderId = $printPackBo->getMasterOrderId();
            if (isset($successInfosKeyByOrderId[$orderId])) {
                $successInfo = $successInfosKeyByOrderId[$orderId];
                $waybillCode = $successInfo['waybillCode'];
                Log::info("提取取号打印内容", [$orderId, $waybillCode]);
                $printDataPackBo->waybill_code = $waybillCode;
                $printDataPackBo->wp_code = $template['wp_code'];
                $printDataPackBo->sub_waybill_code_arr = $successInfo['subWaybillCodes'];
                $waybillsPrintData = new WaybillsPrintDataBo();
                $waybillsPrintData->package_id = $printDataPackBo->package_id;
                $waybillsPrintData->waybill_code = $waybillCode;
                if (isset($successPrintDataKeyByWaybillCode[$waybillCode])) {
                    $waybillsPrintData->encrypted_data = $successPrintDataKeyByWaybillCode[$waybillCode]['printData'];
                    $printDataPackBo->setWaybillsPrintData($waybillsPrintData);
                } else {
                    $error = $errorPrintDataKeyByWaybillCode[$waybillCode];
                    if ($error) {
                        $printDataPackBo->setError([$error['errorCode'], $error['errorMsg']]);
                        $waybillsPrintData->setError([$error['errorCode'], $error['errorMsg']]);
                        Log::info("取号打印内容", [$waybillCode, $error['errorCode'], $error['errorMsg']]);
                    }else{
                        Log::error("取号打印内容不存在", [$waybillCode]);
                    }
                }
                $subWaybillPrintData = [];
                foreach ($printDataPackBo->sub_waybill_code_arr as $subWaybillCode) {
                    $waybillsPrintData = new WaybillsPrintDataBo();
                    $waybillsPrintData->package_id = $printDataPackBo->package_id;
                    $waybillsPrintData->waybill_code = $subWaybillCode;
                    if (isset($successPrintDataKeyByWaybillCode[$subWaybillCode])) {
                        $waybillsPrintData->encrypted_data = $successPrintDataKeyByWaybillCode[$subWaybillCode]['printData'];

                    } else {
                        $error = $errorPrintDataKeyByWaybillCode[$subWaybillCode];
                        if ($error) {
                            $waybillsPrintData->setError([$error['errorCode'], $error['errorMsg']]);
                        }
                    }
                    $subWaybillPrintData[] = $waybillsPrintData;
                }
                $printDataPackBo->setSubWaybillsPrintDataArr($subWaybillPrintData);
            } elseif (isset($errorInfosKeyByOrderId[$orderId])) {
                $errInfo = $errorInfosKeyByOrderId[$orderId];
                $err_msg = $errInfo['errorMsg'] . "({$errInfo['errorCode']})";
                $printDataPackBo->setError(ErrorConst::PLATFORM_ERROR, $err_msg);
            }
            $printDataPackBoList[] = $printDataPackBo;
        }
        Log::info("返回取号数据", $printDataPackBoList);
        return $printDataPackBoList;
    }

    public function handleResponse(array $response, bool $isPoolCurl = false, bool $isDY = false)
    {
        //poolCurl 取号的body都在data里面
//        if($this->dataType=='JSON'){
        return json_decode(ArrayUtil::getArrayValue($response, 'data', '{}'), true);
//        }
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}

