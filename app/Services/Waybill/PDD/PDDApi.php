<?php

namespace App\Services\Waybill\PDD;

use App\Constants\PlatformConst;
use App\Models\OrderTraceList;
use App\Services\Client\PddClient;
use App\Services\Waybill\AbstractWaybillService;
use Illuminate\Support\Facades\Log;
use TopClient\domain\Order;

class PDDApi extends AbstractWaybillService
{
    protected $baseUrl  = 'http://gw-api.pinduoduo.com/api/router';
    protected $apiUrl   = '';
    protected $dataType = 'JSON';
    protected $version  = 'V1';
    private   $authConfig;
    private   $clientID;
    private   $clientSecret;
    private   $timestamp;
    protected $is_test  = false;
    protected $waybillPlatformType = PlatformConst::WAYBILL_PDD; // 电子面单平台类型

    public function __construct(string $accessToken = '')
    {
        $this->authConfig   = config('waybill.pdd');
        $this->clientID     = $this->authConfig['client_id'];
        $this->clientSecret = $this->authConfig['client_secret'];
        $this->accessToken  = $accessToken;
        $this->timestamp    = time();
    }

    public function isTest()
    {
        $this->is_test = true;
    }

	//物流状态码
	const TRACE_ARR = [
        'GOT' => OrderTraceList::STATUS_GOT,
        'SEND' => OrderTraceList::STATUS_SEND,
        'SIGN' => OrderTraceList::STATUS_SIGN,
        'ARRIVAL' => OrderTraceList::STATUS_ARRIVAL,
        'DEPARTURE' => OrderTraceList::STATUS_DEPARTURE,
        'FAIL' => OrderTraceList::STATUS_FAIL,
        'REJECTION' => OrderTraceList::STATUS_REJECTION,
        'STAY_IN_WAREHOUSE' => OrderTraceList::STATUS_STAY_IN_WAREHOUSE,
        'SIGN_ON_BEHALF' => OrderTraceList::STATUS_SIGN_ON_BEHALF,
        'OTHER' => OrderTraceList::STATUS_OTHER,
        'RETURN' => OrderTraceList::STATUS_RETURN,
        'IN_CABINET' => OrderTraceList::STATUS_IN_CABINET,
        'OUT_CABINET' => OrderTraceList::STATUS_OUT_CABINET,
	];

    public function getLoginUrl($shopId)
    {
        return null;
    }

    public function getAccessToken(string $code)
    {
        return [];
    }

    /**
     * 请求接口
     * @param $data
     * @return array|string
     */
    public function request($apiName, $data = array())
    {
        $param    = $this->createRequestParam($apiName, $data);
        $response = $this->Curl($this->apiUrl, $param, 'POST');

//	    Log::info(var_export($response, true));

	    return $this->handleResponse($response);
    }

    /**
     * 组装请求数据
     * @param string $apiName
     * @param array  $data
     * @return array
     */
    public function createRequestParam(string $apiName, array $data = array())
    {
        $params         = [
            'type'         => $apiName,
            'client_id'    => $this->clientID,
            'data_type'    => $this->dataType,
            'version'      => $this->version,
//            'access_token' => $this->accessToken,
            'timestamp'    => $this->timestamp,
        ];
	    if ($accessToken = $this->accessToken) { // 无需授权的接口，该字段不参与sign签名运算
		    $params['access_token'] = $accessToken;
	    }

        $signature      = $this->sign(array_merge($params, $data));
        $params['sign'] = $signature;
        $this->apiUrl  = '';
        $this->apiUrl  = $this->baseUrl . '?';
        foreach ($params as $k => $v) {
            $this->apiUrl .= "$k=" . urlencode($v) . '&';
        }
        $this->apiUrl = substr($this->apiUrl, 0, -1);

        $this->setRequestData($data);
        return $data;
    }

    /**
     * 签名
     * @param string $params
     * @return mixed
     */
    public function sign($params)
    {
        ksort($params);
        $sign = $this->clientSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $this->clientSecret;

        return strtoupper(md5($sign));
    }

    /**
     * 面单查询
     * @param string $wpCode
     * @param string $serviceId
     * @return array
     * @throws \Exception
     */
    public function waybillSubscriptionQuery(string $wpCode = '', string $serviceId = ''): array
    {
        $type   = 'pdd.waybill.search';
        $data   = array(
            'wp_code' => $wpCode
        );
        $result = $this->request($type, $data);
        if (isset($result->error_msg)) {
            Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->waybill_apply_subscription_cols)) {
            return [];
        }
        $waybillsInfo = [];
        foreach ($result->waybill_apply_subscription_cols as $waybill_apply_subscription_col) {
            $branchAccounts = [];
            foreach ($waybill_apply_subscription_col->branch_account_cols as $key => $account_col) {
                $branchAccounts[$key] = [
                    'branch_code'        => $account_col->branch_code ?? 0,
                    'branch_name'        => $account_col->branch_name ?? '',
                    'quantity'           => $account_col->quantity ?? 0,
                    'cancel_quantity'    => $account_col->cancel_quantity ?? 0,
                    'recycled_quantity'  => $account_col->recycled_quantity ?? 0,
                    'allocated_quantity' => $account_col->allocated_quantity ?? 0,
                    'shipp_addresses'    => $account_col->shipp_address_cols
                ];
            }
            $waybillsInfo[] = [
                'branch_account_cols' => $branchAccounts,
                'wp_code'             => $waybill_apply_subscription_col->wp_code
            ];
        }

        return $waybillsInfo;
    }

    /**
     * 获取面单
     * @param     $sender
     * @param     $orders
     * @param     $template
     * @param int $packageNum
     * @return array
     */
    public function assemWaybillPackages($sender, $orders, $template, $packageNum = 1)
    {
	    //一单多包，普通取号
	    $result = [];
	    if ($packageNum > 1) {
		    foreach ($orders as $order) {
			    $idStr     = handleOrderIdStr($order);
			    //单个订单获取面单的详情数组
			    $result[$idStr] = $this->waybillGet($sender, $order->toArray(), $template, $packageNum);
		    }

		    return $result;
	    }

	    //非一单多包情况，并行异步请求
	    $type   = 'pdd.waybill.get';
	    $data   = [];
	    foreach ($orders as $order) {
		    $idStr     = handleOrderIdStr($order);
		    $applyInfo = $this->getWayBillRequestData($sender, $order->toArray(), $template, $packageNum);

		    $data[$idStr] = [
			    'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
			    'url'    => $this->apiUrl,
		    ];
	    }

	    $response = $this->poolCurl($data, 'POST');

	    Log::debug('pdd_response', [$response]);
	    foreach ($response as $orderIdStr => $waybill) {
		    if (isset($waybill->modules)) {
			    foreach ($waybill->modules as $module) {
				    $printData    = json_decode($module->print_data, true);
				    $waybillsData = [
					    'object_id'           => $module->object_id,
					    'waybill_code'        => $module->waybill_code,
					    'print_data'          => $printData['encryptedData'],
					    'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
				    ];
			    }
			    $result[$orderIdStr][] = $waybillsData;
		    }else {
			    Log::error($type . '取号错误', [$orderIdStr => $waybill]);
			    $result[$orderIdStr][] = $waybill;
		    }
	    }

	    return $result;
    }

    /**
     * 获取面单号
     * @param     $sender
     * @param     $order
     * @param     $template
     * @param int $packageNum
     * @return array|bool|string
     */
    public function waybillGet($sender, $order, $template, $packageNum = 1)
    {
        $type      = 'pdd.waybill.get';
        $applyInfo = $this->getWayBillRequestData($sender, $order, $template, $packageNum);
        $result    = [];
        foreach ($applyInfo as $info) {
            try {
                $waybill = $this->request($type, $info);
                if (!isset($waybill->modules)) {
                    Log::error('API=>' . $type, [$waybill]);

                    return false;
                }
                $waybillsData = [];
                foreach ($waybill->modules as $module) {
                    $printData      = json_decode($module->print_data, true);
                    $waybillsData = [
                        'object_id'           => $module->object_id,
                        'waybill_code'        => $module->waybill_code,
                        'print_data'          => $printData['encryptedData'],
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[] = $waybillsData;
            } catch (\Exception $e) {
                Log::error("获取电子面单失败" . $type, [$e->getMessage(), $e->getTraceAsString()]);
                $result[] = $e->getMessage();
            }
        }

        return $result;
    }

    /**
     * 组装面单请求数据
     * @param $sender
     * @param $order
     * @param $template
     * @param $packageNum
     * @return array
     */
    private function getWayBillRequestData($sender, $order, $template, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id']          = $order['tid'] ?? $order['id'] .'-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list'    => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
	                'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id'                   => "",
                'goods_description'    => "",
                'items'                => $items,
                'volume'               => "",
                'weight'               => "",
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => "",
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $template['template_url'];
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                                                = [];
            $data['wp_code']                                     = $template['wp_code'];
            $data['need_encrypt']                                = true;
            $data['trade_order_info_dtos']                       = $tradeOrderInfoDtos;
            $data['sender']                                      = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[] = $temp;
            ++$num;
        }

        return $returnArr;
    }

    /**
     * 取消面单
     * @param string $wpCode
     * @param string $waybillCode
     * @param string $platformWaybillId
     * @return array|bool|mixed|string
     * @throws \Exception
     */
    public function wayBillCancelDiscard(string $wpCode, string $waybillCode,string $platformWaybillId = '')
    {
        $type   = 'pdd.waybill.cancel';
        $data   = array(
            'wp_code'      => $wpCode,
            'waybill_code' => $waybillCode
        );
        $result = $this->request($type, $data);
        \Log::info('API=>' . $type, [$result]);
        if (!isset($result->cancel_result)) {
            if ($result->sub_msg == '面单已取消') {
                return true;
            }
	        if (isset($result->sub_msg)){
		        throw new \Exception($result->sub_msg);
	        }
        }

        return $result;
    }

    /**
     * 获取标准模板
     * @param string $wpCode
     * @return array
     * @throws \Exception
     */
    public function getCloudPrintStdTemplates(string $wpCode = '')
    {
        $type   = 'pdd.cloudprint.stdtemplates.get';
        $data   = array(
            'wp_code' => $wpCode,
        );
        $result = $this->request($type, $data);
        if (!isset($result->result)) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->result->datas['0'])) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception('面单服务查询失败!');
        }

        return array_column($result->result->datas['0']->standard_templates, null, 'standard_waybill_type');
    }

    /**
     * 获取标准模板
     * @param string $wpCode
     * @param string|null $extendedInfo
     * @return array
     * @throws \Exception
     */
    public function getCloudPrintStdTemplatesNew(string $wpCode = '',?string $extendedInfo=null)
    {
        $type   = 'pdd.cloudprint.stdtemplates.get';
        $data   = array(
            'wp_code' => $wpCode,
        );
        $result = $this->request($type, $data);
        if (!isset($result->result)) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception($result->error_msg);
        }
        if (empty($result->result->datas['0'])) {
            \Log::error('API=>' . $type, [$result]);
            throw new \Exception('面单服务查询失败!');
        }

        return array_column($result->result->datas['0']->standard_templates, null);
    }

	/**
	 * 物流轨迹订阅
	 * @param string $receiverPhone
	 * @param string $expressCode
	 * @param string $expressNo
	 * @return bool|mixed
	 */
	public function sendOrderLogisticsTraceMsg(string $receiverPhone, string $expressCode, string $expressNo)
	{
		$type   = 'pdd.logistics.isv.trace.notify.sub';
		$data   = array(
			'ship_code' => $expressCode,
			'tel'       => $receiverPhone,
			'track_no'  => $expressNo,
		);
		$result = $this->request($type, $data);

		\Log::debug('消息订阅返回值', ['express_no' => $expressNo, 'result' => $result]);

		if (isset($result->is_success)) {
			return true;
		}
		return false;
	}

	protected function getClient()
	{
		return new PddClient(config('waybill.pddwb.client_id'), config('waybill.pddwb.client_secret'));
	}

	/**
	 * 获取订单物流详情
	 * @param array $waybill
	 * @return mixed
	 * @throws ClientException
	 */
	protected function sendGetOrderTraceList(array $waybill)
	{
		$client = $this->getClient();
		$params   = [
			'company_code' => $waybill['express_code'],
			'mail_no'      => $waybill['express_no'],
			'cache'        => true, //是否缓存
		];
		$response = $client->execute('pdd.logistics.ordertrace.get', $params);
		Log::info('pdd.logistics.ordertrace.get', $response);

		$result = $this->formatToOrderTrace(array_merge($waybill, [
			'trace_list' => $response['logistics_ordertrace_get_resposne']['trace_list'],
		]));

		return $result;
	}

	/**
	 * 物流数据整理
	 * @param array $orderTrace
	 * @return array
	 */
	public function formatToOrderTrace(array $orderTrace): array
	{
		$latest        = collect($orderTrace['trace_list'])->sortByDesc('time')->first();
//		$orderTraceMap = array_combine(self::TRACE_ARR, OrderTraceList::STATUS_ARR);
		$status = self::TRACE_ARR[$latest['action']] ?? OrderTraceList::STATUS_OTHER;

		return [
			"type"              => $orderTrace['type'],
			'tid'               => $orderTrace['tid'],
			'express_code'      => $orderTrace['express_code'],
			'express_no'        => $orderTrace['express_no'],
			'status'            => $status,
			'action'            => $latest['action'],
			'receiver_province' => $orderTrace['receiver_province'],
			'receiver_name'     => $orderTrace['receiver_name'],
			'send_at'           => $orderTrace['send_at'],
			'latest_updated_at' => $latest['status_time'],
			'latest_trace'      => $latest['desc'],
			'trace_list'        => json_encode($orderTrace['trace_list']),
		];
	}
    public function assemWaybillPackagesForOpenApi($platform,$sender, $orders, $wpCode, $waybillType, $waybillTemp, $packageNum = 1,$productType=null)
    {
        //并行异步请求
        $type   = 'pdd.waybill.get';
        $data   = [];
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            $applyInfo = $this->getWayBillRequestDataForOpenApi($sender, $order->toArray(), $wpCode, $waybillType, $waybillTemp, $packageNum);

            $data[$idStr] = [
                'params' => $this->createRequestParam($type, $applyInfo[0]), //只有一个包裹
                'url'    => $this->apiUrl,
            ];
        }

        $response = $this->poolCurl($data, 'POST');

        Log::debug('pdd_response', [$response]);
        foreach ($response as $orderIdStr => $waybill) {
            if (isset($waybill->modules)) {
                foreach ($waybill->modules as $module) {
                    $printData    = json_decode($module->print_data, true);
                    $insertPrint = [
                        'encryptedData' =>$printData['encryptedData'],
                        'templateUrl'   =>$printData['templateUrl'],
                        'signature'     => $printData['signature'],
                        'ver'           => $printData['ver']
                    ];
                    $waybillsData = [
                        'object_id'           => $module->object_id,
                        'waybill_code'        => $module->waybill_code,
                        'print_data'          => json_encode($insertPrint),
                        'parent_waybill_code' => isset($module->parent_waybill_code) ? $module->parent_waybill_code : ''
                    ];
                }
                $result[$orderIdStr][] = $waybillsData;
            }else {
                Log::error($type . '取号错误', [$orderIdStr => $waybill]);
                $result[$orderIdStr][] = $waybill;
            }
        }

        return $result;
    }

    private function getWayBillRequestDataForOpenApi($sender, $order, $wpCode, $waybillType, $waybillTemp, $packageNum)
    {
        $returnArr = [];
        $num       = 1;
        while ($num <= $packageNum) {
            //发件人信息
            $senderInfo['mobile']              = $sender['mobile'];
            $senderInfo['phone']               = '';
            $senderInfo['name']                = $sender['sender_name'];
            $senderInfo['address']['province'] = $sender['province'];
            $senderInfo['address']['city']     = $sender['city'];
            $senderInfo['address']['district'] = $sender['district'];
            $senderInfo['address']['town']     = '';
            $senderInfo['address']['detail']   = $sender['address'];
            //面单信息
            $tradeOrderInfoDtos = [];
            //订单信息
            $tradeOrderInfoDto['logistics_services'] = '';
            $tradeOrderInfoDto['object_id']          = $order['package_id']??$order['tid'] ?? $order['id'] .'-' . rand(1111, 9999);
            $tradeOrderInfoDto['order_info']         = [
                'order_channels_type' => 'OTHERS',
                'trade_order_list'    => [
                    isset($order['tid']) ? $order['tid'] . '-' . rand(0000, 9999) : $order['id'] . '-' . rand(0000, 9999)
                ]
            ];
            $items = [];
            if (array_key_exists('order_item', $order)) {
                foreach ($order['order_item'] as $good) {
                    $temp = [];
                    $temp['count'] = $good['goods_num'];
                    $temp['name']  = $good['goods_title'] ? $good['goods_title'] : '';
                    $items[]       = $temp;
                }
            }
            if (array_key_exists('production_type', $order)) {
                $items[] = [
                    'count' => is_int($order['num']) ? $order['num'] : 1,
                    'name'  => empty($order['production_type']) ? $order['goods_info'] : $order['production_type'],
                ];
            }

            $tradeOrderInfoDto['package_info'] = [
                'id'                   => "",
                'goods_description'    => "",
                'items'                => $items,
                'volume'               => "",
                'weight'               => "",
                'total_packages_count' => 1,
            ];
            $tradeOrderInfoDto['recipient']    = [
                'address' => [
                    'city'     => $order['receiver_city'],
                    'detail'   => $order['receiver_address'],
                    'district' => $order['receiver_district'],
                    'town'     => "",
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                ],
                'mobile'  => $order['receiver_phone'],
                'name'    => $order['receiver_name'],
                'phone'   => $order['receiver_tel'] ?? '',
            ];
            $tradeOrderInfoDto['template_url'] = $this->getTemplateUrl($waybillType, $waybillTemp);
            $tradeOrderInfoDto['user_id']      = 1;//userId字段目前无意义，随便传个数字即可
            $tradeOrderInfoDtos[]              = $tradeOrderInfoDto;
            //设置主体信息
            $data                                                = [];
            $data['wp_code']                                     = $wpCode;
            $data['need_encrypt']                                = true;
            $data['trade_order_info_dtos']                       = $tradeOrderInfoDtos;
            $data['sender']                                      = $senderInfo;
            $temp = [];
            $temp['param_waybill_cloud_print_apply_new_request'] = json_encode($data);
            $returnArr[] = $temp;
            ++$num;
        }

        return $returnArr;
    }

    public function updateWaybillData($sender, $order, $template, $waybillCode)
    {
        // TODO: Implement updateWaybillData() method.
    }

    public function assemFactoryWaybillPackages($sender, $factoryOrders, $template)
    {
        // TODO: Implement assemFactoryWaybillPackages() method.
    }

    public function getAllCompany(string $wpCode = ''): array
    {
        // TODO: Implement getAllCompany() method.
    }
}
