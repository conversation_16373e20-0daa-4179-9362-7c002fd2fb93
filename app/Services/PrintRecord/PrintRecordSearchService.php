<?php

namespace App\Services\PrintRecord;

use App\Models\PackageOrder;
use App\Models\PrintRecord;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Support\Facades\Log;

/**
 * 打印记录查询服务
 */
class PrintRecordSearchService
{

    /**
     * list
     * @param array $condition
     * @param array $shopIds
     * @param string $keyword
     * @param int $offset
     * @param int $limit
     * @param string $orderBy
     * @param string $wpCode
     * @return array
     */
    public static function search(array  $condition, array $shopIds, string $keyword, int $offset, int $limit,
                                  string $orderBy = '', $wpCode = '', $batchNo = '', $shopIdList = [], $templateIds = [],
                                  $orderType = 0, $mode = 1)
    {
        // 加 package 避免 n+1 查询
        $query   = PrintRecord::query()->with('package', 'order', 'waybillHistory','packageOrders')
            ->where($condition)
            ->whereNotNull('print_index');
        if (in_array($mode, [2, 3])) {
            $query->whereIn('version', [2, 3]);
            $query->whereIn('to_shop_id', $shopIdList);
        }else{
            $query->where('version', 0);
            $shopIds = $shopIdList;
        }
        $query->selectRaw("*,GROUP_CONCAT(order_no) as order_no_str");
        if (!empty($shopIds)) {
            $query->whereIn('shop_id', $shopIds);
        }
        if ($keyword) {
            $orderService = OrderServiceManager::create(config('app.platform'));
            $shops = Shop::query()->whereIn('id', $shopIds)->get();
            $idArr = [];
            foreach ($shops as $shop) {
                $orderService->setShop($shop);
                $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_name' ,$keyword));
                if (isPhoneNumber($keyword)){
                    $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_phone' ,$keyword));
                }
            }

            $tid = tidAddA($keyword);
            $packageIds = PackageOrder::query()->where('tid', $tid)->select('package_id')->get()->pluck('package_id')->toArray();

            $query = $query->where(function ($query) use ($keyword, $idArr, $packageIds) {
                $query->where('order_no', $keyword)
                    ->orWhere('order_no', $keyword . 'A')
                    ->orWhere('waybill_code', $keyword)
                    ->orWhere('batch_no','like', '%' . $keyword . '%');
//                    });
                if ($idArr) {
                    $query->orWhereIn('order_id', $idArr);
                }
                if ($packageIds){
                    $query->orWhereIn('package_id', $packageIds);
                }
            });
        }

        if($wpCode){
            $codeArr = explode(',',$wpCode);
            $query->whereIn('wp_code',$codeArr);
        }
        if($batchNo){
            $query->where('batch_no', $batchNo);
        }
//        if(!empty($shopIdList)){
//        $query->whereIn('to_shop_id', $shopIdList);
//        }
        if(!empty($templateIds)){
            $query->whereIn('template_id', $templateIds);
        }
        if ($orderType > 0) {
            $query->where('order_type', $orderType);
        }

//        $sortArr = explode(' ', $orderBy);

        $query->groupBy(['batch_no', 'waybill_code']);
        //有group by不能直接count
        $baseSql = getSqlByQuery($query);
        Log::info("打印记录查询sql",[$baseSql]);
        $count = \DB::select("select count(*) as count from ($baseSql) as base");
        $ret =  $query->limit($limit)
            ->offset($offset)
            ->orderBy('batch_no', 'asc')
            ->orderBy('print_count', 'asc')
            ->orderBy('print_index', 'asc')
            ->get();
        return array($ret, $count[0]->count);
    }

    public function handleMerge($ret)
    {
        if (empty($ret)) {
            return $ret;
        }
        if (is_object($ret)){
            $ret = $ret->toArray();
        }
        foreach ($ret as $index => $item) {
            $item['print_data'] = '';
//            $item['order_no_str'] = implode(',', array_unique(explode(',', $item['order_no_str'])));
            $item['order_no_str'] = collect($item['package_orders'])->pluck('tid')->unique()->implode(',');
            $item['package_orders'] = '';
//            $item['order_no'] = $item['order_no_str'];
            if($item['order_no']) {
                if (!empty($item['order'])) {
                    $item['seller_nick'] = $item['order']['seller_nick'];
                }
            }
            $ret[$index] = $item;
        }
        $ret = array_values($ret);
        return $ret;
    }

}
