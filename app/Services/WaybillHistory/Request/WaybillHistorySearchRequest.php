<?php

namespace App\Services\WaybillHistory\Request;

/**
 * 取号记录搜索的请求对象
 */
class WaybillHistorySearchRequest implements \JsonSerializable
{
    private $ownerIdList;

    /**
     * @return mixed
     */
    public function getOwnerIdList()
    {
        return $this->ownerIdList;
    }

    /**
     * @param mixed $ownerIdList
     */
    public function setOwnerIdList($ownerIdList): void
    {
        $this->ownerIdList = $ownerIdList;
    }

    private $condition;
    private $keyword;
    private $offset;
    private $limit;
    private $orderBy = '';
    private $waybillStatus;
    private $wpCode;
    private $batchNo;
    private $printMode;
    private $shopIdList;
    private $orderType;
    private $mode;
    private $softRemark;
    private $waybillCodeList;
    private $tidList;


    /**
     * @var array $templateIds 模板ID
     */
    private $templateIds;

    /**
     * @return array
     */
    public function getTemplateIds(): array
    {
        return $this->templateIds;
    }

    /**
     * @param array $templateIds
     */
    public function setTemplateIds(array $templateIds): void
    {
        $this->templateIds = $templateIds;
    }

    /**
     * @return mixed
     */
    public function getShopIdList()
    {
        return $this->shopIdList;
    }

    /**
     * @param mixed $shopIdList
     */
    public function setShopIdList($shopIdList): void
    {
        $this->shopIdList = $shopIdList;
    }

    /**
     * @return mixed
     */
    public function getPrintMode()
    {
        return $this->printMode;
    }

    /**
     * @param mixed $printMode
     */
    public function setPrintMode($printMode): void
    {
        $this->printMode = $printMode;
    }

    /**
     * @return mixed
     */
    public function getCondition()
    {
        return $this->condition;
    }

    /**
     * @param mixed $condition
     */
    public function setCondition($condition): void
    {
        $this->condition = $condition;
    }


    /**
     * @return mixed
     */
    public function getKeyword()
    {
        return $this->keyword;
    }

    /**
     * @param mixed $keyword
     */
    public function setKeyword($keyword): void
    {
        $this->keyword = $keyword;
    }

    /**
     * @return mixed
     */
    public function getOffset()
    {
        return $this->offset;
    }

    /**
     * @param mixed $offset
     */
    public function setOffset($offset): void
    {
        $this->offset = $offset;
    }

    /**
     * @return mixed
     */
    public function getLimit()
    {
        return $this->limit;
    }

    /**
     * @param mixed $limit
     */
    public function setLimit($limit): void
    {
        $this->limit = $limit;
    }

    /**
     * @return string
     */
    public function getOrderBy(): string
    {
        return $this->orderBy;
    }

    /**
     * @param string $orderBy
     */
    public function setOrderBy(string $orderBy): void
    {
        $this->orderBy = $orderBy;
    }

    /**
     * @return mixed
     */
    public function getWaybillStatus()
    {
        return $this->waybillStatus;
    }

    /**
     * @param mixed $waybillStatus
     */
    public function setWaybillStatus($waybillStatus): void
    {
        $this->waybillStatus = $waybillStatus;
    }

    /**
     * @return mixed
     */
    public function getWpCode()
    {
        return $this->wpCode;
    }

    /**
     * @param mixed $wpCode
     */
    public function setWpCode($wpCode): void
    {
        $this->wpCode = $wpCode;
    }

    /**
     * @return mixed
     */
    public function getBatchNo()
    {
        return $this->batchNo;
    }

    /**
     * @param mixed $batchNo
     */
    public function setBatchNo($batchNo): void
    {
        $this->batchNo = $batchNo;
    }


    public function jsonSerialize()
    {
        $vars = get_object_vars($this);
        return $vars;
    }

    /**
     * @return mixed
     */
    public function getOrderType()
    {
        return $this->orderType;
    }

    /**
     * @param mixed $orderType
     */
    public function setOrderType($orderType): void
    {
        $this->orderType = $orderType;
    }

    /**
     * @return mixed
     */
    public function getMode()
    {
        return $this->mode;
    }

    /**
     * @param mixed $mode
     */
    public function setMode($mode): void
    {
        $this->mode = $mode;
    }

    /**
     * @return mixed
     */
    public function getSoftRemark()
    {
        return $this->softRemark;
    }

    /**
     * @param mixed $softRemark
     */
    public function setSoftRemark($softRemark): void
    {
        $this->softRemark = $softRemark;
    }

    /**
     * @return mixed
     */
    public function getWaybillCodeList()
    {
        return $this->waybillCodeList;
    }

    /**
     * @param mixed $waybillCodeList
     */
    public function setWaybillCodeList($waybillCodeList): void
    {
        $this->waybillCodeList = $waybillCodeList;
    }

    /**
     * @return mixed
     */
    public function getTidList()
    {
        return $this->tidList;
    }

    /**
     * @param mixed $tidList
     */
    public function setTidList($tidList): void
    {
        $this->tidList = $tidList;
    }

    /**
     * @var array|null
     */
    public  $with;
}
