<?php

namespace App\Services\WaybillHistory;

use App\Constants\PrintMode;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\Shop;
use App\Models\Template;
use App\Models\WaybillHistoryStatistic;
use App\Utils\OrderUtil;
use App\Utils\ShopUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\Order\OrderServiceManager;
use App\Services\WaybillHistory\Request\WaybillHistorySearchRequest;
use App\Services\WaybillHistory\Request\WaybillHistoryStatisticsRequest;
use Illuminate\Support\Facades\Log;

/**
 * 取号关联了package和packageOrders
 */
class WaybillHistoryPackageQueryService
{

    /**
     *
     * 取号记录查询
     * @param WaybillHistorySearchRequest $request
     * @return array
     */

    public function search(WaybillHistorySearchRequest $request): array
    {
        $condition = $request->getCondition();
        $ownerIdList = $request->getOwnerIdList();
        $toShopIdList=$request->getShopIdList();
        $printMode = $request->getPrintMode();
//        $shops = Shop::getListByIdentifiers($ownerIdList);
//        $ownerShopIds= collect($shops)->pluck('id')->toArray();
//        if (PrintMode::isShop($printMode)) {
//            $shopIds=$ownerShopIds;
//        }
//        if (PrintMode::isFactory($printMode)) {
//            $shopIds = $request->getShopIdList();
//        }

        $keyword = $request->getKeyword();
        $offset = $request->getOffset();
        $limit = $request->getLimit();
        $orderBy = $request->getOrderBy();
        $waybillStatus = $request->getWaybillStatus();
        $wpCode = $request->getWpCode();
        $batch_no = $request->getBatchNo();
        $templateIds = $request->getTemplateIds();
        $orderType = $request->getOrderType();
        $waybillCodeList = $request->getWaybillCodeList();
        $tidList = $request->getTidList();
        Log::info("包裹模式查询 WaybillHistorySearchRequest:".json_encode($request));
        $query = WaybillHistory::query()
//            ->from(DB::raw("waybill_histories FORCE INDEX (waybill_histories_to_shop_id_created_at_index)"))
            ->from(DB::raw("waybill_histories"))
            ->leftJoin('package_orders', 'package_orders.package_id', '=', 'waybill_histories.package_id')
            ->with(['orderCipherInfo','printRecords.packageOrders','toShop:id,shop_name','template:id,name'])
            ->whereIn("waybill_histories.version", [2, 3])
            ->whereIn('waybill_histories.to_shop_id', $toShopIdList);
        if (!empty($ownerIdList)) {
            $query->whereIn('waybill_histories.shop_id', $ownerIdList);
        }
        if(!empty($condition)){
            $query->where($condition);
        }

        $query->selectRaw("waybill_histories.id,
			waybill_histories.auth_source,
			waybill_histories.package_id,
			waybill_histories.order_id,
			waybill_histories.order_type,
			waybill_histories.shop_id,
			waybill_histories.to_shop_id,
			waybill_histories.waybill_code,
			waybill_histories.wp_code,
			waybill_histories.waybill_status,
			waybill_histories.receiver_name,
			waybill_histories.receiver_phone,
			waybill_histories.receiver_province,
			waybill_histories.receiver_city,
			waybill_histories.receiver_district,
			waybill_histories.receiver_address,
			waybill_histories.print_data_items,
			waybill_histories.created_at,
			waybill_histories.batch_no,
			waybill_histories.waybill_index,
			waybill_histories.waybill_count,
			waybill_histories.print_data,
			waybill_histories.send_content,
			waybill_histories.template_id,
			waybill_histories.outer_order_no,
			waybill_histories.sub_waybill_codes,
			waybill_histories.version,
			max(waybill_histories.soft_remark) as soft_remark,
			GROUP_CONCAT(DISTINCT(package_orders.tid)) as order_no_str,
			GROUP_CONCAT(DISTINCT(package_orders.order_id)) as order_id_str,
			GROUP_CONCAT(DISTINCT(waybill_histories.id)) as id_str"
        //			waybill_histories.print_data
        );
//        if (!empty($toShopIdList)) {
//            $query->whereIn('waybill_histories.to_shop_id', $toShopIdList);
//        }
        if ($waybillStatus >= 0) {

            switch ($waybillStatus) {
                case '0':
                    //已发货单号
                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
                        ->where('waybill_histories.package_id', '<>', 0)
                        ->rightJoin('orders', 'orders.id', '=', 'package_orders.order_id')
                        ->whereIn('orders.order_status', [Order::ORDER_STATUS_DELIVERED,Order::ORDER_STATUS_PART_DELIVERED]);
                    break;
                case '1':
                    //已回收单号
                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_YES);
                    break;
                case '2':
                    //已占用单号且打印但未发货
                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
                        ->where('waybill_histories.package_id', '<>', 0)
                        ->rightJoin('orders', 'orders.id', '=', 'package_orders.order_id')
                        ->where('orders.order_status', Order::ORDER_STATUS_PAYMENT)
                        ->where('orders.print_status', Order::PRINT_STATUS_YES);

                    break;
                case '3':
                    //已占用单号但未打印
                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
                        ->where('waybill_histories.package_id', '<>', 0)
                        ->rightJoin('orders', 'orders.id', '=', 'waybill_histories.order_id')
                        ->where('orders.print_status', Order::PRINT_STATUS_NO);
                    break;
                case '4':
                    //未回收单号
                    $query->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO);
                    break;
            }
        }

        if ($keyword) {
            $idArr = [];
            if(!empty($toShopIdList)) {
                $orderService = OrderServiceManager::create(config('app.platform'));
                $shops = Shop::query()->whereIn('id', $toShopIdList)->get();

                foreach ($shops as $shop) {
                    $orderService->setShop($shop);
                    $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_name', $keyword));
                    if (isPhoneNumber($keyword)) {
                        $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_phone', $keyword));
                    }
                }

            }
//            $customizeOrderIds = CustomizeOrder::selectByReturnIdList($ownerIdList, $keyword);
//            \Log::info("查询自由订单", [$keyword, $customizeOrderIds]);
//            $idArr = array_merge($idArr, $customizeOrderIds);

            //批次号
            //$arr = explode('-', $keyword);
            $packageIds = Package::query()->where('batch_no', $keyword)
                ->select('id')->get();

            //订单号
            $orderTid=OrderUtil::appendOrderSuffix($keyword);
            $orderPackageIds = PackageOrder::query()->where('tid', $orderTid)->select('package_id')->get();

            $query = $query->where(function ($query) use ($keyword, $idArr, $packageIds,$orderPackageIds) {
                $query->where('waybill_histories.order_no', $keyword)
                    ->orWhere('waybill_histories.order_no', $keyword . 'A')
                    ->orWhere('waybill_histories.batch_no', $keyword)
                    ->orWhere('waybill_histories.waybill_code', $keyword)
                    ->orWhere('waybill_histories.order_id', $keyword);
//                    ->orWhere('waybill_histories.receiver_phone', $keyword);
//                if ($idArr) {
//                    $query->orWhereIn('order_id', $idArr);
//                }
                if ($packageIds->isNotEmpty()) {
                    $query->orWhereIn('waybill_histories.package_id', $packageIds);
                }
                if($orderPackageIds->isNotEmpty()){
                    $query->orWhereIn('waybill_histories.package_id', $orderPackageIds);
                }
            });
        }

        if ($wpCode) {
            $codeArr = explode(',', $wpCode);
            $query->whereIn('wp_code', $codeArr);
        }
        if ($batch_no) {
            $arr = explode('-', $batch_no);
//            $packageQuery = Package::query()->where('batch_no', 'like', $arr[0] . '%')
//                ->select('id')->getQuery();
//            $query->whereIn('waybill_histories.package_id', $packageQuery);
            $query->where('waybill_histories.batch_no', $arr[0]);
        }
        if(!empty($templateIds)){
            $query->whereIn('waybill_histories.template_id', $templateIds);
        }
        $softRemark = $request->getSoftRemark();
        if ($softRemark) {
            $query->where('waybill_histories.soft_remark', $softRemark);
        }
        if (!empty($waybillCodeList)) {
            $query->whereIn('waybill_histories.waybill_code', $waybillCodeList);
        }
        if (!empty($tidList)) {
            $tidList = batchAddA($tidList);
            $query->whereIn('waybill_histories.order_no', $tidList);
        }

        if($orderType){
            $query->where('waybill_histories.order_type', $orderType);
        }
        $sortArr = explode(' ', $orderBy);
        $query->groupBy(['waybill_histories.waybill_code']);

        //有group by不能直接count
        $baseSql = getSqlByQuery($query);
        Log::info("面单记录查询:".$baseSql);
        $count = DB::select("select count(*) as count from ($baseSql) as base");
        if ($sortArr[1] == 'desc') {
            $query->orderBy('waybill_histories.batch_no', 'desc');
            $query->orderBy('waybill_histories.id', 'desc');
        }else{
            $query->orderBy('waybill_histories.batch_no', 'asc');
            $query->orderBy('waybill_histories.id', 'asc');
        }
        $ret = $query->limit($limit)
            ->offset($offset)
//            ->orderBy('waybill_histories.id', 'asc')
//            ->orderBy('waybill_histories.id', $sortArr[1])
            ->get();
        return array($ret, $count[0]->count);
    }


    /**
     * @param $condition
     * @param $shopIds
     * @param $orderBy
     * @param $wpCode
     * @param $toShopIdList
     * @param $templateIds
     * @param $batchNo
     * @param $mode
     * @param $keyword
     * @return Builder
     * <AUTHOR>
     */
    public static function generateData($condition, $shopIds, $orderBy, $wpCode,$toShopIdList,$templateIds,$batchNo,$mode,$keyword)
    {
        $query = WaybillHistory::query()
//            ->from(DB::raw("waybill_histories FORCE INDEX (waybill_histories_to_shop_id_created_at_index)"))
            ->from(DB::raw("waybill_histories"))
            ->where($condition)->whereIn('to_shop_id', $toShopIdList);
        if ($wpCode) {
            $codeArr = explode(',', $wpCode);
            $query->whereIn('wp_code', $codeArr);
        }
        if(!empty($shopIds)){
            $query->whereIn('shop_id', $shopIds);
        }
//        if(!empty($toShopIdList)){
//            \Log::info("过滤",$toShopIdList);
//            $query->whereIn('to_shop_id', $toShopIdList);
//        }
        if(!empty($templateIds)){
            $query->whereIn('template_id', $templateIds);
        }
        if(!empty($batchNo)){
            $explode = explode('-', $batchNo);
            $query->where('batch_no','like', $explode[0].'%');
        }
        if ($mode > 0){
            $query->whereIn('version',[2,3]);
        }


        if ($keyword) {
            $idArr = [];
            if(!empty($toShopIdList)) {
                $orderService = OrderServiceManager::create(config('app.platform'));
                $shops = Shop::query()->whereIn('id', $toShopIdList)->get();

                foreach ($shops as $shop) {
                    $orderService->setShop($shop);
                    $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_name', $keyword));
                    $idArr = array_merge($idArr, $orderService->getQueryTradeOrderId('receiver_phone', $keyword));
                }

            }
//            $customizeOrderIds = CustomizeOrder::selectByReturnIdList($ownerIdList, $keyword);
//            \Log::info("查询自由订单", [$keyword, $customizeOrderIds]);
//            $idArr = array_merge($idArr, $customizeOrderIds);

            //批次号
            //$arr = explode('-', $keyword);
            $packageIds = Package::query()->where('batch_no', $keyword)
                ->select('id')->get();

            $query = $query->where(function ($query) use ($keyword, $idArr, $packageIds) {
                $query->where('waybill_histories.order_no', $keyword)
                    ->orWhere('waybill_histories.order_no', $keyword . 'A')
                    ->orWhere('waybill_histories.batch_no', $keyword)
                    ->orWhere('waybill_histories.waybill_code', $keyword)
                    ->orWhere('waybill_histories.order_id', $keyword);
//                if ($idArr) {
//                    $query->orWhereIn('order_id', $idArr);
//                }
                if ($packageIds) {
                    $query->orWhereIn('waybill_histories.package_id', $packageIds);
                }
            });
        }



        $sortArr = explode(' ', $orderBy);
        return $query->orderBy('waybill_histories.id', $sortArr[1]);
    }


    /**
     * 面单用量统计
     *
     *
     * @param WaybillHistorySearchRequest $request
     * @return array
     */
    public function statistics(WaybillHistorySearchRequest  $request):array{

        $wpCode      = $request->getWpCode();
        $orderBy     = $request->getOrderBy();
        $batchNo     = $request->getBatchNo();
        $keyword=$request->getKeyword();
//        $beginAt = $request->get;
//        $beginEnd = $request->beginEnd;
        $shopIds = Shop::shopIdsByidentifier($request->getOwnerIdList());
        $toShopIdList=$request->getShopIdList();
        $templateIds = $request->getTemplateIds();
        $mode = $request->getMode();

        $waybillHistoryQuery = $this->generateData($request->getCondition(),$shopIds,$orderBy,$wpCode,$toShopIdList,$templateIds,$batchNo,$mode,$keyword);
//        $waybillHistoryQuery->whereIn('version',[2,3]);
        \Log::info("面单统计",[json_encode($request),$waybillHistoryQuery->toSql()]);
        /**
         * 统计公式
         * 总的面单使用量=平台订单的面单使用量+自由打印订单面单数量
         * 已回收的面单数= 平台订单的面单已回收数量+自由打印订单面单已回收数量
         */
        //总的面单使用量
        $waybillNums    = ($tmpQuery = clone $waybillHistoryQuery)->selectRaw('count(distinct waybill_code) as count')->first()->count;
        \Log::info("总的面单使用量",[$tmpQuery->toSql(),$waybillNums]);
        //自由打印订单面单数量
        $customOrders   = ($tmpQuery = clone $waybillHistoryQuery)->where('package_id',0)->count();
        //平台订单的面单使用量
        $waybillOrders  =  $waybillNums-$customOrders;    //($tmpQuery = clone $waybillHistoryQuery)->where('package_id','>',0)->count();
        //已回收的面单数
        $recoverWaybill = ($tmpQuery = clone $waybillHistoryQuery)->selectRaw('count(distinct waybill_code) as count')
            ->where('waybill_status',WaybillHistory::WAYBILL_RECOVERY_YES)->first()->count;
        //自由打印订单面单已回收数量
        $recoverCustomOrders = ($tmpQuery = clone $waybillHistoryQuery)->where('package_id',0)->where('waybill_status',WaybillHistory::WAYBILL_RECOVERY_YES)->count();

        //平台订单的面单已回收数量
        $recoverWaybillOrders =$recoverWaybill- $recoverCustomOrders;   //  ($tmpQuery = clone $waybillHistoryQuery)->where('waybill_status',WaybillHistory::WAYBILL_RECOVERY_YES)->where('package_id','>',0)->count();

        $waybillSourceArr = [
            Waybill::AUTH_SOURCE_PDD_WB,
            Waybill::AUTH_SOURCE_TWC,
            Waybill::AUTH_SOURCE_LINK,
            Waybill::AUTH_SOURCE_DY,
        ];
        //平台取号 '拼','淘','菜','抖'
        $waybillUsedList =[];
        //平台回收 '拼','淘','菜','抖'
        $waybillRecoverList=[];
        //平台订单取号 '拼','淘','菜','抖'
        $orderWaybillUseList=[];
        //平台订单回收 '拼','淘','菜','抖'
        $orderWaybillRecoverList=[];
        //自由打印平台取号'拼','淘','菜','抖'
        $customOrdersList=[];
        //自由打印平台回收'拼','淘','菜','抖'
        $recoverCustomOrdersList=[];

        $waybillHistoryQuery2 = clone $waybillHistoryQuery;
        $waybillHistoryQuery2
            ->whereIn('auth_source', $waybillSourceArr)
            ->groupBy(['auth_source'])
            ->select('auth_source', DB::raw('count(DISTINCT waybill_code) num'));
        $waybillUsedArr = ($tmpQuery = clone $waybillHistoryQuery2)
            ->get()
            ->pluck('num','auth_source')
            ->toArray();
        $waybillRecoverArr = ($tmpQuery = clone $waybillHistoryQuery2)
            ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_YES)
            ->get()
            ->pluck('num','auth_source')
            ->toArray();
        /**
         * 自由打印使用的电子面单
         */
        $customOrdersArr = ($tmpQuery = clone $waybillHistoryQuery2)
            ->where('package_id', 0)
            ->get()
            ->pluck('num','auth_source')
            ->toArray();
        /**
         * 自由打印以回收的电子面单
         */
        $recoverCustomOrdersArr = ($tmpQuery = clone $waybillHistoryQuery2)
            ->where('package_id', 0)
            ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_YES)
            ->get()
            ->pluck('num','auth_source')
            ->toArray();
        //<AUTHOR>
        //这个地方优化一下减少sql的调用
        //回收的面单数=平台订单回收的面单数+自由打印的回收面单数
        //使用面单数=平台订单使用数+自由打印的面单使用数
        foreach ($waybillSourceArr  as $waybillSource) {
            $waybillUsedList[] = $waybillUsedArr[$waybillSource] ?? 0;
            $waybillRecoverList[] = $waybillRecoverArr[$waybillSource] ?? 0;
            $orderWaybillUseList[] =($waybillUsedArr[$waybillSource] ?? 0)- ($customOrdersArr[$waybillSource] ?? 0);// $orderWaybillUseArr[$waybillSource] ?? 0;
            $orderWaybillRecoverList[]  = ($waybillRecoverArr[$waybillSource] ?? 0)-($recoverCustomOrdersArr[$waybillSource] ?? 0); //  $orderWaybillRecoverArr[$waybillSource] ?? 0;
            $customOrdersList[] = $customOrdersArr[$waybillSource] ?? 0;
            $recoverCustomOrdersList[] = $recoverCustomOrdersArr[$waybillSource] ?? 0;
        };

//        $ret = [
//            'waybill_nums'      => $waybillNums,
//            'recover_waybill'   => $recoverWaybill,
//            'waybill_orders'    => $waybillOrders,
//            'recover_waybill_orders'    => $recoverWaybillOrders,
//            'custom_orders'             => $customOrders,
//            'recover_custom_orders'     => $recoverCustomOrders,
//            'waybill_used'              => $waybillUsedList,
//            'waybill_recover'           => $waybillRecoverList,
//            'order_waybill_use_List'    => $orderWaybillUseList,
//            'order_waybill_recover_list'=> $orderWaybillRecoverList,
//            'custom_orders_list'        => $customOrdersList,
//            'recover_custom_orders_list'=> $recoverCustomOrdersList,
//        ];

        $result = [
            'waybill_nums' => $waybillNums ?? 0,
            'recover_waybill' => $recoverWaybill ?? 0,
            'waybill_orders' => $waybillOrders ?? 0,
            'recover_waybill_orders' => $recoverWaybillOrders ?? 0,
            'custom_orders' => $customOrders ?? 0,
            'recover_custom_orders' => $recoverCustomOrders ?? 0,
            'waybill_used' => $waybillUsedList,
            'waybill_recover' => $waybillRecoverList,
            'order_waybill_use_List' => $orderWaybillUseList,
            'order_waybill_recover_list' => $orderWaybillRecoverList,
            'custom_orders_list' => $customOrdersList,
            'recover_custom_orders_list' => $recoverCustomOrdersList,
        ];
        \Log::info("statistics result",[$result]);
        return $result;
    }

//    /**
//     * 需要同步的WaybillHistory的ID
//     * @param $created_at
//     * @return Long
//     */
//    public function minNeedTraceWaybillHistoryId($createdAt){
//        $result=DB::selectOne("select min(id) min_id from waybill_histories where waybill_status=0 and auth_source in (0,6) and created_at>'".$createdAt."'");
//        return $result->min_id??1;
//    }


    public static function handleMerge($ret,bool  $fullData=false)
    {
        if (empty($ret)) {
            return $ret;
        }
        $templateInfoMap= [];
        /**
         * @var WaybillHistory $item
         */
        foreach ($ret as $item) {
            $orderItems = $sellerMemo = $buyerMessage = [];
            $orderIdArr = explode(',', $item['order_id_str']);
            $item['idArr'] = explode(',', $item['id_str']);
            $item['orderNoArr'] = explode(',', str_replace('A', '', $item['order_no_str']));
            $item['orderNoStr'] = str_replace('A', '', $item['order_no_str']);
            //关联平台订单
            if ($item->package_id > 0) {
                //关联order
                $item->order;
                if (isset($item->order)) {
                    $order = $item->order;
                    $item->receiver_province = $order->receiver_state;
                    $item->receiver_city = $order->receiver_city;
                    $item->receiver_district = $item->order->receiver_district;
                    $item['seller_nick']=$item->order->seller_nick;

                } else {
                    Log::info("有问题的数据", [$item]);
                }
//                if(!$item->package){
//                    Log::warning("有问题的数据", [$item]);
//                    continue;
//                }
//                if($fullData&&isset($item->order)) {
//                    $item->order->shop;
//                }
                if (!isFactory() && $item->order_type == 1) { // 平台订单
                    //按包裹现在的来源类型（取号还是内部发货进行过滤，如果仅取号还没有发货，那么就显示仅取号的，如果发货了，就按发货的包裹显示
                    $sourceType = $item->package->source_type;
                    $packageOrders = $item->packageOrders->where('source_type', $sourceType);
                    Log::info("packageOrders",["packageId"=>$item->package_id,"source_type"=>$sourceType,"packageOrders"=>$packageOrders]);

                    //关联order
                    $item->order;
                    $item->receiver_province = $item->order->receiver_state;
                    $item->receiver_city = $item->order->receiver_city;
                    $item->receiver_district = $item->order->receiver_district;
//                    if ($fullData && isset($item->order)) {
//                        $item->order->shop;
//                    }

                    //关联orderItem
                    $item->orderItem;
                    //从packageOrders中取出orderItemsId
                    $orderItemsId = array_column($packageOrders->toArray(), 'order_item_id');
                    $packageOrderItems=array_column($packageOrders->toArray(), null,'order_item_id');
                    //替换掩码地址
                    if ($item->orderCipherInfo) {
                        $item->receiver_name = $item->orderCipherInfo->receiver_name_mask;
                        $item->receiver_phone = $item->orderCipherInfo->receiver_phone_mask;
                        $item->receiver_address = $item->orderCipherInfo->receiver_address_mask;
                    }
                    $orderItemArr = OrderItem::query()->whereIn('id', $orderItemsId)->get();
                    foreach ($orderItemArr as $orderItem) {
                        $orderItem['goods_num']=$packageOrderItems[$orderItem->id]['num'];
                        $orderItems[] = $orderItem;
                    }
                    //卖家备注
                    if ($item->order && $item->order['seller_memo'] != '[]') {
                        $tmpSellerMemo = json_decode($item->order['seller_memo'], true);
                        $sellerMemo[] = $tmpSellerMemo[0];
                    }
                    //买家留言
                    if ($item->order && $item->order['buyer_message']) {
                        $buyerMessage[] = $item->order['buyer_message'];
                    }
                }
                $item['merge_order_item'] = $orderItems;
            } else {
                $customizeOrder = $item->customizeOrder;
                $goodsInfo = json_decode($customizeOrder->goods_info??'{}', true);
                if ($fullData && isset($goodsInfo)) {
                    $customizeOrderItems = [];
                    foreach ($goodsInfo as $v) {
                        $orderItem = new OrderItem();
                        $orderItem->goods_num = $v['num'];
                        $orderItem->goods_title = $v['title'];
                        $orderItem->sku_id = "0";
                        $orderItem->sku_value = $v['title'];
                        $orderItem->outer_iid = "0";
                        $orderItem->outer_sku_iid = "0";
                        $customizeOrderItems [] = $orderItem;
                    }
                    $item['merge_order_item'] = $customizeOrderItems;
                }

            }
            if($item->template_id){
                if(!isset($templateInfoMap[$item->template_id])){
                    $template = Template::find($item->template_id);
                    $templateInfoMap[$item->template_id] = $template;
                }
                $template = $templateInfoMap[$item->template_id];
                $item->template_name = $template->name??'';
            }
//            \Log::info('订单行数据' ,$item['merge_order_item']);
            $item['seller_memo_arr'] = $sellerMemo;
            $item['buyer_message_arr'] = $buyerMessage;
        }

        return $ret;
    }
}
