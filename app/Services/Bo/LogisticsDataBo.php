<?php

namespace App\Services\Bo;

use ArrayAccess;

class LogisticsDataBo extends BaseBo
{
    /**
     * @var string 运单号
     */
    public $waybill_code = '';

    /**
     * @var string 快递公司编码
     */
    public $wp_code = '';

    /**
     * @var string 快递公司名称
     */
    public $wp_name = '';

    /**
     * @var string 发货时间 2024-07-24 19:14:01
     */
    public $delivery_at = '';

    /**
     * @var string 包裹 id
     */
    public $delivery_id = 0;

    /**
     * @var LogisticsDataProductBo[] 包裹下的商品
     */
    public $product_list = [];

    public function __construct($data = [])
    {
        parent::__construct($data);
        if (!empty($data['product_list'])){
            $this->product_list = [];
            foreach ($data['product_list'] as $product) {
                $this->product_list[] = new LogisticsDataProductBo($product);
            }
        }
    }

}
