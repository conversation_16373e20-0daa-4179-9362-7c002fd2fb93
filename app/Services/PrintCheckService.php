<?php
/**
 * Created by PhpStorm.
 * User: xu<PERSON><PERSON><PERSON>
 * Date: 2022/5/30
 * Time: 14:38
 */
namespace App\Services;

use App\Constants\PlatformConst;
use App\Constants\RefundSubStatusConst;
use App\Exceptions\PrintException;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\Company;
use App\Models\Fix\Order;
use App\Models\Shop;
use App\Models\Template;
use App\Models\UserExtra;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Models\WaybillShareAction;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use App\Utils\WaybillUtil;
use Illuminate\Support\Facades\Log;

class PrintCheckService
{
    private $templateInfo;
    private $isNewPrint;
    private $totalCount;
    private $orderIds;
    private $redis;
    private $shopIds;
    private $orderItemIds;

    public function __construct($templateId, $isNewPrint, $totalCount, $orderIds,$shopIds=[],$orderItemIds=[])
    {
        $this->redis = redis('cache');
        $this->orderIds = $orderIds;
        $this->isNewPrint = $isNewPrint;
        $this->totalCount = $totalCount;
        $this->templateInfo = Template::query()->where('id', $templateId)->first();
        $this->shopIds = $shopIds;
        $this->orderItemIds = $orderItemIds;
    }

    /**
     * 检查店铺授权
     * @throws PrintException
     */
    public function checkShopAuthStatus(array $shopIds, $errorMsg){
        foreach ($shopIds as $shopId){
            $shop = Shop::query()->where('id', $shopId)->first();
            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);
            $checkAuthStatus = $orderService->checkAuthStatus();
            if (!$checkAuthStatus) {
                $codeUrl = socialite()->driver(config('app.platform'))->redirect()->getTargetUrl();
                $shop->codeUrl = $codeUrl;
                \Log::info("电子面单账户检查不通过");
                throw new PrintException([$shop], 1008, $errorMsg);
            }
        }
//        \Log::info("检查店铺授权通过");
    }
    /**
     * @throws PrintException
     */
    public function checkAuthStatusAndWaybillBalance()
    {
        // 电子面单账号检查, 京东、淘宝、快手、抖音平台因为都必须要用的电子面单，所以
        if (WaybillUtil::matchCurrentAuthSource($this->templateInfo->auth_source)) {
            Log::info("检查店铺授权");
            $company = $this->templateInfo->company;
            if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
                // 共享电子面单
                $waybillAuth = Shop::query()->where('id', $company->source_shopid)->first();
            }else{
                $waybillAuth = Shop::query()->where('id', $this->templateInfo->shop_id)->first();
            }
            $orderService = OrderServiceManager::create();
            $orderService->setShop($waybillAuth);
            $checkAuthStatus = $orderService->checkAuthStatus();
            if (!$checkAuthStatus) {
                $codeUrl = socialite()->driver(config('app.platform'))->redirect()->getTargetUrl();
                $this->templateInfo->codeUrl = $codeUrl;
                Log::info("电子面单账户检查不通过");
                throw new PrintException([$this->templateInfo], 1007, '店铺电子面单过期, 请重新授权');
            }
//            if (Environment::isWxOrWxsp()){ // 微信自由打印只支持第三方
//                $this->checkThirdPartWaybillAuth();
//            }
        }else if(Environment::isWxOrWxsp()){
            $waybillAuth = $this->checkThirdPartWaybillAuth();
        }
        else {

            // 快手平台已切换的店铺 取新号不支持第三方电子面单
            if (config('app.platform') == PlatformConst::KS && ksEncryptSwitch($this->templateInfo->shop_id) && $this->isNewPrint) {
                //return $this->success([], '平台订单现不支持第三方电子面单', 1006);
                throw new PrintException([], 1006, '平台订单现不支持第三方电子面单');
            }
            // 抖音不支持第三方电子面单
            if (config('app.platform') == PlatformConst::DY && $this->isNewPrint) {
                throw new PrintException([], 1006, '平台订单现不支持第三方电子面单');
            }
            $waybillAuth = $this->checkThirdPartWaybillAuth();
        }

        $total = $this->totalCount;
        $company = $this->templateInfo->company;
        $authSource = $this->templateInfo->auth_source;

        //虚拟网点直接使用余额
        if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
            $quantity = $company->quantity;
            if ($company->source_status == Company::SOURCE_COMPANY_STATUS_CLOSED) {
                throw new PrintException([], 1010, '共享电子面单分享已关闭');
            }
            if ($quantity != -1 && $total > $quantity && $this->isNewPrint) {
                throw new PrintException(['need_num' => $total, 'balance' => $quantity], 1001, '电子面单余额不足，请联系分享者');
            }
        } else {
            Log::info("waybillAuth",[$waybillAuth]);
            try {
                $waybillService = WaybillServiceManager::init($authSource, $waybillAuth->access_token);
                $waybillService->setShop($waybillAuth);
                $waybill = $waybillService->waybillSubscriptionQuery($company->wp_code, $waybillAuth->service_id??'');
                $quantityInfo = collect($waybill[0]['branch_account_cols'])->where('branch_code', $company->branch_code)->first();
                Log::info("quantityInfo",[$quantityInfo]);
            } catch (\Exception $e) {
                throw new PrintException([$this->templateInfo], 1007, '电子面单账户过期, 请重新授权');
            }

            if ($company->branch_code) {
                $quantity = $quantityInfo['quantity'];
            } else {
                $quantity = ($waybill[0]['branch_account_cols'])[0]['quantity'];
            }
            if (is_null($quantity)){
                $quantity = -1;
            }
            if ($quantity != -1){
                if (in_array($authSource, [Waybill::AUTH_SOURCE_PDD_WB, Waybill::AUTH_SOURCE_PDD]) && $total > $quantity && $waybill[0]['wp_type'] == 1 && $this->isNewPrint) {
                    throw new PrintException(['need_num' => $total, 'balance' => $quantity], 1001, '电子面单余额不足，请充值');
                }
                if (in_array($authSource, [Waybill::AUTH_SOURCE_TWC, Waybill::AUTH_SOURCE_LINK, Waybill::AUTH_SOURCE_DY]) && $total > $quantity && $waybill[0]['wp_type'] != 1 && $this->isNewPrint) {
                    throw new PrintException(['need_num' => $total, 'balance' => $quantity], 1001, '电子面单余额不足，请充值');
                }
            }
        }
    }

    /**
     * @throws PrintException
     */
    public function checkPrintStatusAndOrderStatus(string $scene="unprinted")
    {
        $company = $this->templateInfo->company;
        $shopId = request()->auth->shop_id;
        $this->checkShopAuthStatus($this->shopIds, '订单店铺授权过期, 请重新授权');
//        if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
//            $this->checkShopAuthStatus([$company->source_shopid], '打印模版共享店铺授权过期, 请提醒分享者重新授权');
//        }else{
//            $this->checkShopAuthStatus([$company->shop_id], '打印模版店铺授权过期, 请重新授权');
//        }

        $orderItemIds = array_flatten($this->orderItemIds);
        $refundList = $hasWaybillCodeList = $hasDeliveryList = $hasProcessList = $orderIdArr = $hasCloseList = [];
        foreach ($this->orderIds as $item) {
            $orderIdArr = array_merge($item, $orderIdArr);
        }

        $version = UserExtra::getVersionByShopId($shopId);
        if (in_array($version, [UserExtra::VERSION_FREE])) {
            $count = WaybillHistory::query()
                ->where('shop_id', $shopId)
                ->where('created_at', '>=', date('Y-m-d 00:00:00'))
                ->where('created_at', '<=', date('Y-m-d 23:59:59'))
                ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
                ->count();
            $freeMax = 20;
            if ($count > $freeMax || ($count + count($orderIdArr)) > $freeMax) {
                throw new PrintException([], 1010, '免费用户每天最多取号'.$freeMax.'个');
            }
        }

        \Log::info('获取检查的订单ID',[$orderIdArr]);

        $orderList = Order::query()->without(['shop','orderItem'])->with(['packages' => function ($query) {
            $query->whereNull('recycled_at');
        }])->whereIn('id', $orderIdArr)->get();
        \Log::info('获取检查的订单数据');
        //拉取一把最新状态，遇到错误不要抛出异常。
        $orderArray = $orderList->toArray();
        $tradesOrder = Order::batchGetOrderInfo($orderArray,true);
        \Log::info('拉取平台订单结束',[]);
        $ordersCount=sizeof($orderArray);

        $tradeOrdersGroupByTid = array_column($tradesOrder, null, 'tid');
        $orderResultCount = count($tradeOrdersGroupByTid);
        //是否有已打印/已发货订单
        foreach ($orderList as $order) {
            $redisKey = 'order_print_lock:' . $order['id'];
            if ($this->redis->exists($redisKey)) {
                $hasProcessList[] = $order;
            }
            if (in_array($order['print_status'], [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART])) {
                $hasWaybillCodeList[] = $order;
            }

            $tid = $order['tid'];
            $tradeOrder = $tradeOrdersGroupByTid[$tid] ?? null;
            $orderStatus = isset($tradeOrder) ?
                $tradeOrder['order_status'] :
                $order['order_status'];
            if (in_array($orderStatus, [Order::ORDER_STATUS_DELIVERED, Order::ORDER_STATUS_RECEIVED,
                Order::ORDER_STATUS_SUCCESS])) {
                $hasDeliveryList[] = $order;
                if (!empty($tradeOrder)){
                    // 异步保存订单
                    dispatch(new SyncSaveOrders($order['user_id'], $order['shop_id'], [$tradeOrder]));
                }
            }

            // 有传子订单要判断子订单
            if (!empty($orderItemIds)){
                foreach ($order['orderItem'] as $orderItem) {
                    $tradeOrderItem = collect($tradeOrder['items'])->firstWhere('oid', $orderItem['oid']);
                    if (!in_array($orderItem['id'], $orderItemIds)) {
                        continue;
                    }
                    if (in_array($orderItem['refund_status'], [Order::REFUND_STATUS_YES, Order::REFUND_STATUS_PART])
                        && in_array($orderItem['refund_sub_status'], RefundSubStatusConst::REFUND_PROCESSING_ARRAY)) { // 退款中
                        $refundList[] = $order;
                        continue;
                    }
                    if (isset($tradeOrderItem) && in_array($tradeOrderItem['refund_status'], [Order::REFUND_STATUS_YES, Order::REFUND_STATUS_PART])
                        && isset($tradeOrderItem['refund_sub_status']) && in_array($tradeOrderItem['refund_sub_status'], RefundSubStatusConst::REFUND_PROCESSING_ARRAY)) {// 退款中
                        $refundList[] = $order;
                        if (!empty($tradeOrder)){
                            // 异步保存订单
                            dispatch(new SyncSaveOrders($order['user_id'], $order['shop_id'], [$tradeOrder]));
                        }
                        continue;
                    }
                 }
            }else{
                //是否有退款订单
                $refundStatus = isset($tradeOrder) ? $tradeOrder['refund_status'] : $order['refund_status'];
                if (in_array($refundStatus, [Order::REFUND_STATUS_YES, Order::REFUND_STATUS_PART])) {
                    $refundList[] = $order;
                    if (!empty($tradeOrder)){
                        // 异步保存订单
                        dispatch(new SyncSaveOrders($order['user_id'], $order['shop_id'], [$tradeOrder]));
                    }
                }
            }
//            if (!empty($tradeOrder)){
//                // 异步保存订单
//                dispatch(new SyncSaveOrders($order['user_id'], $order['shop_id'], [$tradeOrder]));
//            }

            if (in_array($orderStatus, [Order::ORDER_STATUS_CLOSE])) {
                $hasCloseList[] = $order;
                if (!empty($tradeOrder)){
                    // 异步保存订单
                    dispatch(new SyncSaveOrders($order['user_id'], $order['shop_id'], [$tradeOrder]));
                }
            }
        }
        if (!empty($hasCloseList)) {
            throw new PrintException($hasWaybillCodeList, 1011, '打印列表中有关闭订单');
        }

        if (!empty($hasProcessList)) {
            throw new PrintException($hasProcessList, 1005, '打印列表中有处理中的订单，请稍后再试！');
        }
        if (!empty($refundList)) {
            throw new PrintException($refundList, 1002, '打印列表中有退款订单');
        }
        if ($scene=="unprinted"&&!empty($hasDeliveryList)) {
            throw new PrintException($hasDeliveryList, 1004, '打印列表中有已发货');
        }
//        if ($scene=="unprinted"&&!empty($hasWaybillCodeList)) {
//            throw new PrintException($hasWaybillCodeList, 1003, '打印列表中有已打印订单');
//        }
        if($orderResultCount!=$ordersCount){
//            throw new PrintException($hasWaybillCodeList, 1009, strval(($ordersCount-$orderResultCount)).'条订单因平台故障暂时未查询到记录');
        }
    }

    /**
     * 检查三方电子面单授权
     * @return mixed
     * @throws PrintException
     */
    public function checkThirdPartWaybillAuth()
    {
        $waybillAuth = Waybill::where([
            'owner_id' => $this->templateInfo->owner_id,
            'auth_source' => $this->templateInfo->auth_source
        ])->orderBy('expires_at', 'desc')->first();

        if (empty($waybillAuth) || strtotime($waybillAuth->expires_at) < time()) {
            throw new PrintException([$this->templateInfo], 1000, '电子面单过期, 请重新授权');
        }
        return $waybillAuth;
    }
}
