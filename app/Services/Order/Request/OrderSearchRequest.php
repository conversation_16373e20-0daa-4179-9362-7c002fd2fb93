<?php

namespace App\Services\Order\Request;

use App\Services\BaseFromRequest;
use App\Utils\Environment;
use App\Utils\StrUtil;

class OrderSearchRequest extends BaseFromRequest
{
    public $shopIds;
    public $factoryShopIds;
    public $timeField;
    public $orderStatusList;
    public $beginAt;
    public $endAt;
    public $search;
    protected $groupColumn;
    public $selectItem;
    public $goodsInclude;
    public $skuInclude;
    public $flag;
    public $remark;
    public $printStatus = -1;   //打印状态
    public $orderStatus = -1;  //订单状态
    public $refundStatus = null;  //退款状态
    public $refundStatusArr = [];  //退款状态数组
    public $ownerIdList;
    public $factoryOwnerIdList;
    public $massMode = false;
    public $remarkType;
    public $tabFlag = '0';        //tab 菜单项
    public $printMode = 1; // 打印模式，1 商家模式 2 厂家模式
    public $customPrintContent; // 自定义打印内容
    public $customGroup; // 自定义分组

    public $quickFilterValue; //快捷筛选
    public $addressGroupId = 0;  //区域筛选， 0是不限
    public $includeOrNot = 1;  //包含 不包含
    public $smartLogistics = -1;  // 物流
    public $sort = 'pay_at,desc'; // 排序
    public $sortArr = []; // 排序
    public $offset = 0;
    public $limit = 10;
    public $buyer_message;

    public $seller_memo;
    // 商品查询
    public $goodsQueryOperator = 'equal'; // 商品查询操作符：include 包含，notInclude 不包含，equal  等于，notEqual 不等于
    public $skuQueryOperator="equal";// sku查询操作符：include 包含，notInclude 不包含，equal  等于，notEqual 不等于
    public $goodsTitle; // 商品名
    public $customTitle; // 商品简称
    public $outerIid; // 商品编码
    public $numIid; // 商品id
    public $goodsColor;
    public $goodsSize;
    public $goodSkuNumType = 0; // 1一种规格一件 2一种规格多件 3一种商品多件 4多种商品 5多款多件
    public $goodsId = []; // 商品id数组
    public $isOnlyGoods = 0; // 只包含商品id的商品,拆分 sku
    public $goodsNum; // 商品数量
    public $ordersNum;  //订单数量
    public $skuValue; // 规格名
    public $customSkuValue; // 规格简称
    public $outerSkuIid; // 规格编码
    public $skuId; // 规格id
    public $skuIdList; // skuId数组

    public $payment; //实付金额
    public $orderTotalFee; // 订单金额
    public $tidList = []; // 订单号数组
    public $waybillCodeList; // 运单号数组
    public $wpCodeList; // 快递公司数组
    public $displayMerge = false; //合并展示
    public $isShippedList = 0; // 是否已发货列表
    public $isPreShipmentList = 0; // 是否预发货列表

    public $isPreSale; // 是否预售 1是 0否

    public $orderBizType; // 订单业务类型 1质检订单 0普通订单

    public $isUnshippedList = 0; // 是否待发货列表
    public $isResendList = 0; // 是否重新发货列表

    public $remainingDeliveryTime = 0; // 剩余发货时间（小时）
    public $authorIdList = []; // 达人id list
    public $authShopId = 0;
    public $authUserId;

    public $showAbnormalType = 0;     // 显示异常订单

    /**
     * @var int|null  0不锁定 1:锁定
     */
    public $lockType;
    public $deliveryType = 0; //发货类型
    public $sellerMemo=null; // 卖家备注
    public $buyerMessage=null;// 买家留言





    //物流状态
    /**
     *
     * 待揽件=>[0]
     * 已揽件，无物流=>[1]
     * 有物流，未签收=>[2,4,5,6,7,8,9,10,11,12,13]
     * 派件中=>[4]
     * 已签收=>[3]
     * @var array $logisticStatus
     */
    public $logisticStatus;
    /**
     * 是否统计接口
     * @var bool
     */
    public $isCountApi = false;

    /**
     *  relativeDays: 截单开始相对天数，正数表示相对当前日期的几天后，负数表示相对当前日期的几天前
     * @var int|null $promiseRelativeDays
     */
    public $cutoffBeginRelativeDays;


    /**
     * @var string|null 截单开始时间的时分秒部分 00:00:00
     */
    public $cutoffBeginHourMinuteSecond;

    /**
     * @var string|null  截单结束相对天数，正数表示相对当前日期的几天后，负数表示相对当前日期的几天前
     *
     */
    public $cutoffEndRelativeDays;

    /**
     * @var string|null 截单结束时间的时分秒部分 00:00:00
     */

    public $cutoffEndHourMinuteSecond;

    /**
     * @var string|null 规格属性1
     */
    public $skuValue1;
    /**
     * @var string|null  规格属性2
     */
    public $skuValue2;

    /**
     * @var string 分配类型
     */
    public $assignType = '';


    public function rules(): array
    {
        return [
            "search" => 'string',
            "begin_at" => 'date',
            "end_at" => 'date',
            "tidList" => 'array',
            "waybillCodeList" => 'array',
            "tab_flag" => "string",
            "addressGroupId" => 'int',   //筛选地址
            "displayMerge" => "boolean",   //合并展示
            "flag" => 'sometimes|nullable',
            "goodsId" => 'sometimes|nullable',
            "goodsNum" => 'string|nullable',
            "goodsTitle" => 'string',
            "skuStyle" => 'sometimes|nullable',
            "sku_value" => 'string',
            "orderTotalFee" => 'string',
            "payment"=>'string',
            "selectItem" => 'int',
            "ownerIdList" => 'array',
            "factoryOwnerIdList" => 'array',
            "quickFilterValue" => 'string',
            "skuIdList" => 'array',
            "includeOrNot" => 'int',
            "smart_logistics" => 'string',
            "printMode" => 'int',
            "isShippedList" => 'int',
            "isUnshippedList" => 'int',
            "custom_print_content" => 'string',
            "custom_group" => 'string',
            "remainingDeliveryTime" => 'int',
            "showAbnormalType" => 'int',
            "delivery_type" => 'int',
            "assign_type" => 'string',
        ];
    }

    /**
     * 获取订单的总金额查询条件
     * @return string|array|null
     */
    public function getOrderTotalFee(){
        if(empty($this->orderTotalFee)) {
            return null;
        }
        //如果orderTotalFee的格式100-  这样的，就是没有结尾的，就给他补上一个最大值
        if(StrUtil::endsWith($this->orderTotalFee, '-')) {
            $this->orderTotalFee .= '9999999';
        }
        //如果orderTotalFee的格式-100  这样的，就是没有开头的，就给他补上一个最小值
        if(StrUtil::startsWith($this->orderTotalFee, '-')) {
            $this->orderTotalFee = '0' . $this->orderTotalFee;
        }
        return StrUtil::extractedNumOrNumArr($this->orderTotalFee);
    }

    /**
     * 订单的实付金额查询条件
     * @return string|array|null
     */
    public function getPayment(){
        if(empty($this->payment)){
            return null;
        }
        if(StrUtil::endsWith($this->payment, '-')) {
            $this->payment .= '9999999';
        }
        if(StrUtil::startsWith($this->payment, '-')) {
            $this->payment = '0' . $this->payment;
        }
        return StrUtil::extractedNumOrNumArr($this->payment);
    }

    /**
     * 获取商品数量查询条件
     * @return float|int|string|string[]|null
     */
    public function getGoodsNum(){
        //如果goodsNumer的格式100-  这样的，就是没有结尾的，就给他补上一个最大值
        if(StrUtil::endsWith($this->goodsNum, '-')) {
            $this->goodsNum .= '9999999';
        }
        //如果goodsNumer的格式-100  这样的，就是没有开头的，就给他补上一个最小值
        if(StrUtil::startsWith($this->goodsNum, '-')) {
            $this->goodsNum = '0' . $this->goodsNum;
        }
        return StrUtil::extractedNumOrNumArr($this->goodsNum);
    }

    /**
     * @return string
     */
    public function getGroupColumn()
    {
        $groupColumn = 'receiver_phone';
        if (Environment::isKs()){
            $groupColumn = 'address_md5'; // Ks 没有跨天不一样， 如果换成 receiver_phone 是索引串 导致查不出来
        }
        $this->groupColumn = $groupColumn;
        return $this->groupColumn;
    }


}
