<?php

namespace App\Services\Order;

use App\Constants\RefundSubStatusConst;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Order\PackageSearchRequest;
use App\Models\CommoditySku;
use App\Models\Company;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Services\Goods\CommodityService;
use App\Services\Goods\GoodsService;
use App\Services\Goods\GoodsServiceManager;
use App\Services\Order\Request\OrderDeliverReportRequest;
use App\Services\Order\Request\OrderDeliveryRequest;
use App\Services\Order\Request\OrderSearchRequest;
use App\Services\Order\Request\GoodsMergeParam;
use App\Services\Order\Result\DeliverySkuItem;
use App\Services\Order\Result\DeliverySummaryReport;
use App\Services\Order\Result\OrderStockingSummaryReport;
use App\Services\Order\Result\ReportOrderItem;
use App\Services\ShippingFee\CompanyShippingFeeTemplateService;
use App\Services\ShippingFee\ShippingFeeTemplateService;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 报表
 */
class OrderStatisticsService
{
    /**
     * @var CommodityService $commodityService
     */
    protected  $commodityService;

    /**
     * @var CompanyShippingFeeTemplateService $companyShippingFeeTemplateService
     */
    protected  $companyShippingFeeTemplateService;
    /**
     * @var ShippingFeeTemplateService $shippingFeeTemplateService
     */
    protected  $shippingFeeTemplateService;

    /**
     * @var GoodsService $goodsService
     */
    protected  $goodsService;

    /**
     * @param CommodityService $commodityService
     * @param ShippingFeeTemplateService $shippingFeeTemplateService
     * @param GoodsService $goodsService
     * @param CompanyShippingFeeTemplateService $companyShippingFeeTemplateService
     */
    public function __construct(CommodityService $commodityService, ShippingFeeTemplateService $shippingFeeTemplateService, GoodsService $goodsService, CompanyShippingFeeTemplateService $companyShippingFeeTemplateService)
    {
        $this->commodityService = $commodityService;
        $this->shippingFeeTemplateService = $shippingFeeTemplateService;
        $this->goodsService = $goodsService;
        $this->companyShippingFeeTemplateService = $companyShippingFeeTemplateService;
    }


    /**
     * 发货统计
     * @param OrderDeliverReportRequest $orderDeliverReportRequest
     * @param GoodsMergeParam $goodsMergeParam
     * @return DeliverySummaryReport
     * @throws ErrorCodeException
     */
    public function deliveryReport(OrderDeliverReportRequest $orderDeliverReportRequest,GoodsMergeParam $goodsMergeParam): DeliverySummaryReport
    {

        $currentShopId=$orderDeliverReportRequest->currentShopId;
        $operationShopIds = $orderDeliverReportRequest->operationShopIds;
        $commoditySkus = $this->commodityService->findManyCommoditySkusByShopId($currentShopId);
        $companyShippingFeeTemplates=$this->companyShippingFeeTemplateService->findManyByShopId($currentShopId);
        Log::info('运费模板',[$companyShippingFeeTemplates]);

        $deliverySummaryReport = new DeliverySummaryReport($goodsMergeParam);
        $deliverySummaryReport->companyShippingFeeTemplates=$companyShippingFeeTemplates;
        $deliverySummaryReport->setCommoditySkus($commoditySkus);
        $shopIds = $orderDeliverReportRequest->orderShopIds;
        $refundStatus = $orderDeliverReportRequest->refundStatus;
        $refundStatusArr = $orderDeliverReportRequest->refundStatusArr;
        $wpCodes = $orderDeliverReportRequest->wpCodes;
        $keyword = $orderDeliverReportRequest->keyword;
        $skuIdList = $orderDeliverReportRequest->skuIdList;
        $deliverTypes = $orderDeliverReportRequest->deliveryTypes;
        Log::info('deliveryReport', [$orderDeliverReportRequest]);
        $goodsCollection=new Collection();
        $companyCollection=new Collection();

        $query = PackageOrder::query()->join('packages', 'packages.id', '=', 'package_orders.package_id')
            ->join('orders', 'orders.id', '=', 'package_orders.order_id')
            ->join('order_items', 'order_items.id', '=', 'package_orders.order_item_id')
            ->whereIn('packages.shop_id', $shopIds)
            ->whereNull('packages.recycled_at')
            ->where('package_orders.source_type',1)
            ->where('packages.source_type',1)
//            ->whereIn('packages.send_waybill_type', [Package::SEND_WAYBILL_TYPE_NORMAL, Package::SEND_WAYBILL_TYPE_MULTI_MAIN])
            ->where('packages.send_at', ">=",$orderDeliverReportRequest->beginAt)
            ->where('packages.send_at', "<=",$orderDeliverReportRequest->endAt);
//            ->whereIn('packages.operation_shop_id', $operationShopIds);


//            ->whereIn('order_status', [
//                Order::ORDER_STATUS_PART_DELIVERED,
//                Order::ORDER_STATUS_DELIVERED,
//                Order::ORDER_STATUS_RECEIVED,
//                Order::ORDER_STATUS_SUCCESS,
//                 Order::ORDER_STATUS_CLOSE
//            ]);
        //关联发货记录表，把不是我们这边发的单子过滤掉  0 无售后 1 退款中 2 售后完成
//        if ($refundStatus == 0) { // 无售后
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_NO]);
//            $query->where('order_items.refund_status', Order::REFUND_STATUS_NO);
//        } else if ($refundStatus == 1) { // 退款中
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
//            $query->where('order_items.refund_status', Order::REFUND_STATUS_YES);
//            $query->whereIn('order_items.refund_sub_status', RefundSubStatusConst::REFUND_PROCESSING_ARRAY);
//        } else if ($refundStatus == 2) { // 售后完成
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
//            $query->where('order_items.refund_status', Order::REFUND_STATUS_YES);
//            $query->whereIn('order_items.refund_sub_status', RefundSubStatusConst::REFUND_COMPLETE_ARRAY);
//        } else {
//            //全部
//            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
//            $query->whereIn('order_items.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_YES]);
//        }
        // 0无退款、1 退款中、2退款完成
        if (!empty($refundStatusArr)){
            $ordersRefundStatusArr = [];
            $orderItemsRefundStatusArr = [];
            $orderItemsRefundSubStatusArr = [];
            if (in_array(0,$refundStatusArr)){ // 无退款
                $ordersRefundStatusArr = array_merge($ordersRefundStatusArr, [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_NO]);
                $orderItemsRefundStatusArr = array_merge($orderItemsRefundStatusArr, [Order::REFUND_STATUS_NO]);
                $orderItemsRefundSubStatusArr = array_merge($orderItemsRefundSubStatusArr, [RefundSubStatusConst::NONE]);;
            }
            if (in_array(1, $refundStatusArr)){ // 退款中
                $ordersRefundStatusArr = array_merge($ordersRefundStatusArr, [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
                $orderItemsRefundStatusArr = array_merge($orderItemsRefundStatusArr, [Order::REFUND_STATUS_YES]);
                $orderItemsRefundSubStatusArr = array_merge($orderItemsRefundSubStatusArr, RefundSubStatusConst::REFUND_PROCESSING_ARRAY);;
            }
            if (in_array(2, $refundStatusArr)){ // 退款完成
                $ordersRefundStatusArr = array_merge($ordersRefundStatusArr, [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
                $orderItemsRefundStatusArr = array_merge($orderItemsRefundStatusArr, [Order::REFUND_STATUS_YES]);
                $orderItemsRefundSubStatusArr = array_merge($orderItemsRefundSubStatusArr, RefundSubStatusConst::REFUND_COMPLETE_ARRAY);;
            }
            $query->whereIn('orders.refund_status', $ordersRefundStatusArr);
            $query->whereIn('order_items.refund_status', $orderItemsRefundStatusArr);
            $query->whereIn('order_items.refund_sub_status', $orderItemsRefundSubStatusArr);

        }
        if ($wpCodes) {
            $wp_code = explode(',', $wpCodes);
            $query->whereIn('packages.wp_code', $wp_code);
        }
        if(!empty($deliverTypes)) {
            $query->whereIn('packages.delivery_type', $deliverTypes);
        }

        if ($keyword) {
            $query = $query->where(function ($query) use ($keyword) {
                $query->where('order_items.goods_title', 'like', "%{$keyword}%")
                    ->orWhere('order_items.outer_iid', $keyword)
                    ->orWhere('order_items.sku_value', 'like', "%{$keyword}%")
                    ->orWhere('order_items.sku_id', $keyword)
                    ->orWhere('order_items.num_iid', $keyword)
                    ->orWhere('order_items.outer_sku_iid', $keyword);
            });
        }
        if ($skuIdList) {
            $query = $query->whereIn('package_orders.sku_id', $skuIdList);
        }
        $query->orderBy('packages.send_at');
        Log::info('deliveryReport.sql', [$query->toSql(),$query->getBindings()]);
        $query->select(
            [
                'package_orders.id',
                'order_items.sku_id',
                'order_items.sku_value',
                'order_items.num_iid',
                'order_items.goods_title',
                'order_items.goods_pic',
                'order_items.goods_price',
                'order_items.outer_iid',
                'order_items.outer_sku_iid',
                'package_orders.num',
                'packages.wp_code',
                'packages.waybill_code',
                'packages.company_id',
                'package_orders.tid',
                'packages.send_waybill_type',
                'orders.district_code'
                //发货数量，如果order_items的记录有send_num 则用send_num，否则用goods_nums
//                DB::raw('sum(goods_num) goods_nums')
            ]
        )->chunk(1000, function ($items) use ($deliverySummaryReport,$commoditySkus,$goodsCollection,$companyCollection) {
            Log::info("分页发货对账统计开始");
//            $numIidArr = collect($items)->pluck('num_iid')->unique()->toArray();
            //$goodsCollection中没有的numIidArr 提取出来，再从数据库中查询
//            $numIidArr = array_diff($numIidArr,$goodsCollection->pluck('num_iid')->toArray());

//            $companyIdArr = collect($items)->pluck('company_id')->unique()->toArray();
            //$companyCollection中没有的$companyIdArr 提取出来，再从数据库中查询
//            $companyIdArr = array_diff($companyIdArr,$companyCollection->pluck('id')->toArray());
//            $companyList = Company::query()->whereIn('id', $companyIdArr)->get();
            foreach ($items as $item) {
//                Log::info("packageOrderItem",[$item]);
                //获取商品Sku信息
                /**
                 * @var CommoditySku $commoditySku
                 */
                $commoditySku = $commoditySkus->where('platform_sku_out_id', $item->sku_id)->first();
                $skuWeight = $commoditySku ?( $commoditySku->weight??0) : 0;

                $reportOrderItem = new ReportOrderItem();
                $reportOrderItem->skuId = $item->sku_id;
                $reportOrderItem->orderSn = $item->tid;
                $reportOrderItem->skuValue = $item->sku_value;
                $reportOrderItem->sendWaybillType=$item->send_waybill_type;
                $numIid = $item->num_iid;
                $reportOrderItem->numIid = $numIid;
                //先从上下文的缓存中获取goods信息，没有取到就加载数据库
                $goods = $goodsCollection->get($numIid);//->where("num_iid", $numIid)->first();
                if(!$goods){
//                    $goods = Goods::query()->where('num_iid',$item->num_iid)->first(); // 查不到商品的时候会一直查
                    $goods = Goods::query()->where("num_iid", $numIid)->first();
                    if($goods){
                        $goodsCollection->put($numIid, $goods);
                    }
                    elseif(Environment::isXhs()){
                        $goods=$this->goodsService->findGoodsBySkuId($item->sku_id);
                        if($goods){
                            $goodsCollection->push($goods);
                            $reportOrderItem->numIid =$goods->num_iid;
                        }
                    }
                }
                $companyId = $item->company_id;
                $company=null;
                if($companyId) {
                    $company = $companyCollection->get($companyId);//->where('id', $companyId)->first();
                    if (!$company) {
//                        $company = Company::query()->where('id', $companyId)->withTrashed()->first();
                        $company = Company::query()->where('id', $companyId)->first();
                        if ($company) {
                            $companyCollection->put($companyId, $company);
                        }
                    }
                }else{
                    Log::info("companyId is null",[$item]);
                }

                $reportOrderItem->goodsTitle = $goods?$goods->goods_title:$item->goods_title; //如果能匹配到goods就用goods里面的信息
                $reportOrderItem->goodsPic = $goods?$goods->goods_pic:$item->goods_pic;
                $reportOrderItem->goodsPrice = $item->goods_price;
                $reportOrderItem->customTitle = $goods?$goods->custom_title:$item->custom_title;
                $reportOrderItem->customSkuValue =$item->customGoodsSkus ? $item->customGoodsSkus->custom_sku_value : null;
                $reportOrderItem->num = $item->num;
                $deliverySummaryReport->addOrderItem($reportOrderItem);
                $deliverySummaryReport->handleWaybillOrderItem($item->waybill_code, $companyId,$item->wp_code,$item->district_code,$item->num,$skuWeight);

            }
            Log::info("分页发货对账统计结束");
        });
        $deliverySummaryReport->companyCollection=$companyCollection;
        $deliverySummaryReport->goodsItems=$deliverySummaryReport->goodsItems->values();

        $deliverySummaryReport->build();
        Log::info("发货对账汇总统计完成");
        return $deliverySummaryReport;


    }


    /**
     * 已发货统计(新)
     * @param PackageSearchRequest $packageSearchRequest
     * @param GoodsMergeParam $goodsMergeParam
     * @return OrderStockingSummaryReport
     */
    public function deliveryStatisticsReport(PackageSearchRequest $packageSearchRequest, GoodsMergeParam $goodsMergeParam): OrderStockingSummaryReport{
        $orderQueryBuilder = new OrderQueryBuilder();
        $orderItemQuery= $orderQueryBuilder->buildDeliveryStatisticsQuery($packageSearchRequest);
        $packageQueryBuilder=new PackageQueryBuilder();
        $packageOrderQuery=$packageQueryBuilder->buildDeliveryStatisticsQuery($packageSearchRequest);
        $packageOrderQuery->joinSub(getSqlByQuery($orderItemQuery),"orderitems",'orderitems.id',"=","package_orders.order_item_id");

        Log::info("已发货按商品统计",["sql"=>getSqlByQuery($packageOrderQuery)]);
        $orderStockingSummaryReport = new OrderStockingSummaryReport($goodsMergeParam);
        $hasMore=true;
        $limit=500;
        $offset=0;
        while($hasMore) {

            $packageOrderQuery->limit($limit);
            $packageOrderQuery->offset($offset);
//            Log::info("已发货按商品统计", ["sql" => getSqlByQuery($packageOrderQuery)]);
            $result = $packageOrderQuery->get(['package_orders.*','packages.shop_id','packages.waybill_code','packages.send_waybill_type','orderitems.*']);
            Log::info("已发货按商品统计-获取订单信息",["offset"=>$offset]);
            $orderQueryBuilder->filterGoodsNum($result, $packageSearchRequest);
            $index = 0;
            /**
             * @var PackageOrder $item
             */
            foreach ($result as $item) {
                /**
                 * @var OrderItem $orderItem
                 */
                $orderItem=$item->orderItem;
                $order=$item->fixOrder;


                $reportItem = new ReportOrderItem();
                $reportItem->orderSn = $item->tid;
                $reportItem->skuId = $item->sku_id;
                $reportItem->skuValue = $orderItem->sku_value;
                $reportItem->numIid = $orderItem->num_iid;
                $reportItem->goodsTitle = $orderItem->goods_title;
                $reportItem->goodsPic = $orderItem->goods_pic;
                $reportItem->goodsPrice = $orderItem->goods_price;
                $reportItem->num = $item->num;
                $reportItem->waybillCode = $item->waybill_code;
                $reportItem->goodsOutIid = $orderItem->outer_iid;
                $reportItem->skuOuterIid = $orderItem->outer_sku_iid;
                $reportItem->sellerMemo=$order->seller_memo;
                $reportItem->payment=bcdiv(bcmul($orderItem->payment,$item->num,2),$orderItem->goods_num,2);
                $reportItem->sendWaybillType=$item->send_waybill_type;
                $reportItem->buyerMessage=$order->buyer_message;
                // $item->customGoods ? $item->customGoods->custom_title : null;
                $reportItem->customTitle = $orderItem->customGoods ? $orderItem->customGoods->custom_title : null;
                // $item->customGoods ? $item->customGoods->custom_title : null;
                $reportItem->customSkuValue=$orderItem->customGoodsSkus?$orderItem->customGoodsSkus->custom_sku_value:null;
                $reportItem->sellerFlag = $order->seller_flag;
                $reportItem->shopId=$item->shop_id;
                $reportItem->packageId = $item->package_id;
                $reportItem->skuValue1 = $orderItem->sku_value1;
                $reportItem->skuValue2 = $orderItem->sku_value2;

                Log::info("已发货按商品统计-添加reportItem",["index"=>$index]);
                $orderStockingSummaryReport->addOrderItem($reportItem);
                $index++;
            }
            $hasMore=count($result)>=$limit;
            Log::info("已发货按商品统计-获取订单信息-完成",["offset"=>$offset]);
            $offset+=$limit;

        }
        $orderStockingSummaryReport->setGoodsInfoCollection(GoodsServiceManager::batchGoodsInfo($orderStockingSummaryReport->getGoodsDetailRequests()));
        $orderStockingSummaryReport->build();
        return $orderStockingSummaryReport;



    }


    /**
     * 订单备货报表
     * @param OrderSearchRequest $oderSearchRequest
     * @param GoodsMergeParam $goodsMergeParam
     * @return OrderStockingSummaryReport
     */


    public function orderStockingReport(OrderSearchRequest $oderSearchRequest, GoodsMergeParam $goodsMergeParam): OrderStockingSummaryReport
    {
        $orderQueryBuilder = new OrderQueryBuilder();

        $query = $orderQueryBuilder->buildOrderStockingQuery($oderSearchRequest);
        Log::info('备货单查询SQL', ["sql"=>getSqlByQuery($query)]);



        $limit = 500;
        $query->limit($limit);
        $offset=0;
        $hasMore=true;
        $orderStockingSummaryReport = new OrderStockingSummaryReport($goodsMergeParam);
        //进行分页
        while($hasMore) {


            $query->offset($offset);
            $result = $query->get();
            Log::info('备货单查询', ["offset"=>$offset,"count"=>$result->count()]);
            $orderQueryBuilder->filterGoodsNum($result, $oderSearchRequest);

            /**
             * @var OrderItem $item
             */
            foreach ($result as $item) {
//                Log::info('订单信息', [$item]);
                $reportItem = new ReportOrderItem();
                $reportItem->orderSn = $item->tid;
                $reportItem->skuId = $item->sku_id;
                $reportItem->buyerMessage= $item->buyer_message;
                $reportItem->sellerFlag = $item->seller_flag;
                $reportItem->sellerMemo = $item->seller_memo;
                $reportItem->skuValue = $item->sku_value;
                $reportItem->numIid = $item->num_iid;
                $reportItem->goodsOutIid = $item->outer_iid;
                $reportItem->skuOuterIid = $item->outer_sku_iid;
                $reportItem->goodsTitle = $item->goods_title;
                $reportItem->goodsPic = $item->goods_pic;
                $reportItem->customSkuValue = $item->customGoodsSkus ? $item->customGoodsSkus->custom_sku_value : null;
                $reportItem->customTitle = $item->customGoods ? $item->customGoods->custom_title : null;
                $reportItem->goodsPrice = $item->goods_price;
                $reportItem->skuValue1  = $item->sku_value1;
                $reportItem->skuValue2  = $item->sku_value2;
                $reportItem->payAt = $item->pay_at;
                $reportItem->num = ($item->send_remain_num>0?$item->send_remain_num:$item->goods_num) - $item->pre_send_num;
                if($reportItem->num<=0){
                    continue;
                }
                $reportItem->payment = $item->payment;
                $reportItem->shopId = $item->shop_id;
                $orderStockingSummaryReport->addOrderItem($reportItem);
            }
            Log::info('备货单查询分页统计完成', ["offset"=>$offset,"count"=>$result->count()]);

            if($result->count()< $limit){
                $hasMore=false;

            }else{
                $offset=$offset+$limit;
            }
        }
        $orderStockingSummaryReport->setGoodsInfoCollection(GoodsServiceManager::batchGoodsInfo($orderStockingSummaryReport->getGoodsDetailRequests()));
        $orderStockingSummaryReport->build();



        return $orderStockingSummaryReport;
    }

}
