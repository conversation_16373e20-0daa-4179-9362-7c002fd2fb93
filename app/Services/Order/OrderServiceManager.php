<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 20:32
 */

namespace App\Services\Order;


use App\Constants\PlatformConst;
use App\Services\Order\Impl\AlbbOrderImpl;
use App\Services\Order\Impl\Alc2mOrderImpl;
use App\Services\Order\Impl\CnOrderImpl;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Order\Impl\JdOrderImpl;
use App\Services\Order\Impl\KsOrderImpl;
use App\Services\Order\Impl\PddOrderImpl;
use App\Services\Order\Impl\TaobaoOrderImpl;
use App\Services\Order\Impl\WxOrderImpl;
use App\Services\Order\Impl\WxSpOrderImpl;
use App\Services\Order\Impl\XhsOrderImpl;
use InvalidArgumentException;

class OrderServiceManager
{
    protected static $initMap = [
        PlatformConst::TAOBAO => TaobaoOrderImpl::class,
        PlatformConst::PDD => PddOrderImpl::class,
        PlatformConst::KS => KsOrderImpl::class,
        PlatformConst::WX => WxOrderImpl::class,
        PlatformConst::DY => DyOrderImpl::class,
        PlatformConst::JD => JdOrderImpl::class,
        PlatformConst::WXSP => WxSpOrderImpl::class,
        PlatformConst::XHS => XhsOrderImpl::class,
        PlatformConst::ALC2M => Alc2mOrderImpl::class,
        PlatformConst::ALBB => AlbbOrderImpl::class,
        PlatformConst::CN => CnOrderImpl::class,
    ];

    /**
     * 创建一个订单server
     * @param $name
     * @return AbstractOrderService
     * <AUTHOR>
     */
    public static function create($name = ''): AbstractOrderService
    {
        if (empty($name)){
            $name = config('app.platform');
        }
        if (isset(self::$initMap[$name])) {
            return new self::$initMap[$name]();
        }
        throw new InvalidArgumentException('不存在的 OrderServiceManager:' . $name);
    }
}
