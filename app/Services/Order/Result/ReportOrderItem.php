<?php

namespace App\Services\Order\Result;

use App\Models\Order;

/**
 * 订单
 */
class ReportOrderItem
{
    /**
     * @var string  买家留言
     */
    public $buyerMessage;
    /**
     * @var string  卖家备注
     */
    public $sellerMemo;
    /**
     * @var string  卖家旗帜
     */
    public $sellerFlag;
    /**
     * @var string  平台的skuId
     */
    public $skuId;
    /**
     * @var string  sku的名称
     */
    public $skuValue;

    /**
     * @var string  sku编码（外部的）
     */
    public $skuOuterIid;

    public $goodsOutIid;

    /**
     * @var string  sku简称（自定义）
     */
    public $customSkuValue;

    /**
     * @var string  平台商品ID
     */
    public $numIid;
    /**
     * @var string  商品标题
     */
    public $goodsTitle;

    /**
     * @var string  商品简称
     */
    public $customTitle;

    /**
     * @var string  商品图片
     */
    public $goodsPic;

    /**
     * @var string  店铺ID
     */
    public $shopId;


    /**
     * @var string  商品单价
     */
    public $goodsPrice;

    /**
     * @var string    支付金额
     */
    public $payment = "0";


    /**
     * @var int  商品数量
     */
    public $num;
    /**
     * @var string  订单号
     */
    public $orderSn;

    /**
     * @var int   包裹ID
     */
    public $packageId;

    /**
     * @var string  运单号
     */
    public $waybillCode;

    /**
     * @var string  发货方式 发货单号类型 1:普通单号 2:一单多包主包裹 3:一单多包从包裹
     * 计算包裹的数据，都要加上
     * 但计算发货数量的时候，需要去掉
     */

    public $sendWaybillType;

    /**
     * @var string  支付时间
     */
    public $payAt;


    /**
     * @var string  sku值1
     */
    public $skuValue1;

    /*
     * @var string  sku值2
     */
    public $skuValue2;

    /**
     * 获取合并的字段和值
     * @param int $skuMergeType
     * @return string|null;
     */
    public function getSkuMergeValue(int $skuMergeType): string
    {
        //$skuMergeType 1 商品ID合并，2 简称合并（没有简称按商品标题） 3 标题合并 4编码合并（没有编码按商品ID）
        $mergeValue = null;
        if ($skuMergeType == 1) {
            $mergeValue = $this->skuId;
        }
        if ($skuMergeType == 2) {
            //如果简称没有，则用商品标题
            $mergeValue = $this->customSkuValue ?: $this->skuValue;
        }
        if ($skuMergeType == 3) {
            $mergeValue = $this->skuValue;
        }
        if ($skuMergeType == 4) {
            $mergeValue = $this->skuOuterIid ?: $this->skuId;
        }
        return $mergeValue;
    }

    /**
     * 获取商品合并的字段和值
     * @param int $goodsMergeType
     * @return string
     */

    public function getGoodsMergeValue(int $goodsMergeType): string
    {

        //根据入参来确认商品合并的字段和值，返回一个array field的值是字段，value的值是是值

        $mergeValue = null;
        if ($goodsMergeType == 1) {
            $mergeValue = $this->numIid;
        }
        if ($goodsMergeType == 2) {
            //如果简称没有，则用商品标题
            $mergeValue = $this->customTitle ?: $this->goodsTitle;
        }
        if ($goodsMergeType == 3) {
            $mergeValue = $this->goodsTitle;
        }
        if ($goodsMergeType == 4) {
            $mergeValue = $this->goodsOutIid ?: $this->numIid;
        }
        return $mergeValue;
    }

    /**
     * @return bool 是否有补充
     */
    public function isComment(): bool
    {

        return !empty($this->buyerMessage) or !empty($this->sellerMemo && $this->sellerMemo != "[]") or (!empty($this->sellerFlag) && $this->sellerFlag != Order::FLAG_GRAY);
    }


    /**
     * @return OrderComment 生成订单备注对象
     */
    public function ofOrderComment(): OrderComment
    {

        return new OrderComment($this->orderSn, $this->sellerFlag, $this->sellerMemo, $this->buyerMessage);
    }

    /**
     * 是否需要计算商品数量，从包裹是不计算数量的
     * 1:普通单号 2:一单多包主包裹 3:一单多包从包裹
     *
     * @return bool
     */
    public function isCalGoodsNum(): bool
    {
        //3:一单多包从包裹 无需计算商品数量
        return $this->sendWaybillType != 3;
    }
}
