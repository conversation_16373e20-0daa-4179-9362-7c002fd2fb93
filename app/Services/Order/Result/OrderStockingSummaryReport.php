<?php

namespace App\Services\Order\Result;

use App\Services\Goods\GoodsDetailRequest;
use App\Services\Order\Request\GoodsMergeParam;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class OrderStockingSummaryReport
{

    /**
     * @var GoodsMergeParam $goodsMergeParam
     */
    public $goodsMergeParam;

    /**
     *
     * @var Collection<DeliverySkuItem>
     */
    protected $skuItemsKeyByMergeValue;


    /**
     * @var int $totalGoodsNum 商品件数
     */
    public $totalGoodsNum = 0;

    /**
     * @var int $goodsKindNum 商品种类
     */
    public $goodsKindNum = 0;

    /**
     * @var int $skuKindNum 商品SKU种类
     */
    public $skuKindNum = 0;


    /**
     *
     *
     * @var int $orderCount 订单数量
     */
    public $orderCount = 0;


    /**
     * 订单号,用来判断订单是否已经存在
     * @var array
     */
    private $orderSns = [];

    /**
     * @var array $waybillCodes 运单号列表
     */

    public $waybillCodes;

    /**
     * 订单总金额
     * @var string
     */
    public $payAmount = "0";

    /**
     * @var Collection<DeliveryGoodsItem> $goodsSummary 商品列表
     */
    public $goodsSummary;


    /**
     * 记录SKU的合并值和DeliveryGoodsItem的映射关系
     * @var Collection<string,DeliveryGoodsItem> $skuMergeValueDeliveryGoodsItem
     */
    protected $skuMergeValueDeliveryGoodsItem;


    /**
     * @var Collection<array> $goodsInfoCollection 商品信息列表
     */
    protected $goodsInfoCollection;


    /**
     * @var int $packageCount 包裹数
     */
    public $packageCount = 0;

    public function setGoodsInfoCollection(Collection $goodsInfoCollection): void
    {
        $this->goodsInfoCollection = $goodsInfoCollection;
    }


    public function __construct(GoodsMergeParam $goodsMergeParam)
    {
        $this->skuItemsKeyByMergeValue = new Collection();
        $this->goodsMergeParam = $goodsMergeParam;
        $this->goodsSummary = new Collection();
        $this->skuMergeValueDeliveryGoodsItem = new Collection();
        $this->waybillCodes = new Collection();
        Log::info("OrderStockingSummaryReport", [$goodsMergeParam]);
    }

    public function addOrderItem(ReportOrderItem $reportOrderItem)
    {
//        Log::info("addOrderItem",[$reportOrderItem]);
        $goodsMergeValue = $reportOrderItem->getGoodsMergeValue($this->goodsMergeParam->goodsMergeType);
        $skuMergeValue = $reportOrderItem->getSkuMergeValue($this->goodsMergeParam->skuMergeType);
        if ($this->goodsMergeParam->outputFormat == GoodsMergeParam::GOODS_MERGE_TYPE) {
            //如果是按商品合并，只有商品相同的前提下的SKU才进行合并，所以把商品的合并值也加到$skuMergeValue中
            $skuMergeValue = $goodsMergeValue . $skuMergeValue;
        }

        /**
         * @var DeliverySkuItem $skuItem
         */
//        $skuItem = $this->skuItems->where("skuMergeValue", $skuMergeValue)->first();
        $skuItem = $this->skuItemsKeyByMergeValue->get($skuMergeValue);
//        Log::info("skuItem",[$skuMergeValue,$skuItem]);
        if (!$skuItem) {
            $skuItem = new DeliverySkuItem();
            $skuItem->skuId = $reportOrderItem->skuId;
            $skuItem->skuValue = $reportOrderItem->skuValue;
            $skuItem->setNumIid($reportOrderItem->numIid);
            $skuItem->setGoodsTitle($reportOrderItem->goodsTitle);
            $skuItem->shopId = $reportOrderItem->shopId;
            $skuItem->skuMergeValue = $skuMergeValue;
            $skuItem->setGoodsPic($reportOrderItem->goodsPic);
            $skuItem->customSkuValue = $reportOrderItem->customSkuValue;
            $skuItem->customTitle = $reportOrderItem->customTitle;
            $skuItem->goodsPrice = $reportOrderItem->goodsPrice;
            $skuItem->goodsPic = $reportOrderItem->goodsPic;
            $skuItem->goodsOutIid = $reportOrderItem->goodsOutIid;
            $skuItem->skuOutIid = $reportOrderItem->skuOuterIid;
            $skuItem->payAtMin = $reportOrderItem->payAt;
            $skuItem->skuValue1  = $reportOrderItem->skuValue1;
            $skuItem->skuValue2  = $reportOrderItem->skuValue2;
//            $skuItem->num = $reportOrderItem->num;
//            $skuItem->payment = $reportOrderItem->payment;
            $this->skuItemsKeyByMergeValue->put($skuMergeValue, $skuItem);
        }else{
            $skuItem->setPayAtMin( $reportOrderItem->payAt);
        }
//        Log::info("addOrderItem", [$reportOrderItem]);
        if ($reportOrderItem->isCalGoodsNum()) {
//            Log::info("sumSkuNum", [$reportOrderItem]);
            $skuItem->num += $reportOrderItem->num;
            $skuItem->payment = bcadd($skuItem->payment, $reportOrderItem->payment, 2);
        }
        $skuItem->addSkuId($reportOrderItem->shopId, $reportOrderItem->skuId);
        if ($reportOrderItem->isComment()) {
            $skuItem->addOrderComment($reportOrderItem->ofOrderComment());
        }

        //如果是按商品进行合并
        if ($this->goodsMergeParam->outputFormat == GoodsMergeParam::GOODS_MERGE_TYPE) {
            $goodsItem = $this->goodsSummary->where("goodsMergeValue", $goodsMergeValue)->first();
//            Log::info("匹配goodsItem",[$goodsMergeValue,$goodsItem]);
            if (!$goodsItem) {

                $goodsItem = new DeliveryGoodsItem();
                $goodsItem->goodsPic = $reportOrderItem->goodsPic; //这里是规格的图片，先临时填充，等最后获取商品的信息的时候再填充
                $goodsItem->goodsTitle = $reportOrderItem->goodsTitle;
                $goodsItem->outIid = $reportOrderItem->goodsOutIid;
                $goodsItem->shopId = $reportOrderItem->shopId;
                $goodsItem->numIid = $reportOrderItem->numIid;
                $goodsItem->customTitle = $reportOrderItem->customTitle;
                $goodsItem->goodsMergeValue = $goodsMergeValue;
                $goodsItem->payAtMin = $reportOrderItem->payAt;
                $this->goodsSummary->push($goodsItem);
                $this->skuMergeValueDeliveryGoodsItem->put($skuMergeValue, $goodsItem);

            }
            $goodsItem->addNumIid($reportOrderItem->shopId, $reportOrderItem->numIid);
            $goodsItem->addSkuItem($skuItem);
            $goodsItem->setPayAtMin($reportOrderItem->payAt);
        } else {

            if ($this->skuMergeValueDeliveryGoodsItem->has($skuMergeValue)) {
                $goodsItem = $this->skuMergeValueDeliveryGoodsItem->get($skuMergeValue);
            } else {
                $goodsItem = $skuItem->toGoodsItem();
                $goodsItem->addSkuItem($skuItem);
                $this->goodsSummary->push($goodsItem);
                $this->skuMergeValueDeliveryGoodsItem->put($skuMergeValue, $goodsItem);
            }
        }
        if($reportOrderItem->isCalGoodsNum()) {
            $goodsItem->num += $reportOrderItem->num;
            $goodsItem->payment = round_bcadd($goodsItem->payment, $reportOrderItem->payment);
            $this->payAmount = round_bcadd($this->payAmount, $reportOrderItem->payment);
            $this->totalGoodsNum += $reportOrderItem->num;
        }

        //累计订单支付金额，放在这里是因为每个订单的单价可能不一样

        if (!array_key_exists($reportOrderItem->orderSn, $this->orderSns)) {
//            Log::info("addOrderItem",[$reportOrderItem->orderSn,$this->orderSns]);
            $this->orderSns[$reportOrderItem->orderSn] = $reportOrderItem->orderSn;
            $this->orderCount += 1;
        }

        //保存处理过的waybillCode，防止重复
        if ($reportOrderItem->waybillCode && !array_key_exists($reportOrderItem->waybillCode, $this->waybillCodes)) {
            $this->waybillCodes[$reportOrderItem->waybillCode] = $reportOrderItem->waybillCode;
            $this->packageCount += 1;
        }
//        if($reportOrderItem->waybillCode&&!$this->waybillCodes->($reportOrderItem->waybillCode)){
//            $this->waybillCodes->push($reportOrderItem->waybillCode);
//            $this->packageCount+=1;
//        }
    }

    public function build()
    {

        $this->goodsKindNum = $this->goodsSummary->count();
        $this->skuKindNum = $this->goodsSummary->sum(function (DeliveryGoodsItem $item) {
            return $item->skuItems->count();

        });
        //DeliveryGoodsItem-skuItems的总数进行类型
        $this->skuKindNum = $this->goodsSummary->sum(function (DeliveryGoodsItem $item) {
            return $item->skuItems->count();
        });
        /**
         * @var DeliveryGoodsItem $item
         */
        foreach ($this->goodsSummary as $key => $item) {
            if ($this->goodsInfoCollection->has($item->numIid)) {
                $goodsInfo = $this->goodsInfoCollection[$item->numIid];
                $item->goodsPic = $goodsInfo["goods_pic"];
            }
        }


    }

    /**
     * 获取商品列表的numIid
     * @return array
     */
    public function getNumIids(): array
    {
        return $this->goodsSummary->pluck("numIid")->toArray();
    }

    public function getGoodsDetailRequests(): array
    {
        Log::info("getGoodsDetailRequests", [$this->goodsSummary]);
        return $this->goodsSummary->map(function (DeliveryGoodsItem $item) {
            return new GoodsDetailRequest($item->numIid, $item->shopId);
        })->toArray();

    }
}
