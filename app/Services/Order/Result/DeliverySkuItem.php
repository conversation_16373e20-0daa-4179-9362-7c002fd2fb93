<?php

namespace App\Services\Order\Result;

use App\Models\Order;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 发货统计的SKU
 */
class DeliverySkuItem
{

    public $shopId;
    public $skuId;
    /**
     * @var array $skuIds
     */
    public $skuIds=[];
    public $skuOutIid;
    public $goodsOutIid;

    public $skuValue;
    /**
     * @var string  商品简称
     */
    public $customTitle;

    /**
     * @var string  商品ID
     */
    public $numIid;
    public $goodsTitle;
    public $goodsPic;
    public $goodsPrice;
    public $weight;

    /**
     * @var string  SKU简称
     */
    public $customSkuValue;

    /**
     * @var string 支付金额
     */
    public $payment = "0";
    public $netWeight;
    public $settlementPrice;
    public $num = 0;
    public $totalSettlementPrice;

    public $skuMergeValue;

    public $payAtMin;

    /**
     * @var Collection<string,OrderComment> 备注信息
     *
     */
    public $comments;


    public $skuValue1;

    public $skuValue2;

    public function __construct()
    {
        $this->comments = new Collection();
    }

    /**
     * 添加备注信息
     * @param OrderComment $comment
     * @return void
     */
    public function addOrderComment(OrderComment $comment)
    {
//        Log::info("添加备注信息", ["comments" => $this->comments->toArray(),"comment"=>$comment]);
        if (!$this->comments->has($comment->tid)) {
            $this->comments->put($comment->tid, $comment);
        }
    }

    /**
     * 将当前SKU转换出商品信息
     * @return DeliveryGoodsItem
     */

    public function toGoodsItem(): DeliveryGoodsItem
    {
        $goodsItem = new DeliveryGoodsItem();
        $goodsItem->numIid = $this->numIid;
        $goodsItem->goodsTitle = $this->goodsTitle;
        $goodsItem->goodsPic = $this->goodsPic;
        $goodsItem->customTitle = $this->customTitle;
        $goodsItem->outIid = $this->goodsOutIid;
        $goodsItem->shopId = $this->shopId;
        return $goodsItem;

    }

    public function setCustomTitle(string $customTitle): void
    {
        $this->customTitle = $customTitle;
    }

    public function setNumIid(string $numIid): void
    {
        $this->numIid = $numIid;
    }

    /**
     * @param mixed $goodsTitle
     */
    public function setGoodsTitle($goodsTitle): void
    {
        $this->goodsTitle = $goodsTitle;
    }

    /**
     * @param mixed $goodsPic
     */
    public function setGoodsPic($goodsPic): void
    {
        $this->goodsPic = $goodsPic;
    }

    public function addSkuId(int $shopId,string $skuId): void{
        $value = $shopId."_".$skuId;
        if(!in_array($value,$this->skuIds)){
            $this->skuIds[] = $value;

        }
    }

    public function setPayAtMin(?string $payAt=null): void{
        if($payAt == null){
            return;
        }
        //比较支付时间，取最小值
        if($this->payAtMin == null || $payAt < $this->payAtMin){
            $this->payAtMin = $payAt;
        }
    }

}
