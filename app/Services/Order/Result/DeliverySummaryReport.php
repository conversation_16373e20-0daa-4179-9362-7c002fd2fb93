<?php

namespace App\Services\Order\Result;

use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\CommoditySku;
use App\Models\Company;
use App\Models\CompanyShippingFeeTemplate;
use App\Models\ShippingFeeTemplate;
use App\Services\Order\Request\GoodsMergeParam;
use App\Services\ShippingFee\ShippingFee;
use App\Services\ShippingFee\ShippingFeeTemplateExtraSettingItem;
use App\Utils\ArrayUtil;
use App\Utils\ExpressCompanyUtil;
use App\Utils\ObjectUtil;
use App\Utils\StrUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;

class DeliverySummaryReport
{
    /**
     * @var GoodsMergeParam $goodsMergeParam
     */
    private $goodsMergeParam;
    public function __construct(GoodsMergeParam $goodsMergeParam)
    {
        $this->goodsItems = new Collection();
        $this->shippingFeeWpSummaries=new Collection();
        $this->goodsMergeParam=$goodsMergeParam;
    }

    /**
     * @var Collection<CompanyShippingFeeTemplate> 网点运费模板集合
     */
    public $companyShippingFeeTemplates;




    /**
     * @var Collection<Company> 网点集合
     */
    public $companyCollection;

    public $orderCount=0;
    public $skuKindCount=0;



    public function setCommoditySkus(Collection $commoditySkus): void
    {
        $this->commoditySkus = $commoditySkus;
    }

    public $totalGoodsNum=0;

    public $packageNum=0;

    /**
     * 结算总金额
     * @var string $settlementAmount
     */
    public $settlementAmount="0";

    /**
     * 运费总金额
     * @var string $shippingFeeAmount
     */
    public $shippingFeeAmount="0";

    /**
     * 全部金额（结算金额+运费总金额）
     * @var string $totalAmount
     */
    public $totalAmount="0";


    /**
     * 订单总金额
     * @var string
     */
    public $payAmount="0";
    /**
     *
     * @var Collection<DeliverySkuItem>
     */
    public $goodsItems;

    /**
     * 运单信息
     * @var WaybillOrderItem[] $waybillOrderItems
     */
    protected $waybillOrderItems=[];
    /**
     * @var  Collection<CommoditySku> $commoditySkus
     */
    private  $commoditySkus;


    /**
     * 订单号,用来判断订单是否已经存在
     * @var array
     */
    private $orderSns = [];

    /**
     * 运费汇总
     * @var Collection<ShippingFee> $shippingFeeWpSummaries
     */
    public $shippingFeeWpSummaries;

    /**
     * 合并处理商品
     * @param ReportOrderItem $reportOrderItem
     * @return void
     */
    public function addOrderItem(ReportOrderItem $reportOrderItem)
    {
        $goodsMergeValue=$reportOrderItem->getGoodsMergeValue($this->goodsMergeParam->goodsMergeType);
        $skuMergeValue=$reportOrderItem->getSkuMergeValue($this->goodsMergeParam->skuMergeType);
        //这个case里面goodsMergeType
        if($this->goodsMergeParam->outputFormat==GoodsMergeParam::GOODS_MERGE_TYPE){
            //如果是按商品合并，只有商品相同的前提下的SKU才进行合并，所以把商品的合并值也加到$skuMergeValue中
            $skuMergeValue=$goodsMergeValue.$skuMergeValue;
        }
//        Log::info("addOrderItem",[$reportOrderItem]);
        //通过skuId判断是否已经存在，如果存在就把数量相加
        $goodsItem = $this->goodsItems->get($skuMergeValue);//->where("skuMergeValue", $skuMergeValue)->first();
        if (!$goodsItem) {
            $goodsItem = new DeliverySkuItem();
            $goodsItem->skuId = $reportOrderItem->skuId;
            $goodsItem->skuValue = $reportOrderItem->skuValue;
            $goodsItem->setNumIid($reportOrderItem->numIid);
            $goodsItem->setGoodsTitle($reportOrderItem->goodsTitle);
            $goodsItem->goodsPic = $reportOrderItem->goodsPic;
            $goodsItem->skuMergeValue = $skuMergeValue;
            $goodsItem->goodsPrice = $reportOrderItem->goodsPrice;

            $this->goodsItems->put($skuMergeValue, $goodsItem);
        }
//        Log::info("addOrderItem",[$reportOrderItem]);
        if($reportOrderItem->isCalGoodsNum()){
            $goodsItem->num += $reportOrderItem->num;
            $this->payAmount=round_bcadd($this->payAmount,round_bcmul($reportOrderItem->goodsPrice,$reportOrderItem->num));
            $this->totalGoodsNum+=$reportOrderItem->num;
        }
        //累计订单支付金额，放在这里是因为每个订单的单价可能不一样

        if(!array_key_exists($reportOrderItem->orderSn,$this->orderSns)){
//            Log::info("addOrderItem",[$reportOrderItem->orderSn,$this->orderSns]);
            $this->orderSns[$reportOrderItem->orderSn]=$reportOrderItem->orderSn;
            $this->orderCount+=1;
        }
    }


    /**
     * 汇总运单信息
     * @param string $waybillCode
     * @param int|null $companyId
     * @param string $wpCode
     * @param string $districtCode
     * @param int $goodsNum
     * @param int $unitWeight
     * @return void
     */
    public function handleWaybillOrderItem(string $waybillCode,?int $companyId, string $wpCode, string $districtCode, int $goodsNum, int $unitWeight) {
//        Log::info("handleWaybillOrderItem",[$waybillCode,$companyId,$wpCode,$districtCode,$goodsNum,$unitWeight]);
//        foreach ($this->waybillOrderItems as $existWaybillItem){
//            if($existWaybillItem->wpCode ==$wpCode && $existWaybillItem->districtCode ==$districtCode){
//                $existWaybillItem->num += 1;
//                break;
//            }
//        }
        $waybillItem=new WaybillOrderItem();
        $waybillItem->districtCode=$districtCode;
        $waybillItem->companyId=$companyId;
        $waybillItem->num=$goodsNum;
        $waybillItem->weight=$unitWeight;
        $waybillItem->wpCode=$wpCode;
        $waybillItem->waybillCode=$waybillCode;
        $this->waybillOrderItems[]=$waybillItem;
//        $this->waybillCodes[]=$waybillCode;
    }

    /**
     * 构建报告
     * @return void
     * @throws ErrorCodeException
     */
    public function build()
    {
        //计算价格
        /**
         * @var DeliverySkuItem $goodsItem
         */
        foreach ($this->goodsItems as $goodsItem){
            $skuId=$goodsItem->skuId;
            $commoditySku=$this->commoditySkus->where('platform_sku_out_id',$skuId)->first();
            if($commoditySku&&$commoditySku->settlement_price){
                $goodsItem->settlementPrice=$commoditySku->settlement_price;
                $goodsItem->weight=$commoditySku->weight;
                $goodsItem->totalSettlementPrice=bcmul($goodsItem->settlementPrice,$goodsItem->num,2);
                $this->settlementAmount=round_bcadd($this->settlementAmount,$goodsItem->totalSettlementPrice);
            }
        }
        /**
         * 包裹数量是waybillItems的数量汇总
         *
         */
        $waybillCodes = array_unique(array_column($this->waybillOrderItems, 'waybillCode'));
//        Log::info("waybillCodes",$waybillCodes);
        $this->packageNum=count($waybillCodes);
        /**
         * SKU种类数是goodsItems的数量
         */
        $this->skuKindCount=count($this->goodsItems);

        //计算运费
        //先把每个运单里面的子订单按照运单号分组，然后再计算这个运单的运费


        $waybillOrderItemCollection = new Collection($this->waybillOrderItems);
        Log::info("waybillOrderItemCollection.size=".sizeof($waybillOrderItemCollection));

        $waybillOrderItemsGroupByWaybillCode = $waybillOrderItemCollection->groupBy('waybillCode');
        /**
         * @var Collection<WaybillFee> $waybillFees
         */
        $waybillFees=new Collection();
        /**
         * 汇总WaybillOrderItem 计算一个运单的费用
         * @var string $waybillCode
         * @var Collection $waybillOrderItems
         */
        foreach ($waybillOrderItemsGroupByWaybillCode as $waybillCode => $waybillOrderItems) {
            $waybillFee=new WaybillFee();
            $waybillFee->waybillCode=$waybillCode;
            /**
             * @var  WaybillOrderItem $firstWaybillOrderItem
             */
            $firstWaybillOrderItem = $waybillOrderItems->first();
            $waybillFee->districtCode= $firstWaybillOrderItem->districtCode;
            $companyId = $firstWaybillOrderItem->companyId;
            $company = null;
            if($companyId) {
                /**
                 * @var Company $company
                 */
                $company = $this->companyCollection->where('id', $companyId)->first();
            }else{
                $companyId=0;
            }

            $waybillFee->companyId=$companyId;
            $waybillFee->authSource=$company->auth_source??null;
            $waybillFee->companyDesc=$company?$company->getCompanyAddressDesc():null;
            $waybillFee->wpCode= $firstWaybillOrderItem->wpCode;
            $summaryWeight=array_sum(array_column($waybillOrderItems->toArray(),'weight'));
            //重量为0的时候，按照1kg计算
            if($summaryWeight==0){
                $summaryWeight=1;
            }else{
                //向上取整
                $summaryWeight=ceil($summaryWeight);
            }

            $shippingFee =null;
            if($company) {
                $shippingFee= $this->findMatchedShippingFee($company, $waybillFee->districtCode, $waybillFee->wpCode);
            }
           //如果有匹配到运费配置
            if($shippingFee) {

                //计算运费，首重+续重,首重直接加上，续重按照标准计算
                $waybillFee->fee = $shippingFee->startFee;
                //如果有续重
                if ($summaryWeight > $shippingFee->startStandard) {
                    //如果配置了续重标准、续重金额，按需要的逻辑计算，没有配置续重标准，按照首重计算
                    if ($shippingFee->addStandard && bccomp($shippingFee->addStandard, "0") > 0 && $shippingFee->addFee && bccomp($shippingFee->addFee, "0") > 0) {
                        $waybillFee->fee = round_bcadd($waybillFee->fee, round_bcmul(ceil(($summaryWeight - $shippingFee->startStandard) / $shippingFee->addStandard), $shippingFee->addFee));
                    }
                }
                $waybillFee->districtName=$shippingFee->districtName;
            }else{
                $waybillFee->fee="0";
                $waybillFee->districtName="未配置运费";
            }

            $waybillFees->add($waybillFee);

        }
        Log::info("运费统计",$waybillFees->toArray());


        $waybillFeesGroupByCompanyId = $waybillFees->groupBy('companyId');

        foreach ($waybillFeesGroupByCompanyId as $companyId => $wpCodeWaybillFees) {
            /**
             * @var WaybillFee $firstWaybillCodeFee
             */
            $firstWaybillCodeFee=$wpCodeWaybillFees[0];
            $wpCode=$firstWaybillCodeFee->wpCode;
            Log::info("快递统计",['wpCode'=>$wpCode,'num'=>count($wpCodeWaybillFees)]);
            $shippingFeeWpSummary=new ShippingFeeWpSummary();
            $shippingFeeWpSummary->wpCode=$wpCode;
            $shippingFeeWpSummary->authSource=$firstWaybillCodeFee->authSource;
            $shippingFeeWpSummary->companyDesc=$firstWaybillCodeFee->companyDesc;
            $shippingFeeWpSummary->companyId=$companyId;
            $shippingFeeWpSummary->wpName=ExpressCompanyUtil::findExpressCompanyName($wpCode);;
            $waybillFeesGroupDistrictName = $wpCodeWaybillFees->groupBy('districtName');
            foreach ($waybillFeesGroupDistrictName as $districtName => $districtNameWaybillFees) {
                $shippingFeeDistrictSummary=new ShippingFeeWpItem();
                $shippingFeeDistrictSummary->districtName=$districtName;
                $shippingFeeDistrictSummary->num=count($districtNameWaybillFees);
                $shippingFeeDistrictSummary->amount=array_reduce(array_column($districtNameWaybillFees->toArray(),'fee'),function($carry,$item){
                    return round_bcadd($carry,$item);
                },"0");
                $shippingFeeWpSummary->addShippingFeeWpItem($shippingFeeDistrictSummary);
                $this->shippingFeeAmount=round_bcadd($this->shippingFeeAmount,$shippingFeeDistrictSummary->amount);
            }
            $this->shippingFeeWpSummaries->push($shippingFeeWpSummary);

        }
        $this->totalAmount=round_bcadd($this->settlementAmount,$this->shippingFeeAmount);

    }

    /**
     * 查找匹配的运费模板
     * @param Company $company
     * @param string $districtCode
     * @param string $wpCode
     * @return ShippingFee|null
     */
    public function findMatchedShippingFee(Company  $company,string $districtCode, string $wpCode):?ShippingFee
    {

        /**
         * @var  ShippingFeeTemplate $shippingFeeTemplate
         */
        $shippingFeeTemplate=null;
        $wpCompanyShippingFeeTemplates=$this->companyShippingFeeTemplates->where('wp_code',$wpCode);

//        Log::info("匹配快递公司模板",[$this->companyShippingFeeTemplates,$company]);
        /**
         * @var CompanyShippingFeeTemplate $companyShippingFeeTemplate
         */
        foreach ($wpCompanyShippingFeeTemplates as $companyShippingFeeTemplate) {
            Log::info("匹配快递公司模板",[$companyShippingFeeTemplate,$company]);
            if($companyShippingFeeTemplate->province==$company->province
                && $companyShippingFeeTemplate->city==$company->city
                && $companyShippingFeeTemplate->district==$company->district
                && $companyShippingFeeTemplate->detail==$company->detail
                && $companyShippingFeeTemplate->auth_source==$company->auth_source
            ){
                if($company->branch_code&&$companyShippingFeeTemplate->branch_code!=$company->branch_code){
                    continue;
                }
                if($company->street&&$companyShippingFeeTemplate->street!=$company->street){
                    continue;
                }
                $shippingFeeTemplate= $companyShippingFeeTemplate->shippingFeeTemplate;
                Log::info("匹配到快递公司模板",[$shippingFeeTemplate]);
                break;
            }
            $shippingFeeTemplate= $companyShippingFeeTemplate->shippingFeeTemplate;
        }

        if(!$shippingFeeTemplate){
//            throw_error_code_exception(StatusCode::PARAM_ERROR, StrUtil::format('未找到{wpCode}对应的运费模板',$wpCode));
            return null;
        }
        Log::info("运费模板",[$shippingFeeTemplate]);
        $shippingFee=new  ShippingFee();
        $shippingFee->districtName="全国(不含加收区域)";
        $shippingFee->startFee=$shippingFeeTemplate->start_fee;
        $shippingFee->startStandard=$shippingFeeTemplate->start_standard;
        $shippingFee->addFee=$shippingFeeTemplate->add_fee;
        $shippingFee->addStandard=$shippingFeeTemplate->add_standard;
        if($shippingFeeTemplate->extra_setting){
            $extraSetting=json_decode($shippingFeeTemplate->extra_setting,true);

            if($extraSetting){
//                Log::info("运费加收配置",[$shippingFeeTemplate->extra_setting]);
                $shippingFeeTemplateExtraSettingItems=ObjectUtil::batchMapToObject($extraSetting,ShippingFeeTemplateExtraSettingItem::class);
                /**
                 * @var ShippingFeeTemplateExtraSettingItem $shippingFeeTemplateExtraSettingItem
                 */
                foreach ($shippingFeeTemplateExtraSettingItems as $shippingFeeTemplateExtraSettingItem){
                    if(in_array($districtCode,explode(",",$shippingFeeTemplateExtraSettingItem->defineDistrictCodes) ) ){
                        $shippingFee->districtName=$shippingFeeTemplateExtraSettingItem->customerAreas;
                        $shippingFee->startFee=$shippingFeeTemplateExtraSettingItem->startFee;
                        $shippingFee->startStandard=$shippingFeeTemplateExtraSettingItem->startStandard;
                        $shippingFee->addFee=$shippingFeeTemplateExtraSettingItem->addFee;
                        $shippingFee->addStandard=$shippingFeeTemplateExtraSettingItem->addStandard;
                        break;
                    }
                }
            }

        }
        return $shippingFee;

    }
}
