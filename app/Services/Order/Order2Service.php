<?php

namespace App\Services\Order;

use App\Events\Orders\OrderUpdateEvent;
use App\Models\AbnormalOrder;
use App\Models\Blacklist;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\PtLogistics;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use App\Models\WaybillHistory;
use App\Services\BusinessException;
use App\Services\Order\Request\OrderSearchRequest;
use App\Utils\Environment;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class Order2Service
{
    public function packageManager(array $packages, $shop_id): array
    {
        $res = DB::transaction(function () use ($packages, $shop_id) {
            $allTids = array_merge(...$packages);

            // 校验订单
            $orderList = Order::query()
                ->whereIn('tid', $allTids)
                ->whereIn('shop_id', ShopBind::getAllRelationShopIds($shop_id))
                ->select(['tid'])->get();
            $dbTids = array_column($orderList->toArray(), 'tid');
            $notInDbTids = array_diff($allTids, $dbTids);
            if (count($notInDbTids) > 0) {
                throw new BusinessException(join(',', $notInDbTids) . '不存在或不属于你');
            }
            if ($orderList->pluck('shop_id')->unique()->count()>1){
                throw new BusinessException('不同店铺的订单不能合并！');
            }
            $exeRes = [];
            foreach ($packages as &$tids) {
                $data = [
                    'merge_flag' => 'merge_' . uniqid($shop_id),
                ];
                $res = Order::query()->whereIn('tid', $tids)
                    ->update($data);
                $exeRes[] = [
                    'tids'=> $tids,
                    'res' => $res
                ];
            }
            return $exeRes;
        });

        return $res;
    }
    public function sendEvent($tids, $request, $opType) {
        $columns = ['id', 'tid'];
        $beforeOrders = Order::query()->whereIn('tid', $tids)->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $beforeOrders, $opType))->setClientInfoByRequest($request));
    }

    public function countMergable($data, $shop_id)
    {
        $receiver_phone_idx_arr = array_get($data, 'receiver_phone_idx_arr');
        $order_status = array_get($data, 'order_status');
        $print_status = array_get($data, 'print_status');
        $refund_status = array_get($data, 'refund_status');

        $query = \App\Models\Fix\Order::query();
        $query->whereIn('shop_id',  ShopBind::getAllRelationShopIds($shop_id));
        $query->whereIn('receiver_phone', $receiver_phone_idx_arr);

        if (!empty($order_status)) {
            $query->whereIn('order_status', $order_status);
        }
        if (!empty($print_status)) {
            $query->whereIn('print_status', $print_status);
        }
        if (!empty($refund_status)) {
            $query->whereIn('refund_status', $refund_status);
        }
        $query->selectRaw('count(distinct concat(address_md5, merge_flag)) as count, concat(shop_id,";", receiver_phone) shop_id_receiver_phone');
        $query->groupBy(['shop_id_receiver_phone']);
        $query->having('count', '>', 1);
        $ret = $query->get();
        return $ret;
    }

    public function getMergableInfo(array $data, $shop_id)
    {
        $receiver_phone_idx = array_get($data, 'receiver_phone_idx');
        $order_status = array_get($data, 'order_status');
        $print_status = array_get($data, 'print_status');
        $refund_status = array_get($data, 'refund_status');
        $orderShopId = array_get($data, 'order_shop_id');

        $query = \App\Models\Fix\Order::query();
        $query->whereIn('shop_id',  ShopBind::getAllRelationShopIds($shop_id));
        $query->where('receiver_phone', $receiver_phone_idx);
        $query->where('shop_id', $orderShopId);

        if (!empty($order_status)) {
            $query->whereIn('order_status', $order_status);
        }
        if (!empty($print_status)) {
            $query->whereIn('print_status', $print_status);
        }
        if (!empty($refund_status)) {
            $query->whereIn('refund_status', $refund_status);
        }
        $query->with(['orderItem','orderCipherInfo']);
        $ret = $query->get();
        return $ret;
    }

    /**
     * 云仓还在用
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function shippedList(\Illuminate\Http\Request $request)
    {
        $orderSearchRequest = new OrderSearchRequest();
        $orderSearchRequest->validate($request);
//        is_null($orderSearchRequest->refundStatus) && $orderSearchRequest->refundStatus = 2;
//        $orderSearchRequest->orderStatus = Order::ORDER_STATUS_DELIVERED;
//        $orderQueryBuilder = new OrderQueryBuilder();

        $ownerIdList = $orderSearchRequest->ownerIdList;
        $allShopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id,true);
        $selectShopIds = [];
        $shopIds = [];
        if (!empty($ownerIdList)) {
            $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
            $selectShopIds = $shops->pluck('id')->toArray();
            $shopIds = $selectShopIds;
        }
        if (empty($selectShopIds)) {
            $shopIds = $allShopIds;
        }
//
//        $orderSearchRequest->printMode = 1;
//        $orderSearchRequest->shopIds = $shopIds;
//
//        $orderQuery = $orderQueryBuilder->buildShippedListOrderQuery($orderSearchRequest, [], true);
        $sort = $orderSearchRequest->sort;

        $package = $request->input('package');
        $status = array_get($package,'status');
        $wpCodeList = array_get($package,'wpCodeList'); // 快递公司
        $waybillCodeList = array_get($package,'waybillCodeList'); // 快递单号
        $printStatus = array_get($package,'printStatus'); // 打印状态
        $softRemark = array_get($package,'softRemark'); // 软件备注
        $templateIds = array_get($package,'template_ids'); // 电子面单模板
        $timeField = array_get($package,'timeField'); //
        $beginAt = array_get($package,'beginAt'); //
        $endAt = array_get($package,'endAt'); //
        $batchNoArr = array_get($package,'batchNoArr',[]); //

//        $orderIdArr = $orderQuery->get(['orders.id'])->pluck('id')->toArray();
        $orderIdArr = null;
        if (!empty($orderSearchRequest->tidList)) {
            $tidList = handleOrderIdsWithA($orderSearchRequest->tidList);
            $orderIdArr = Order::query()->whereIn('tid', $tidList)->get(['id','tid'])->pluck('id')->toArray();
        }
        $orderTidArr = [];

        $query = Package::query();
//         已发货状态
        $query->whereIn('packages.status', [Package::ORDER_STATUS_DELIVERED,Package::ORDER_STATUS_PART_DELIVERED]);
        if (!is_null($printStatus)){
            $query->where('packages.print_status', $printStatus);
        }
//        if ($status){
//            $query->where('packages.status', $status);
//        }
        if (!empty($wpCodeList)){
            $query->whereIn('packages.wp_code', $wpCodeList);
        }
        if (!empty($waybillCodeList)){
            $query->whereIn('packages.waybill_code', $waybillCodeList);
        }
        if (!empty($softRemark)){
            $query->where('waybill_histories.soft_remark', $softRemark);
        }
        if (!empty($templateIds)){
            $query->whereIn('packages.template_id', $templateIds);
        }

        if (!empty($beginAt)) {
            $query->where('waybill_histories.'.$timeField,'>=',$beginAt);
        }
        if (!empty($endAt)) {
            $query->where('waybill_histories.'.$timeField,'<=',$endAt);
        }
        if (!empty($batchNoArr)){
            $query->whereIn('packages.batch_no', $batchNoArr);
        }

        $sortArr = explode(',', $sort);
        list($packageList, $count) = $this->getPackageList($query, $allShopIds, $selectShopIds, $orderIdArr,
            $orderSearchRequest->limit, $orderSearchRequest->offset, $sortArr);

        return ['total' => $count, 'list' => $packageList];
    }

    public function abnormalOrderListV2(\Illuminate\Http\Request $request)
    {
        $page = $request->input('page',0);
        $pageSize = $request->input('pageSize',10);
        $status = $request->input('status');
        $tidList = $request->input('tidList');
        $type = $request->input('type');
        $sendStatus = $request->input('sendStatus'); // 30:未发货  40:已经发货
        $ownerIdList = $request->input('ownerIdList', []);
        $beginTime = date('Y-m-d', strtotime('-2 day'));
        $endTime = date('Y-m-d 23:59:59');

//        $ownerIdList = ShopBind::getValidIdentifierByRelation($request->auth->shop_id, $ownerIdList, 1);
        $shops = Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $shopIds = collect($shops)->pluck('id')->toArray();

        $query = AbnormalOrder::query();
        $query->leftJoin('orders','abnormal_order.order_id','=','orders.id');
        $query->whereIn('abnormal_order.shop_id',$shopIds);
        $query->whereBetween('abnormal_order.created_at', [$beginTime, $endTime]);
        if (!is_null($status)){
            $query->where('abnormal_order.status',$status);
        }
        if (!empty($tidList)){
//            str_replace('A','',$tid);
            $tidList = batchAddA($tidList);
            $query->whereIn('orders.tid',$tidList);
        }
        if (!empty($type)){
            $query->where('abnormal_order.type',$type);
        }
//        if (!empty($sendStatus)){
//            if ($sendStatus == 30){
//                $query->whereNull('orders.send_at');
//            }elseif ($sendStatus == 40){
//                $query->whereNotNull('orders.send_at');
//            }
//        }
//        $queryCount= clone $query;
        $query1 = clone $query;
        $query2 = clone $query;
        $query3 = clone $query;
//        $query4 = clone $query;
        $query5 = clone $query;
        $query6 = clone $query;
        $query7 = clone $query;

        $abnormalOrderData = $query
            ->with(['order', 'order.packages', 'order.orderCipherInfo', 'order.orderItem'])
            ->orderBy('abnormal_order.id','desc')
            ->paginate($pageSize, ['abnormal_order.*'], 'page', $page);

        $refundCount = $query1->where('abnormal_order.type', AbnormalOrder::TYPE_OF_ORDER_UNSENT_REFUND)->count();
        $addressCount = $query2->where('abnormal_order.type', AbnormalOrder::TYPE_OF_ADDRESS_UPDATE)->count();
        $remarkCount = $query3->where('abnormal_order.type', AbnormalOrder::TYPE_OF_ORDER_REMARK)->count();
//        $addressWarCount = $query4->where('type', AbnormalOrder::TYPE_OF_ADDRESS_ABNORMAL)->count();
        $orderSendRefundCount = $query5->where('abnormal_order.type', AbnormalOrder::TYPE_OF_ORDER_SEND_REFUND)->count();
        $orderMergeChangeCount = $query6->where('abnormal_order.type', AbnormalOrder::TYPE_OF_ORDER_MERGE_CHANGE)->count();
        $orderPrintFailCount = $query7->where('abnormal_order.type', AbnormalOrder::TYPE_PRINT_FAIL)->count();

        $data = [
            'addressCount' => $addressCount,
            'remarkCount' => $remarkCount,
//            'addressWarCount' => $addressWarCount,
            'orderUnsentRefundCount' => $refundCount,
            'orderSendRefundCount' => $orderSendRefundCount,
            'orderMergeChangeCount' => $orderMergeChangeCount,
            'orderPrintFailCount' => $orderPrintFailCount,
            'rowsFound' => $abnormalOrderData->total(),
        ];
        return ['count' => $data, 'list' => $abnormalOrderData];
    }

    public function splitPackageList($shopId, array $orderIdArr)
    {
        $orderItemArr = OrderItem::query()->whereIn('order_id', $orderIdArr)->limit(100)->get();
        $toShopIds = $orderItemArr->pluck('shop_id')->toArray();
        $shopIds = ShopBind::getAllRelationShopIds($shopId);

        $query = Package::query();
        $query->whereNull('recycled_at'); // 未回收
        $query->where('waybill_status', Package::WAYBILL_STATUS_SUCCESS);

        list($splitPackageList,) = $this->getPackageListV2($query, $shopIds, $toShopIds, $orderIdArr, 20, 0);
        $splitList = [];
        foreach ($splitPackageList as $index => $item) {
            $tmp = $item['waybill_history']['order'];
            $tmp['order_item'] = $item['package_order_items'];
            $tmp['order_cipher_info'] = $item['waybill_history']['order_cipher_info'];
            $tmp['package_all'] = [$item];
            $splitList[] = $tmp;
        }
        $splitOrderItemCollect = collect($splitPackageList)->pluck('package_order_items')->collapse();
        $unSplit = [];
        foreach ($orderItemArr as $orderItem) {
            $temp = $orderItem;
            $temp['unSplitNum'] = $orderItem['goods_num'] - $splitOrderItemCollect->where('id', $orderItem['id'])->sum('package_num');
            if (empty($temp['unSplitNum'])){
                continue;
            }
            $unSplit[] = $temp;
        }
        return ['splitList' => $splitList, 'unSplit' => $unSplit];
    }

    /**
     * @param array $shopIds
     * @param $orderIdArr
     * @param $limit
     * @param $offset
     * @return array
     */
    public function getPackageList(Builder $query, array $shopIds, array $toShopIds, $orderIdArr, $limit, $offset,
                                   $sort = ['id' ,'desc'],$isPreShipment = false): array
    {
        $orderFields = [
            'id',
            'tid',
            'shop_id',
            'receiver_state',
            'receiver_city',
            'receiver_district',
            'receiver_town',
            'receiver_name',
            'receiver_phone',
            'receiver_address',
//            'print_status',
//            'printed_at',
        ];
        $waybillHistoriesFields = [
            'id',
            'order_id',
            'package_id',
            'template_id',
            'waybill_code',
            'wp_code',
            'waybill_status',
            'version',
            'batch_no',
            'waybill_index',
            'waybill_count',
            'created_at',
//            'print_data',
            'soft_remark',
        ];

        $query->where('packages.waybill_status', Package::WAYBILL_STATUS_SUCCESS);
        $query->whereIn('packages.shop_id', $shopIds);
//        $query->where('package_orders.source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY);
        $with = [
            'waybillHistory:' . implode(',', $waybillHistoriesFields),
            'waybillHistory.order:' . implode(',', $orderFields),
            'waybillHistory.orderCipherInfo', 'packageOrders', 'packageOrderItems', 'packageOrderItems.order'];
        if ($isPreShipment){
            $with[] = 'orderTrace';
        }
        $query->with($with);
        $query->leftJoin('waybill_histories','waybill_histories.package_id','=','packages.id');
        if (!empty($toShopIds)) {
            $query->whereIn('waybill_histories.to_shop_id', $toShopIds);
//            $query->whereExists(function ($query) use ($toShopIds) {
//                $query->select(DB::raw(1))
//                    ->from('waybill_histories')
//                    ->whereRaw('waybill_histories.package_id = packages.id')
//                    ->whereIn('to_shop_id', $toShopIds);
//            });
        }
        if (!is_null($orderIdArr)) {
            $query->whereExists(function ($query) use ($orderIdArr) {
                $query->select(DB::raw(1))
                    ->from('package_orders')
                    ->whereRaw('package_orders.package_id=  packages.id')
                    ->whereIn('order_id', $orderIdArr);
            });
        }

        $selectArr = [
            // 云仓在用
//            DB::raw('COUNT(distinct orders.id) as merge_orders_num'),
//            DB::raw('SUM(package_orders.num) as merge_goods_total'),
        ];
        $query->groupBy(['packages.id']);
        $query->select(array_merge(['packages.id'], $selectArr));
        $count = DB::query()->fromSub($query->getQuery(),'t')->count();
//        $count = $query->count();
        $packageList = $query
            ->orderBy($sort[0], $sort[1])
            ->orderBy('id','desc')
            ->select(array_merge(['packages.*'], $selectArr))
            ->limit($limit)
            ->offset($offset)
            ->get();

        $packageList = array_map(function ($item) {
            $package_orders = collect($item['package_orders'])->keyBy('order_item_id')->toArray();
            $item['package_order_items'] = collect($item['package_order_items'])->unique('id')->values()->map(function ($item) use ($package_orders) {
                $num = $package_orders[$item['id']]['num'];
                $item['package_num'] = $num;
                return $item;
            })->toArray();
            return $item;
        }, $packageList->toArray());
        return array($packageList, $count);
    }

    /**
     * @param array $shopIds
     * @param $orderIdArr
     * @param $limit
     * @param $offset
     * @return array
     */
    public function getPackageListV2($query, $limit, $offset, $sort = ['id' ,'desc'],$isPreShipment = false): array
    {

        $selectArr = [
            DB::raw('COUNT(distinct orders.id) as merge_orders_num'),
            DB::raw('SUM(package_orders.num) as merge_goods_total'),
        ];
        $query->select(array_merge(['packages.id'], $selectArr));
        $query->where('packages.version', 3);
        $count = DB::query()->fromSub($query,'t')->count();
//        $count = $query->count();
        $packageList = $query
            ->orderBy($sort[0], $sort[1])
            ->orderBy('id','desc')
            ->select(array_merge(['packages.*'], $selectArr))
            ->limit($limit)
            ->offset($offset)
            ->get();

        $packageList = array_map(function ($item) {
            $package_orders = collect($item['package_orders'])->keyBy('order_item_id')->toArray();
            $item['package_order_items'] = collect($item['package_order_items'])->unique('id')->values()->map(function ($item) use ($package_orders) {
                $num = $package_orders[$item['id']]['num']??0;
                $item['package_num'] = $num;
                return $item;
            })->toArray();
            return $item;
        }, $packageList->toArray());
        return array($packageList, $count);
    }

    public function authorList(array $data)
    {
        $ownerIdList = $data['ownerIdList'];
        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $shopIds = $shops->pluck('id')->toArray();
        $collection = \App\Models\Fix\Order::query()
            ->leftJoin('order_items', 'orders.id', '=', 'order_items.order_id')
            ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])
            ->whereIn('orders.shop_id', $shopIds)
            ->where('order_items.author_id', '!=', '0')
            ->groupBy(['order_items.author_id'])
            ->get([DB::raw('order_items.author_id,order_items.author_name')]);
        return $collection;
    }

    /**
     * 直播打印订单列表
     * @param array $data
     * @param $shop_id
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public function livePrintOrderList(array $data,$shop_id)
    {
        $timeField = array_get($data, 'timeField');
        $startTime = array_get($data, 'startTime');
        $endTime = array_get($data, 'endTime');
        $isPrint = array_get($data, 'isPrint',0);
        $isAuto = array_get($data, 'isAuto',0);
        $page = array_get($data, 'page',1);
        $pageSize = array_get($data, 'pageSize',20);
        $tidArr = array_get($data, 'tidList');
//        $status = array_get($data, 'status');
        $statusArr = array_get($data, 'statusArr');
        $ownerIdList = array_get($data, 'ownerIdList');
        $goodsTitle = array_get($data, 'goodsTitle');
        $buyerNick = array_get($data, 'buyerNick');
        $authorIdList = array_get($data, 'authorIdList');
        $authorName = array_get($data, 'authorName');
        $refundStatus = array_get($data, 'refundStatus');
        $blacklistStatus = array_get($data, 'blacklistStatus');

        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $shopIds = $shops->pluck('id')->toArray();

        if ($timeField == 'print_tag_at'){
            $timeField = 'order_items.' . $timeField;
        }else{
            $timeField = 'orders.' . $timeField;
        }

        $query = \App\Models\Fix\OrderItem::query();
        $query->leftJoin('orders', 'orders.id', '=', 'order_items.order_id');
        $query->with(['order', 'order.orderCipherInfo','order.blacklist']);
        $query->whereIn('order_items.shop_id', $shopIds);

        if ($isAuto){
            $query->where(function (Builder $query) {
                $query->where(function (Builder $query) {
                    $query->whereNull('order_items.print_tag_at');
                    $query->where('order_items.status',OrderItem::ORDER_STATUS_PAYMENT);
                });
                $query->orWhere('order_items.status',OrderItem::ORDER_STATUS_CLOSE);
            });
        }else{
            !empty($statusArr) && $query->whereIn('order_items.status', $statusArr);
            if (!is_null($isPrint)){
                if ($isPrint){
                    $query->whereNotNull('order_items.print_tag_at');
                }else{
                    $query->whereNull('order_items.print_tag_at');
                }
            }
        }


        $query->where($timeField, '>=', $startTime);
        $query->where($timeField, '<=', $endTime);
        $query->orderBy('orders.pay_at','desc');
        $newTidArr = handleOrderIdsWithA($tidArr);
        if (!empty($newTidArr)){
            $query->where(function ($query) use ($newTidArr, $tidArr) {
                $query->whereIn('order_items.tid', $newTidArr);
                $query->orWhereIn('order_items.oid', $tidArr);
            });
        }
        !empty($goodsTitle) && $query->where('order_items.goods_title', 'like', "%$goodsTitle%");
//        !empty($buyerNick) && $query->where('orders.buyer_nick', 'like', "%$buyerNick%");
        !empty($authorIdList) && $query->whereIn('order_items.author_id', $authorIdList);
        !empty($authorName) && $query->where('order_items.author_name', 'like', "%$authorName%");
        if (!is_null($refundStatus)){
            $query->where('order_items.refund_status', $refundStatus);
        }
        if (!is_null($blacklistStatus)){
            $func = function ($query) {
                $query->select(DB::raw(1))
                    ->from('blacklists')
                    ->whereRaw('blacklists.shop_id = order_items.shop_id')
                    ->whereRaw('blacklists.type = '.Blacklist::TYPE_BUYER_ID)
                    ->whereRaw('blacklists.identifier = orders.buyer_id')
                    ->whereNull('blacklists.deleted_at');
            };
            if ($blacklistStatus){
                $query->whereExists($func);
            }else{
                $query->whereNotExists($func);
            }
        }


        return $query->paginate($pageSize, ['order_items.*'], 'page', $page);
    }

    public function shippedListV2(\Illuminate\Http\Request $request, $isPreShipment = false): array
    {
        // 防止内存溢出
        ini_set('memory_limit', '1024M');

        $deliveryType = $request->input('delivery_type');
        list($orderSearchRequest, $allShopIds, $selectShopIds, $query, $orderIdArr) = $this->handleShippedListV2Request($request, $isPreShipment);
        $this->buildPackageListV2($query, $allShopIds, $selectShopIds, $orderIdArr, $isPreShipment);
        if (!empty($deliveryType)) {
            $this->buildDeliveryTypeQuery($query, $deliveryType);
        }

        list($sortColumn, $sortDirection) = explode(',', $orderSearchRequest->sort);
        switch ($sortColumn) {
            case 'printed_time':
                $sortColumn = 'packages.print_at';
                break;
            case 'shipping_time':
                $sortColumn = 'packages.send_at';
                break;
            case 'pre_shipment_at':
                $sortColumn = 'packages.pre_shipment_at';
                break;
        }
        $sortArr = [$sortColumn, $sortDirection];
        list($packageList, $count) = $this->getPackageListV2($query, $orderSearchRequest->limit, $orderSearchRequest->offset, $sortArr,$isPreShipment);

        return ['total' => $count, 'list' => $packageList];
    }
    public function shippedListV2Count(\Illuminate\Http\Request $request,$isPreShipment = false)
    {
        list($orderSearchRequest, $allShopIds, $selectShopIds, $query, $orderIdArr) = $this->handleShippedListV2Request($request, $isPreShipment);
        $this->buildPackageListV2($query, $allShopIds, $selectShopIds, $orderIdArr, $isPreShipment);
        $total = DB::query()->fromSub($query,'t')->count();
        // 统计各个发货类型
        $delivery_type_count = [];
        $arr = Package::DELIVERY_TYPE_ARRAY;
        foreach ($arr as $value) {
            $tempQuery = clone $query;
            $this->buildDeliveryTypeQuery($tempQuery, $value);
            $delivery_type_count[] = [
                'delivery_type' => $value,
                'count' => DB::query()->fromSub($tempQuery,'t')->count(),
            ];
        }
        return ['total' => $total, 'delivery_type_count' => $delivery_type_count];
    }

    public function resendList(\Illuminate\Http\Request $request, $validate)
    {
        $ptLogisticsQuery = $request->input('pt_logistics');
        $deliveryType = array_get($ptLogisticsQuery, 'delivery_type'); // 发货类型

        $limit = array_get($validate, 'limit', 100);
        $offset = array_get($validate, 'offset', 0);
        list($sortColumn, $sortDirection) = explode(',', $request->input('sort'));
        switch ($sortColumn) {
            case 'printed_time':
                $sortColumn = 'packages.print_at';
                break;
            case 'shipping_time':
                $sortColumn = 'pt_logistics.delivery_at';
                break;
        }
        $query = $this->buildResendList($validate, $request);

        if (!empty($deliveryType)) {
            $this->buildDeliveryTypeQuery($query,$deliveryType);
        }
        $rows_found = DB::query()->fromSub($query,'t')->count();

        $query->orderBy($sortColumn, $sortDirection);
        Log::info('resendList', ['query' =>getSqlByQuery($query) ]);
        $idArr = $query->offset($offset)->limit($limit)->get()->pluck('id')->toArray();

        $query2 = PtLogistics::query()
            ->with(['mergePtLogisticsItems.orderItem.order.shop:id,shop_name','order.shop:id,shop_name',
                'orderCipherInfo', 'package', 'subWaybillCodes','splitSubPtLogistics.ptLogisticsItems.orderItem.order.shop:id,shop_name',
                'newPackages' => function ($query) {
                    $query->where(DB::raw('IFNULL(packages.status, 0)'), '!=', Package::ORDER_STATUS_DELIVERED);
                }])
            ->leftJoin('packages', 'packages.id', '=', 'pt_logistics.package_id')
            ->limit($limit)
            ->whereIn('pt_logistics.id', $idArr);

        $query2->orderBy($sortColumn, $sortDirection);

        $pagination = [
            'rows_found' => $rows_found,
            'offset' => $offset,
            'limit' => $limit,
            'delivery_type_arr' => [],
        ];
//        $query2->orderBy('pt_logistics.id', 'desc');
        $list = $query2->select('pt_logistics.*')->get();

        $list = array_map(function ($item) {
            if (empty($item['delivery_at'])){
                // 抖音追加的包裹没有发货时间
                $item['delivery_at'] = $item['package']['send_at'];
            }
            foreach ($item['split_sub_pt_logistics'] as $index => $splitSubPtLogistic) {
                $item['merge_pt_logistics_items'] = array_merge($item['merge_pt_logistics_items'], $splitSubPtLogistic['pt_logistics_items']);
            }
            unset($item['split_sub_pt_logistics']);
            $merge_orders = [];
            foreach ($item['merge_pt_logistics_items'] as $index => $merge_pt_logistics_item) {
                $order = $merge_pt_logistics_item['order_item']['order']??null;
                if (!empty($order)) {
                    if ($order['id'] != $item['order']['id']){ // 只加非当前订单
                        $merge_orders[] = array_only($order,['id','tid','order_status','seller_flag','seller_memo','buyer_message','order_created_at','pay_at','shop']);
                    }
                    $item['merge_pt_logistics_items'][$index]['order_item']['order'] = null;
                    $item['merge_pt_logistics_items'][$index]['order'] = array_only($order,['id','order_status']);
                }
            }
            $item['merge_pt_logistics_items'] = collect($item['merge_pt_logistics_items'])->unique('id')->values()->toArray();
            $item['merge_orders'] = collect($merge_orders)->unique('id')->values()->toArray();
            return $item;
        },$list->toArray());
        if ($sortDirection == 'asc'){
            $sort_order = SORT_ASC;
        }else{
            $sort_order = SORT_DESC;
        }
        // 使用 array_multisort 根据 $idArr 的顺序 对 $list 进行排序
//        array_multisort(array_column($list, 'id'), $sort_order,SORT_REGULAR, $list);

        return [$pagination, $list];
    }
    public function resendListCount(\Illuminate\Http\Request $request, array $validate)
    {
        $query = $this->buildResendList($validate, $request);

        $total = DB::query()->fromSub($query,'t')->count();

        // 统计各个发货类型
        $delivery_type_count = [];
        $arr = array_merge(Package::DELIVERY_TYPE_ARRAY, [Package::DELIVERY_TYPE_EXTERNAL]); // 88 外部发货
        foreach ($arr as $value) {
            $tempQuery = clone $query;
            $this->buildDeliveryTypeQuery($tempQuery, $value);
            $delivery_type_count[] = [
                'delivery_type' => $value,
                'count' => DB::query()->fromSub($tempQuery,'t')->count(),
            ];
        }

        return ['total' => $total, 'delivery_type_count' => $delivery_type_count];
    }

    public function preShipmentList(\Illuminate\Http\Request $request): array
    {
        return $this->shippedListV2($request, true);
    }


    /**
     * 构建包裹的query
     * @param Builder $query
     * @param array|null $package
     * @param bool $isPreShipment
     * @return Builder
     */
    private function buildPackageQuery(Builder $query, ?array $package, $isPreShipment = false): Builder
    {
        $status = array_get($package, 'status');
        $wpCodeList = array_get($package, 'wpCodeList'); // 快递公司
        $waybillCodeList = array_get($package, 'waybillCodeList'); // 快递单号
        $printStatus = array_get($package, 'printStatus'); // 打印状态
        $softRemark = array_get($package, 'softRemark'); // 软件备注
        $templateIds = array_get($package, 'template_ids'); // 电子面单模板
        $timeField = array_get($package, 'timeField'); //
        $beginAt = array_get($package, 'begin_at'); //
        $endAt = array_get($package, 'end_at'); //
        $batchNoArr = array_get($package, 'batchNoArr', []); //
        $orderTotalFee = array_get($package, 'orderTotalFee'); // 订单金额
        $goodsNum = array_get($package, 'goodsNum'); // 商品数量
//        $isPreShipment = array_get($package, 'isPreShipment ', 0); //

//         已发货状态
        if (!$isPreShipment) {
            // todo packages.status 有 bug  status 没写进去状态
//            $query->whereIn('packages.status', [Package::ORDER_STATUS_DELIVERED, Package::ORDER_STATUS_PART_DELIVERED]);
            $query->whereIn('orders.order_status', Order::ORDER_STATUS_DELIVERED_ARRAY);
            $query->whereIn('package_orders.source_type', [Package::SOURCE_TYPE_INTERNAL_DELIVERY, Package::SOURCE_TYPE_PRINT]);
        }else{
            //如果是预发货，订单的状态要限制成待发货或者部分发货
            $query->whereIn('orders.order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED]);
//            $query->where(function ($query){
//                $query->where('packages.status', 0);
//                $query->orWhereNull('packages.status');
//            });
        }

        // 用户不希望看到被更换的单号
//        $query->where('packages.delivery_type','!=', Package::DELIVERY_TYPE_BE_CHANGED);

        if (!is_null($printStatus)) {
            $query->where('packages.print_status', $printStatus);
        }
//        if ($status){
//            $query->where('packages.status', $status);
//        }
        if (!empty($wpCodeList)) {
            $query->whereIn('packages.wp_code', $wpCodeList);
        }
        if (!empty($waybillCodeList)) {
            $query->where(function ($query) use ($waybillCodeList){
                $packageList = Package::query()->whereIn('waybill_code', $waybillCodeList)->get(['id','waybill_wp_index','multi_package_main_waybill_code']);
                $multiPackageMainWaybillCodeArr = $packageList->pluck('multi_package_main_waybill_code')->toArray();
                $multiPackageList = Package::query()->whereIn('waybill_code', $multiPackageMainWaybillCodeArr)->get(['id']); // 一单多包
                $waybillWpIndexArr = $packageList->pluck('waybill_wp_index')->toArray();
                $packageIdArr = $packageList->pluck('id')->toArray();
                $packageIdArr = array_merge($packageIdArr, $multiPackageList->pluck('id')->toArray());
//                $query->whereIn('packages.waybill_code', $waybillCodeList);
//                $query->orWhereIn('pt_logistics.waybill_wp_index', $waybillWpIndexArr);
                $query->orWhereIn('packages.id', $packageIdArr);
            });
        }

        if (!empty($templateIds)) {
            $query->whereIn('packages.template_id', $templateIds);
        }

        if (!empty($batchNoArr)) {
            $query->whereIn('packages.batch_no', $batchNoArr);
        }

        if (!empty($softRemark)) {
            $query->where('waybill_histories.soft_remark', $softRemark);
        }
        if (!empty($beginAt)) {
            $query->where('packages.' . $timeField, '>=', $beginAt);
        }
        if (!empty($endAt)) {
            $query->where('packages.' . $timeField, '<=', $endAt);
        }
        if (!empty($orderTotalFee)) {
            $range = explode('-', $orderTotalFee);
            $query->whereBetween('orders.total_fee', $range);
        }
        if (!empty($goodsNum)) {
            $range = explode('-', $goodsNum);
//            $query->whereBetween('merge_goods_total', $range);
            $query->having('merge_goods_total', '>=', $range[0]);
            $query->having('merge_goods_total', '<=', $range[1]);
        }
        if ($isPreShipment) { // 预发货
            $query->whereIn('packages.pre_shipment_status', [Package::PRE_SHIPMENT_STATUS_YES, Package::PRE_SHIPMENT_STATUS_PAUSE, Package::PRE_SHIPMENT_STATUS_FINISHED]);
            $query->where('package_orders.source_type',Package::SOURCE_TYPE_INTERNAL_DELIVERY);
        }else{
            $query->whereNotIn('packages.pre_shipment_status',[Package::PRE_SHIPMENT_STATUS_YES ]);
        }
        return $query;
    }

    /**
     * @param $validate
     * @param \Illuminate\Http\Request $request
     * @return array|Builder|\Illuminate\Database\Query\Builder|\LaravelIdea\Helper\App\Models\_IH_PtLogistics_QB
     * @throws \App\Exceptions\ApiException
     */
    public function buildResendList($validate, \Illuminate\Http\Request $request)
    {

        $ownerIdList = array_get($validate, 'ownerIdList');
        $shopIds = ShopBind::getValidRelationShopIds($request->auth->shop_id, $ownerIdList);

        $orderSearchRequest = new OrderSearchRequest();
        $orderSearchRequest->validate($request);
        $orderQueryBuilder = new OrderQueryBuilder();
        $orderSearchRequest->shopIds = $shopIds;
        $query = PtLogistics::query()
            ->select([
                DB::raw('min(pt_logistics.id) as id'),
                'pt_logistics.waybill_wp_index',
                DB::raw('COUNT(distinct orders.id) as merge_orders_num'),
                DB::raw('sum(pt_logistics_items.num) as sum_num'),
            ])
//            ->with(['ptLogisticsItems.orderItem.order', 'orderCipherInfo', 'package','newPackages'])
            ->leftJoin('pt_logistics_items', 'pt_logistics_items.pt_logistics_id', '=', 'pt_logistics.id')
            ->leftJoin('orders', 'orders.id', '=', 'pt_logistics.order_id')
            ->leftJoin('order_items', 'order_items.id', '=', 'pt_logistics_items.order_item_id')
            ->leftJoin('packages', 'packages.id', '=', 'pt_logistics.package_id')
            ->whereIn('pt_logistics.shop_id', $shopIds);
        $orderQueryBuilder->buildShippedListV2OrderQuery($query, $orderSearchRequest, [], false);
        $query->whereIn('orders.order_status', Order::ORDER_STATUS_DELIVERED_ARRAY);
        $query->where(function (Builder $query) {
            $query->whereIn('packages.send_waybill_type', [
                Package::SEND_WAYBILL_TYPE_NORMAL,
                Package::SEND_WAYBILL_TYPE_MULTI_MAIN,
                Package::SEND_WAYBILL_TYPE_MULTI_SUB,
                Package::SEND_WAYBILL_TYPE_SPILT_MAIN,
//            Package::SEND_WAYBILL_TYPE_SPILT_SUB,
//            Package::SEND_WAYBILL_TYPE_EMPTY,
            ]);
            $query->orWhere('pt_logistics.package_id','=',0);
        });

        $query->groupBy(['pt_logistics.waybill_wp_index']);
//        $query->orderBy('pt_logistics.id', 'desc');

        $ptLogisticsQuery = $request->input('pt_logistics');
        $wpCodeList = array_get($ptLogisticsQuery, 'wpCodeList'); // 原快递公司
        $waybillCodeList = array_get($ptLogisticsQuery, 'waybillCodeList'); // 原快递号
        $newWaybillCodeList = array_get($ptLogisticsQuery, 'newWaybillCodeList'); // 新快递号
        $orderTotalFee = array_get($ptLogisticsQuery, 'orderTotalFee'); // 订单金额
        $goodsNum = array_get($ptLogisticsQuery, 'goodsNum'); // 商品数量
        $timeField = array_get($ptLogisticsQuery, 'timeField'); //
        $beginAt = array_get($ptLogisticsQuery, 'begin_at'); //
        $endAt = array_get($ptLogisticsQuery, 'end_at'); //

        if (!empty($wpCodeList)) {
            $query->whereIn('pt_logistics.wp_code', $wpCodeList);
        }
        if (!empty($waybillCodeList)) {
            $query->where(function ($query) use ($waybillCodeList){
                $packageList = Package::query()->whereIn('waybill_code', $waybillCodeList)->get(['id','waybill_wp_index','multi_package_main_waybill_code']);
                $multiPackageMainWaybillCodeArr = $packageList->pluck('multi_package_main_waybill_code')->toArray();
                $multiPackageList = Package::query()->whereIn('waybill_code', $multiPackageMainWaybillCodeArr)->get(['id']); // 一单多包
                $waybillWpIndexArr = $packageList->pluck('waybill_wp_index')->filter()->toArray();
                $packageIdArr = $packageList->pluck('id')->toArray();
                $packageIdArr = array_merge($packageIdArr, $multiPackageList->pluck('id')->toArray());
                // 查找是否有删除的运单号，作为补充
                $thisPtLogistics = PtLogistics::query()->whereIn('waybill_code', $waybillCodeList)->withTrashed()->first();
                $query->whereIn('pt_logistics.waybill_code', $waybillCodeList);
                $query->orWhereIn('pt_logistics.waybill_wp_index', $waybillWpIndexArr);
                $query->orWhereIn('packages.id', $packageIdArr);
                if (!empty($thisPtLogistics->deleted_at)){ // 用于查询被外部变更单号的原单号 查询不到的情况
                    $query->orWhere('pt_logistics.order_id', $thisPtLogistics->order_id);
                }
            });
        }
        if (!empty($newWaybillCodeList)) {
            $query->whereHas('newPackages', function ($query) use ($newWaybillCodeList) {
                $query->whereIn('waybill_code', $newWaybillCodeList);
            });
        }
        if (!empty($orderTotalFee)) {
            $range = explode('-', $orderTotalFee);
            $query->whereBetween('orders.total_fee', $range);
        }
        if (!empty($goodsNum)) {
            $range = explode('-', $goodsNum);
            $query->having('sum_num', '>=', $range[0]);
            $query->having('sum_num', '<=', $range[1]);
//            $query->whereBetween('pt_logistics_items.num', $range);
        }

        switch ($timeField) {
            case 'send_at':
                $timeField = 'pt_logistics.delivery_at';
                break;
            default:
                $timeField = 'packages.' . $timeField;
                break;
        }
        if (!empty($beginAt)) {
            $query->where($timeField, '>=', $beginAt);
        }
        if (!empty($endAt)) {
            $query->where($timeField, '<=', $endAt);
        }

        return $query;
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param $isPreShipment
     * @return array
     */
    public function handleShippedListV2Request(\Illuminate\Http\Request $request, $isPreShipment): array
    {
        $orderSearchRequest = new OrderSearchRequest();
        $orderSearchRequest->validate($request);
//        is_null($orderSearchRequest->refundStatus) && $orderSearchRequest->refundStatus = 2;
//        $orderSearchRequest->orderStatus = Order::ORDER_STATUS_DELIVERED;
        if ($isPreShipment) {
            $orderSearchRequest->isPreShipmentList = 1;
        }
        $orderQueryBuilder = new OrderQueryBuilder();

        $ownerIdList = $orderSearchRequest->ownerIdList;
        $allShopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id, true);
        $selectShopIds = [];
        $shopIds = [];
        if (!empty($ownerIdList)) {
            $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
            $selectShopIds = $shops->pluck('id')->toArray();
            $shopIds = $selectShopIds;
        }
        if (empty($selectShopIds)) {
            $shopIds = $allShopIds;
        }
//
//        $orderSearchRequest->printMode = 1;
        $orderSearchRequest->shopIds = $shopIds;
        $orderSearchRequest->displayMerge = true;
        $query = Package::query();
//        $query->where('packages.source_type', Package::TYPE_SEND);
        $query->leftJoin('package_orders', 'package_orders.package_id', '=', 'packages.id');
        $query->leftJoin('orders', 'orders.id', '=', 'package_orders.order_id');
        $query->leftJoin('order_items', 'order_items.id', '=', 'package_orders.order_item_id');
        $orderQueryBuilder->buildShippedListV2OrderQuery($query, $orderSearchRequest, [], false);

        $package = $request->input('package');
        $this->buildPackageQuery($query, $package, $isPreShipment);
        $query->whereIn('packages.send_waybill_type', [Package::SEND_WAYBILL_TYPE_NORMAL,
            Package::SEND_WAYBILL_TYPE_MULTI_MAIN, Package::SEND_WAYBILL_TYPE_SPILT_MAIN, Package::SEND_WAYBILL_TYPE_SPILT_SUB]);

        $orderIdArr = null;
        if (!empty($orderSearchRequest->tidList)) {
            $tidList = handleOrderIdsWithA($orderSearchRequest->tidList);
            $orderIdArr2 = Order::query()->whereIn('tid', $tidList)->get(['id', 'tid'])->pluck('id')->toArray();
            $orderIdArr = $orderIdArr2;
        }

        return array($orderSearchRequest, $allShopIds, $selectShopIds, $query, $orderIdArr);
    }

    /**
     * @param Builder $query
     * @param array $shopIds
     * @param $isPreShipment
     * @param array $toShopIds
     * @param $orderIdArr
     * @return Builder
     */
    public function buildPackageListV2(Builder $query, array $shopIds, array $toShopIds, $orderIdArr,$isPreShipment): Builder
    {
        $orderFields = [
            'id',
            'tid',
            'shop_id',
            'receiver_state',
            'receiver_city',
            'receiver_district',
            'receiver_town',
            'receiver_name',
            'receiver_phone',
            'receiver_address',
//            'print_status',
//            'printed_at',
        ];
        $waybillHistoriesFields = [
            'id',
            'order_id',
            'package_id',
            'template_id',
            'waybill_code',
            'wp_code',
            'waybill_status',
            'version',
            'batch_no',
            'waybill_index',
            'waybill_count',
            'created_at',
//            'print_data',
            'soft_remark',
        ];

        $query->where('packages.waybill_status', Package::WAYBILL_STATUS_SUCCESS);
        $query->whereIn('packages.shop_id', $shopIds);
//        $query->where('package_orders.source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY);
        $with = [
            'waybillHistory:' . implode(',', $waybillHistoriesFields),
            'waybillHistory.order:' . implode(',', $orderFields),
            'waybillHistory.orderCipherInfo',
            'packageOrders' => function ($query) {
                $query->where('package_orders.source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY);
            },
            'packageOrderItems' => function ($query) {
                $query->where('package_orders.source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY);
            }, 'packageOrderItems.order', 'subMultiPackages'];
        if ($isPreShipment) {
            $with[] = 'orderTrace';
        }
        $query->with($with);
        $query->leftJoin('waybill_histories', 'waybill_histories.package_id', '=', 'packages.id');
        if (!empty($toShopIds)) {
            $query->whereIn('waybill_histories.to_shop_id', $toShopIds);
//            $query->whereExists(function ($query) use ($toShopIds) {
//                $query->select(DB::raw(1))
//                    ->from('waybill_histories')
//                    ->whereRaw('waybill_histories.package_id = packages.id')
//                    ->whereIn('to_shop_id', $toShopIds);
//            });
        }
        if (!is_null($orderIdArr)) {
            $query->whereExists(function ($query) use ($orderIdArr) {
                $query->select(DB::raw(1))
                    ->from('package_orders')
                    ->whereRaw('package_orders.package_id=  packages.id')
                    ->whereIn('order_id', $orderIdArr);
            });
        }


        $query->select('packages.*');
        $query->groupBy(['packages.id']);
        return $query;
    }

    /**
     * @param $deliveryType
     * @param $query
     * @return void
     */
    public function buildDeliveryTypeQuery( $query,$deliveryType): void
    {
        if ($deliveryType == Package::DELIVERY_TYPE_EXTERNAL) { // 外部发货
            $query->where('pt_logistics.source_type', '=', PtLogistics::SOURCE_TYPE_OUTSIDE);
//            $query->where('pt_logistics.package_id', '=', 0);
        } elseif ($deliveryType == Package::DELIVERY_TYPE_FIRST) { // 需要兼容一下老的数据
            $query->whereIn('packages.delivery_type', [Package::DELIVERY_TYPE_FIRST]);
        } else {
            $query->where('packages.delivery_type', $deliveryType);
        }
    }


}
