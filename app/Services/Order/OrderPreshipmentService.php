<?php

namespace App\Services\Order;

use App\Constants\PlatformConst;
use App\Events\Orders\OrderPreshipmentCancelEvent;
use App\Events\Orders\OrderPreshipmentJoinEvent;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\Fix\Shop;
use App\Models\PackageOrder;
use App\Models\UserExtra;
use App\Models\WaybillHistory;
use App\Services\Order\Request\DeliveryOrderItem;
use App\Services\Order\Request\PreshipmentCancelRequest;
use App\Services\Order\Request\PreshipmentDeliveryRequest;
use App\Services\Order\Request\PreshipmentWaybillItem;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderTraceList;
use App\Models\Package;
use App\Utils\DateTimeUtil;
use App\Utils\ShopUtil;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

/**
 * 订单预发货处理
 */
class OrderPreshipmentService
{


    /**
     * 取消预发货
     * @param PreshipmentCancelRequest $preshipmentCancelRequest
     * @return void
     */
    public function cancelPreshipment(PreshipmentCancelRequest $preshipmentCancelRequest)
    {
        Log::info("取消预发货", ["preshipmentCancelRequest" => $preshipmentCancelRequest]);


        //修改包裹的预发状态
        Package::query()->whereIn("waybill_code", $preshipmentCancelRequest->getWaybillCodes())->where('pre_shipment_status', Package::PRE_SHIPMENT_STATUS_YES)->update([
            'status' => Package::ORDER_STATUS_PAYMENT,
            'pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_NO,
            'pre_shipment_at' => null,
        ]);

        //把包裹里面的packageOrder找出来，去修改对应的OrderItem里面的预发货的数量
        $packageList = Package::query()->whereIn("waybill_code", $preshipmentCancelRequest->getWaybillCodes())->get();
        $packageList->each(function (Package $package) {
            $package->packageOrders()->where('source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY)->each(function (PackageOrder $packageOrder) {
                $packageOrder->orderItem()->decrement('pre_send_num', $packageOrder->num);
            });
        });


        $orderIds = $preshipmentCancelRequest->getOrderIds();
        Order::query()->whereIn('id', $orderIds)->where('pre_shipment_status', Package::PRE_SHIPMENT_STATUS_YES)->each(function (Order $order) {
            $order->cancelPreShipment();
            $order->updateOrderPrintStatus();
        });
        $orderList = Order::query()->whereIn('id', $orderIds)->get();
        $deliveryList = [];
        foreach ($preshipmentCancelRequest->items as $index => $item) {
            $orderIds = array_unique($item->orderIds);
            foreach ($orderIds as $orderId) {
                $order = $orderList->where('id', $orderId)->first();
                $package = $packageList->where('waybill_code', $item->waybillCode)->first();
                $deliveryList[] = [
                    'id' => $order->id,
                    'tid' => $order->tid,
                    'waybill_code' => $package->waybill_code,
                    'wp_code' => $package->wp_code,
                ];
            }
        }
        $shop = Shop::query()->where('id', $preshipmentCancelRequest->shopId)->first();
        // 预发货取消事件
        event((new OrderPreshipmentCancelEvent($shop->user, $shop, time(), $deliveryList))->setClientInfoByRequest(\request()));
    }


    /**
     * 预发货成功
     * @param array{
     *  waybillCode:string,
     *  wpCode:string,
     *  tid:string,
     *  deliveryType:int,
     *  packs:array{
     *       oid:int,
     *  shippedNum:int,
     *  num:string
     *  }[]}[] $fullDeliverSuccessArr
     *
     * @param array{
     * tid:string,
     * shopId:int,
     * waybillCodes:array{
     *  waybillCode:string,
     *  expressCode:string,
     *  packs:array{oid:int,shippedNum:int}[],
     *  }[],
     *}[] $splitDeliverSuccessArr
     *
     *
     * @return void
     */

    public function preshipmentSuccess(array $fullDeliverSuccessArr = [], array $splitDeliverSuccessArr = [])
    {
        $fullDeliverTids = array_column($fullDeliverSuccessArr, 'tid');

        $fullDeliverWaybillCodes = array_column($fullDeliverSuccessArr, 'waybillCode');
        $splitDeliverCollection = new Collection($splitDeliverSuccessArr);
        $splitDeliverWaybillCodes = $splitDeliverCollection->pluck('waybillCodes')->collapse()->pluck("waybillCode")->toArray();
        $splitDeliverTids = $splitDeliverCollection->pluck('tid')->toArray();
        $tids = array_merge($fullDeliverTids, $splitDeliverTids);
        $waybillCodes = array_merge($fullDeliverWaybillCodes, $splitDeliverWaybillCodes);
        Log::info("更新预发货", ["successArr" => $fullDeliverSuccessArr]);


        if (!empty($tids)) {
            Order::query()->whereIn('tid', $tids)->update([
                'pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_NO,
            ]);
        }

        Package::query()->whereIn('waybill_code', $waybillCodes)->where(
            'pre_shipment_status',
            Package::PRE_SHIPMENT_STATUS_YES
        )->update([
            'pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_FINISHED,
            'send_at' => DateTimeUtil::strNow(),
        ]);
    }


    /**
     * 预发货，把包裹里面的预发货标志打上
     *
     * @param array{
     *     tid:string,
     *     waybillCodes:array{
     *          waybillCode:string,
     *          expressCode:string,
     *          packs:array{
     *              oid:int,
     *              shippedNum:int,
     *              num:string
     *          }[],
     *         }[]
     * }[] $successResponseArr
     * @return void
     */
    public function handlePreshipment(array $successResponseArr)
    {
        $waybillCodes = collect($successResponseArr)->pluck('waybillCodes')->collapse()->pluck('waybillCode')->toArray();
        $tids = collect($successResponseArr)->pluck('tid')->toArray();
        Log::info("更新预发货状态", [$successResponseArr]);
        //        Package::query()->whereIn('waybill_code', $waybillCodes)->update([
        //            'pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_YES,
        //        ]);
        //把订单上面的pre_shipment_status 和pre_shipment_at都赋值
        Order::query()->whereIn('tid', $tids)->where('pre_shipment_status', Package::PRE_SHIPMENT_STATUS_NO)->update([
            'pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_YES,
            'pre_shipment_at' => DateTimeUtil::strNow(),
        ]);

        //用订单ID吧订单关联的所有的包裹ID都取出来


        //更新OrderItem里面的预发货数量
        //1.把$successArr里面的waybillCodes.packs都取出来,合并到一个数组中
        $allPacks = collect($successResponseArr)->pluck('waybillCodes')->collapse()->pluck("packs")->collapse()->toArray();
        //然后循环，在原来的基础上累加
        foreach ($allPacks as $pack) {
            OrderItem::query()->where('oid', $pack['oid'])->increment('pre_send_num', $pack['shippedNum']);
        }


        //把这些包裹ID的包裹都打上预发货标志
        Package::query()->whereIn("waybill_code", $waybillCodes)
            //            ->where('source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY) // source_type 是在后面的逻辑 set 的，这里导致无法修改
            ->where('pre_shipment_status', Package::PRE_SHIPMENT_STATUS_NO)->update([

                'pre_shipment_status' => Package::PRE_SHIPMENT_STATUS_YES,
                'pre_shipment_at' => DateTimeUtil::strNow(),

            ]);
        $this->generateOrderTrace($waybillCodes);

        //如果包裹加入了预发货以后，就要更新订单的打印状态
        Order::query()->whereIn('tid', $tids)->each(function (Order $order) {
            $order->updateOrderPrintStatus();
        });
    }


    /**
     * @param PreshipmentDeliveryRequest $preshipmentDeliveryRequest
     * @return array
     * @throws ErrorCodeException
     */
    public function preshipmentDeliver(PreshipmentDeliveryRequest $preshipmentDeliveryRequest): array
    {
        Log::info("预发货发货", [$preshipmentDeliveryRequest]);
        $orderIds = $preshipmentDeliveryRequest->getOrderIds();
        $orderItemIds = $preshipmentDeliveryRequest->getOrderItemIds();

        $optionShop = Shop::query()->where('id', $preshipmentDeliveryRequest->shopId)->first();
        $orderDeliveryService = new OrderDeliveryService();
        $orderList = \App\Models\Fix\Order::query()->with('shop', 'orderItem')->whereIn('id', $orderIds)->get();
        //把waybillCode都取出来
        $waybillCodes = $preshipmentDeliveryRequest->getWaybillCodes();
        //用这些waybillCodes,去查询对应的包裹
        $packages = Package::query()->whereIn('waybill_code', $waybillCodes)->get();
        //把包裹分按send_scenes,是预发时候的用的是整单还是拆单，is_split是取号的时候看是整单还是拆单
        $sendScenes = $packages->groupBy(function (Package $package) {
            $sendScene = $package->send_scene;
            if (!isset($sendScene)) {
                if ($package->is_split) {
                    $sendScene = Package::SEND_SCENE_MULTI;
                } else {
                    $sendScene = Package::SEND_SCENE_SINGLE;
                }
            }
            return $sendScene;
        });
        //single 是一键发货组
        $singleSendPackages = $sendScenes->get(Package::SEND_SCENE_SINGLE, new Collection());
        $multiSendPackages = $sendScenes->get(Package::SEND_SCENE_MULTI, new Collection());
        $singleSends = [];
        foreach ($singleSendPackages as $singleSendPackage) {
            $waybillCode = $singleSendPackage->waybill_code;
            $wpCode = $singleSendPackage->wp_code;
            $waybillOrderItems = $preshipmentDeliveryRequest->getWaybillItemsByWaybillCode($waybillCode);
            if (!empty($waybillOrderItems)) {
                foreach ($waybillOrderItems as $waybillOrderItem) {
                    //对加入的idStr需要进行去重
                    $orderIdStr = strval($waybillOrderItem->getFirstOrderId());
                    $singleSends[] = [
                        "idStr" => $orderIdStr,
                        "waybillCodeStr" => $waybillCode,
                        "express_code" => $wpCode
                    ];
                }
            }
        }
        $orderSuccessCounter = 0;
        $orderFail = 0;
        $failOrders = [];

        if (!empty($singleSends)) {
            /**
             * $successArr[] = [
             * 'waybillCode' => $item['expressNo'],
             * 'wpCode' => $item['expressCode'],
             * 'tid' => $item['tid'],
             * "deliveryType" => Package::DELIVERY_TYPE_REPLACEMENT,
             * 'packs' => [
             * 'oid' =>string
             * 'shippedNum' =>string
             * //重复这个目的是为了兼容追加发货时候的问题
             * 'num'=>string
             * ]
             * ];
             * @var array{} $successArr
             */
            list($successArr, $singleFailOrders) = $orderDeliveryService->fullOrderDelivery($singleSends, $orderList);
            Log::info("整单发货结果", $successArr);
            $orderSuccessCounter += count($successArr);
            $orderFail += count($singleFailOrders);
            $failOrders = array_merge($singleFailOrders, $failOrders);

            //把发货成功的包裹的预发货状态处理下
            $this->preshipmentSuccess($successArr);
        }
        if (!empty($multiSendPackages)) {
            $orderItemList = OrderItem::query()->whereIn('id', $orderItemIds)->get();
            $multiSendWaybillCodes = $multiSendPackages->pluck('waybill_code')->toArray();
            $multiList = [];
            foreach ($multiSendWaybillCodes as $waybillCode) {
                //把每个包裹里面的orderItemId都提取出来
                $waybillItems = $preshipmentDeliveryRequest->getWaybillItemsByWaybillCode($waybillCode);
                /**
                 * @var PreshipmentWaybillItem $waybillItemFirst
                 */
                $waybillItemFirst = array_first($waybillItems);

                foreach ($waybillItems as $waybillItem) {
                    $items = [];
                    foreach ($waybillItem->items as $item) {
                        $orderId = $item->orderId;
                        $orderItemId = $item->orderItemId;
                        $items[] = [
                            'orderId' => $orderId,
                            'orderItemId' => $orderItemId,
                            'num' => $item->num
                        ];
                    };
                    //把每个包裹里面的orderItemId都提取出来
                    $multiList[] = [
                        "waybillCode" => $waybillCode,
                        "wpCode" => $waybillItemFirst->wpCode,
                        "items" => $items
                    ];
                }
            }
            //把list中里面属于是拆单发货的部分提取处理

            /**
             * @var  [
             * tid:string,
             * shopId:int,
             * deliveryType =>int,
             * waybillCodes:array
             * waybillCodeStr => string
             * waybillCodeArr=> array,
             * ][]  $successArr
             */
            //淘宝的需要特殊处理，默认子订单是全部发完
            $consignStatus = [];
            if (PlatformConst::TAOBAO == config('app.platform')) {
                foreach ($orderItemIds as $id) {
                    $consignStatus[] = [
                        'orderItemId' => $id,
                        'isPartConsign' => false,
                    ];
                }
            }
            list(, $successArr, $multiFailOrders) = $orderDeliveryService->splitOrderDelivery($optionShop, $multiList, $multiSendPackages, $orderList, $orderItemList, false, $consignStatus);

            Log::info("拆单发货结果", $successArr);
            $orderSuccessCounter += count($successArr);
            $orderFail += count($multiFailOrders);
            $failOrders = array_merge($multiFailOrders, $failOrders);
            //把发货成功的包裹的预发货状态处理下
            $this->preshipmentSuccess([], $successArr);
        }
        return [
            'orderTotal' => $orderSuccessCounter + $orderFail,
            'orderSuccess' => $orderSuccessCounter,
            'orderFail' => $orderFail,
            'failOrders' => $failOrders
        ];
    }


    /**
     *
     * @param int[] $shopIdList
     * @param string $waybillCode
     * @return array
     * @throws ErrorCodeException
     */
    public function scanDeliver(int $shopId, array $shopIdList, string $waybillCode): array
    {

        $package = Package::query()->whereIn('shop_id', $shopIdList)->where('waybill_code', $waybillCode)->first();
        Log::info("扫描发货", [$package]);
        if (is_null($package)) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：' . $waybillCode . '未识别');
        }
        if (!is_null($package->recycled_at)) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：' . $waybillCode . '已经回收');
        }
        $preshipmentDeliveryRequest = new PreshipmentDeliveryRequest();
        $preshipmentDeliveryRequest->shopId = $shopId;
        $preshipmentWaybillItem = new PreshipmentWaybillItem();
        $preshipmentWaybillItem->waybillCode = $waybillCode;
        $preshipmentWaybillItem->wpCode = $package->wp_code;
        $packageOrders = $package->packageOrders;
        $packageOrders = $packageOrders->where('source_type', Package::SOURCE_TYPE_PRINT);
        if (empty($packageOrders)) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：' . $waybillCode . '没有可发货的包裹');
        }
        foreach ($packageOrders as $packageOrder) {
            $deliveryOrderItem = new DeliveryOrderItem();
            $deliveryOrderItem->orderId = $packageOrder->order_id;
            $deliveryOrderItem->orderItemId = $packageOrder->order_item_id;
            $deliveryOrderItem->num = $packageOrder->num;
            $preshipmentWaybillItem->addItem($deliveryOrderItem);
        }
        $preshipmentDeliveryRequest->addItem($preshipmentWaybillItem);
        return $this->preshipmentDeliver($preshipmentDeliveryRequest);


    }

    /**
     * 生成
     * @param  array  $waybillCodes
     * @return void
     * @throws \Throwable
     */
    public function generateOrderTrace(array $waybillCodes): void
    {
        Package::query()->whereIn("waybill_code", $waybillCodes)->where('source_type', Package::SOURCE_TYPE_INTERNAL_DELIVERY)->get()->each(
            function (Package $package) {
                $operationShopId = $package->operation_shop_id;
                if($operationShopId){
                    $userExtra = ShopUtil::firstByIdWithCache($operationShopId)->userExtra;
                    if($userExtra&&$userExtra->version!=UserExtra::VERSION_SENIOR){

//                        Log::info('操作店铺版本太低，不生成订单轨迹', ['shop_id' => $operationShopId, 'version' => $userExtra->version]);
                        return ;
                    }

                }
                //把PackageOrders里面的订单ID都提取出来
                $orderTraceArr = array_map(function ($tid) use ($package) {
                    return [
                        'tid' => $tid,
                        'user_id' => $package->user_id,
                        'shop_id' => $package->shop_id,
                        'express_code' => $package->wp_code,
                        'express_no' => $package->waybill_code,
                        'send_at' => DateTimeUtil::strNow(),
                        'status' => OrderTraceList::STATUS_SHIPPED,
                        'latest_updated_at' => DateTimeUtil::strNow(),
                        'auth_source'=>$package->auth_source
                        //                        'action'=>
                    ];
                }, array_unique(array_pluck($package->packageOrders, 'tid')));


                OrderTraceList::batchSave($orderTraceArr, $package->user_id, $package->shop_id);
                //                OrderTraceList::create);
            }
        );
    }
}
