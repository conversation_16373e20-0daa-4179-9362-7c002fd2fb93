<?php

namespace App\Services\Order\Impl;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Models\Address;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Order;
use App\Models\Shop;
use App\Models\OrderItem;
use App\Models\UserExtra;
use App\Services\Client\WxClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Utils\Environment;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use App\Models\CustomizeOrder;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

class WxOrderImpl extends AbstractOrderService
{
	protected $platformType = Shop::PLATFORM_TYPE_WX;

	protected $orderStatusMap
		= [
			0   => Order::ORDER_STATUS_UNKNOWN,
			10  => Order::ORDER_STATUS_PADDING,
			20  => Order::ORDER_STATUS_PAYMENT,
			21  => Order::ORDER_STATUS_PART_DELIVERED,
			30  => Order::ORDER_STATUS_DELIVERED,
			100 => Order::ORDER_STATUS_RECEIVED,
			200 => Order::ORDER_STATUS_FAILED,
			250 => Order::ORDER_STATUS_CLOSE,
		];

	const OTHER_CODE = 9999;  //其他

	const CODE_UNBIND_AUTH = 24; //取消授权错误码

	//错误码映射
	protected $errorCodeMap
		= [
			self::CODE_UNBIND_AUTH => self::ERROR_CODE_UNBIND,
		];

    /**
     * 商品总数
     * @var int
     */
    public $goodsTotalCount = 0;

	//对应平台快递公司
	protected $expressCodeList
		= [
			'HT'              => 'HTKY',
			'SF'              => 'SF',
			'ZTO'             => 'ZTO',
			'YTO'             => 'YTO',
			'STO'             => 'STO',
			'YUNDA'           => 'YD',
			'EMS'             => 'EMS',
			'JD'              => 'JD',
			'TT'              => 'HHTT',
			'ZJS'             => 'ZJS',
			'YZXB'            => 'YZPY',
			'YS'              => 'UC',
			'DB'              => 'DBL',
			'AIR'             => 'YFSD',
			'HTKY'            => 'HTKY',
			'POSTB'           => 'YZPY',
			'YDKY'            => 'YDKY',
			'ZTOKY'           => 'ZTOKY',
			'HOAU'            => 'HOAU',
			'BESTQJT'         => 'BTWL',
			'RRS'             => 'RRS',
			'KYE'             => 'KYSY',
			'SDSD'            => 'DSWL',
			'ANKY'            => 'ANEKY',
			'OTP'             => 'CND',
			'AXWL'            => 'AX',
			'SZKKE'           => 'JGSD',
			'SXJD'            => 'SXJD',
			'DEBANGWULIU'     => 'DBL',
			'SFKY'            => 'SF',
			'ZTOINTER'        => 'ZTO',
			'YDGJ'            => 'YD',
			'STOINTER'        => 'STO_INTL',
			'JIUYE'           => 'JIUYE',
			'GJ'              => 'GJ',
			'CN7000001021040' => 'YDKY',
			'CN7000001003751' => 'KYSY',
			'100007887'       => self::OTHER_CODE,
			'SURE'            => 'SURE',
			'CP570969'        => self::OTHER_CODE,
			'2608021499_235'  => 'ANE',
			'CP468398'        => 'YTO',
			'FAST'            => 'FAST',
			'3108002701_1011' => 'ZTOKY',
			'DBKD'            => 'DBL',
			'CN7000001000869' => 'ANEKY',
			'CP471906'        => 'SXJD',
			'GTO'             => self::OTHER_CODE,
			'QFKD'            => self::OTHER_CODE,
			'EYB'             => 'EMS',
			'CP457538'        => self::OTHER_CODE,
			'SNWL'            => 'SNWL',
			'CN7000001017817' => 'STO',
			'100004928'       => 'RFD',
			'CP443514'        => self::OTHER_CODE,
			'FEDEX'           => 'FEDEX',
			'5000000007756'   => 'YZPY',
			'TTKDEX'          => 'HHTT',
			'UC'              => 'UC',
            'JTSD'            => 'JTSD',
            'FENGWANG'        => 'FWX',
            'YOUZHENGGUONEI'  =>'EMS',
        ];

	protected $refundStatusMap
		= [
			0  => OrderItem::REFUND_STATUS_NO,
			10 => OrderItem::REFUND_STATUS_WAIT_SELLER, // 买家仅退款申请
			11 => OrderItem::REFUND_STATUS_WAIT_SELLER, // 买家退货退款申请
			20 => OrderItem::REFUND_STATUS_PLATFORM_IN, // 平台介入-买家仅退款申请
			21 => OrderItem::REFUND_STATUS_PLATFORM_IN, // 平台介入-买家退货退款申请
			22 => OrderItem::REFUND_STATUS_PLATFORM_IN, // 平台介入-已确认退货退款
			30 => OrderItem::REFUND_STATUS_PROCESSING, // 商品回寄信息待买家更新
			40 => OrderItem::REFUND_STATUS_WAIT_SELLER, // 商品回寄信息待卖家确认
			50 => OrderItem::REFUND_STATUS_REFUNDING, // 退款执行中
			60 => OrderItem::REFUND_STATUS_SUCCESS, // 退款成功
			70 => OrderItem::REFUND_STATUS_CLOSED, // 退款失败
		];

	protected $orderFlagMap
		= [

		];

	/**
	 * 每次拉取订单间隔的分钟
	 * @var int
	 */
	public $orderTimeInterval = 60;

	/**
	 * 每次拉取增量订单间隔的分钟
	 * @var int
	 */
	public $orderIncrTimeInterval = 60;

	/**
	 * 每次拉取退款订单间隔的分钟
	 * @var int
	 */
	public $refundOrderTimeInterval = 60;
	/**
	 * 临时的游标
	 * @var
	 */
	private $tmp_cursor;
    private $gatewayUrl = 'https://api.weixin.qq.com';

    /**
	 * @inheritDoc
	 */
	public function formatToOrder(array $trade)
	: array
	{
		$orderItems  = [];
		$tradeDetail = $trade['order_detail'];
		foreach ($tradeDetail['product_infos'] as $index => $item) {
			$total = $item['sale_price'] * $item['sku_cnt'];

			$sku_value = '';
			foreach ($item['sku_attrs'] as $desc) {
				//	            $sku_value .= $desc['name'] . ':' . $desc['value'] . ';'; //带规格名：尺寸：XL;颜色:红色;
				$sku_value .= $desc['attr_value'] . ';';     //不带规格名：XL;红色;
			}
			$status = $this->formatOrderStatus($trade['status']);
			$refund_status = ($item['on_aftersale_sku_cnt'] || $item['finish_aftersale_sku_cnt']) ? Order::REFUND_STATUS_YES : Order::REFUND_STATUS_NO;
			$orderItems[] = [
				"tid"              => strval($trade['order_id']), //主订单
				"oid"              => strval($trade['order_id']), //子订单号
				"type"             => $this->platformType, //订单类型
				"payment"          => formatToYuan($total), //实付金额
				"total_fee"        => formatToYuan($total), //总金额
				//				"discount_fee"     => formatToYuan($item['discountFee']), //优惠金额
				"goods_pic"        => $item['thumb_img'], //商品图片
				"goods_title"      => $item['title'], //商品标题
				"goods_price"      => formatToYuan($item['sale_price']), //商品单价
				"goods_num"        => $item['sku_cnt'], //商品数量
				"num_iid"          => $item['product_id'], //商品id
				"sku_id"           => $item['sku_id'] ?? '', //sku id
				"sku_value"        => $sku_value, //sku值
//                "outer_iid"        => $item['sku_code'] ?? '', //商家外部商品编码
                "outer_sku_iid"    => $item['sku_code'] ?? '', //商家外部sku编码
				"order_created_at" => $trade['create_time'], //订单创建时间
				"order_updated_at" => $trade['update_time'], //订单修改时间
//				"refund_id"        => !empty($trade['aftersale_detail']['aftersale_order_list']) ?
//                    $trade['aftersale_detail']['aftersale_order_list'][0]['aftersale_order_id']: 0,
				"refund_status"    => $refund_status,
				"status" => $status,
			];
		}

		$sendAt = $tradeDetail['delivery_info']['delivery_product_info'][0]['delivery_time'] ?? '';
        if (!empty($tradeDetail['delivery_info']['pickup_address'])) {
            // 跳过自提订单
            return [];
        }
        $receiver_name = $tradeDetail['delivery_info']['address_info']['user_name'];
        $receiver_phone = $tradeDetail['delivery_info']['address_info']['tel_number'];
        $receiver_address = $tradeDetail['delivery_info']['address_info']['detail_info'];

        $orderData = [
			"tid"               => strval($trade['order_id']), //主订单
			"type"              => $this->platformType, //订单类型
			"express_no"        => $tradeDetail['delivery_info']['delivery_product_info'][0]['waybill_id'] ?? null, //快递单号
			"buyer_id"          => $trade['buyerId'] ?? '', //买家ID
			"buyer_nick"        => $trade['buyerNick'] ?? '', //买家昵称
			"seller_nick"       => $trade['sellerNick'] ?? null, //卖家昵称
			"order_status"      => $this->formatOrderStatus($trade['status']), //订单状态
			"refund_status"     => $trade['aftersale_detail']['on_aftersale_order_cnt'] ?? 0, //退款状态
			"receiver_state"    => $tradeDetail['delivery_info']['address_info']['province_name'], //收货人省份
			"receiver_city"     => $tradeDetail['delivery_info']['address_info']['city_name'], //收货人城市
			"receiver_district" => $tradeDetail['delivery_info']['address_info']['county_name'], //收货人地区
			//            "receiver_town" => '', //收货人街道
			"receiver_name"     => $receiver_name, //收货人名字
			"receiver_phone"    => $receiver_phone, //收货人手机
			"receiver_address"  => $receiver_address, //收件人详细地址
            "receiver_zip"      => $tradeDetail['delivery_info']['address_info']['postal_code'] ?? "", //收件人邮编
            "payment"           => formatToYuan($tradeDetail['price_info']['order_price']), //实付金额
			"total_fee"         => formatToYuan($tradeDetail['price_info']['product_price']), //总金额
			"discount_fee"      => isset($tradeDetail['price_info']['discounted_price']) ? formatToYuan($tradeDetail['price_info']['discounted_price']) : 0, //优惠金额
			"post_fee"          => formatToYuan($tradeDetail['price_info']['freight']), //运费
			"seller_flag"       => Order::FLAG_NONE, //卖家备注旗帜
			"seller_memo"       => empty($trade['ext_info']['merchant_notes']) ? '[]' : json_encode([$trade['ext_info']['merchant_notes']], 320), //卖家备注
			"buyer_message"     => $trade['ext_info']['customer_notes'], //买家留言
			"has_buyer_message" => empty($trade['ext_info']['customer_notes']) ? 0 : 1, //买家留言
			"express_code"      => $tradeDetail['delivery_info']['delivery_product_info'][0]['delivery_id'] ??
			                       null, //快递公司代码
			"order_created_at"  => date('Y-m-d H:i:s', strtotime($trade['create_time'])), //订单创建时间
			"order_updated_at"  => date('Y-m-d H:i:s', strtotime($trade['update_time'])), //订单修改时间
			"send_at"           => !empty($sendAt) ? date('Y-m-d H:i:s', $sendAt) : null, //发货时间
			//            "finished_at" => , //订单完成时间
			"pay_at"            => $tradeDetail['pay_info']['pay_time'] ?? null, //支付时间
			//                "refund_id" => $trade[''], //退款id
			'num'               => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
			'sku_num'           => count($orderItems), // SKU数量
			'is_pre_sale'       => 0, // 是否预售1是0否
			//            'promise_ship_at' => , // 承诺发货时间
			'items'             => $orderItems
		];
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);
		//主订单退款状态为未退款 同时判断子订单是否有退款 更改主订单为部分退款
        if ($orderData['refund_status'] == Order::REFUND_STATUS_NO && in_array(Order::REFUND_STATUS_YES, array_column($orderItems, 'refund_status'))) {
            $orderData['refund_status'] = Order::REFUND_STATUS_PART;
        }



        //发货状态的时候已锁单 locked_at 为null
        if (in_array($this->formatOrderStatus($trade['status']), [Order::ORDER_STATUS_DELIVERED])){
            $orderData['locked_at'] = null;
        }

		if (!empty($orderData['pay_at'])) {
			$orderData['promise_ship_at'] = date('Y-m-d H:i:s', strtotime('+72 hour', strtotime($orderData['pay_at'])));
		}
		if (empty($orderData['express_no'])) {
            unset($orderData['send_at']);
            unset($orderData['express_no']);
            unset($orderData['express_code']);
		}
//        $cipher_info = [
//            'receiver_phone_ciphertext' => '',
//            'receiver_name_ciphertext' => '',
//            'receiver_address_ciphertext' => '',
//            'receiver_phone_mask' => '',
//            'receiver_name_mask' => '',
//            'receiver_address_mask' => '',
//        ];
        // 微信加密，内测白名单
        if (in_array($this->getShop()->id, explode(',', env('WX_ENCRYPT_SHOP_IDS', '')))) {
            $orderData['receiver_name'] = md5($receiver_name);
            $orderData['receiver_phone'] = md5($receiver_phone);
            $orderData['receiver_address'] = md5($receiver_address);
            $cipher_info = [
                'receiver_phone_ciphertext' => appEncrypt($receiver_phone),
                'receiver_name_ciphertext' => appEncrypt($receiver_name),
                'receiver_address_ciphertext' => appEncrypt($receiver_address),
                'receiver_phone_mask' => dataDesensitizationForOpenApi($receiver_phone, 3, 4),
                'receiver_name_mask' => dataDesensitizationForOpenApi($receiver_name, 1, 0),
                'receiver_address_mask' => addressDesensitization($receiver_address),
            ];
            $orderData['cipher_info'] = $cipher_info;
        }

		return $orderData;
	}

	/**
	 * @inheritDoc
	 */
	public function formatToOrders(array $orders)
	: array
	{
		$list = [];
		foreach ($orders as $index => $order) {
		    if (!isset($order['order_detail']['delivery_info']['address_info'])) {
                continue;
			}
			$list[] = $this->formatToOrder($order);
		}
		return $list;
	}

	/**
	 * 商品构建
	 * @param array $good
	 * @return array
	 */
	public function formatToGood(array $good)
	: array
	{
		$skus = [];
		foreach ($good['skus'] as $index => $item) {
			$sku_value = '';
			foreach ($item['sku_attrs'] as $attr) {
				//				$sku_value .= $attr['attr_key'] . ':'. $attr['attr_value'] . ';'; //属性键key + 属性值
				$sku_value .= $attr['attr_value'] . ';'; // 属性值
			}

			$skus[] = [
				"type"           => $this->platformType,
				"sku_id"         => $item['sku_id'],
				"sku_value"      => $sku_value,
				"outer_id"       => $item['out_sku_id'],
				"outer_goods_id" => $item['out_product_id'],
				"sku_pic"        => $item['thumb_img'],
				"is_onsale"      => $item['status'] == 5 ? GoodsSku::IS_ONSALE_YES : GoodsSku::IS_ONSALE_NO,
			];
		}
		return [
			"type"           => $this->platformType,
			'num_iid'        => $good['product_id'],
			'outer_goods_id' => $good['out_product_id'],
			'goods_title'    => $good['title'],
			'goods_pic'      => $good['head_img'][0],
			'is_onsale'      => $good['status'] == 5 ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
			//			'goods_created_at' => date('Y-m-d H:i:s', $good['createTime'] / 1000),
			//			'goods_updated_at' => date('Y-m-d H:i:s', $good['updateTime'] / 1000),
			'skus'           => $skus
		];
	}

	/**
	 * 商品批量格式转换
	 * @param array $goods
	 * @return array
	 */
	public function formatToGoods(array $goods)
	: array
	{
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());
		$list = [];
		foreach ($goods as $index => $good) {
			$skus = [];
			foreach ((array)$good['skus'] as $index2 => $datum) {
				$params = [
					'product_id' => $good['product_id'],
					'sku_id'     => $datum['sku_id'],
				];
				$data   = $client->execute('post', '/product/sku/get', $params);
				$skus[] = $data['data'];
			}
			$good['skus'] = $skus;
//            $params = [
//                'product_id' => $good['product_id'],
//            ];
//            $data   = $client->execute('post', '/product/sku/get_list', $params);
//            $good['skus'] = $data['skus'];
			$list[]       = $this->formatToGood($good);
		}

		return $list;
	}

	/**
	 * @inheritDoc
	 * @throws ClientException
	 */
	protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
	{
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());
		$params = [
			'start_update_time' => date('Y-m-d H:i:s', $startTime),
			'end_update_time'   => date('Y-m-d H:i:s', $endTime),
			//			'status'        => 10, // 订单状态， 10 待付款 20 待发货 30 待收货 100 交易成功订单 200 全部商品售后之后，订单取消 250 用户主动取消或待付款超时取消
			'page_size'         => $this->pageSize,
			'page'              => 1,
		];
		//第一次仅拉取待发货
		if ($isFirstPull) {
			$params['status'] = 20;
		}

		$response = $client->execute('post', '/product/order/get_list', $params);
        $this->handleErrorCode($response);
        $count    = collect($response['orders'])->count();
		if ($count < $this->pageSize) {
			$this->hasNext = false;
		}
		else {
			$this->hasNext = true;
		}

		return $response['orders'];
	}

	/**
	 * 订单详情
	 * @param string $tid
	 * @return mixed
	 * @throws ClientException
	 */
	protected function sendGetOrderInfo(string $tid)
	{
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());
		$params   = [
			'order_id' => $tid,
		];
		$response = $client->execute('post', '/product/order/get', $params);

		return $response['order'] ?? [];
	}

	/**
	 * 获取商品列表
	 * @param int int $pageSize,
	 * @param int $currentPage
	 * @return array
	 * @throws ClientException
	 */
	protected function sendGetGoods(int $pageSize, int $currentPage = 1)
	{
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());
		$params = [
			'page_size' => $pageSize,
			'status'    => 5,
			'page'      => $currentPage,
		];
		$goods  = $client->execute('post', '/product/spu/get_list', $params);

		\Log::info('wx_goods_result', [$goods]);
		if (!isset($goods['errcode']) || $goods['errcode'] != 0 || !isset($goods['spus'])) {
			\Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
			return [];
		}
        if (($currentPage * $pageSize) < $goods['total_num']){
            $this->hasNext = true;
        }
		return $goods['spus'];
	}

	/**
	 * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return bool|mixed
	 * @throws ClientException
	 */
	protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [], bool $silent = true)
	{
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());
        $params = [
            'order_id'      => $tid,
            'delivery_list' => [
                [
                    'waybill_id'  => !empty($expressNo) ? $expressNo : '',
                    'delivery_id' => !empty($expressCode) ? $this->expressCodeList[$expressCode] : '',
                ]
            ],
        ];
		$result = $client->execute('post', '/product/delivery/send', $params);

		//已经发货
        if (isset($result['errcode']) && $result['errcode'] == 109001) {
            return true;
        }
		if (!isset($result['errcode']) || $result['errcode'] != 0) {
			\Log::error('订单发货失败!', [$result, $this->platformType, $tid, $expressCode, $expressNo]);
            $this->handleErrorCode($result);
            return false;
		}

		return true;
	}

    /**
     * @inheritDoc
     * @param int $startTime
     * @param int $endTime
     * @param bool $isFirstPull
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     * @throws GuzzleException
     */
	protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());

		$params = [
			'start_update_time' => date('Y-m-d H:i:s', $startTime),
			'end_update_time'   => date('Y-m-d H:i:s', $endTime),
			//			'status'        => 10, // 订单状态， 10 待付款 20 待发货 30 待收货 100 交易成功订单 200 全部商品售后之后，订单取消 250 用户主动取消或待付款超时取消
			'page_size'         => $this->pageSize,
			'page'              => $this->page,
		];
		//第一次仅拉取待发货
		if ($isFirstPull) {
			$params['status'] = 20;
		}

		$response = $client->execute('post', '/product/order/get_list', $params);
        $this->handleErrorCode($response);
        $count    = collect($response['orders'])->count();
		if ($count < $this->pageSize) {
			$this->hasNext = false;
		}
		else {
			$this->hasNext = true;
		}

		return $response['orders'];
	}


	protected function getClient()
	{
		$appKey    = config('socialite.wx.client_id');
		$secretKey = config('socialite.wx.client_secret');
		return new WxClient($appKey, $secretKey);
	}

    protected function getClientAndInit()
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        return $client;
    }

	/**
	 * @inheritDoc
	 */
	public function openSubscribeMsg():bool
	{
        return false;
	}

	/**
	 * @inheritDoc
	 */
	public function consumeSubscribeMsg()
	{
		// TODO: Implement consumeSubscribeMsg() method.
	}

	/**sendGetAfterSaleOrderInfo
	 * @inheritDoc
	 */
	public function confirmSubscribeMsg(array $idArr)
	{
		// TODO: Implement confirmSubscribeMsg() method.
	}

	/**
	 * @inheritDoc
	 */
	public function handleSubscribeMsg($data)
	{
		// TODO: Implement handleOrderMsg() method.
	}

	/**
	 * @inheritDoc
	 */
	public function createWebsocketConnection()
	{
		// TODO: Implement createWebsocketServer() method.
	}


	/**
	 * 获取token
	 * @return mixed
	 * <AUTHOR>
	 */
	public function getAccessToken()
	{
		if (!empty($this->accessToken)) {
			return $this->accessToken;
		}
		$shop = $this->getShop();
		if (empty($shop)) {
			throw new NotFoundResourceException('未找到授权');
		}
		return $this->accessToken = $shop->access_token;
	}

	/**
	 * 获取service_id
	 * @return mixed
	 * <AUTHOR>
	 */
	public function getServiceId()
	{
		if (!empty($this->serviceId)) {
			return $this->serviceId;
		}
		$shop = $this->getShop();
		if (empty($shop)) {
			throw new NotFoundResourceException('未找到授权serviceId');
		}
		return $this->serviceId = $shop->service_id;
	}

	/**
	 * 获取specification_id
	 * @return mixed
	 * <AUTHOR>
	 */
	public function getSpecificationId()
	{
		if (!empty($this->specificationId)) {
			return $this->specificationId;
		}
		$shop = $this->getShop();
		if (empty($shop)) {
			throw new NotFoundResourceException('未找到授权serviceId');
		}
		return $this->specificationId = $shop->specification_id;
	}

	/**
	 * @inheritDoc
	 */
	public function sendServiceInfo()
	{
		$shop = $this->getShop();

		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());

		$params   = [];
		$response = $client->execute('post', '/product/service/get_list', $params);
		$ver = collect($response['service_list'])->sortByDesc('expire_time')->first();
        \Log::info('订购版本data返回',[$response]);
		return [
			'user_id'       => $shop->user_id,
			'shop_id'       => $shop->id,
			'identifier'    => $shop->identifier,
			'platform_type' => $this->platformType,
			'expired_at'    => $ver['expire_time'], //暂定免费版使用30天
			'version'       => $ver['specification_id'],
			'version_desc'  => array_get(UserExtra::VERSION_MAP_ARR, $ver['specification_id'], UserExtra::VERSION_FREE),
			'pay_amount'    => 0,
		];
	}

	protected function sendRefundOrders(int $startTime, int $endTime)
	{
		$client = $this->getClient();
		$client->setAccessToken($this->getAccessToken());
		$client->setServiceId($this->getServiceId());
		$client->setSpecificationId($this->getSpecificationId());

		$params = [
			'start_update_time' => date('Y-m-d H:i:s', $startTime),
			'end_update_time'   => date('Y-m-d H:i:s', $endTime),
			'status'            => 200, // 订单状态， 10 待付款 20 待发货 30 待收货 100 交易成功订单 200 全部商品售后之后，订单取消 250 用户主动取消或待付款超时取消
			'page_size'         => $this->pageSize,
			'page'              => $this->page,
		];

		$response = $client->execute('post', '/product/order/get_list', $params);
		$count    = collect($response['orders'])->count();
		if ($count < $this->pageSize) {
			$this->hasNext = false;
		}
		else {
			$this->hasNext = true;
		}

		return $response['orders'];
	}

	/**
	 * @inheritDoc
	 */
	protected function formatRefundOrder($order)
	{
		return [
			'tid'               => $order['order_id'],
			'oid'               => $order['order_id'],
			'refund_id'         => $order['refundId'] ?? 0,
			'refund_created_at' => $order['update_time'],
			'refund_updated_at' => $order['update_time'],
			'refund_status'     => OrderItem::REFUND_STATUS_SUCCESS,
		];
	}

    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo):bool
    {
        return true;
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());

        $params = [
            'start_create_time' => $beginAt,
            'end_create_time'   => $endAt,
            'page_size'         => $this->pageSize,
            'page'              => $this->getPage(),
        ];

        $result = $client->execute('post', '/product/service/get_order_list', $params);
        \Log::Info('获取店铺订购订单列表  shop_id:'. $this->getShop()->id .' result:',[$result]);
        if (!isset($result['errcode']) || $result['errcode'] != 0) {
            \Log::error('获取店铺订购服务订单失败！ shop_id：'.$this->getShop()->id);
            return [];
        }

        return $result['service_order_list'];
    }

    public function sendBatchGetOrderInfo($orders,$safe=false)
    {
        //并行异步请求
        $data  = $result = [];
        $url   = '/product/order/get';
        foreach ($orders as $order) {
            $idStr     = handleOrderIdStr($order);
            $validParams = [
                'access_token'     => $this->getAccessToken(),
                'service_id'       => $this->getServiceId(),
                'specification_id' => $this->getSpecificationId(),
            ];
            $data[$idStr] = [
                'params' => ['order_id' => $order['tid']],
                'url' => $this->gatewayUrl . $url . '?' . http_build_query($validParams)
            ];
        }

        $response = $this->poolCurl($data, 'post');
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['order'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $result[$orderId] = $trade;
//            $result[$orderId] = $this->formatToOrder($trade);
        }

        return $result;
    }

    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }


    private function handleErrorCode($result)
    {
        if (!isset($result['errcode'])) {
            throw new OrderException('微信服务异常');
        }
        $errcode = $result['errcode'] ?? 0;
        switch ($errcode) {
            case 0: // 正常
                break;
            case 109001:
                throw new ApiException(ErrorConst::ORDER_DELIVERED);
            case 40001:
            case 42001:
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                throw new OrderException('微信服务异常：' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }
	}

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sentBatchDecrypt($order)
    {
        // TODO: Implement sentBatchDecrypt() method.
    }

    /**
     * 批量解密
     * @param $params
     * @return mixed
     */
    protected function sendDecrypt($params)
    {
        $data[] = [
            'text' => appDecrypt($params['text']),
        ];
        return $data;
    }

    /**
     * 批量加密
     * @param $order
     * @return mixed
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'page_size' => 1,
            'status'    => 5,
            'page'      => 1,
        ];
        $goods  = $client->execute('post', '/product/spu/get_list', $params);

        try {
            $this->handleErrorCode($goods);
        } catch (ApiException $e) {
            if (ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0] == $e->getCode()) {
                return false;
            }
        } catch (\Exception $e) {

        }
        return true;

    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendDDShopRoleType() method.
        return 0;
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getDDTradesOrder() method.
    }



    /**
     * 请求查询交易订单的 Tid
     * <AUTHOR>
     * @param array $query_list
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $client = $this->getClientAndInit();
        // 没找到接口
        return [];
    }

    public function getQueryTradeOrderId(string $type, string $search)
    {
        $conditions = [];
        $beginAt = \request()->input('begin_at', '');
        $endAt = \request()->input('end_at', '');
        $timeField = \request()->input('timeField', '');
        if ($timeField && $beginAt && $endAt) {
            $conditions[] = [$timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $conditions[] = [$timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }
        // 微信加密，内测白名单
        if (in_array($this->getShop()->id, explode(',', env('WX_ENCRYPT_SHOP_IDS', '')))) {
            if ($type == 'receiver_name') {
                // receiver_name 只有 30 的长度
                $search = substr(md5($search),0,30);
            }else{
                $search = md5($search);
            }
        }
        $shop = $this->getShop();
        $idArr = Order::query()->select('id')
            ->where('shop_id', $shop->id)
            ->where($conditions)
            ->where($type, $search)
            ->get()
            ->pluck('id')
            ->toArray();

        //自由订单
        $customIdArr = CustomizeOrder::query()->select('id')
            ->where('shop_id', $shop->id)
            //->where($conditions)
            ->where($type, $search)
            ->get()
            ->pluck('id')
            ->toArray();

        //return $idArr;
        return array_merge($idArr, $customIdArr);
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $method = '/product/delivery/send';
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $expressCode = $orderDeliveryRequest->expressCode;
            $expressNo = $orderDeliveryRequest->expressNo;
            $orderItemOId = $orderDeliveryRequest->orderItemOId;

            $params = [
                'order_id'      => $tid,
                'delivery_list' => [
                    [
                        'waybill_id'  => !empty($expressNo) ? $expressNo : '',
                        'delivery_id' => !empty($expressCode) ? ($this->expressCodeList[$expressCode]??self::OTHER_CODE) : '',
                    ]
                ],
            ];
            list($requestUrl, $apiParams) = $this->getApiParamsAndUrl($method, $params);
            $requestId = $orderDeliveryRequest->getRequestId();
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl,
            ];
        }
        $responseData = $this->poolCurl($requestData, 'post');
        $results = [];
        foreach ($responseData as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);

                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->code = $e->getCode();
                $commonResponse->message = $e->getMessage();
            } finally {
                $results[] = $commonResponse;
            }
        }

        return $results;

    }

    private function getApiParamsAndUrl($method, $params)
    {
        $apiParams = $params;
        $validParams = [
            'access_token'     => $this->getAccessToken(),
            'service_id'       => $this->getServiceId(),
            'specification_id' => $this->getSpecificationId(),
        ];
        $requestUrl = $this->gatewayUrl . $method . '?' . http_build_query($validParams);
        return[$requestUrl, $apiParams];
    }



    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }
    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams,$order)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());

        return $client->execute($requestMethod, $apiMethod, $apiParams);
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        return $apiParams;
    }
}
