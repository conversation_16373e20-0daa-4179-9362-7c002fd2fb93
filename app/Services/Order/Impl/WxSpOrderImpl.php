<?php
/**
 * Created by PhpStorm.
 * User: xuji<PERSON><PERSON>
 * Date: 2022/9/8
 * Time: 14:39
 */

namespace App\Services\Order\Impl;


use App\Constants\ErrorConst;
use App\Constants\RefundSubStatusConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Bo\OrderResponseBo;
use App\Services\Client\WxClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Services\Order\Request\OrderDeliveryRequest;
use App\Utils\OrderUtil;
use App\Utils\RandomUtil;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\Translation\Exception\NotFoundResourceException;
use Workerman\Connection\AsyncTcpConnection;

class WxSpOrderImpl extends AbstractOrderService
{
    /**
     * 订单类型
     * @var int
     */
    protected $platformType = Shop::PLATFORM_TYPE_WXSP;

    protected $orderStatusMap
        = [
            0 => Order::ORDER_STATUS_UNKNOWN,
            10 => Order::ORDER_STATUS_PADDING,
            20 => Order::ORDER_STATUS_PAYMENT,
            21 => Order::ORDER_STATUS_PART_DELIVERED,
            30 => Order::ORDER_STATUS_DELIVERED,
            100 => Order::ORDER_STATUS_RECEIVED,
            200 => Order::ORDER_STATUS_CLOSE,
            250 => Order::ORDER_STATUS_FAILED,
        ];

    /**
     * @var string
     */
    private $gatewayUrl = 'https://api.weixin.qq.com';

    const OTHER_CODE = "OTHER";  //其他

    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 60 * 2;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60 * 2;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    //对应平台快递公司
    protected $expressCodeList
        = [
            'HT' => 'HTKY',
            'SF' => 'SF',
            'ZTO' => 'ZTO',
            'YTO' => 'YTO',
            'STO' => 'STO',
            'YUNDA' => 'YD',
            'EMS' => 'EMS',
            'JD' => 'JD',
            'TT' => 'HHTT',
            'ZJS' => 'ZJS',
            'YZXB' => 'YZPY',
            'YS' => 'UC',
            'DB' => 'DBL',
            'AIR' => 'YFSD',
            'HTKY' => 'HTKY',
            'POSTB' => 'YZPY',
            'YDKY' => 'YDKY',
            'ZTOKY' => 'ZTOKY',
            'HOAU' => 'HOAU',
            'BESTQJT' => 'BTWL',
            'RRS' => 'RRS',
            'KYE' => 'KYSY',
            'SDSD' => 'DSWL',
            'ANKY' => 'ANEKY',
            'OTP' => 'CND',
            'AXWL' => 'AX',
            'SZKKE' => 'JGSD',
            'SXJD' => 'SXJD',
            'DEBANGWULIU' => 'DBL',
            'SFKY' => 'SF',
            'ZTOINTER' => 'ZTO',
            'YDGJ' => 'YD',
            'STOINTER' => 'STO_INTL',
            'JIUYE' => 'JIUYE',
            'GJ' => 'GJ',
            'CN7000001021040' => 'YDKY',
            'CN7000001003751' => 'KYSY',
            '100007887' => self::OTHER_CODE,
            'SURE' => 'SURE',
            'CP570969' => self::OTHER_CODE,
            '2608021499_235' => 'ANE',
            'CP468398' => 'YTO',
            'YUANTONG' => 'YTO',
            'JTEXPRESS' => 'JTSD',
            'FAST' => 'FAST',
            '3108002701_1011' => 'ZTOKY',
            'DBKD' => 'DBL',
            'CN7000001000869' => 'ANEKY',
            'CP471906' => 'SXJD',
            'GTO' => self::OTHER_CODE,
            'QFKD' => self::OTHER_CODE,
            'EYB' => 'EMS',
            'CP457538' => self::OTHER_CODE,
            'SNWL' => 'SNWL',
            'CN7000001017817' => 'STO',
            '100004928' => 'RFD',
            'CP443514' => self::OTHER_CODE,
            'FEDEX' => 'FEDEX',
            '5000000007756' => 'YZPY',
            'TTKDEX' => 'HHTT',
            'UC' => 'UC',
            'JTSD' => 'JTSD',
            'FENGWANG' => 'FWX',
            'CP449455' => 'JGSD',
            'ZHONGTONG' => 'ZTO',
            'YOUZHENGGUONEI' => 'EMS',
            'LE09252050' => self::OTHER_CODE
        ];

    /**
     * 是否使用虚拟号码
     * @param array $tradeDetail
     * @return bool
     */
    public function isUseVirtualTelPhone(array $tradeDetail): bool
    {
        return Arr::get($tradeDetail, 'delivery_info.address_info.use_tel_number', false);
    }

    /**
     * 格式化成订单表结构
     * @param array $trade
     * @return array
     * <AUTHOR>
     */
    public function formatToOrder(array $trade): array
    {

//        if(strval($trade['order_id']) == '3723366141818442752'){
//            Log::info('订单数据',$trade);
//        }
        $orderItems = [];
        $tradeDetail = $trade['order_detail'];
        $orderDeliveryDeadline = 0;

        //最早发货时间
        $orderDeliveryDeadline = 0;
        $is_pre_sale = 0;
        $skuSendNumArr = [];
        foreach ($tradeDetail['delivery_info']['delivery_product_info'] as $delivery_product_info) {
            foreach ($delivery_product_info['product_infos'] as $product_info) {
                if (!isset($skuSendNumArr[$product_info['sku_id']])) {
                    $skuSendNumArr[$product_info['sku_id']] = 0;
                }
                $skuSendNumArr[$product_info['sku_id']] += $product_info['product_cnt'];
            }

        }
        foreach ($tradeDetail['product_infos'] as $index => $item) {
            $goodsNum = $item['sku_cnt'];
            $total = $item['sale_price'] * $goodsNum;

//            $sku_value = '';
//            foreach ($item['sku_attrs'] as $desc) {
//                $sku_value .= $desc['attr_value'] . ';';     //不带规格名：XL;红色;
//            }
            $skuList = [];
            if (!empty($item['sku_attrs'])){
                foreach ($item['sku_attrs'] as $desc) {
                    $skuList[] = ['name'=>$desc['attr_key'], 'value'=>$desc['attr_value']];
                }
            }
            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);
            if (empty($skuValue)) {
                $skuValue = $item['title']; // 没有用标题
            }
            //比较发货截止时间，取最小的那个
            $itemDeliveryDeadline = $item['delivery_deadline'] ?? null;
            if (!empty($itemDeliveryDeadline)) {
                if (empty($orderDeliveryDeadline)) {
                    $orderDeliveryDeadline = $itemDeliveryDeadline;
                } elseif ($itemDeliveryDeadline < $orderDeliveryDeadline) {
                    $orderDeliveryDeadline = $itemDeliveryDeadline;
                }
            }

//            $status = $this->formatOrderStatus($trade['status']);
            $refund_status = ($item['on_aftersale_sku_cnt'] || $item['finish_aftersale_sku_cnt']) ? Order::REFUND_STATUS_YES : Order::REFUND_STATUS_NO;
            $sendNum = $skuSendNumArr[$item['sku_id']] ?? 0;
            $sendRemainNum = $goodsNum - $sendNum;
            // 判断 $sendRemainNum 剩余数量
            if ($sendRemainNum == $goodsNum) {
                $status = OrderItem::ORDER_STATUS_PAYMENT;
            } elseif ($sendRemainNum > 0) {
                $status = OrderItem::ORDER_STATUS_PART_DELIVERED;
            } else {
                $status = OrderItem::ORDER_STATUS_DELIVERED;
            }
            $orderItems[] = [
                "tid" => strval($trade['order_id']), //主订单
                "oid" => $trade['order_id'] . '-' . ($item['sku_id'] ?? ''), //子订单号
                "type" => $this->platformType, //订单类型
                "payment" => formatToYuan($total), //实付金额
                "total_fee" => formatToYuan($total), //总金额
                "goods_pic" => $item['thumb_img'], //商品图片
                "goods_title" => $item['title'], //商品标题
                "goods_price" => formatToYuan($item['sale_price']), //商品单价
                "goods_num" => $goodsNum, //商品数量
                "num_iid" => $item['product_id'], //商品id
                "sku_id" => $item['sku_id'], //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "send_num" => $sendNum, //已发货数量
                "send_remain_num" => $sendRemainNum, //剩余发货数量
                "outer_sku_iid" => $item['sku_code'] ?? '', //商家外部sku编码
                "order_created_at" => $trade['create_time'], //订单创建时间
                "order_updated_at" => $trade['update_time'], //订单修改时间
                "promise_ship_at" => date('Y-m-d H:i:s', $itemDeliveryDeadline), // 承诺发货时间
                "refund_status" => $refund_status,
                "refund_sub_status" => $this->formatSubRefundStatus($item), //
                "status" => $status,
            ];
        }
        //比较$min_predict_delivery_time 和$orderDeliveryDeadline 取最小值，要排除0，,0是null
//        if ($min_predict_delivery_time > 0 && $orderDeliveryDeadline > 0) {
//            $min_predict_delivery_time = min($min_predict_delivery_time, $orderDeliveryDeadline);
//        }else{
//            $min_predict_delivery_time = $min_predict_delivery_time > 0 ? $min_predict_delivery_time : $orderDeliveryDeadline;
//        }


        $itemRefundSum = collect($orderItems)->pluck('refund_status')->sum();
        if ($itemRefundSum > 0 && $itemRefundSum < count($orderItems)) {
            $refund_status = Order::REFUND_STATUS_PART;
        } elseif ($itemRefundSum > 0 && $itemRefundSum >= count($orderItems)) {
            $refund_status = Order::REFUND_STATUS_YES;
        } else {
            $refund_status = Order::REFUND_STATUS_NO;
        }
        $sendAt = $tradeDetail['delivery_info']['delivery_product_info'][0]['delivery_time'] ?? '';
        if (!empty($tradeDetail['delivery_info']['pickup_address'])) {
            // 跳过自提订单
            return [];
        }

        //如果启用了虚拟号，就用把虚拟号直接保存到订单表中，但合单需要用真实号码的掩码
        $isUseVirtualTelPhone = $this->isUseVirtualTelPhone($tradeDetail);
        if ($isUseVirtualTelPhone) {
            //使用虚拟号码，就把虚拟号码保存到订单表中
//            $receiver_phone = Arr::get($tradeDetail, 'delivery_info.address_info.tel_number_ext_info.virtual_tel_number', '');
            $receiver_phone = $tradeDetail['delivery_info']['address_info']['tel_number'];
            Log::info("使用了虚拟号码", [strval($trade['order_id']), $receiver_phone]);
        } else {
            //不使用虚拟号码，就把真实号码保存到订单表中
            $receiver_phone = $tradeDetail['delivery_info']['address_info']['tel_number'];
            Log::info("没有使用虚拟号码", [strval($trade['order_id']), $receiver_phone]);
        }
        $province_name = $tradeDetail['delivery_info']['address_info']['province_name'];
        $city_name = $tradeDetail['delivery_info']['address_info']['city_name'];
        $county_name = $tradeDetail['delivery_info']['address_info']['county_name'];
        $user_name = $tradeDetail['delivery_info']['address_info']['user_name'];
        $receiver_phone = implode('_', [$province_name, $city_name, $county_name, $user_name, $receiver_phone]);
        // 包裹信息
        $logistics_data = [];
        foreach ($tradeDetail['delivery_info']['delivery_product_info'] as $logistics_info) {
            $product_list = [];
            foreach ($logistics_info['product_infos'] as $item) {
//                $sku_value = '';
//                foreach ($item['sku_specs'] as $sku) {
//                    $sku_value .= $sku['value'] . ';';
//                }
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $trade['order_id'] . '-' . ($item['sku_id'] ?? ''); //子订单号
                $logisticsDataProductBo->sku_id = $item['sku_id'];
                $logisticsDataProductBo->outer_sku_id = '';
                $logisticsDataProductBo->num_iid = $item['product_id'];
                $logisticsDataProductBo->num = $item['product_cnt'];
                $logisticsDataProductBo->goods_title = '';
                $logisticsDataProductBo->sku_value = '';
                $product_list[] = $logisticsDataProductBo;
            }
            $logisticsDataBo = new LogisticsDataBo();
            $logisticsDataBo->waybill_code = $logistics_info['waybill_id']; // 运单号
            $logisticsDataBo->wp_code = $logistics_info['delivery_id'];
            $logisticsDataBo->wp_name = $logistics_info['delivery_name'] ?? '';
            $logisticsDataBo->delivery_at = !empty($logistics_info['delivery_time']) ? date('Y-m-d H:i:s', $logistics_info['delivery_time']) : null;
            $logisticsDataBo->delivery_id = '';// 包裹 id
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
        }

        $orderData = [
            "tid" => strval($trade['order_id']), //主订单
            "type" => $this->platformType, //订单类型
            "express_no" => $tradeDetail['delivery_info']['delivery_product_info'][0]['waybill_id'] ?? null, //快递单号
            "buyer_id" => $trade['buyerId'] ?? '', //买家ID
            "buyer_nick" => $trade['buyerNick'] ?? '', //买家昵称
            "seller_nick" => $trade['sellerNick'] ?? null, //卖家昵称
            "order_status" => $this->formatOrderStatus($trade['status']), //订单状态
//            "refund_status" => $trade['aftersale_detail']['on_aftersale_order_cnt'] ?? 0, //退款状态
            "refund_status" => $refund_status, //退款状态
            "receiver_state" => $province_name, //收货人省份
            "receiver_city" => $city_name, //收货人城市
            "receiver_district" => $county_name, //收货人地区
            "receiver_name" => $user_name, //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_zip" => $tradeDetail['delivery_info']['address_info']['postal_code'] ?? "", //收件人邮编
            "receiver_address" => $tradeDetail['delivery_info']['address_info']['detail_info'], //收件人详细地址
            "payment" => formatToYuan($tradeDetail['price_info']['order_price']), //实付金额
            "total_fee" => formatToYuan($tradeDetail['price_info']['product_price']), //总金额
            "discount_fee" => isset($tradeDetail['price_info']['discounted_price']) ? formatToYuan($tradeDetail['price_info']['discounted_price']) : 0, //优惠金额
            "post_fee" => formatToYuan($tradeDetail['price_info']['freight']), //运费
            "seller_flag" => Order::FLAG_NONE, //卖家备注旗帜
            "seller_memo" => empty($tradeDetail['ext_info']['merchant_notes']) ? '[]' : json_encode([$tradeDetail['ext_info']['merchant_notes']], 320), //卖家备注
            "buyer_message" => $tradeDetail['ext_info']['customer_notes'], //买家留言
            "has_buyer_message" => empty($tradeDetail['ext_info']['customer_notes']) ? 0 : 1, //买家留言
            "express_code" => $tradeDetail['delivery_info']['delivery_product_info'][0]['delivery_id'] ??
                null, //快递公司代码
            "order_created_at" => date('Y-m-d H:i:s', $trade['create_time']), //订单创建时间
            "order_updated_at" => date('Y-m-d H:i:s', $trade['update_time']), //订单修改时间
            "send_at" => !empty($sendAt) ? date('Y-m-d H:i:s', $sendAt) : null, //发货时间
            "pay_at" => isset($tradeDetail['pay_info']['pay_time']) ? date('Y-m-d H:i:s', $tradeDetail['pay_info']['pay_time']) : null, //支付时间
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => $is_pre_sale, // 是否预售1是0否
            'promise_ship_at' => !empty($orderDeliveryDeadline) ? date('Y-m-d H:i:s', $orderDeliveryDeadline) : null, // 承诺发货时间
            'items' => $orderItems,
            'order_code' => Arr::get($tradeDetail, 'delivery_info.ewaybill_order_code', null),//订单代码
            'address_md5' => $tradeDetail['hash_code'] ?? '',
            'order_extra' => [
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];
        //是否启用虚拟号码，这个保存到订单对象中，合单需要用真实号码的掩码
        $orderData['isUseVirtualTelPhone'] = $isUseVirtualTelPhone;
        $realTelPhone = $tradeDetail['delivery_info']['address_info']['tel_number'];

        if ($isUseVirtualTelPhone) {
            //使用虚拟号码，就把真实的号码保存到订单对象中，但合单需要用真实号码的掩码
            $orderData['real_tel_phone'] = Arr::get($tradeDetail, 'delivery_info.address_info.tel_number_ext_info.real_tel_number', '');
            $cipher_info = [
                'receiver_phone_ciphertext' => '',
                'receiver_name_ciphertext' => '',
                'receiver_address_ciphertext' => '',
                'receiver_phone_mask' => $tradeDetail['delivery_info']['address_info']['tel_number'],
                'receiver_name_mask' => $user_name,
                'receiver_address_mask' => $tradeDetail['delivery_info']['address_info']['detail_info'],
            ];
        } else {
            $cipher_info = [
                'receiver_phone_ciphertext' => appEncrypt($realTelPhone),
                'receiver_name_ciphertext' => appEncrypt($orderData['receiver_name']),
                'receiver_address_ciphertext' => appEncrypt($orderData['receiver_address']),
                'receiver_phone_mask' => dataDesensitizationForOpenApi($realTelPhone, 3, 4),
                'receiver_name_mask' => dataDesensitizationForOpenApi($orderData['receiver_name'], 1, 0),
                'receiver_address_mask' => addressDesensitization($orderData['receiver_address']),
            ];
        }

        $orderData['cipher_info'] = $cipher_info;
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);
        //主订单退款状态为未退款 同时判断子订单是否有退款 更改主订单为部分退款
        if ($orderData['refund_status'] == Order::REFUND_STATUS_NO && in_array(Order::REFUND_STATUS_YES, array_column($orderItems, 'refund_status'))) {
            $orderData['refund_status'] = Order::REFUND_STATUS_PART;
        }

        //发货状态的时候已锁单 locked_at 为null
        if (in_array($this->formatOrderStatus($trade['status']), [Order::ORDER_STATUS_DELIVERED])) {
            $orderData['locked_at'] = null;
        }

        if (!empty($orderData['pay_at']) && empty($orderData['promise_ship_at'])) {
            $orderData['promise_ship_at'] = date('Y-m-d H:i:s', strtotime('+72 hour', strtotime($orderData['pay_at'])));
        }
        if (empty($orderData['express_no'])) {
            unset($orderData['send_at']);
            unset($orderData['express_no']);
            unset($orderData['express_code']);
        }
        return $orderData;
    }

    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    /**
     * 批量格式化成订单表结构
     * @param array $orders
     * @return array
     * <AUTHOR>
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $index => $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * 批量格式化成商品表结构
     * @param array $goods
     * @return array
     * <AUTHOR>
     */
    public function formatToGoods(array $goods): array
    {
        // TODO: Implement formatToGoods() method.
    }

    /**
     * 获取service_id
     * @return mixed
     * <AUTHOR>
     */
    public function getServiceId()
    {
        if (!empty($this->serviceId)) {
            return $this->serviceId;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权serviceId');
        }
        return $this->serviceId = $shop->service_id;
    }

    /**
     * 获取specification_id
     * @return mixed
     * <AUTHOR>
     */
    public function getSpecificationId()
    {
        if (!empty($this->specificationId)) {
            return $this->specificationId;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权serviceId');
        }
        return $this->specificationId = $shop->specification_id;
    }

    /**
     * 发送获取订单列表请求
     * 通过创建时间或者支付时间查询
     * @param int $startTime
     * @param int $endTime
     * @param int $isFirstPull
     * @return array
     * @throws OrderException
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'update_time_range' => [
                'start_time' => $startTime,
                'end_time' => $endTime
            ],
            'page_size' => $this->pageSize,
        ];
        if ($this->nextKey) {
            $params['next_key'] = $this->nextKey;
        }
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['status'] = 20;
        }

        $response = $client->execute('post', '/channels/ec/order/list/get', $params);
        $this->handleErrorCode($response, $params);
        $list = $this->sendBatchGetOrderInfoByTidArr($response['order_id_list']);
        if ($response['has_more']) {
            $this->hasNext = true;
            $this->nextKey = $response['next_key'];
        } else {
            $this->hasNext = false;
        }

        return $list;
    }

    public function sendBatchGetOrderInfo($orders, $safe = false)
    {
        //并行异步请求
        $data = $result = [];
        $url = '/channels/ec/order/get';
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $validParams = [
                'access_token' => $this->getAccessToken(),
                'service_id' => $this->getServiceId(),
                'specification_id' => $this->getSpecificationId(),
            ];

            $param = ['order_id' => $order['tid']];
            if ($this->isTestShop()) {
                Log::info('获取订单详情接口参数', [$param]);
                $param['encode_sensitive_info'] = true;
            }
            $data[$idStr] = [
                'params' => $param,
                'url' => $this->gatewayUrl . $url . '?' . http_build_query($validParams)
            ];
        }

        $response = $this->poolCurl($data, 'post');
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            if ($this->isTestShop()) {
                Log::info('获取订单详情接口返回', [$orderInfo]);
            }
            $trade = $orderInfo['order'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $result[$orderId] = $trade;
        }

        return $result;
    }

    /**是否是测试店铺
     * @return bool
     * @throws ApiException
     */

    public function isTestShop()
    {
        /**
         * @var Shop $shop
         */
        $shop = $this->getShop();
        return $shop->id == 2;
    }

    /**
     * 批量获取订单详情
     * @param $orderIdList
     * @return array
     * @throws ApiException
     */
    public function sendBatchGetOrderInfoByTidArr($orderIdList)
    {
        //并行异步请求
        $data = $result = [];
        $url = '/channels/ec/order/get';
        foreach ($orderIdList as $orderId) {
            $params = ['order_id' => $orderId];
            if ($this->isTestShop()) {
                $param['encode_sensitive_info'] = true;
                \Log::info('获取订单详情接口参数', [$params]);
            }
            $validParams = [
                'access_token' => $this->getAccessToken(),
                'service_id' => $this->getServiceId(),
                'specification_id' => $this->getSpecificationId(),
            ];
            $data[] = [
                'params' => $params,
                'url' => $this->gatewayUrl . $url . '?' . http_build_query($validParams)
            ];
        }
        $response = $this->poolCurl($data, 'post');
        foreach ($response as $index => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['order'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $result[$index] = $trade;
        }

        return $result;
    }

    /**
     * 发送获取订单详情请求
     * @param string $tid
     * @return mixed
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'order_id' => $tid,
        ];
        if ($this->isTestShop()) {
            $params['encode_sensitive_info'] = true;
            \Log::info('获取订单详情接口参数', [$params]);
        }
        $response = $client->execute('post', '/channels/ec/order/get', $params);

        return $response['order'] ?? [];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * 发送获取商品列表请求
     * @param int $pageSize
     * @param int $currentPage
     * @return mixed
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'page_size' => $pageSize,
            'status' => 5
        ];
        if ($this->nextKey) {
            $params['next_key'] = $this->nextKey;
        }
        $goods = $client->execute('post', '/channels/ec/product/list/get', $params);

        \Log::info('wx_goods_result', [$goods]);
        if (!isset($goods['errcode']) || $goods['errcode'] != 0 || !isset($goods['spus'])) {
            \Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
            return [];
        }
        if (($currentPage * $pageSize) < $goods['total_num']) {
            $this->hasNext = true;
            $this->nextKey = $goods['next_key'];
        }
        return $goods['product_ids'];
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return mixed
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true)
    {
        // TODO: Implement deliverySellerOrders() method.
    }

    /**
     * 重新订单发货
     *
     * @doc https://developers.weixin.qq.com/doc/channels/API/order/deliveryinfo_update.html
     * @param OrderDeliverAgainRequest $orderDeliverAgainRequest
     * @return mixed
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     * @throws OrderException
     */
    protected function deliverySellerOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
//        注意事项
//        发货完成的订单可以修改，最多修改1次；
//        拆包发货的订单暂不允许修改物流；
//        虚拟商品订单暂不允许修改物流。

        $tid = $orderDeliverAgainRequest->tid;
        $expressCode = $orderDeliverAgainRequest->wpCode;
        $expressNo = $orderDeliverAgainRequest->waybillCode;
        $orderItemOId = [];
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $order = Order::query()->with(['orderItem'])->where('tid', $tid)->first();
        $product_infos = [];
        foreach ($order['orderItem'] as $index => $item) {
            $product_infos[] = [
                'product_cnt' => $item['goods_num'],
                'product_id' => $item['num_iid'],
                'sku_id' => $item['sku_id']
            ];
        }
        $delivery_list = [];
        $delivery_list[] = [
            'waybill_id' => $expressNo,
            'delivery_id' => $expressCode,
            'deliver_type' => 1,
            'product_infos' => $product_infos,
        ];
        $params = [
            'order_id' => $tid,
            'delivery_list' => $delivery_list,
        ];

        $response = $client->execute('post', '/channels/ec/order/deliveryinfo/update', $params);
        $this->handleErrorCode($response, $params);
        return true;
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return mixed
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }

    /**
     * 发送获取订单列表请求 订单增量
     * @param int $startTime
     * @param int $endTime
     * @param bool $isFirstPull
     * @return array
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     * @throws OrderException
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'update_time_range' => [
                'start_time' => $startTime,
                'end_time' => $endTime
            ],
            'page_size' => $this->pageSize,
        ];
        if ($this->nextKey) {
            $params['next_key'] = $this->nextKey;
        }
        //第一次仅拉取待发货
        if ($isFirstPull) {
            $params['status'] = 20;
        }

        $response = $client->execute('post', '/channels/ec/order/list/get', $params);
        $this->handleErrorCode($response, $params);
        $list = $this->sendBatchGetOrderInfoByTidArr($response['order_id_list']);
        if ($response['has_more']) {
            $this->hasNext = true;
            $this->nextKey = $response['next_key'];
        } else {
            $this->hasNext = false;
            $this->nextKey = '';
        }

        return $list;
    }

    /**
     * 发送服务订购消息
     * @return mixed
     * <AUTHOR>
     */
    public function sendServiceInfo()
    {
        $shop = $this->getShop();
        $client = $this->getClient();
        $platformAccessToken = $client->getPlatformAccessToken();
        $client->setAccessToken($platformAccessToken);

        $response = $client->getServiceBuyer($platformAccessToken['access_token'], $shop->identifier);
        $ver = collect($response['buyer']['spec_list'])->sortByDesc('expire_time')->first();
        Log::info('wxsp 订购版本data返回', [$response]);
        return [
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'platform_type' => $this->platformType,
            'expired_at' => date('Y-m-d H:i:s', $ver['expire_time']), //暂定免费版使用30天
            'version' => $ver['specification_id'],
            'version_desc' => $ver['specification_id'] == 'free' ? UserExtra::VERSION_MAP_ARR[UserExtra::VERSION_FREE] : UserExtra::VERSION_MAP_ARR[UserExtra::VERSION_STANDARD],
            'pay_amount' => 0,
        ];
    }

    /**
     * 批量解密
     * @param $list
     * @return mixed
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     * @throws OrderException
     */
    public function batchDecrypt($list)
    {
        $result = [];
        \Log::info('wxsp_batch_decrypt', [$list]);

        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'order_id' => $list['tid'],
        ];

        $response = $client->execute('post', '/channels/ec/order/sensitiveinfo/decode', $params);
        $this->handleErrorCode($response, $params);

        $fieldMapping = [
            'receiver_phone' => 'tel_number',
            'receiver_name' => 'user_name',
            'receiver_address' => 'detail_info',
        ];
        foreach ($list as $key => $item) {
            if ($key == 'tid'|| $key == 'oaid') {
                continue;
            }
            $field = $fieldMapping[$key];
            $result[$key] = $response['address_info'][$field];
        }

        return $result;


    }

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sendDecrypt($order)
    {

    }

    /**
     * 批量加密
     * @param $order
     * @return mixed
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * 检查授权状态
     * @return bool
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     * <AUTHOR>
     */
    public function checkAuthStatus(): bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());
        $params = [
            'update_time_range' => [
                'start_time' => time()-1,
                'end_time' => time()
            ],
            'page_size' => 1,
            'status' => 20
        ];
        $goods = $client->execute('post', '/channels/ec/order/list/get', $params);

        try {
            $this->handleErrorCode($goods);
        } catch (ApiException $e) {
            if (ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0] == $e->getCode()) {
                return false;
            }
        } catch (\Exception $e) {

        }
        return true;
    }

    /**
     * 批量发货
     * @param OrderDeliveryRequest[] $orderDeliveryRequests
     * @return CommonResponse[]
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $method = '/channels/ec/order/delivery/send';
        $data = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $expressCode = $orderDeliveryRequest->expressCode;
            $expressNo = $orderDeliveryRequest->expressNo;
            //$orderItemOId = $orderDeliveryRequest->orderItemOId;
            $productInfos = [];
            foreach ($orderDeliveryRequest->order->orderItem as $item) {
                $productInfos[] = [
                    'product_cnt' => $item['goods_num'],
                    'product_id' => $item['num_iid'],
                    'sku_id' => $item['sku_id']
                ];
            }

            $params = [
                'order_id' => $tid,
                'delivery_list' => [
                    [
                        'waybill_id' => !empty($expressNo) ? $expressNo : '',
                        'delivery_id' => !empty($expressCode) ? $this->expressCodeList[strtoupper($expressCode)] :
                            self::OTHER_CODE,
                        'deliver_type' => !empty($expressNo) ? 1 : 3,
                        'product_infos' => $productInfos
                    ]
                ],
            ];

            $validParams = [
                'access_token' => $this->getAccessToken()
            ];
            $data[] = [
                'params' => $params,
                'url' => $this->gatewayUrl . $method . '?' . http_build_query($validParams)
            ];
        }
        $responseData = $this->poolCurl($data, 'post');
        $results = [];


        foreach ($responseData as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);

                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->code = $e->getCode();
                $commonResponse->message = $e->getMessage();
            } finally {
                $results[] = $commonResponse;
            }
        }

        return $results;
    }

    /**
     * 获取客户端
     * @return mixed
     * <AUTHOR>
     */
    protected function getClient()
    {
        $appKey = config('socialite.wx.client_id');
        $secretKey = config('socialite.wx.client_secret');
        return new WxClient($appKey, $secretKey);
    }

    /**
     * 开启订阅消息
     * @return bool
     * <AUTHOR>
     */
    public function openSubscribeMsg(): bool
    {
        return false;
    }

    /**
     * 消费订阅消息
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * 确认订阅消息
     * @param array $idArr
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * 处理订单的订阅消息
     * @param $data
     * @return array
     * <AUTHOR>
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleSubscribeMsg() method.
    }

    /**
     * 创建WebsocketServer服务
     * @return AsyncTcpConnection
     * @throws OrderException
     * <AUTHOR>
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketConnection() method.
    }

    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * 请求售后列表接口
     * @param int $startTime
     * @param int $endTime
     * @return mixed
     * <AUTHOR>
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        // TODO: Implement sendRefundOrders() method.
    }

    /**
     * 格式化退款订单
     * @param $order
     * @return mixed
     * <AUTHOR>
     */
    protected function formatRefundOrder($order)
    {
        // TODO: Implement formatRefundOrder() method.
    }

    /**
     * @throws GuzzleException
     * @throws ApiException
     * @throws ClientException
     */
    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $params = [
            'order_id' => $tid,
            'merchant_notes' => $sellerMemo
        ];

        $result = $client->execute('post', '/channels/ec/order/merchantnotes/update', $params);

        if (isset($result['errcode']) && $result['errcode'] == 0) {
            return true;
        }

        return false;
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());

        $params = [
            'start_create_time' => $beginAt,
            'end_create_time' => $endAt,
            'page_size' => $this->pageSize,
            'page' => $this->getPage(),
        ];

        $result = $client->execute('post', '/product/service/get_order_list', $params);
        \Log::Info('获取店铺订购订单列表  shop_id:' . $this->getShop()->id . ' result:', [$result]);
        if (!isset($result['errcode']) || $result['errcode'] != 0) {
            \Log::error('获取店铺订购服务订单失败！ shop_id：' . $this->getShop()->id);
            return [];
        }

        return $result['service_order_list'];
    }

    /**
     * 请求查询交易订单的 Tid
     * @param array $query_list
     * <AUTHOR>
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * 请求地址列表
     * @return array [{"name":"海南省","code":460000,"parent_code":1,"level":1,"sub":[{"name":"海口市","code":460100,"parent_code":460000,"level":2,"sub":[{"name":"其它区","code":460109,"parent_code":460100,"level":3}]}]}]
     * <AUTHOR>
     */
    public function sendAddress(): array
    {
        // TODO: Implement sendAddress() method.
    }

    public function getQueryTradeOrderId(string $type, string $search)
    {
        if (empty($search)) {
            return [];
        }
        $fieldMapping = [
            'receiver_phone' => 'tel_number_last4',
            'receiver_name' => 'user_name',
        ];
        if ($type == 'receiver_phone') {
            // 取最后4位数
            $search = substr($search, -4);
        }
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $client->setServiceId($this->getServiceId());
        $client->setSpecificationId($this->getSpecificationId());

        $params = [
            'search_condition' => [
                $fieldMapping[$type] => $search
            ],
            'next_key' => '',
            'page_size' => 100,
        ];

        $result = $client->execute('post', '/channels/ec/order/search', $params);
        \Log::info('getQueryTradeOrderId', [$result]);
        $apiTidArr = $result['order_id_list'] ?? [];

        $conditions = [];
        $beginAt = \request()->input('begin_at', '');
        $endAt = \request()->input('end_at', '');
        $timeField = \request()->input('timeField', '');
        if ($timeField && $beginAt && $beginAt) {
            $conditions[] = [$timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $conditions[] = [$timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }

        $shop = $this->getShop();
        $idArr = Order::query()->select('id')
            ->where('shop_id', $shop->id)
            ->where($conditions)
            ->whereIn('tid', $apiTidArr)
//            ->where(function ($query) use ($type, $search, $apiTidArr) {
//                $column = $type == 'receiver_phone'?'receiver_phone_ciphertext':'receiver_name_ciphertext';
//                $query->whereIn('tid', $apiTidArr);
//                $query->orWhereExists(function ($query) use ($search, $column) {
//                    $query->select(DB::raw(1))
//                        ->from('order_cipher_info')
//                        ->where($column, appEncrypt($search))
//                        ->whereRaw('orders.id = order_cipher_info.order_id');
//                });
//            })
            ->get()
            ->pluck('id')
            ->toArray();

        //自由订单
        $customIdArr = CustomizeOrder::query()->select('id')
            ->where('shop_id', $shop->id)
            //->where($conditions)
            ->where($type, $search)
            ->get()
            ->pluck('id')
            ->toArray();

        //return $idArr;
        return array_merge($idArr, $customIdArr);
    }

    private function handleErrorCode($result, $params = [])
    {
        if (!isset($result['errcode'])) {
            throw new OrderException('微信服务异常');
        }
        $errcode = $result['errcode'] ?? 0;
        if ($errcode != 0) {
            \Log::info('WxSpOrderImpl handleErrorCode', compact('result', 'params'));
        }
        switch ($errcode) {
            case 0: // 正常
                break;
            case 109001:
                throw new ApiException(ErrorConst::ORDER_DELIVERED);
//            case 40001: // accesstoken不存在
            case 42001: // accesstoken过期
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                throw new OrderException('微信服务异常：' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }
    }

    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendFactoryShopRoleType() method.
    }

    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getFactoryTradesOrder() method.
    }

    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        switch ($apiMethod) {
            case '/channels/ec/logistics/ewaybill/biz/order/create':
            case '/channels/ec/logistics/ewaybill/biz/order/precreate':
                Log::info('填充订单数据', [$apiParams, $order]);
                $ewaybillOrderCode = Arr::get($order, 'order_code', null);
                $order_cipher_info = $order['order_cipher_info'];
                $mobile = $order_cipher_info['receiver_phone_ciphertext'] ?: $order['receiver_phone'];
                $mobile = OrderUtil::removeVirtualPhoneDashSuffix($mobile);
                $receiverInfo = [
                    'city' => $order['receiver_city'],
                    'county' => $order['receiver_district'],
                    'address' =>!empty($order_cipher_info['receiver_address_ciphertext'])? OrderUtil::removeVirtualPhoneSuffix($order_cipher_info['receiver_address_ciphertext'] ):stripslashes
                    ($order_cipher_info['receiver_address_mask']),
                    'province' => $order['receiver_state'] ?? $order['receiver_province'],
                    'street' => $order['receiver_town'] ?? '无',
                    'mobile' => $mobile,
                    'name' => !empty($order_cipher_info['receiver_name_ciphertext'])?OrderUtil::removeVirtualPhoneSuffix($order_cipher_info['receiver_name_ciphertext']) : $order_cipher_info['receiver_name_mask']
                ];
                $apiParams['receiver'] = $receiverInfo;
                Log::info('填充收件人', [$receiverInfo]);
                $apiParams['ec_order_list'][0]['ewaybill_order_code'] = $ewaybillOrderCode;
                $apiParams['ec_order_list'][0]['ewaybill_order_appid'] = $orderShop->identifier;
                break;
            default:
                break;
        }
        Log::info('fillApiParamByOrder', ["apiMethod" => $apiMethod, "apiParams" => $apiParams, "order" => $order]);
        return $apiParams;
    }

    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $result = $client->execute($requestMethod, $apiMethod, $apiParams);

        return $result;
    }

    public function buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder): string
    {
        $isUseVirtualTelPhone = $order['isUseVirtualTelPhone'];
        $md5_receiver_address = filterZeroChar($isUseVirtualTelPhone ? OrderUtil::removeVirtualPhoneSuffix($order['receiver_address']) : $order['receiver_address']);
        //如果使用了虚拟号，合单的时候，使用真实号码（掩码），否则使用原来的手机号（也就是真实号码）
//        $receiverPhone = $isUseVirtualTelPhone ? $order['real_tel_phone'] : $order['receiver_phone'];
        $receiverPhone = $order['receiver_phone']; // tel_number 也是真实手机号掩码了
        Log::info('buildAddressMd5', [$isUseVirtualTelPhone, $receiverPhone, $md5_receiver_address]);
        if ($isUseVirtualTelPhone && !empty($order['address_md5'])) {
            return $order['address_md5'];
        }
        $addressArr = [
            $order['shop_id'],
            $order['buyer_id'],
            $order['buyer_nick'],
            $order['receiver_state'],
            $order['receiver_city'],
            $order['receiver_district'],
            $md5_receiver_address,
            $isUseVirtualTelPhone ? OrderUtil::removeVirtualPhoneSuffix($order['receiver_name']) : $order['receiver_name'],
            //如果用了虚拟号，要把尾部的[XXXX]去掉
            $receiverPhone,
        ];
        if ($isCrossShopMergeOrder) {
            array_shift($addressArr); // 跨店合单，不需要店铺id
        }

        return md5(implode(',', $addressArr));
    }

    /**
     * 视频号一单多包裹发货
     * @param int $shopId
     * @param array $orderDeliveryRequests
     * @return array
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function orderMultiPackagesDelivery(int $shopId, array $orderDeliveryRequests): array
    {
        $requestData = [];
        $method = '/channels/ec/order/delivery/send';
        $this->poolCurlAbnormalOutputOriginalData = true;
        foreach ($orderDeliveryRequests as $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest['tid'];
            /**
             * 数组的格式是
             * [
             * [
             * "waybillCode"=>"6919675406348588608",
             * "expressCode"=>"xxx",
             *  "packs"=>[
             *      ["oid"=>111,"shippedNum"=>1],
             *  ]
             * ]
             */
            $waybillCodes = [];
            $deliveryList = [];
            foreach ($orderDeliveryRequest['packs'] as $pack) {
                $deliveryItem = [];
                $waybillCode = $pack['waybillCode'];
                $deliveryItem['delivery_id'] = $this->expressCodeList[strtoupper($pack['expressCode'])] ?? self::OTHER_CODE;
                $deliveryItem['waybill_id'] = strval($waybillCode);
                $deliveryItem['deliver_type'] = 1;

                $productInfos = [];

                $expressCode = $pack['expressCode'];
//                $packItem['company_code'] = $this->expressCodeMap[$expressCode] ?? $expressCode;
//                $packItem['shipped_order_info'] = [];
                $packs = [];
                foreach ($pack['goods'] as $goods) {
                    $oid = $goods['oid'];
                    $productInfos[] = [
                        'product_cnt' => $goods['shippedNum'],
                        'product_id' => strval($goods['num_iid']),
                        'sku_id' => strval($goods['sku_id'])
                    ];
                    $packs[] = ["oid" => $oid, "shippedNum" => $goods['shippedNum']];
                }
                $deliveryItem['product_infos'] = $productInfos;
//                $requestItem['pack_list'][] = $packItem;
                $waybillCodes[] = ["waybillCode" => $waybillCode, "expressCode" => $expressCode, "packs" => $packs];
                $deliveryList[] = $deliveryItem;
            }

            $params = [
                'order_id' => $tid,
                'delivery_list' => $deliveryList
            ];

//            $requestItem['company_code'] = $orderDeliveryRequest['expressCode'];
//
//            $shippedOrderInfos = [];
//            foreach ($orderDeliveryRequest['goodsInfos'] as $goodsInfo) {
//                $shippedOrderInfos[] = [
//                    'shipped_order_id' => $goodsInfo['oid'],
//                    'shipped_num' => $goodsInfo['shippedNum'],
//                ];
//            }
//            $requestItem['shipped_order_info'] = $shippedOrderInfos;
            $validParams = [
                'access_token' => $this->getAccessToken()
            ];
            $requestData[$tid] = [
                'waybillCodes' => $waybillCodes,
                'shopId' => $shopId,
                'params' => $params,
                'url' => $this->gatewayUrl . $method . '?' . http_build_query($validParams)
            ];
        }
        \Log::info('视频号拆单发货请求', [$requestData]);
        $responses = $this->poolCurl($requestData, 'post');
        $successes = [];
        $failures = [];


        foreach ($responses as $tid => $response) {
            $request = $requestData[$tid];
            \Log::info('拆单发货', [$tid, $request, $response]);
            //这个地方如果是正常会返回一个stdClass，反之会返回一个数组
            if ($response instanceof \stdClass) {
                $code = $response->errcode;
                $errMsg = $response->errmsg;
            } else {
                $code = $response['errcode'] ?? null;
                $errMsg = $response['errmsg'] ?? null;
            }
            if (isset($code) && $code == 0) {
                \Log::info('拆单发货成功', [$tid, $request, $response]);
                $successes[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $request['waybillCodes']];
            } else {

                //进入了异常情况，返回的是一个数组
                $subMsg = $errMsg ?? '未知';

                \Log::info('拆单发货失败', [$tid, $request, $response]);
                $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $request['waybillCodes'], 'msg' =>
                    $subMsg];
            }
        }
        return ["successes" => $successes, "failures" => $failures];
    }

    private function formatSubRefundStatus($item)
    {
        if ($item['on_aftersale_sku_cnt'] > 0) { // 正在售后/退款流程中的 sku 数量
            return RefundSubStatusConst::MERCHANT_PROCESSING; // 商家处理中
        }
        if ($item['finish_aftersale_sku_cnt'] > 0) { // 完成售后/退款的 sku 数量
            return RefundSubStatusConst::REFUND_COMPLETE; // 退款完成
        }
        return RefundSubStatusConst::NONE;
    }


}
