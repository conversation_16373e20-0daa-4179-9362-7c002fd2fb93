<?php

namespace App\Services\Order\Impl;

use App\Constants\RefundSubStatusConst;
use App\Exceptions\ApiException;
use App\Models\Order;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Client\AlbbClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\Request\OrderDeliveryRequest;
use App\Utils\ArrayUtil;
use App\Utils\DateTimeUtil;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class AlbbOrderImpl extends AbstractOrderService
{
    /**
     * 平台类型
     * @var string
     */
    protected $platformType = Shop::PLATFORM_TYPE_ALBB;
    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 30;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 30;

    protected $orderStatusMap = [
        'waitbuyerpay' => Order::ORDER_STATUS_PADDING,
        'waitsellersend' => Order::ORDER_STATUS_PAYMENT,
        'waitlogisticstakein' => Order::ORDER_STATUS_DELIVERED,
        'waitbuyerreceive' => Order::ORDER_STATUS_DELIVERED,
        'waitbuyersign'=>Order::ORDER_STATUS_DELIVERED,
        'send_goods_but_not_fund'=>Order::ORDER_STATUS_DELIVERED, // 已发货，未到账
        'signinfailed'=>Order::ORDER_STATUS_DELIVERED, // 签收失败
        'signinsuccess' => Order::ORDER_STATUS_RECEIVED,
        'confirm_goods' => Order::ORDER_STATUS_RECEIVED,
        'cancel'=> Order::ORDER_STATUS_CLOSE,
        'success' => Order::ORDER_STATUS_SUCCESS,
        'terminated' => Order::ORDER_STATUS_FAILED,
        'confirm_goods_but_not_fund' => Order::ORDER_STATUS_SUCCESS, // 已收货，未到账
        'confirm_goods_and_has_subsidy' => Order::ORDER_STATUS_SUCCESS, // 已收货，已贴现
//        101 => Order::ORDER_STATUS_PART_DELIVERED,
    ];
    protected $refundStatusMap = [
        0 => Order::REFUND_STATUS_NO,//售后初始化
        'WAIT_SELLER_AGREE' => Order::REFUND_STATUS_YES, // (退货) 退货中-用户申请
        7 => Order::REFUND_STATUS_YES, //售后退货中
        27 => Order::REFUND_STATUS_NO, //拒绝售后申请
        'REFUND_SUCCESS' => Order::REFUND_STATUS_YES, //售后成功
        28 => Order::REFUND_STATUS_NO, //售后失败(取消售后)
        'WAIT_BUYER_SEND' => Order::REFUND_STATUS_YES, //售后已发货
        'WAIT_SELLER_RECEIVE' => Order::REFUND_STATUS_YES, //退货后拒绝退款
        13 => Order::REFUND_STATUS_YES, //售后换货商家发货
        14 => Order::REFUND_STATUS_YES, //售后换货用户收货
        'REFUND_CLOSED' => Order::REFUND_STATUS_NO, //取消成功
        53 => Order::REFUND_STATUS_YES, //逆向交易完成
        'WAIT_BUYER_MODIFY' => Order::REFUND_STATUS_YES, //买家修改申请
    ];
    protected $orderFlagMap = [
        '' => Order::FLAG_GRAY,
        1 => Order::FLAG_RED,
        2 => Order::FLAG_BLUE,
        3 => Order::FLAG_GREEN,
        4 => Order::FLAG_YELLOW,
    ];
    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        $orderItems = [];
        $promiseShipAt = [];
//        Log::info("formatToOrder", [$trade]);
        $baseInfo = $trade['baseInfo'];
        $tid = $baseInfo['id'];
//        if (empty($baseInfo['id'])){
//            throw new BusinessException(ErrorCode::ORDER_DATA_NOT_COMPLETE);
//        }
        $orderStatus = $this->formatOrderStatus($baseInfo['status']);
//
        foreach ($trade['productItems'] as $item) {
            $total = $item['itemAmount'];
            $skuList = [];
            if (!empty($item['skuInfos'])){
                foreach ($item['skuInfos'] as $desc) {
                    $skuList[] = ['name'=>$desc['name'], 'value'=>$desc['value']];
                }
            }
            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);

            $status = $this->formatOrderStatus($item['status']);
            $refundStatus = $this->hasRefundStatus($item['refundStatus'] ?? 0);
            $goodsPic = $item['productImgUrl'][1] ?? "";
            $orderItem = [
                "tid" => $tid, //主订单
                "oid" => (string)$item['subItemID'], //子订单号
                "type" => $this->platformType, //订单类型
                "orders_status" => $status, //订单状态
                "payment" => $total, //实付金额
                "total_fee" => $total, //总金额
                "discount_fee" => bcsub($item['price'], $total), //优惠金额
                "goods_pic" => $goodsPic, //商品图片
                "goods_title" => $item['name'], //商品标题
                "goods_price" => $item['price'], //商品单价
                "goods_num" => $item['quantity'], //商品数量
                "num_iid" => $item['productID'], //商品id
                "sku_pic" => $goodsPic, //sku图片
                "sku_id" => $item['skuID'] ?? '', //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "outer_iid" => $item['productCargoNumber'] ?? '', //商家外部商品编码,商家编码code
                "outer_sku_iid" => $item['cargoNumber'] ?? '', // 商家外部sku编码
                // 转换  20230721164607000+0800 到 2023-07-21 16:46:07
                "order_created_at" => DateTimeUtil::fromTimezoneToDateTimeString($item['gmtCreate']),
                "order_updated_at" => DateTimeUtil::fromTimezoneToDateTimeString($item['gmtModified']),
                "status" => $status,
                "refund_status" => $refundStatus, //
                "refund_sub_status" => $this->formatSubRefundStatus($item), //
                'send_at' =>  null,//发货时间
                'is_comment' =>  0, // 是否评价
            ];
            //子订单发货时间
//            if ($item['exp_ship_time']) {
//                $promiseShipAt[] = $item['exp_ship_time'];
//            }
            $orderItems[] = $orderItem;
        }
        !empty($promiseShipAt) && sort($promiseShipAt);
//
//        //主订单退款状态
        $refund_status = in_array(Order::REFUND_STATUS_YES, array_column($orderItems, 'refund_status')) ? Order::REFUND_STATUS_YES : Order::REFUND_STATUS_NO;

//        //判读部分退款还是全部退款 有一个未退款就是部分退款(主订单状态是退款或者子订单中有退款 并且有未退款的子订单)
        if (($refund_status == Order::REFUND_STATUS_YES || in_array(Order::REFUND_STATUS_YES, array_column($orderItems, 'refund_status'))) && in_array(Order::REFUND_STATUS_NO, array_column($orderItems, 'refund_status'))) {
            $refund_status = Order::REFUND_STATUS_PART;
        }
        //部分发货且部分退款 修正订单状态
//        if ($orderStatus == OrderConst::ORDER_STATUS_PART_DELIVERED) {
//            $noOrderItem = collect($orderItems)->whereIn('status', [OrderConst::ORDER_STATUS_PAYMENT, OrderConst::ORDER_STATUS_FAILED, OrderConst::ORDER_STATUS_CLOSE]);
//            $hasNoRefund = collect($noOrderItem)->where('refund_status', 0);
//            //剩下未发货子订单 全部退款了 修正订单状态为已发货
//            if (count($hasNoRefund) == 0) {
//                $orderStatus = OrderConst::ORDER_STATUS_DELIVERED;
//            }
//            //部分发货主订单上没有发货时间 取子订单最后发货时间
////            $shipTime = $this->getShipTime($order['sku_order_list']);
//
//        }

        $orderRateInfo = $trade['orderRateInfo'];

        $receiverInfo = $trade['nativeLogistics'];
        $city = $receiverInfo['city'] ?? '';
        $receiver_name = $receiverInfo['contactPerson'];
        $receiver_address =$receiverInfo['address'];
        $receiverState = $receiverInfo['province'] ?? '';
        $receiverTown = $receiverInfo['town'] ?? '';
        $receiverZip = $receiverInfo['zip'] ?? '';
        $receiver_phone = $receiverInfo['mobile'];
        $receiver_phone = implode('_', [$city, $receiverState, $receiverTown, $receiver_phone]);
        $cipher_info = [
            'receiver_phone_ciphertext' => '',
            'receiver_name_ciphertext' => '',
            'receiver_address_ciphertext' => '',
            'receiver_phone_mask' => $receiverInfo['mobile'] ?? '',
            'receiver_name_mask' => $receiverInfo['contactPerson'],
            'receiver_address_mask' => $receiverInfo['address'],
            'oaid' => $receiverInfo['caid']??'',
        ];
        // 包裹信息
        $logistics_data = [];
        $logisticsItems = $trade['nativeLogistics']['logisticsItems'] ?? [];
        foreach ($logisticsItems as $logistics_info) {
            $subItemIdArr = explode(',', $logistics_info['subItemIds']);
            $product_list = [];
            foreach ($subItemIdArr as $itemId) {
                $orderItem = collect($orderItems)->where('oid', $itemId)->first();
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $orderItem['oid'];
                $logisticsDataProductBo->sku_id = $orderItem['sku_id'];
                $logisticsDataProductBo->outer_sku_id = $orderItem['outer_sku_iid'];
                $logisticsDataProductBo->num_iid = $orderItem['num_iid'];
                $logisticsDataProductBo->num = $orderItem['goods_num'];
                $logisticsDataProductBo->goods_title = $orderItem['goods_title'];
                $logisticsDataProductBo->sku_value = $orderItem['sku_value'];;
                $product_list[] = $logisticsDataProductBo;
            }
            $logisticsDataBo = new LogisticsDataBo();
            $logisticsDataBo->waybill_code = $logistics_info['logisticsBillNo']; // 运单号
            $logisticsDataBo->wp_code = $logistics_info['logisticsCompanyNo'];
            $logisticsDataBo->wp_name = $logistics_info['logisticsCompanyName'];
            $logisticsDataBo->delivery_at =  DateTimeUtil::fromTimezoneToDateTimeString($logistics_info['deliveredTime']);
            $logisticsDataBo->delivery_id = $logistics_info['id'];// 包裹 id
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
        }
//        if (empty($receiverInfo['mobile'])){
//            var_dump(json_encode($orderOriginal));
//        }
//
        $orderData = [
            "tid" => $tid, //主订单
            "type" => $this->platformType, //订单类型
//            "order_biz_type" => Order::ORDER_BIZ_TYPE_PT_ORDER, //订单业务类型
//            "express_code" => array_search($trade['logistics_id'], $this->expressCodeList) ?: null , //快递公司代码
//            "express_no" => array_get($trade, 'logistics_code', null), //快递单号
            "buyer_id" => $baseInfo['buyerOpenUid'], //买家ID
            "buyer_nick" => $baseInfo['buyerLoginId'], //买家昵称
            "seller_nick" => $baseInfo['sellerLoginId'], //卖家昵称
            "shop_title" =>'', //$order['shop_name'], //卖家昵称
            "order_status" => $orderStatus, //订单状态
            "after_sales_status" => $refund_status, //退款状态
            "receiver_state" => $receiverState, //收货人省份
            "receiver_city" => $city, //收货人城市
            "receiver_district" => $receiverInfo['area'] ?? '', //收货人地区
            "receiver_town" => $receiverTown, //收货人街道
            "receiver_name" => $receiver_name, //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_zip" => $receiverZip, //收件人邮编
            "receiver_address" => $receiver_address, //收件人详细地址
            "payment" => $baseInfo['totalAmount'], //实付金额
            "total_fee" => $baseInfo['totalAmount'], //总金额
            "discount_fee" => formatToYuan($baseInfo['discount'] ?? 0), //优惠金额
            "post_fee" => $baseInfo['shippingFee'], //运费
            //"seller_flag" => Order::FLAG_NONE, //卖家备注旗帜
            "seller_flag" => $this->formatOrderFlag($baseInfo['sellerRemarkIcon']??''), //卖家备注旗帜
            "seller_memo" => empty($baseInfo['sellerMemo']) ? '[]' : json_encode([$baseInfo['sellerMemo']], 320), //卖家备注
            "buyer_message" => $baseInfo['buyerFeedback'] ?? null,//$order['buyer_words'], //买家留言
            "has_buyer_message" => empty($baseInfo['buyerFeedback']) ? 0 : 1, //买家留言
            "order_created_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['createTime']), //订单创建时间
            "order_updated_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['modifyTime']), //订单修改时间
            "send_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['allDeliveredTime']??null),
            "pay_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['payTime']??null), //支付时间
            'is_comment' => $orderRateInfo['buyerRateStatus'] == 4 ? Order::IS_COMMENT_YES : 0, // 是否评价
            'goods_total_num' => array_sum(array_column($orderItems, 'sku_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => 0, // 是否预售1是0否
            'promise_ship_at' => null,// 承诺发货时间

//            'district_code' => $trade['post_addr']['town']['id']??0,
            'items' => $orderItems,
            'cipher_info' => $cipher_info,
            'order_extra' => [
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];

        //可能为空
        if (empty($orderData['express_code']) || empty($orderData['express_no'])) {
            unset($orderData['express_code']);
            unset($orderData['express_no']);
        }

//        var_dump($orderData);
        return $orderData;
    }

    /**
     * @return int
     */
    public function getPage(): int
    {
        return $this->page;
    }


    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function formatToGoods(array $goods): array
    {
        // TODO: Implement formatToGoods() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $client = $this->getClient();
        $params = [
            'modifyStartTime' => DateTimeUtil::toDateTimeTimezoneString($startTime),
            'modifyEndTime' => DateTimeUtil::toDateTimeTimezoneString($endTime),
            'page' => $this->getPage(),
            'pageSize' => $this->pageSize,
            // 业务类型，支持： "cn"(普通订单类型), "ws"(大额批发订单类型), "yp"(普通拿样订单类型), "yf"(一分钱拿样订单类型),
            // "fs"(倒批(限时折扣)订单类型), "cz"(加工定制订单类型), "ag"(协议采购订单类型), "hp"(伙拼订单类型), "gc"(国采订单类型),
            // "supply"(供销订单类型), "nyg"(nyg订单类型), "factory"(淘工厂订单类型), "quick"(快订下单), "xiangpin"(享拼订单),
            // "nest"(采购商城-鸟巢), "f2f"(当面付), "cyfw"(存样服务), "sp"(代销订单标记), "wg"(微供订单),
            // "factorysamp"(淘工厂打样订单), "factorybig"(淘工厂大货订单)
//            'bizTypes' => ['cn'],
        ];
        $method = 'com.alibaba.trade:alibaba.trade.ec.getOrderList.sellerView';
        $result = $client->execute($method, $params);
        $client::handleResp($result);

        if (count($result['result']) >= $this->pageSize) {
            $this->hasNext = true;
        }
        $this->pageTotal = $result['totalRecord'] ?? -1;
        if (empty($result['result'])) {
            return [];
        }
        $tidArr = Arr::pluck($result['result'], 'baseInfo.id');
        $orderList = $this->sendBatchGetOrderInfoByTidArr($tidArr);

        return $orderList;
    }

    /**
     * @inheritDoc
     */
    protected function sendGetOrderInfo(string $tid)
    {

        $arr = $this->sendBatchGetOrderInfoByTidArr([$tid]);
        return $arr[0] ?? [];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        // TODO: Implement sendGetGoods() method.
    }

    /**
     * @inheritDoc
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true)
    {
        // TODO: Implement deliverySellerOrders() method.
    }



    /**
     * @inheritDoc
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $client = $this->getClient();
        $params = [
            'modifyStartTime' => DateTimeUtil::toDateTimeTimezoneString($startTime),
            'modifyEndTime' => DateTimeUtil::toDateTimeTimezoneString($endTime),
            'page' => $this->getPage(),
            'pageSize' => $this->pageSize,
            // 业务类型，支持： "cn"(普通订单类型), "ws"(大额批发订单类型), "yp"(普通拿样订单类型), "yf"(一分钱拿样订单类型),
            // "fs"(倒批(限时折扣)订单类型), "cz"(加工定制订单类型), "ag"(协议采购订单类型), "hp"(伙拼订单类型), "gc"(国采订单类型),
            // "supply"(供销订单类型), "nyg"(nyg订单类型), "factory"(淘工厂订单类型), "quick"(快订下单), "xiangpin"(享拼订单),
            // "nest"(采购商城-鸟巢), "f2f"(当面付), "cyfw"(存样服务), "sp"(代销订单标记), "wg"(微供订单),
            // "factorysamp"(淘工厂打样订单), "factorybig"(淘工厂大货订单)
//            'bizTypes' => ['cn'],
        ];
        $method = 'com.alibaba.trade:alibaba.trade.ec.getOrderList.sellerView';
        $result = $client->execute($method, $params);
        $client::handleResp($result);

        if (count($result['result']) >= $this->pageSize) {
            $this->hasNext = true;
        }
        $this->pageTotal = $result['totalRecord'] ?? -1;
        if (empty($result['result'])) {
            return [];
        }
        $tidArr = Arr::pluck($result['result'], 'baseInfo.id');
        $orderList = $this->sendBatchGetOrderInfoByTidArr($tidArr);

        return $orderList;
    }
    public function sendBatchGetOrderInfo($orders, $safe = false)
    {
        return $this->sendBatchGetOrderInfoByTidArr(Arr::pluck($orders, 'tid'),$safe);
    }
    public function sendBatchGetOrderInfoByTidArr($tidArr, $safe = false)
    {
        $client = $this->getClient();
        $paramsArr = [];
        $apiMethod = 'com.alibaba.trade:alibaba.trade.ec.getOrder.sellerView';

        foreach ($tidArr as $tid) {
            $params = [
                'orderId' => intval($tid),
                'access_token'=>$this->accessToken
            ];

            $paramsArr[] = [
                'tid' => $tid,
                "url" => $client->getApiFullUrl($apiMethod),
                "params" => $client->buildRequestData($params, $apiMethod)
            ];
        }
        $resultArr = array();
//        Log::info("batchGetOrderInfo", [$paramsArr]);
        $orderInfoResponses=$this->poolCurl($paramsArr, "post_form");
        foreach ($orderInfoResponses as $index=>$orderInfoResponse){
            try {
                $response = $client::handleResp($orderInfoResponse);

//                Log::info("batchGetOrderInfo", [$response]);
                $resultArr[] = Arr::get($response, 'result', []);

            }catch (ApiException $e){
                Log::errorException("获取订单信息失败", $e);
                Log::warning("获取订单信息失败",["tid"=>$paramsArr[$index]['tid']]);
            }
            catch (\Throwable $e){
                $resultArr[$index] = [];
            }
        }
        return $resultArr;

    }

    /**
     * @inheritDoc
     */
    public function sendServiceInfo()
    {
        $shop = $this->getShop();
        $param=[
            "aliId"=> $shop->identifier,
            'gmtCreate'=> DateTimeUtil::strNow('Ymdhisv')."+0800"  //这个值取当前时间到毫秒的字符串  加上时区平台返回一个月内的数据
        ];
        $client = $this->getClient();
        $result = $client->execute("cn.alibaba.open:app.order.get", $param);
        Log::info("阿里巴巴订阅信息",$result);
        //把返回的数据全部存起来，然后再到数据库里面去查找当前的时间段的数据

        $isvOrderItems = Arr::get($result, 'returnValue', []);
        $isvOrderItem = collect($isvOrderItems)->sortByDesc('gmtServiceEnd')->first();
        $result = [
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'platform_type' => $this->platformType,
            'expired_at' => $isvOrderItem['gmtServiceEnd']? DateTimeUtil::format(DateTimeUtil::fromDateTimeTimezone
            ($isvOrderItem['gmtServiceEnd'])): DateTimeUtil::strEndOfDay(),
            'version' => UserExtra::VERSION_PROFESSIONAL,
            'version_desc' => UserExtra::VERSION_MAP_ARR[UserExtra::VERSION_PROFESSIONAL],
            'pay_amount' => 0,
        ];
        return $result;
    }

    /**
     * @inheritDoc
     */
    protected function sendDecrypt($order)
    {
        // TODO: Implement sendDecrypt() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        try {
            $this->sendGetTradesOrders(time()-1,time());
        }catch (\Exception $e){
            return false;
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $method = 'com.alibaba.logistics:alibaba.logistics.OpDeliverySendOrder.offline';
        $client = $this->getClient();
        $orderDeliveryRequestArr = json_decode(json_encode($orderDeliveryRequests), true);
        $orderItemList = \App\Models\Fix\OrderItem::whereIn('tid', Arr::pluck($orderDeliveryRequestArr, 'tid'))->get();
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
//            $orderItemOId = $orderDeliveryRequest->orderItemOId;
            $tmpOrderItems = $orderItemList->where('tid', $orderDeliveryRequest->tid)->all();
            $sendGoodEntries = [];
            foreach ($tmpOrderItems as $tmpOrderItem) {
                $sendGoodEntries[] = [
                    'sourceEntryId' => $tmpOrderItem->oid,
                    'amount' => $tmpOrderItem->goods_num,
                    'weight' => 0,
                ];
            }
            $sendGoods = [];
            $sendGoods[] = [
                'sourceId' => $orderDeliveryRequest->tid,
                'sendGoodEntries' => $sendGoodEntries,
            ];
            $params = [
                'sendGoods'=>json_encode($sendGoods,JSON_UNESCAPED_UNICODE),
                'extBody'=>json_encode(['cpCode'=>$orderDeliveryRequest->expressCode,'logisticsCpName'=>$orderDeliveryRequest->expressCode,'mailNo'=>$orderDeliveryRequest->expressNo]),
            ];
            $requestData[$index] = [
                'params' => $client->buildRequestData($params, $method),
                'url' => $client->getApiFullUrl($method),
            ];

        }
//        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'post_form');
        $responseArr = json_decode(json_encode($responses), true);
        Log::info('batchDeliveryOrders poolCurl',[$requestData, $responseArr]);
        $results = [];
        foreach ($requestData as $index => $request) {
            $commonResponse = new CommonResponse();
            try {
                $result = $responseArr[$index] ?? null;
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
//                $result = json_decode(json_encode($result), true);
                $client->handleResp($result);

                if ($result['success']){
                    $commonResponse->setSuccess(true);
                }else{
                    $commonResponse->setSuccess(false);
                    $commonResponse->setMessage( '发货失败:'.json_encode($result));
                }
            } catch (\Throwable $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @inheritDoc
     */
    public function orderMultiPackagesDelivery(int $shopId, array $orderDeliveryRequests): array
    {
        $method = 'com.alibaba.logistics:alibaba.logistics.OpDeliverySendOrder.offline';
        $client = $this->getClient();
        $requestData = [];
        $requestParamsArr = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            // 一个订单多个运单号
            foreach ($orderDeliveryRequest['packs'] as $waybill) {
                $sendGoodEntries = [];
                foreach ($waybill['goods'] as $pack) {
                    $sendGoodEntries[] = [
                        'sourceEntryId' => $pack['oid'],
                        'amount' =>  $pack['shippedNum'],
                        'weight' => 0,
                    ];
                }
                $sendGoods = [];
                $sendGoods[] = [
                    'sourceId' => $orderDeliveryRequest['tid'],
                    'sendGoodEntries' => $sendGoodEntries,
                ];
                $waybillCode = $waybill['waybillCode'];
                $expressCode = $waybill['expressCode'];
                $params = [
                    'sendGoods' => json_encode($sendGoods, JSON_UNESCAPED_UNICODE),
                    'extBody' => json_encode(['cpCode' => $expressCode, 'logisticsCpName' => $expressCode, 'mailNo' => $waybillCode]),
                ];
                $requestData[$index] = [
                    'params' => $client->buildRequestData($params, $method),
                    'url' => $client->getApiFullUrl($method),
                ];

                $requestParamsArr[$index] = [
                    'tid' => $orderDeliveryRequest['tid'],
                    'waybillCodes' => [
                        [
                            'packs' => $waybill['goods'],
                            'waybillCode' => $waybillCode,
                            'expressCode' => $expressCode,
                        ]
                    ]
                ];
            }
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $successes = [];
        $failures = [];
        foreach ($responses as $index => $response) {
            $requestParams = $requestParamsArr[$index];
            $tid = $requestParams['tid'];
            Log::info('albb split delivery response', [$requestParams, $response]);
            $waybillCodes = $requestParams['waybillCodes'];
            try {
                $client->handleResp($response);
                $response = json_decode(json_encode($response), true);
//                $waybillCodes = $this->generateWaybillCodes($request['waybills']);
                if ($response['success']){
                    $successes[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes];
                }else{
                    $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => '发货失败:'.json_encode($response)];
                }
            } catch (\Exception $e) {
                //进入了异常情况，返回的是一个数组
                $subMsg = $e->getMessage();
                Log::info('albb split delivery error', [$tid, $requestParams, $response]);
                $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => $subMsg];
            }
        }
        return ["successes" => $successes, "failures" => $failures];
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        return AlbbClient::newInstance($this->getAccessToken());
    }

    /**
     * @inheritDoc
     */
    public function openSubscribeMsg():bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketConnection() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        // TODO: Implement sendRefundOrders() method.
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        // TODO: Implement formatRefundOrder() method.
    }

    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo):bool
    {
        $client = $this->getClient();
        if (empty($sellerFlag) || $sellerFlag == Order::FLAG_GRAY){
            // 一定要给个颜色，没办法，给个中性的蓝色
            $sellerFlag = Order::FLAG_BLUE;
        }
        // 备忘图标，目前仅支持数字。1位红色图标，2为蓝色图标，3为绿色图标，4为黄色图标
        $flag = array_flip($this->orderFlagMap)[$sellerFlag] ?? '';
        $params = [
            'orderId' => $tid,
            'memo' => $sellerMemo,
            'remarkIcon' => $flag,
        ];
        $method = 'com.alibaba.trade:alibaba.order.memoAdd';
        $result = $client->execute($method, $params);
        Log::info('sendEditSellerRemark', [$method,$params,$result]);
        $client::handleResp($result);
        return true;
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendFactoryShopRoleType() method.
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getFactoryTradesOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $client = $this->getClient();
        $data = $this->requestAddressList($client);
        Log::info('address',[$data]);
        return $data;
    }

    private function requestAddressList($client, $level = 1, $parentCode = 1): array
    {
        if ($level > 4){
            return [];
        }
        $params = [
            'webSite'=>'1688',
        ];
        $parentCode != 1 && $params['areaCode'] = $parentCode;
        $response = $client->execute('com.alibaba.trade:alibaba.trade.addresscode.getchild', $params);
        $client->handleResp($response);
        $list = [];
        Log::info('result',[$level,$parentCode,$response['result']]);
        if (empty($response['result'])){
            return $list;
        }

        foreach ($response['result'] as $index => $item) {
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parentCode,
                'level' => $level,
                'sub' => $this->requestAddressList($client, $level+1, $item['code'])
            ];
            $list[] = $arr;
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function getQueryTradeOrderId(string $type, string $search)
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        switch ($apiMethod) {
            case 'cainiao.waybill.ii.get';
                if (empty($apiParams['param_waybill_cloud_print_apply_new_request']) || !is_string($apiParams['param_waybill_cloud_print_apply_new_request'])) {
                    return $apiParams;
                }
                $request = json_decode($apiParams['param_waybill_cloud_print_apply_new_request'], true);
                if (empty($request['trade_order_info_dtos']) || !is_array($request['trade_order_info_dtos'])){
                    return $apiParams;
                }
                if (empty($request['trade_order_info_dtos'][0])){
                    throw new \Exception('trade_order_info_dtos 值应为数组');
                }
                foreach ($request['trade_order_info_dtos'] as $index => $trade_order_info_dto) {
                    //收件人信息
                    $receiver =[
                        "address"=> [
                            "city"=> $order['receiver_city'],
                            "detail"=> $order['receiver_address'],
                            "district"=> $order['receiver_district'],
                            "province"=> $order['receiver_state'],
                            "town"=> ""
                        ],
                        "mobile"=> '',
                        "name"=> $order['order_cipher_info']['receiver_name_mask'],
                        "tid"=> $order['tid'],
                        "caid"=> $order['order_cipher_info']['oaid'],

                    ];
                    Log::info('fillApiParamByOrder1', [$receiver,$order]);
                    $request['trade_order_info_dtos'][$index]['recipient'] = $receiver;
                }
                $apiParams['param_waybill_cloud_print_apply_new_request'] = json_encode($request);
                break;

            default:
                break;
        }
        Log::info('fillApiParamByOrder2', [$apiParams]);
        return $apiParams;
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $client = $this->getClient();
        $apiParams['access_token']=$this->accessToken; //补上access_token
        Log::info("sendByCustom", [$apiMethod, $apiParams, $requestMethod]);
        return $client->execute($apiMethod, $apiParams, $requestMethod);
    }

    private function formatSubRefundStatus($item)
    {
        // WAIT_SELLER_AGREE 等待卖家同意 REFUND_SUCCESS 退款成功 REFUND_CLOSED 退款关闭 WAIT_BUYER_MODIFY 待买家修改 WAIT_BUYER_SEND 等待买家退货 WAIT_SELLER_RECEIVE 等待卖家确认收货
        $status =  RefundSubStatusConst::NONE;
        if (empty($item['refundStatus'])){
            return $status;
        }
        switch ($item['refundStatus']) {
            case 'WAIT_SELLER_AGREE': // 待卖家同意
                $status = RefundSubStatusConst::MERCHANT_PROCESSING;
                break;
            case 'REFUND_SUCCESS': // 退款成功
                $status = RefundSubStatusConst::REFUND_COMPLETE;
                break;
            case 'REFUND_CLOSED': // 退款关闭
                $status = RefundSubStatusConst::REFUND_CLOSE;
                break;
            case 'WAIT_BUYER_MODIFY': // 待买家修改
                $status = RefundSubStatusConst::MERCHANT_REFUSE_REFUND;
                break;
            case 'WAIT_BUYER_SEND': // 待买家发货
                $status = RefundSubStatusConst::WAIT_BUYER_RETURN;
                break;
            case 'WAIT_SELLER_RECEIVE': // 待卖家收货
                $status = RefundSubStatusConst::RETURN_WAIT_MERCHANT;
                break;

        }
        return $status;
    }
}
