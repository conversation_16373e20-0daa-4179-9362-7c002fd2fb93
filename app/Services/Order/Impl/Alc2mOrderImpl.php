<?php

namespace App\Services\Order\Impl;

use App\Constants\RefundSubStatusConst;
use App\Events\Subscription\SubscriptionOrderSucceeded;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Models\Order;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Services\Client\Alc2mClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Utils\DateTimeUtil;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class Alc2mOrderImpl extends AbstractOrderService
{
    /**
     * 平台类型
     * @var string
     */
    protected $platformType = Shop::PLATFORM_TYPE_ALC2M;
    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 30;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 30;
    const ORDER_DETAIL_API_METHOD = 'com.alibaba.trade:alibaba.ascp.trade.sellerView.get';

    protected $pageSize = 20;
    protected $orderStatusMap = [
        '' => Order::ORDER_STATUS_CLOSE,
        'waitsellersend' => Order::ORDER_STATUS_PAYMENT,
        'waitbuyerreceive' => Order::ORDER_STATUS_DELIVERED,
        'cancel' => Order::ORDER_STATUS_CLOSE,
        'success' => Order::ORDER_STATUS_SUCCESS,
//        101 => Order::ORDER_STATUS_PART_DELIVERED,
    ];

    protected $refundStatusMap = [
        '' => Order::REFUND_STATUS_NO,//售后初始化
        0 => Order::REFUND_STATUS_NO,//售后初始化
        'WAIT_SELLER_AGREE' => Order::REFUND_STATUS_YES, // (退货) 退货中-用户申请
        7 => Order::REFUND_STATUS_YES, //售后退货中
        27 => Order::REFUND_STATUS_NO, //拒绝售后申请
        'REFUND_SUCCESS' => Order::REFUND_STATUS_YES, //售后成功
        'REFUND_END' => Order::REFUND_STATUS_PART, //售后成功
        28 => Order::REFUND_STATUS_NO, //售后失败(取消售后)
        'WAIT_BUYER_SEND' => Order::REFUND_STATUS_YES, //售后已发货
        'WAIT_SELLER_RECEIVE' => Order::REFUND_STATUS_YES, //退货后拒绝退款
        13 => Order::REFUND_STATUS_YES, //售后换货商家发货
        14 => Order::REFUND_STATUS_YES, //售后换货用户收货
        'REFUND_CLOSED' => Order::REFUND_STATUS_NO, //取消成功
        53 => Order::REFUND_STATUS_YES, //逆向交易完成
        'WAIT_BUYER_MODIFY' => Order::REFUND_STATUS_YES, //买家修改申请
    ];

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        $orderItems = [];
        $promiseShipAt = [];
        Log::info("formatToOrder", [$trade]);
        $baseInfo = $trade['baseInfo'];
        $tid = $baseInfo['id'];
//        if (empty($baseInfo['id'])) {
//            throw new BusinessException(ErrorCode::ORDER_DATA_NOT_COMPLETE);
//        }
        $orderStatus = $this->formatOrderStatus($baseInfo['status']);
//
        foreach ($trade['productItems'] as $item) {
            $itemAmount = formatToYuan($item['itemAmount']); ///实付金额，分转元
            $skuList = [];
            if (!empty($item['skuInfos'])){
                foreach ($item['skuInfos'] as $desc) {
                    $skuList[] = ['name'=>$desc['name'], 'value'=>$desc['value']];
                }
            }
            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);
            $status = $this->formatOrderStatus($item['status']);
            $refundStatus = $this->hasRefundStatus($item['refundStatus'] ?? '');
            $goodsPic = $item['productImgUrl'][0] ?? "";
            $orderItem = [
                "tid" => $tid, //主订单
                "oid" => (string)$item['subItemID'], //子订单号
                "type" => $this->platformType, //订单类型
                "orders_status" => $status, //订单状态
                "payment" => $itemAmount, //实付金额
                "total_fee" => $itemAmount, //总金额
                "discount_fee" => 0, //优惠金额
                "goods_pic" => $goodsPic, //商品图片
                "goods_title" => $item['name'], //商品标题
                "goods_price" => 0, //商品单价
                "goods_num" => $item['quantity'], //商品数量
                "num_iid" => $item['productID'], //商品id
                "sku_pic" => $goodsPic, //sku图片
                "sku_id" => $item['skuID'] ?? '', //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "outer_iid" => $item['productCargoNumber'] ?? '', //商家外部商品编码,商家编码code
                "outer_sku_iid" => $item['cargoNumber'] ?? '', // 商家外部sku编码
                // 转换  20230721164607000+0800 到 2023-07-21 16:46:07
                "order_created_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['createTime']),
                "order_updated_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['modifyTime']),
                "status" => $status,
                "refund_status" => $refundStatus, //
                "refund_sub_status" => $this->formatSubRefundStatus($item), //
                'send_at' => null,//发货时间
                'is_comment' => 0, // 是否评价
            ];
            //子订单发货时间
//            if ($item['exp_ship_time']) {
//                $promiseShipAt[] = $item['exp_ship_time'];
//            }
            $orderItems[] = $orderItem;
        }
        !empty($promiseShipAt) && sort($promiseShipAt);
//
//        //主订单退款状态
        $refund_status = in_array(Order::REFUND_STATUS_YES, array_column($orderItems, 'refund_status')) ? Order::REFUND_STATUS_YES : Order::REFUND_STATUS_NO;

//        //判读部分退款还是全部退款 有一个未退款就是部分退款(主订单状态是退款或者子订单中有退款 并且有未退款的子订单)
        if (($refund_status == Order::REFUND_STATUS_YES || in_array(Order::REFUND_STATUS_YES, array_column($orderItems, 'refund_status'))) && in_array(Order::REFUND_STATUS_NO, array_column($orderItems, 'refund_status'))) {
            $refund_status = Order::REFUND_STATUS_PART;
        }
        //部分发货且部分退款 修正订单状态
//        if ($orderStatus == PtOrderConst::ORDER_STATUS_PART_DELIVERED) {
//            $noOrderItem = collect($orderItems)->whereIn('status', [PtOrderConst::ORDER_STATUS_PAYMENT, PtOrderConst::ORDER_STATUS_FAILED, PtOrderConst::ORDER_STATUS_CLOSE]);
//            $hasNoRefund = collect($noOrderItem)->where('refund_status', 0);
//            //剩下未发货子订单 全部退款了 修正订单状态为已发货
//            if (count($hasNoRefund) == 0) {
//                $orderStatus = PtOrderConst::ORDER_STATUS_DELIVERED;
//            }
//            //部分发货主订单上没有发货时间 取子订单最后发货时间
////            $shipTime = $this->getShipTime($order['sku_order_list']);
//
//        }


        $receiverInfo = $baseInfo['buyerContact'];
        $city = $receiverInfo['city'] ?? '';
        $receiver_name = $receiverInfo['name'];
        $receiver_address = $receiverInfo['address'];
        $receiverState = $receiverInfo['province'] ?? '';
        $receiverTown = $receiverInfo['town'] ?? '';
        $receiverZip = $receiverInfo['zip'] ?? '';
        $receiver_phone = $receiverInfo['mobile'];
        $receiver_phone = implode('_', [$city, $receiverState, $receiverTown, $receiver_phone]);
        $cipher_info = [
            'receiver_phone_ciphertext' => '',
            'receiver_name_ciphertext' => '',
            'receiver_address_ciphertext' => '',
            'receiver_phone_mask' => $receiverInfo['mobile'] ?? '',
            'receiver_name_mask' => $receiverInfo['name'],
            'receiver_address_mask' => $receiverInfo['address'],
            'oaid' => $receiverInfo['oaid'] ?? '',
        ];
//        if (empty($receiverInfo['mobile'])){
//            var_dump(json_encode($orderOriginal));
//        }
//
        $orderData = [
            "tid" => $tid, //主订单
            "type" => $this->platformType, //订单类型
//            "order_biz_type" => Order::, //订单业务类型
//            "express_code" => array_search($trade['logistics_id'], $this->expressCodeList) ?: null , //快递公司代码
//            "express_no" => array_get($trade, 'logistics_code', null), //快递单号
            "buyer_id" => '',//$baseInfo['buyerOpenUid'], //买家ID
            "buyer_nick" =>'',// $baseInfo['buyerLoginId'], //买家昵称
            "seller_nick" => '', //卖家昵称
            "shop_title" => $baseInfo['ascpSupplierName']??'', //卖家昵称
            "order_status" => $orderStatus, //订单状态
            "after_sales_status" => $refund_status, //退款状态
            "receiver_state" => $receiverState, //收货人省份
            "receiver_city" => $city, //收货人城市
            "receiver_district" => $receiverInfo['area'] ?? '', //收货人地区
            "receiver_town" => $receiverTown, //收货人街道
            "receiver_name" => $receiver_name, //收货人名字
            "receiver_phone" => $receiver_phone, //收货人手机
            "receiver_zip" => $receiverZip, //收件人邮编
            "receiver_address" => $receiver_address, //收件人详细地址
            "payment" => formatToYuan($baseInfo['totalAmount']), //实付金额
            "total_fee" => formatToYuan($baseInfo['totalAmount']), //总金额
            "discount_fee" =>0,// formatToYuan($baseInfo['discount'] ?? 0), //优惠金额
            "post_fee" => formatToYuan($baseInfo['shippingFee']), //运费
            //"seller_flag" => Order::FLAG_NONE, //卖家备注旗帜
            "seller_flag" => '', //卖家备注旗帜
            "seller_memo" => null,//empty($order['seller_words']) ? '[]' : json_encode([$order['seller_words']], 320),//卖家备注
            "buyer_message" => $baseInfo['remark'] ?? null,//$order['buyer_words'], //买家留言
            "has_buyer_message" => empty($baseInfo['remark']) ? 0 : 1, //买家留言
            "order_created_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['createTime']), //订单创建时间
            "order_updated_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['modifyTime']), //订单修改时间
            "send_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['allDeliveredTime'] ?? ''),
            "pay_at" => DateTimeUtil::fromTimezoneToDateTimeString($baseInfo['payTime']), //支付时间
            'is_comment' => 0,//$orderRateInfo['buyerRateStatus'] == 4 ? Order::IS_COMMENT_YES : 0, // 是否评价
            'goods_total_num' => array_sum(array_column($orderItems, 'sku_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => 0, // 是否预售1是0否
            'promise_ship_at' => null,// 承诺发货时间

//            'district_code' => $trade['post_addr']['town']['id']??0,
            'items' => $orderItems,
            'cipher_info' => $cipher_info,
        ];

        //可能为空
        if (empty($orderData['express_code']) || empty($orderData['express_no'])) {
            unset($orderData['express_code']);
            unset($orderData['express_no']);
        }

//        var_dump($orderData);
        return $orderData;
    }


    public function formatToAfterSale(array $trade)
    {
        // TODO: Implement formatToAfterSale() method.
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function formatToGoods(array $goods): array
    {
        // TODO: Implement formatToGoods() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $client = $this->getClient();
        $this->hasNext = false;
        $params = [
            'modifyStartTime' => DateTimeUtil::toDateTimeTimezoneString($startTime),
            'modifyEndTime' => DateTimeUtil::toDateTimeTimezoneString($endTime),
            'page' => $this->page,
            'pageSize' => $this->pageSize,
        ];
        $result = $client->execute('com.alibaba.trade:alibaba.ascp.trade.sellerView.getList', $params, 'post_form');
        if (empty($result['result'])) {
            return [];
        }
        if (count($result['result']) >= $this->pageSize) {
            $this->hasNext = true;
        }
        $this->setOrderTotalCount($result['result']['totalRecord'] ?? -1);

        return (array)$result['result'];
    }

    /**
     * @inheritDoc
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $res = $this->sendBatchGetOrderInfo([['tid' => $tid]]);
        return array_first($res);
    }

    public function sendBatchGetOrderInfo($orders, $safe = false)
    {
        $client = $this->getClient();
        //并行异步请求
        $result = [];
        $paramsArr = [];
        $method = 'com.alibaba.trade:alibaba.ascp.trade.sellerView.get';
        foreach ($orders as $order) {
            $tid = $order['tid'];
            $params = [
                'orderId' => intval($tid),
                'access_token' => $this->accessToken
            ];

            $paramsArr[] = [
                'tid' => $tid,
                "url" => $client->getApiFullUrl($method),
                "params" => $client->buildRequestData($params, $method)
            ];
        }

        $response = $this->poolCurl($paramsArr, 'post_form', 5, $safe);
        Log::info('sendBatchGetOrderInfo', [$response]);
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['result'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $result[$orderId] = $trade;
        }

        return $result;
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {
        // TODO: Implement sendGetAfterSaleOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetGoods(int $pageSize, int $currentPage)
    {
        // TODO: Implement sendGetGoods() method.
    }

    /**
     * @inheritDoc
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId, bool $silent = true)
    {
        // TODO: Implement deliverySellerOrders() method.
    }


    /**
     * @inheritDoc
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        // TODO: Implement deliverySellerOrdersForOpenApi() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $client = $this->getClient();
        $this->hasNext = false;
        $params = [
            'modifyStartTime' => DateTimeUtil::toDateTimeTimezoneString($startTime),
            'modifyEndTime' => DateTimeUtil::toDateTimeTimezoneString($endTime),
            'page' => $this->page,
            'pageSize' => $this->pageSize,
        ];
        $result = $client->execute('com.alibaba.trade:alibaba.ascp.trade.sellerView.getList', $params,'post_form');
        if (empty($result['result'])) {
            return [];
        }
        if (count($result['result']) >= $this->pageSize) {
            $this->hasNext = true;
        }
        $this->setOrderTotalCount($result['totalRecord'] ?? -1);

        return (array)$result['result'];

    }

    /**
     * @inheritDoc
     * @return array
     * @throws ApiException
     * @throws ErrorCodeException
     */
    public function sendServiceInfo()
    {
        $shop = $this->getShop();
        $shopId = $shop->id;
        $userId = $shop->user_id;
        $subscriptionServiceInfo = PlatformOrder::getServiceInfo($shop);
        Log::info('获取订阅服务信息', ['shop_id' => $shopId, 'subscription' => $subscriptionServiceInfo]);
        if (!$subscriptionServiceInfo) {
            Log::info('首次授权，初始化一个免费版本', ['shop_id' => $shopId, 'user_id' => $userId]);
            $platformOrder = PlatformOrder::initFreeVersion($shopId, $userId);
            event(new SubscriptionOrderSucceeded($platformOrder));
            $subscriptionServiceInfo = PlatformOrder::getServiceInfo($shop);
        }
        //因为会出现代下单的情况，就是platformOrder里面的user和shop里面的userId不一致，所以这里要重新获取
        $subscriptionServiceInfo->userId = $userId;
        return $subscriptionServiceInfo->toArray();
    }

    public function batchDecrypt($list)
    {
        $client = $this->getClient();
        $params = [
            'orderId' => $list['tid'],
            'oaid' => $list['oaid'],
            'scene' => '1006',
        ];
        $result = $client->execute('com.alibaba.trade:alibaba.ascp.trade.sellerView.decyrptRecevier', $params);
        $client::handleResp($result);
        Log::debug('batchDecrypt', [$result]);
        $resultData = Arr::get($result, 'result.ascpTradeContact',[]);
        if (empty($resultData)) {
            return [];
        }
        $arr = [
            'name'=>'receiver_name',
            'mobile'=>'receiver_phone',
            'address'=>'receiver_address',
        ];
        $returnArr = [];
        foreach ($arr as $index => $item) {
            $returnArr[$item] = $resultData[$index];
        }
        return $returnArr;
    }

    /**
     * @inheritDoc
     */
    protected function sendDecrypt($order)
    {

        return [];
    }

    /**
     * @inheritDoc
     */
    protected function sendBatchEncrypt($order)
    {
        // TODO: Implement sendBatchEncrypt() method.
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        return true;
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $method = 'com.alibaba.trade:alibaba.ascp.logistics.offline.send';
        $client = $this->getClient();
        $orderDeliveryRequestArr = json_decode(json_encode($orderDeliveryRequests), true);
        $orderItemList = \App\Models\Fix\OrderItem::whereIn('tid', Arr::pluck($orderDeliveryRequestArr, 'tid'))->get();

        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tmpOrderItems = $orderItemList->where('tid', $orderDeliveryRequest->tid)->all();
            $sendGoodEntries = [];
            foreach ($tmpOrderItems as $tmpOrderItem) {
                $sendGoodEntries[] = [
                    'sourceEntryId' => $tmpOrderItem->oid,
                ];
            }
            $ascpSendPackages = [
                [
                    'cpCode' => $orderDeliveryRequest->expressCode,
                    'mailNo' => $orderDeliveryRequest->expressNo,
                ]
            ];
            $params = [
                'sendGoodEntries' => json_encode($sendGoodEntries, JSON_UNESCAPED_UNICODE),
                'ascpSendPackages' => json_encode($ascpSendPackages, JSON_UNESCAPED_UNICODE),
                'sourceId' => $orderDeliveryRequest->tid,
            ];
            $requestData[$index] = [
                'params' => $client->buildRequestData($params, $method),
                'url' => $client->getApiFullUrl($method),
            ];

        }
//        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'post_form');
        $responseArr = json_decode(json_encode($responses), true);
        Log::info('batchDeliveryOrders poolCurl',[$requestData, $responseArr]);
        $results = [];
        foreach ($requestData as $index => $request) {
            $commonResponse = new CommonResponse();
            try {
                $result = $responseArr[$index] ?? null;
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
//                $result = json_decode(json_encode($result), true);

                $client->handleResp($result);
                if ($result['result']['success']){
                    $commonResponse->setSuccess(true);
                }else{
                    $commonResponse->setSuccess(false);
                    $commonResponse->setMessage( '发货失败:'.json_encode($result));
                }
            } catch (\Throwable $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }
    /**
     * @inheritDoc
     */
    public function orderMultiPackagesDelivery(int $shopId, array $orderDeliveryRequests): array
    {
        $method = 'com.alibaba.trade:alibaba.ascp.logistics.offline.send';
        $client = $this->getClient();
        $requestData = [];
        $requestParamsArr = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            // 一个订单多个运单号
            foreach ($orderDeliveryRequest['packs'] as $waybill) {
                $sendGoodEntries = [];
                foreach ($waybill['goods'] as $pack) {
                    $sendGoodEntries[] = [
                        'sourceEntryId' => $pack['oid'],
                    ];
                }
                $ascpSendPackages = [
                    [
                        'cpCode' => $waybill['expressCode'],
                        'mailNo' => $waybill['waybillCode'],
                    ]
                ];
                $params = [
                    'sendGoodEntries' => json_encode($sendGoodEntries, JSON_UNESCAPED_UNICODE),
                    'ascpSendPackages' => json_encode($ascpSendPackages, JSON_UNESCAPED_UNICODE),
                    'sourceId' => $orderDeliveryRequest['tid'],
                ];
                $requestData[$index] = [
                    'params' => $client->buildRequestData($params, $method),
                    'url' => $client->getApiFullUrl($method),
                ];

                $requestParamsArr[$index] = [
                    'tid' => $orderDeliveryRequest['tid'],
                    'waybillCodes' => [
                        'packs' => $waybill['goods'],
                        'waybillCode' =>  $waybill['waybillCode'],
                        'expressCode' => $waybill['expressCode'],
                    ]
                ];
            }
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $successes = [];
        $failures = [];
        foreach ($responses as $index => $response) {
            $requestParams = $requestParamsArr[$index];
            $tid = $requestParams['tid'];
            Log::info('c2m split delivery response', [$requestParams, $response]);
            $waybillCodes = $requestParams['waybillCodes'];
            try {
                $client->handleResp($response);
                $response = json_decode(json_encode($response), true);
//                $waybillCodes = $this->generateWaybillCodes($request['waybills']);
                if ($response['success']){
                    $successes[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes];
                }else{
                    $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => '发货失败:'.json_encode($response)];
                }
            } catch (\Exception $e) {
                //进入了异常情况，返回的是一个数组
                $subMsg = $e->getMessage();
                Log::info('c2m split delivery error', [$tid, $requestParams, $response]);
                $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => $subMsg];
            }
        }
        return ["successes" => $successes, "failures" => $failures];
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        return Alc2mClient::newInstance($this->accessToken);
    }

    /**
     * @inheritDoc
     */
    public function openSubscribeMsg():bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketConnection() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * @inheritDoc
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        // TODO: Implement sendRefundOrders() method.
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        // TODO: Implement formatRefundOrder() method.
    }

    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo):bool
    {
        // TODO: Implement sendEditSellerRemark() method.
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendFactoryShopRoleType() method.
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getFactoryTradesOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $client = $this->getClient();
        $data = $this->requestAddressList($client);
        Log::debug('address',[$data[0]]);
        return $data;
    }

    private function requestAddressList($client, $level = 1, $parentCode = 1): array
    {
        if ($level > 4){
            return [];
        }
        $params = [
            'webSite'=>'1688',
        ];
        $parentCode != 1 && $params['areaCode'] = $parentCode;
        $response = $client->execute('com.alibaba.trade:alibaba.trade.addresscode.getchild', $params);
        $client->handleResp($response);
        $list = [];
        Log::info('result',[$level,$parentCode,$response['result']]);
        if (empty($response['result'])){
            return $list;
        }

        foreach ($response['result'] as $index => $item) {
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parentCode,
                'level' => $level,
                'sub' => $this->requestAddressList($client, $level+1, $item['code'])
            ];
            $list[] = $arr;
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function getQueryTradeOrderId(string $type, string $search)
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        return $apiParams;
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $client = $this->getClient();
        Log::info("sendByCustom", [$apiMethod, $apiParams, $requestMethod]);
        return $client->execute($apiMethod, $apiParams, $requestMethod);
    }

    private function formatSubRefundStatus($item)
    {
        if (empty($item['refundStatus'])){
            return RefundSubStatusConst::NONE;
        }
        // WAIT_SELLER_AGREE 等待卖家同意 REFUND_SUCCESS 退款成功 REFUND_CLOSED 退款关闭 WAIT_BUYER_MODIFY 待买家修改 WAIT_BUYER_SEND 等待买家退货 WAIT_SELLER_RECEIVE 等待卖家确认收货
        switch ($item['refundStatus']) {
            case 'WAIT_SELLER_AGREE':
                $status = RefundSubStatusConst::MERCHANT_PROCESSING;
                break;
            case 'REFUND_SUCCESS':
                $status = RefundSubStatusConst::REFUND_COMPLETE;
                break;
            case 'REFUND_CLOSED':
                $status = RefundSubStatusConst::REFUND_CLOSE;
                break;
            case 'WAIT_BUYER_MODIFY':
                $status = RefundSubStatusConst::MERCHANT_REFUSE_REFUND;
                break;
            case 'WAIT_BUYER_SEND':
                $status = RefundSubStatusConst::WAIT_BUYER_RETURN;
                break;
            case 'WAIT_SELLER_RECEIVE':
                $status = RefundSubStatusConst::RETURN_WAIT_MERCHANT;
                break;
            default:
                $status = RefundSubStatusConst::NONE;
                break;
        }
        return $status;

    }
}
