<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 19:46
 */

namespace App\Services\Order\Impl;


use AlibabaAscpLogisticsConsignModifyRequest;
use AlibabaAscpLogisticsConsignResendRequest;
use AlibabaAscpLogisticsOfflineSendRequest;
use App\Constants\ErrorConst;
use App\Constants\RefundSubStatusConst;
use App\Constants\TaobaoConst;
use App\Events\BaseRequestEvent;
use App\Events\Event;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\SqlLogEvent;
use App\Events\Users\UserLoginEvent;
use App\Exceptions\ApiException;
use App\Exceptions\OrderException;
use App\Exceptions\RequestRetryException;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Client\TbClient;
use App\Services\Client\YchClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OrderCipherInterface;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Services\PlatformOrder\PlatformOrderServiceManager;
use App\Services\Waybill\Taobao\TaoBaoApi;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\ArrayUtil;
use App\Utils\WaybillUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use LogisticsTraceGetRequest;
use SecretNoExtendRequest;
use TopClient\request\CustomRequest;
use TopClient\request\RefundsReceiveGetRequest;
use TopClient\request\TmcMessagesConfirmRequest;
use TopClient\request\TmcMessagesConsumeRequest;
use TopClient\request\TmcUserPermitRequest;
use TopClient\request\TradeFullinfoGetRequest;
use TopClient\request\TradesSoldGetRequest;
use TopClient\TopClient;
use TopConsignGoodsRequest;
use TopConsignPkgRequest;
use TopSecretExtendRequest;
use TradeMemoUpdateRequest;
use TradesSoldIncrementGetRequest;
use VasOrderSearchRequest;
use VasSubscSearchRequest;

class TaobaoOrderImpl extends AbstractOrderService implements OrderCipherInterface
{
    /**
     * @see https://open.taobao.com/doc.htm?docId=102856&docType=1
     * @var array
     */
    protected $orderStatusMap;
    /**
     * @var string
     */
    private $orderInfoFields = "tid,
        buyer_nick,
        buyer_open_uid,
        seller_nick,
        status,
        title,
        receiver_state,
        receiver_city,
        receiver_district,
        receiver_town,
        receiver_name,
        receiver_mobile,
        receiver_phone,
        receiver_zip,
        receiver_address,
        seller_flag,
        has_buyer_message,
        payment,
        total_fee,
        discount_fee,
        post_fee,
        service_orders,
        combine_logistics_details,
        orders,
        collect_time,
        delivery_time,
        pay_time,
        consign_time,
        est_con_time,
        logistics_consign_info,
        logistics_consign_info.related_id,
        logistics_consign_info.consign_time,
        logistics_consign_info.render_consign_time,
        delivery_plan,
        delivery_plan.order_id,
        delivery_plan.ship_time_begin,
        created,
        modified,
        end_time,
        buyer_message,
        seller_memo";

    public function __construct()
    {
        parent::__construct();
        $this->orderStatusMap = TaobaoConst::$orderStatusMap;
        $this->refundStatusMap = TaobaoConst::$refundStatusMap;
    }

    protected $refundStatusMap;

    protected $orderFlagMap = [
        1 => Order::FLAG_RED,
        2 => Order::FLAG_YELLOW,
        3 => Order::FLAG_GREEN,
        4 => Order::FLAG_BLUE,
        5 => Order::FLAG_PURPLE,
        6 => Order::FLAG_ORANGE,
        7 => Order::FLAG_CYAN,
        8 => Order::FLAG_PINK,
        9 => Order::FLAG_DARK_GREEN,
        10 => Order::FLAG_ROSE_RED,
        -1 => Order::FLAG_GRAY,  //有淘宝订单返回了-1，这里做个容错
        0 => Order::FLAG_GRAY, // 反转的时候替代-1
    ];


    protected $expressCodeMap = array(
        'LE32538030' => 'DISTRIBUTOR_31365136',
        '100005492' => 'DISTRIBUTOR_13204867',
        'SNWL' => 'DISTRIBUTOR_13452378',
        'CP570969' => 'DISTRIBUTOR_13503931',
        'LE14027000' => 'DISTRIBUTOR_30752655',
        '21000026002' => 'DISTRIBUTOR_13365751',
        'CN7000001003751' => 'DISTRIBUTOR_13211725',
        '100004928' => 'BJRFD-001',
        '2383545689_32' => 'DISTRIBUTOR_13323734',
        'CP468398' => 'DISTRIBUTOR_13469985',
        '2608021499_235' => 'DISTRIBUTOR_12017865',
        '100007887' => 'DISTRIBUTOR_13460212',
        'CP443514' => 'DISTRIBUTOR_13468073',
        'CP449455' => 'DISTRIBUTOR_13462882',
        'LE10032270' => 'DISTRIBUTOR_30506953',
        'CP446881' => 'DISTRIBUTOR_13468071',
        'LE09252050' => 'DISTRIBUTOR_30464910',
        'LE21354850' => 'DISTRIBUTOR_31071684',
        'CN7000001009020' => 'DISTRIBUTOR_13327526',
        '100002392' => 'DISTRIBUTOR_13211787',
        '3108002701_1011' => 'DISTRIBUTOR_13222803',
        'CN7000001000869' => 'DISTRIBUTOR_12017865',
        'CN7000001021040' => 'DISTRIBUTOR_13421750',
        'CN7000001017817' => 'DISTRIBUTOR_13422944',
        'LE33128010' => 'DISTRIBUTOR_31375544',
        'CP471906' => 'DISTRIBUTOR_13484485',
        'CN7000001028572' => 'DISTRIBUTOR_13509344',
        'LE14066700' => 'DISTRIBUTOR_30764443',
    );


    protected $platformType = Shop::PLATFORM_TYPE_TAOBAO;

    /**
     * 每次拉取订单间隔的分钟
     * 淘宝最大间隔是三个月
     * 设置一个月 43200
     * @var int
     */
    public $orderTimeInterval = 60;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60;


    public function formatToAfterSale(array $trade)
    {

    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        $orderItems = [];
        $waybillCodeMap = [];
        $orderMap = [];
        $logistics_data = [];
        //用于其他包裹，无法识别是哪些子订单发的
        $productList = [];
        foreach ($trade['orders']['order'] as $index => $order) {
            $orderMap[$order['oid']] = $order;
            //子订单发过货，记录发货包裹数据
            if (!empty($order['consign_time']) && !empty($order['invoice_no'])) {
                if (!empty($waybillCodeMap[$order['invoice_no']])) {
                    //已经存在code了
                    $logisticsDataBo = $waybillCodeMap[$order['invoice_no']];
                } else {
                    $logisticsDataBo = new LogisticsDataBo();
                    $logisticsDataBo->waybill_code = $order['invoice_no']; // 运单号
                    $logisticsDataBo->wp_code = '';
                    $logisticsDataBo->wp_name = $order['logistics_company'];
                    $logisticsDataBo->delivery_at = $order['consign_time'];
                    $logisticsDataBo->product_list = [];
                }
                $logisticsDataProductBo = new LogisticsDataProductBo();
                $logisticsDataProductBo->oid = $order['oid'];
                $logisticsDataProductBo->sku_id = $order['sku_id'] ?? '';
                $logisticsDataProductBo->outer_sku_id = $order['outer_sku_id'] ?? '';
                $logisticsDataProductBo->num_iid = $order['num_iid'];
                $logisticsDataProductBo->num = $order['num'];
                $logisticsDataProductBo->goods_title = $order['title'];
                $logisticsDataProductBo->sku_value = $order['sku_properties_name'] ?? '';
                $logisticsDataBo->product_list = array_merge([$logisticsDataProductBo], $logisticsDataBo->product_list);
                $waybillCodeMap[$order['invoice_no']] = $logisticsDataBo;
            }
            $productBo = new LogisticsDataProductBo();
            $productBo->oid = $order['oid'];
            $productBo->sku_id = $order['sku_id'] ?? '';
            $productBo->outer_sku_id = $order['outer_sku_id'] ?? '';
            $productBo->num_iid = $order['num_iid'];
            $productBo->num = $order['num'];
            $productBo->goods_title = $order['title'];
            $productBo->sku_value = $order['sku_properties_name'] ?? '';
            $productList[] = $productBo;

            $status = $this->formatOrderStatus($order['status']);
            $send_num = 0;
            // 状态已发货，数量就记商品数量
            if (in_array($status,Order::ORDER_STATUS_DELIVERED_ARRAY)){
                $send_num = $order['num'];
            }
            if (!empty($order['estimate_con_time']) && !empty($trade['pay_time'])){ // 付款后30天内
                preg_match('/付款后(\d+)天内/', $order['estimate_con_time'], $match);
                $day = $match[1] ?? 0;
                $promise_ship_at = date('Y-m-d H:i:s',strtotime("+$day day", strtotime($trade['pay_time'])));
            }else{
                $promise_ship_at = !empty($trade['pay_time'])?date('Y-m-d H:i:s', strtotime('+48 hour', strtotime($trade['pay_time']))) : null; // 承诺发货时间
            }
            $sku_value_str = $order['sku_properties_name'] ?? ''; //sku值  颜色分类:白色;参考身高:建议130cm-140cm
            $skuValueArr = explode(';', $sku_value_str);
            $skuList = [];
            foreach ($skuValueArr as $item) {
                if (empty($item)) {
                    continue;
                }
                $itemArr = explode(':', $item);
                $skuList[] = ['name'=>$itemArr[0], 'value'=>$itemArr[1]];
            }
            list($skuValue, $skuValue1, $skuValue2) = $this->getSkuValueAnd12($skuList);

            $orderItems[] = [
                "tid" => $trade['tid'], //主订单
                "oid" => $order['oid'], //子订单号
                "type" => $this->platformType, //订单类型
                "payment" => $order['payment'], //实付金额
                "total_fee" => $order['total_fee'], //总金额
                "discount_fee" => $order['discount_fee'], //优惠金额
                "goods_pic" => $order['pic_path'] ?? '', //商品图片
                "goods_title" => $order['title'], //商品标题
                "goods_price" => $order['price'], //商品单价
                "send_num" => $send_num, //商品数量
                "goods_num" => $order['num'], //商品数量
                "num_iid" => $order['num_iid'], //商品id
                "sku_id" => $order['sku_id'] ?? '', //sku id
                "sku_value" => $skuValue,
                "sku_value1" => $skuValue1,
                "sku_value2" => $skuValue2,
                "outer_iid" => $order['outer_iid'] ?? '', //商家外部商品编码
                "outer_sku_iid" => $order['outer_sku_id'] ?? '', //商家外部sku编码
                "order_created_at" => $trade['created'], //订单创建时间
                "order_updated_at" => $trade['modified'], //订单修改时间
                "refund_id" => $order['refund_id'] ?? 0, //订单修改时间
                "send_at" => $order['consign_time'] ?? null, //发货时间
                "status" => $status,//子订单状态
                "promise_ship_at" => $promise_ship_at, // 承诺发货时间
                'refund_status' => empty($order['refund_status']) ? Order::REFUND_STATUS_NO : $this->formatRefundStatus($order['refund_status']),
                'refund_sub_status' => $this->formatRefundSubStatus($order),
            ];
        }
        //这是拆单的情况
        if (!empty($trade['combine_logistics_details'])) {
            //先将拆单的包裹按照invoice_no 分组
            collect($trade['combine_logistics_details']['combine_logistics_detail'])->groupBy('invoice_no')->each(function ($item, $key) use ($trade, $orderMap, &$waybillCodeMap) {
                if (!empty($waybillCodeMap[$key])) {
                    //orders.order里面已经有了这里的运单号
                    $logisticsDataBo = $waybillCodeMap[$key];
                    foreach ($item as $waybillItem) {
                        $amount = 0;
                        foreach ($waybillItem['send_goods_details']['send_goods_detail'] as $productInfo) {
                            $amount += $productInfo['amount'] ?? 0;
                        }
                        //判断运单号对应的子订单是否已经在$waybillCodeMap中存在
                        $existList = collect($logisticsDataBo->product_list)->filter(function ($obj) use ($waybillItem) {
                            return $obj['oid'] == $waybillItem['sub_order_id'];
                        })->toArray();

                        //如果子订单不在，则加入进去
                        if (empty($existList)) {
                            $order = $orderMap[$waybillItem['sub_order_id']];
                            $logisticsDataProductBo = new LogisticsDataProductBo();
                            $logisticsDataProductBo->oid = $waybillItem['sub_order_id'];
                            $logisticsDataProductBo->sku_id = $order['sku_id'];
                            $logisticsDataProductBo->outer_sku_id = $order['outer_sku_id'];
                            $logisticsDataProductBo->num_iid = $order['num_iid'];
                            $logisticsDataProductBo->goods_title = $order['title'];
                            $logisticsDataProductBo->sku_value = $order['sku_properties_name'];
                            $logisticsDataProductBo->num = $amount ?? $order['num'];
                            $logisticsDataBo->product_list = array_merge([$logisticsDataProductBo], $logisticsDataBo->product_list);
                            $waybillCodeMap[$order['invoice_no']] = $logisticsDataBo;
                            continue;
                        }
                        //子订单存在，则更新数量
                        if ($amount > 0) {
                            foreach ($logisticsDataBo->product_list as &$obj) {
                                if ($obj['oid'] == $waybillItem['sub_order_id'] && $amount != $obj['num']) {
                                    $obj['num'] = $amount;
                                }
                            }
                            $waybillCodeMap[$key] = $logisticsDataBo;
                        }
                    }
                } else {
                    //orders里面并没有
                    $logisticsDataBo = new LogisticsDataBo();
                    $logisticsDataBo->waybill_code = $key; // 运单号
                    $logisticsDataBo->wp_code = '';
                    $logisticsDataBo->wp_name = $item[0]['logistics_company'];
                    foreach ($item as $waybillItem) {
                        $amount = 0;
                        foreach ($waybillItem['send_goods_details']['send_goods_detail'] as $productInfo) {
                            $amount += $productInfo['amount'] ?? 0;
                        }
                        $order = $orderMap[$waybillItem['sub_order_id']];
                        $logisticsDataProductBo = new LogisticsDataProductBo();
                        $logisticsDataProductBo->oid = $waybillItem['sub_order_id'];
                        $logisticsDataProductBo->sku_id = $order['sku_id'] ?? '';
                        $logisticsDataProductBo->outer_sku_id = $order['outer_sku_id'] ?? '';
                        $logisticsDataProductBo->num_iid = $order['num_iid'];
                        $logisticsDataProductBo->goods_title = $order['title'];
                        $logisticsDataProductBo->sku_value = $order['sku_properties_name'] ?? '';
                        $logisticsDataProductBo->num = $amount ?? $order['num'];
                        $logisticsDataBo->product_list = array_merge([$logisticsDataProductBo], $logisticsDataBo->product_list);
                    }
                    $waybillCodeMap[$key] = $logisticsDataBo;
                }
            });
        }
        $logistics_data = array_values($waybillCodeMap);
        if (!empty($trade['existWaybills'])) {
            //不在已发货包裹里的需要删除
            $logistics_data = collect($logistics_data)->whereIn("waybill_code", collect($trade['existWaybills'])->pluck("waybillCode")->toArray())->toArray();
            //得到平台已经发货的包裹
            foreach ($trade['existWaybills'] as $waybill) {
                if (collect($logistics_data)->where("waybill_code", $waybill['waybillCode'])->count() > 0) {
                    //如果订单里已经有了，忽略
                    continue;
                }
                //没有的话，加进去
                $logisticsData = new LogisticsDataBo();
                $logisticsData->waybill_code = $waybill['waybillCode']; // 运单号
                $logisticsData->wp_code = '';
                $logisticsData->wp_name = $waybill['wpName'];
                $logisticsData->delivery_at = $waybill['deliveryAt'];
                $logisticsData->product_list = $productList;
                $logistics_data[] = $logisticsData;
            }
        }

        $sellerMemo = empty($trade['seller_memo']) ? [] : [$trade['seller_memo']];

        if (isset($trade['buyer_message'])) {
            $has_buyer_message = !empty($trade['buyer_message']) ? 1 : 0;
        } else {
            $has_buyer_message = 0;
        }
        if (empty($trade['oaid'])) {
            Log::info('淘宝订单没有oaid', [$trade['tid']]);
            // 没有收货地址的是虚拟物品订单，不保存
            return [];
        }

        $is_home_decoration = 0;
        if (!empty($trade['service_orders']['service_order'])) {
            foreach ($trade['service_orders']['service_order'] as $service_order) {
                if (!empty($service_order['tmser_spu_code']) && in_array($service_order['tmser_spu_code'], [
                        "家装干线服务", "家装干支服务", "家装干支装服务", "卫浴大件干线", "卫浴大件干支", "卫浴大件安装",
                        "地板干线", "地板干支", "地板安装", "灯具安装", "卫浴小件安装", "集成吊顶大件干线", "集成吊顶大件干支", "集成吊顶大件安装",
                        "涂料干线", "瓷砖干线", "油漆干线", "涂料干支", "瓷砖干支", "油漆干支", "涂料安装", "瓷砖安装", "油漆安装", "浴霸安装",
                        "墙纸安装", "电子门锁安装", "集成吊顶小件安装"])) {
                    $is_home_decoration = 1;
                }
            }
        }
        $buyer_open_uid = $trade['buyer_open_uid'] ?? '';
        $receiver_phone = $trade['receiver_mobile'] ?? ($trade['receiver_phone'] ?? '');
        $receiver_district = $trade['receiver_district'] ?? '';
        $receiver_phone = md5($buyer_open_uid . $trade['receiver_state'] . $trade['receiver_city'] . $receiver_district . $receiver_phone);
        if (!empty($logistics_data)) {
            //获取快递公司列表
            try {
                $taobaoWaybillService = new TaoBaoApi($this->accessToken);
                $companyList = $taobaoWaybillService->getAllCompany('');
                if (!empty($companyList)) {
                    foreach ($logistics_data as &$data) {
                        $match = collect($companyList)->where('name', $data['wp_name'])->first();
                        if (!empty($match)) {
                            $data['wp_code'] = $match['code'];
                            $data['delivery_id'] = $match['id'];
                        }
                    }
                }
            } catch (\Exception $e) {
                \Log::info("get taobao company error,tid=" . $trade['tid'] . 'message=' . $e->getMessage());
            }
        }
        Log::info('final taobao order delivery packages:', [$logistics_data]);
        $orderData = [
            "tid" => $trade['tid'], //主订单
            "type" => $this->platformType, //订单类型
//                "user_id" => $trade[''], //用户ID
//            "express_no" => $trade[''], //快递单号
            "buyer_id" => $buyer_open_uid, //买家ID
            "buyer_nick" => $trade['buyer_nick'], //买家昵称
            "seller_nick" => $trade['seller_nick'], //卖家昵称
            "order_status" => $this->formatOrderStatus($trade['status']), //订单状态
            "refund_status" => $this->hasRefundStatus($orderItems), //退款状态
//                "print_status" => $trade[''], //打印状态
            "shop_title" => $trade['title'] ?? '', //店铺名
            "receiver_state" => $trade['receiver_state'] ?? null, //收货人省份
            "receiver_city" => $trade['receiver_city'] ?? null, //收货人城市
            "receiver_district" => $trade['receiver_district'] ?? null, //收货人地区
            "receiver_town" => $trade['receiver_town'] ?? null, //收货人街道
            "receiver_name" => $trade['receiver_name'] ?? null, //收货人名字
//            "receiver_phone" => $trade['receiver_mobile'], //收货人手机
            "receiver_phone" => $receiver_phone, //收货人手机，md5 是为了缩短字符串长度
//            "address_md5" => md5($trade['seller_nick'] . $buyer_open_uid . $trade['oaid']), //
            "receiver_zip" => $trade['receiver_zip'], //收件人邮编
            "receiver_address" => $trade['receiver_address'], //收件人详细地址
            "payment" => $trade['payment'], //实付金额
            "total_fee" => $trade['total_fee'], //总金额
            "discount_fee" => $trade['discount_fee'], //优惠金额
            "post_fee" => $trade['post_fee'], //运费
            "seller_flag" => $this->formatOrderFlag($trade['seller_flag']), //卖家备注旗帜
            "seller_memo" => json_encode($sellerMemo, 320), //卖家备注
            "buyer_message" => $trade['buyer_message'] ?? '', //买家留言
            "has_buyer_message" => $has_buyer_message, //是否有买家留言
//                "express_code" => $trade[''], //快递公司代码
            "order_created_at" => $trade['created'], //订单创建时间
            "order_updated_at" => $trade['modified'], //订单修改时间
            "send_at" => $trade['consign_time'] ?? null, //发货时间
            "finished_at" => $trade['end_time'] ?? null, //订单完成时间
            "pay_at" => $trade['pay_time'] ?? null, //支付时间
//                "refund_id" => $trade[''], //退款id
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => 0, // 是否预售1是0否
            'promise_ship_at' => min(array_column($orderItems, 'promise_ship_at')), // 承诺发货时间
            'items' => $orderItems,
            'is_home_decoration' => $is_home_decoration, // 是否是家装
            'cipher_info' => [
                "oaid" => $trade['oaid'] ?? '', //主订单
                'receiver_phone_mask' => $trade['receiver_mobile'] ?? '',
                'receiver_name_mask' => $trade['receiver_name'] ?? '',
                'receiver_address_mask' => $trade['receiver_address'] ?? '',
            ],
            'order_extra' => [
                'order_biz_type' => OrderExtra::BIZ_TYPE_GENERAL, // 订单业务类型
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);
//        if (empty($orderData['promise_ship_at']) && !empty($orderData['pay_at'])) {
//            $orderData['promise_ship_at'] = date('Y-m-d H:i:s', strtotime('+48 hour', strtotime($orderData['pay_at'])));
//        }
        return $orderData;
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $res): array
    {
        $orders = [];
        foreach ($res as $index => $item) {
            $orders[] = $this->formatToOrder($item);
        }
        return $orders;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['sku'] as $index => $item) {

            $propertiesArr = explode(';', $item['properties']);
            foreach ($propertiesArr as $value) {
                $item['properties_name'] = str_replace($value . ':', '', $item['properties_name']);
            }

            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['sku_id'],
                "sku_value" => $item['properties_name'],
                "outer_id" => $item['outer_id'] ?? null,
                "outer_goods_id" => $good['outer_id'] ?? null,
                "sku_pic" => $item['imageUrl'] ?? null,
                "is_onsale" => 0,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $good['num_iid'],
            'outer_goods_id' => $good['outer_id'] ?? null,
            'goods_title' => $good['title'],
            'goods_pic' => $good['pic_url'],
            'is_onsale' => $good['approve_status'] == 'onsale' ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => $good['modified'],
            'goods_updated_at' => $good['modified'],
            'skus' => $skus
        ];
    }

    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        if (empty($goods['items']['item'])) {
            return [];
        }
        $list = [];
        foreach ($goods['items']['item'] as $index => $item) {
            $list[] = $this->formatToGood($item);
        }

        return $list;
    }

    /**
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return mixed
     * @throws \Exception
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [], bool $silent = true)
    {
        $c = new TopClient;
        $c->format = 'json';
        $conf = config('waybill.taobao');
        $c->appkey = $conf['appkey'];
        $c->secretKey = $conf['secret'];
        $req = new AlibabaAscpLogisticsOfflineSendRequest;
        // 是否拆单发货
        if ($orderItemOId) {
            $req->setSubTid(implode(",", $orderItemOId));
        }
        $req->setTid($tid);
        $consign_pkgs = new TopConsignPkgRequest;
        $consign_pkgs->out_sid = $expressNo;
        $consign_pkgs->company_code = $expressCode;
        $req->setConsignPkgs(json_encode($consign_pkgs));
        $resp = $c->execute($req, $this->getAccessToken());
        \Log::info('tb_delivery_result:', [$resp]);
        if (isset($resp->result->success) && $resp->result->success) {
            return true;
        }
        if (!$silent) {
            throw new \Exception($resp->error_response->sub_msg);
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $this->hasNext = false;
        $topClient = $this->getClient();
        $token = $this->getAccessToken();
        $req = new TradesSoldGetRequest;
        $req->setFields("tid,
        buyer_nick,
        buyer_open_uid,
        seller_nick,
        status,
        title,
        receiver_state,
        receiver_city,
        receiver_district,
        receiver_town,
        receiver_name,
        receiver_mobile,
        receiver_phone,
        receiver_zip,
        receiver_address,
        seller_flag,
        has_buyer_message,
        payment,
        total_fee,
        discount_fee,
        post_fee,
        orders
        send_time,
        promise_collect_time,
        pay_time,
        consign_time,
        created,
        modified,
        end_time,
        logistics_infos,
        extend_info");
        $req->setStartCreated(date('Y-m-d H:i:s', $startTime));
        $req->setEndCreated(date('Y-m-d H:i:s', $endTime));

        //首次仅拉取待发货
        if ($isFirstPull) {
            $req->setStatus('WAIT_SELLER_SEND_GOODS');
        }

//        $req->setStatus('TRADE_FINISHED');
//        $req->setBuyerNick($tbNickName);
        $req->setType('fixed'); // fixed
        $req->setExtType("service");
//        !empty($commentStatus) && $req->setRateStatus($commentStatus);
        $req->setPageNo($this->page . '');
        $req->setPageSize($this->pageSize . '');
//        $req->setPageSize(10 . '');
        $req->setUseHasNext("true");
        $req->putOtherTextParam('include_oaid', 'true');

        $resp = $topClient->execute($req, $token);

        $shop = $this->getShop();
        $appid = \request('appId', -1);
        \Log::info('sendGetTradesOrders:' . $shop->id . ':' . $appid);

        $resp = $this->handleResp($resp);
        if (isset($resp['has_next']) && $resp['has_next'] == true) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }
        $trades = $resp['trades']['trade'] ?? [];
        if (empty($trades)) {
            return [];
        }
        $orderInfos = $this->sendBatchGetOrderInfo($trades);
        $orderInfos = array_pluck($orderInfos, null, 'tid');
        $trades = array_map(function ($trade) use ($orderInfos) {
            // 额外拉取备注
            return array_merge($trade, $orderInfos[$trade['tid']] ?? []);
        }, $trades);

        $this->ychBatchLogByOrders($trades);
//        $resp['trades']['trade'] = $trades;
        return $trades;
    }

    /**
     * 发送订单详情
     * @param string $tid
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $topClient = $this->getClient();
        $token = $this->getAccessToken();
        //$req = new TradeGetRequest;
        $req = new TradeFullinfoGetRequest;
        $req->setFields($this->orderInfoFields);
        $req->setTid($tid);
        $resp = $topClient->execute($req, $token);

        $shop = $this->getShop();
        $appid = \request('appId', -1);
        \Log::info('sendGetOrderInfo:' . $shop->id . ':' . $appid);

        $resp = $this->handleResp($resp);

        $this->ychBatchLogByOrders([$resp['trade']]);
        return $resp['trade'];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {

    }

    /**
     * @return TopClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        return topClient();
    }

    /**
     * @return TbClient
     * <AUTHOR>
     */
    protected function getTbClient()
    {
        $appKey = config('socialite.taobao_top.client_id');
        $secretKey = config('socialite.taobao_top.client_secret');
        $tbClient = new TbClient($appKey, $secretKey, $this->getAccessToken());
        $tbClient->setAccessToken($this->getAccessToken());
        return $tbClient;
    }

    function hasRefundStatus($array): int
    {
        //return array_sum(array_column($array, 'refund_id')) > 0 ? Order::REFUND_STATUS_YES : Order::REFUND_STATUS_NO;
        $refundStatus = Order::REFUND_STATUS_NO;
        foreach ($array as $item) {
            if ($item['refund_status'] > 0 && $item['refund_status'] != OrderItem::REFUND_STATUS_CLOSED) {
                $refundStatus = Order::REFUND_STATUS_YES;
                break;
            }
        }

        if ($refundStatus == Order::REFUND_STATUS_YES && in_array(0, array_column($array, 'refund_status'))) {
            $refundStatus = Order::REFUND_STATUS_PART;
        }

        return $refundStatus;
    }

    /**
     * 处理响应
     *
     * @param $response
     * @param bool $isPoolCurl
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    public function handleResp($response)
    {
        return $this->getTbClient()->handleResp($response);
    }

    /**
     * @inheritDoc
     * @throws OrderException
     */
    public function openSubscribeMsg(): bool
    {
        //  todo 淘订单权限申请中
//        if (config('taobaotop.connections.app.app_key') == '12178510') {
        if (in_array(config('taobaotop.connections.app.app_key'), [
            '12178510',
            '12565698',
            '23695186',
            '12056931', // 嗖嗖打单
            '12027327', // 速印标签
        ])) {
            return true;
        }
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new TmcUserPermitRequest;
        $arr = [
            'taobao_trade_TradeBuyerPay', // 买家付完款，或万人团买家付完尾款
            'taobao_refund_RefundCreated', // 退款创建消息
            'taobao_trade_TradeSellerShip', // 卖家发货消息
//            'taobao_trade_TradeSuccess', // 交易成功消息
//            'taobao_trade_TradeClose', // 关闭交易消息
            'taobao_trade_TradeMemoModified', // 交易备注修改消息
//            'taobao_logistics_LogsticDetailTrace', // 物流详情跟踪消息
        ];
        $req->setTopics(implode(',', $arr));
        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);
        if ($resp['is_success'] != true) {
            throw new OrderException("TaobaoOrder openSubscribeMsg error:" . json_encode($resp, JSON_UNESCAPED_UNICODE));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        $content = json_decode($data['content'], true);
        return $content;
        if (empty($content['tid'])) {
            return [];
        }

//        switch ($data['topic']){ // 消息所属主题
//            case 'taobao_logistics_LogsticDetailTrace':
//
//                break;
//        }
        $tid = $content['tid'];
        if (!empty($content['status'])) {
            $order_status = $this->formatOrderStatus($content['status']);
        }
        if (!empty($content['refund_id'])) {
            $refund_status = Order::REFUND_STATUS_YES;
        }
        if (!empty($content['seller_memo'])) {
            $seller_memo = $content['seller_memo'];
        }
        if (!empty($data['pub_time'])) {
            // 消息发布时间
            $order_updated_at = $data['pub_time'];
        }
        return compact('tid', 'order_status', 'refund_status', 'seller_memo', 'order_updated_at');
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        $topClient = $this->getClient();
        $req = new TmcMessagesConsumeRequest;
        $req->setQuantity("100");
        $req->setGroupName('fuwu');
        $resp = $topClient->execute($req);
        $resp = objectToArray($resp);
        //$resp = $this->handleResp($resp);
        return $resp['messages']['tmc_message'] ?? [];
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        $topClient = $this->getClient();
        $req = new TmcMessagesConfirmRequest;
        $req->setSMessageIds(implode(',', $idArr));
        $resp = $topClient->execute($req);
        $resp = objectToArray($resp);
        //$resp = $this->handleResp($resp);
        if (!isset($resp['is_success']) || $resp['is_success'] != true) {
            throw new OrderException("TaobaoOrder confirmSubscribeMsg error:" . json_encode($resp, JSON_UNESCAPED_UNICODE));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        return null;
    }

    /**
     * @inheritDoc
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $this->hasNext = false;
        $topClient = $this->getClient();
        $token = $this->getAccessToken();
        //        $token = '700121016052758181c360bc057ae5eb786556cb6e6bd9fbc5be5fa11aab81c6945c5c23941878445';
        $req = new TradesSoldIncrementGetRequest;
        $req->setFields("tid,
        buyer_nick,
        buyer_open_uid,
        seller_nick,
        status,
        title,
        receiver_state,
        receiver_city,
        receiver_district,
        receiver_town,
        receiver_name,
        receiver_mobile,
        receiver_phone,
        receiver_zip,
        receiver_address,
        seller_flag,
        has_buyer_message,
        payment,
        total_fee,
        discount_fee,
        post_fee,
        orders
        pay_time,
        consign_time,
        created,
        modified,
        end_time");
        $req->setStartModified(date('Y-m-d H:i:s', $startTime));
        $req->setEndModified(date('Y-m-d H:i:s', $endTime));

        //首次仅拉取待发货
        if ($isFirstPull) {
            $req->setStatus('WAIT_SELLER_SEND_GOODS');
        }
        //        $req->setBuyerNick($tbNickName);
        $req->setType('fixed'); // fixed
        $req->setExtType("service");
        //        !empty($commentStatus) && $req->setRateStatus($commentStatus);
        $req->setPageNo($this->page . '');
        $req->setPageSize($this->pageSize . '');
        //        $req->setPageSize(10 . '');
        $req->setUseHasNext("true");
        $req->putOtherTextParam('include_oaid', 'true');

        $resp = $topClient->execute($req, $token);

        $shop = $this->getShop();
        $appid = \request('appId', -1);
        \Log::info('sendGetTradesOrdersByIncr:' . $shop->id . ':' . $appid);

        $resp = $this->handleResp($resp);
        if (isset($resp['has_next']) && $resp['has_next'] == true) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }
        $trades = $resp['trades']['trade'] ?? [];
        if (empty($trades)) {
            return [];
        }

        $orderInfos = $this->sendBatchGetOrderInfo($trades);
        $orderInfos = array_pluck($orderInfos, null, 'tid');
        $trades = array_map(function ($trade) use ($orderInfos) {
            // 有备注 额外拉取备注
            return array_merge($trade, $orderInfos[$trade['tid']] ?? []);
        }, $trades);

        $this->ychBatchLogByOrders($trades);
//        $resp['trades']['trade'] = $trades;
        return $trades;
    }

    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        return [];
        //没权限先注释掉
//        $topClient = $this->getClient();
//        $token = $this->getAccessToken();
//        $req = new CustomRequest();
//        $req->setApiMethodName('taobao.items.onsale.get');
//        $req->setFields("approve_status,
//	        num_iid,
//	        title,
//	        nick,
//	        type,cid,
//	        pic_url,
//	        num,props,
//	        valid_thru,
//	        list_time,
//	        price,
//	        has_discount,
//	        has_invoice,
//	        has_warranty,
//	        has_showcase,
//	        modified,
//	        delist_time,
//	        postage_id,
//	        seller_cids,
//	        outer_id,
//	        sold_quantity");
//        $req->putOtherTextParam('page_size', $pageSize);
//        $req->putOtherTextParam('page_no', $currentPage);
//
//        $resp = $topClient->execute($req, $token);
//        $resp = $this->handleResp($resp);
//
//        if (empty($resp['items']['item'])) {
//            return [];
//        }
//        $resp['items']['item'] = array_map(function ($item) {
//            // 额外拉取备注
//            $tmp = $this->sendGetSkuInfo($item['num_iid']);
//            $item = $item + $tmp;
//            return $item;
//        }, $resp['items']['item']);
//        return $resp;
    }

//    protected function sendGetSkuInfo(string $numIid)
//    {
//        $topClient = $this->getClient();
//        $token = $this->getAccessToken();
//        $req = new CustomRequest;
//        $req->setApiMethodName('taobao.item.skus.get');
//        $req->setFields("sku_id,
//			properties,
//			quantity,
//			price,
//			created,
//			modified,
//			status,
//			properties_name,
//			sku_spec_id,
//			outer_id,
//			barcode,
//			num_iid,");
//        $req->putOtherTextParam('num_iids', $numIid);
//        $resp = $topClient->execute($req, $token);
//        $resp = $this->handleResp($resp);
//        return $resp['skus'];
//    }

    /**
     * @inheritDoc
     * @throws OrderException
     */
    public function sendServiceInfo()
    {
        $client = $this->getClient();
        $shop = $this->getShop();
        $nickName = $shop->name;

        $orderService = PlatformOrderServiceManager::create(config('app.platform'));
        $orderService->setShop($shop);

        if (env('APP_ARTICLE_CODE') == 'FW_GOODS-1000373384') {
            $order = ['pay_at' => '2020-12-20', 'cycle_end_at' => '2021-12-19', 'version' => UserExtra::VERSION_PROFESSIONAL, 'pay_fee' => 200000];
            return [
                'user_id' => $shop->user_id,
                'shop_id' => $shop->id,
                'identifier' => $shop->identifier,
                'platform_type' => $this->platformType,
                'pay_at' => $order['pay_at'],
                'expired_at' => $order['cycle_end_at'],
                'version' => $order['version'],
                'version_desc' => array_get(UserExtra::VERSION_MAP_ARR, $order['version'], ''),
                'pay_amount' => $order['pay_fee'],
            ];

        } else {
            //修改服务市场订单shop_id为空的记录
            PlatformOrder::query()->where(['auth_user_id' => $nickName, 'shop_id' => 0])->update([
                'user_id' => $shop->user_id,
                'shop_id' => $shop->id,
                'identifier' => $shop->identifier,
            ]);
            $order = $orderService->getVersionDesc();
            if (!isset($order['pay_at'])) {
                $req = new CustomRequest();
                $req->setApiMethodName('taobao.vas.subscribe.get');
                //            $req->putOtherTextParam("page_no",1);
                //            $req->putOtherTextParam("page_size",100);
                $req->putOtherTextParam('article_code', config('taobaotop.connections.app.article_code'));
                $req->putOtherTextParam('nick', $nickName);
                $resp = $client->execute($req);
                $response = $this->handleResp($resp);
                \Log::debug('API => taobao.vas.subscribe.get', $response);
                $arr = $response['article_user_subscribes']['article_user_subscribe'] ?? [];
                if ((strtotime($arr[0]['deadline']) - time()) > 86400 * 7) {
                    $version = UserExtra::VERSION_STANDARD;
                    $version_desc = UserExtra::VERSION_STANDARD_NAME;
                    if (in_array(config('taobaotop.connections.app.app_key'), [
                        '12056931', // 嗖嗖打单
                        '12027327', // 速印标签
                    ])) {
                        $version = UserExtra::VERSION_PROFESSIONAL;
                        $version_desc = UserExtra::VERSION_PROFESSIONAL_NAME;
                    }
                } else {
                    $version = 'Free';
                    $version_desc = '免费';
                }
                return [
                    'user_id' => $shop->user_id,
                    'shop_id' => $shop->id,
                    'identifier' => $shop->identifier,
                    'platform_type' => $this->platformType,
                    'pay_at' => "",
                    'expired_at' => $arr[0]['deadline'],
                    'version' => $version,
                    'version_desc' => $version_desc,
                    'pay_amount' => "",
                    'service_name' => "",
                ];
            } else {
                return [
                    'user_id' => $shop->user_id,
                    'shop_id' => $shop->id,
                    'identifier' => $shop->identifier,
                    'platform_type' => $this->platformType,
                    'pay_at' => $order['pay_at'],
                    'expired_at' => $order['cycle_end_at'],
                    'version' => $order['version'],
                    'version_desc' => array_get(UserExtra::VERSION_MAP_ARR, $order['version'], ''),
                    'pay_amount' => $order['pay_fee'],
                    'service_name' => $order['pay_fee'],
                ];
            }
        }
    }

    /**
     * @inheritDoc
     * @throws OrderException
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new RefundsReceiveGetRequest();
        $req->setFields("refund_id,tid,oid,title, total_fee, status, created,modified, refund_fee,refund_phase");
        $req->setStartModified($startTime);
        $req->setEndModified($endTime);
//        $req->setStatus('WAIT_SELLER_SEND_GOODS'); // WAIT_SELLER_SEND_GOODS
        $req->setType('fixed'); // fixed
//        !empty($commentStatus) && $req->setRateStatus($commentStatus);
        $req->setPageNo($this->page . '');
        $req->setPageSize($this->pageSize . '');
        $req->setUseHasNext("true");
        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);
        if (isset($resp['has_next']) && $resp['has_next'] == true) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }
        $this->ychBatchLogByOrders($resp['refunds']['refund']);

        return $resp['refunds']['refund'];
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        return [
            'tid' => $order['tid'],
            'oid' => $order['oid'],
            'refund_id' => $order['refund_id'],
//            'express_no' => $order['tracking_number'],
            'refund_created_at' => $order['created'],
            'refund_updated_at' => $order['modified'],
            'refund_status' => $this->formatRefundStatus($order['status']),
        ];
    }

    /**
     * @return YchClient
     */
    private function getYchClient()
    {
        return new YchClient();
    }

    /**
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @return mixed
     * <AUTHOR>
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId)
    {
        $orderInfo = $this->getOrderInfo($tid);
        $c = $this->getClient();
        $c->format = 'json';
        $req = new AlibabaAscpLogisticsOfflineSendRequest;
        // 是否拆单发货
        if ($orderItemOId) {
            $req->setSubTid(implode(",", $orderItemOId));
        }
        $req->setTid($tid);
        $consign_pkgs = new TopConsignPkgRequest;
        $consign_pkgs->out_sid = $expressNo;
        $consign_pkgs->company_code = $expressCode;
        $req->setConsignPkgs(json_encode($consign_pkgs));
        $resp = $c->execute($req, $this->getAccessToken());
        \Log::info('tb_delivery_api_result:', [$resp]);
        $this->handleResp($resp);
        if (isset($resp->result->success) && $resp->result->success) {
            return true;
        }

        return false;
    }

    /**
     * @see https://open.taobao.com/api.htm?docId=54714&docType=2&source=search
     * @param $order
     * @return mixed
     * <AUTHOR>
     */
    protected function sendDecrypt($order)
    {
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new CustomRequest();
        $req->setApiMethodName('taobao.top.oaid.decrypt');
        $query_list = [];
        foreach ($order as $index => $order) {
            $query_list[] = [
                'tid' => $order['tid'],
                'oaid' => $order['oaid'],
                'scene' => '1006',
            ];
        }
        $req->putOtherTextParam('query_list', json_encode($query_list));

        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);
        $receiver_list = array_map(function ($item) {
            return [
                'receiver_name' => $item['name'],
                'receiver_phone' => $item['mobile'],
                'receiver_address' => $item['address_detail'],
            ];
        }, $resp['receiver_list']['receiver']);
        return $receiver_list;
    }

    /**
     * @param $list
     * @return mixed
     * <AUTHOR>
     */
    protected function sendBatchEncrypt($list)
    {

    }

    /**
     * @param $tid
     * @param $sellerFlag
     * @param $sellerMemo
     * @return mixed
     * @throws OrderException
     * <AUTHOR>
     */
    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo): bool
    {
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new TradeMemoUpdateRequest;
        $req->setTid($tid);
        $req->setMemo($sellerMemo);
        if ($sellerFlag) {
            $req->setFlag((string)array_flip($this->orderFlagMap)[$sellerFlag]);
        }
        $reset = false;
        if (empty($sellerMemo)) {
            $reset = true;
        }
        $req->setReset($reset);
        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);
        Log::info('tb_add_remark  params:' . json_encode(['flag' => array_flip($this->orderFlagMap)[$sellerFlag] ?? "", 'memo' => $sellerMemo, 'reset' => $reset]) . '  response:' . json_encode($resp));
        if (isset($resp['trade'])) {
            return true;
        }

        return false;
    }

    /**
     * @param $beginAt
     * @param $endAt
     * @return mixed
     * <AUTHOR>
     */
    public function sendServiceOrderList($beginAt, $endAt)
    {
//        $shop = $this->getShop();
        $this->hasNext = false;
        $client = $this->getClient();
        $req = new VasOrderSearchRequest;
        $req->setArticleCode(config('taobaotop.connections.app.article_code'));
        $req->setPageNo($this->getPage() . '');
        $pageSize = 20;
        $req->setPageSize($pageSize . '');
        $req->setStartCreated($beginAt);
        $req->setEndCreated($endAt);
//        $req->setNick($shop->name);
        $resp = $client->execute($req);
        $resp = objectToArray($resp);
        \Log::info('pull taobao article_biz_orders:' . json_encode($resp));
        if (isset($resp['sub_code']) && $resp['sub_code'] == 'accesscontrol.limited-by-api-access-count') {
            throw new RequestRetryException();
        }
        $list = $resp['article_biz_orders']['article_biz_order'] ?? [];
        if (count($list) != $pageSize) {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }
        return $list;
    }

    /**
     * @inheritDoc
     */
    public function cipherDecryptBatch()
    {
        // TODO: Implement cipherDecryptBatch() method.
    }

    /**
     * @inheritDoc
     */
    public function cipherDecryptMaskBatch(array $list): array
    {
        // TODO: Implement cipherDecryptMaskBatch() method.
    }

    /**
     * @inheritDoc
     */
    public function cipherExtractSearch(string $encryptedData): string
    {
        // TODO: Implement cipherExtractSearch() method.
    }

    public function getSearchIndex(string $tid, string $type): string
    {

    }

    public function sendBatchGetOrderInfo($orders, $safe = false)
    {
        $tbClient = $this->getTbClient();
        $data = $result = [];
        foreach ($orders as $index => $order) {
            $request = $this->getTradeFullInfoReq((string)$order['tid']);
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($request);
            $data[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }

        $response = $this->poolCurl($data, 'post_form');
        foreach ($response as $index => $data) {
            if (!$safe) {
                $this->handleResp($data);
            }
            $data = json_decode(json_encode($data), true);
            $trade = $data['trade_fullinfo_get_response']['trade'] ?? [];
            if (empty($trade)) {
                \Log::info('sendBatchGetOrderInfo empty', $data);
                continue;
            }
            $result[$index] = $trade;
//            $result[$index] = $this->formatToOrder($trade);
        }

        //淘宝比较特殊，坑爹的发货包裹逻辑
        //物流流转信息查询
        $tids = collect($result)->whereIn('status', ['SELLER_CONSIGNED_PART', 'WAIT_BUYER_CONFIRM_GOODS'])->pluck("tid")->toArray();
        if (!empty($tids)) {
            $data2 = [];
            foreach ($tids as $idx => $tid) {
                $req = new LogisticsTraceGetRequest();
                $req->setTid($tid);
                list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
                $data2[$idx] = [
                    'params' => $apiParams,
                    'url' => $requestUrl
                ];
            }
            $resp = $this->poolCurl($data2, 'post_form');
            $tid2WaybillMap = [];
            foreach ($resp as $data) {
                $this->handleResp($data);
                $data = json_decode(json_encode($data), true);
                $waybills = $data['logistics_trace_get_response']['result']['transit_step_result'] ?? [];
                if (!empty($waybills)) {
                    $codeList = [];
                    foreach ($waybills as $waybill) {
                        if (!isset($waybill['out_sid'])) {
                            continue;
                        }
                        $codeList[] = [
                            'waybillCode' => $waybill['out_sid'],
                            'wpName' => $waybill['company_name'],
                            'deliveryAt' => $waybill['trace_list']['transit_step_info'][0]['status_time']
                        ];
                    }
                    $tid2WaybillMap[$waybills[0]['tid']] = $codeList;
                }
            }
            Log::info('taobao order has delivery packages:', $tid2WaybillMap);
            foreach ($result as &$trade) {
                $trade['existWaybills'] = $tid2WaybillMap[$trade['tid']] ?? [];
            }
        }
        return $result;
    }


    /**
     * @see https://open.taobao.com/api.htm?docId=54749&docType=2
     * @inheritDoc
     */
    public function sendQueryTradeTid(array $query_list)
    {
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new CustomRequest();
        $req->setApiMethodName('taobao.trades.sold.query');
        $query_list['start_created'] = Carbon::now()->subDay(90)->toDateTimeString();
        $query_list['end_created'] = Carbon::now()->toDateTimeString();
        $list = [];
        foreach ($query_list as $index => $item) {
            if (!in_array($index, ['receiver_phone', 'receiver_mobile', 'receiver_name', 'start_created', 'end_created'])) {
                continue;
            }
            $list[$index] = $item;
        }
        $req->putOtherTextParam('query_list', json_encode($list));
        $req->putOtherTextParam('scene', '1002');

        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);
        \Log::info('sendQueryTradeTid:', $resp['tid_list']);
        return $resp['tid_list'];
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        try {
            if (in_array(config('taobaotop.connections.app.app_key'), ['12565698'])) {
                // 店伙计没有订单权限
                return true;
            }

            $this->sendGetTradesOrders(time() - 60, time());
            return true;
        } catch (\Exception $ex) {
            \Log::info("checkAuthFailed shopId=" . $this->getShop()->id . 'message=' . $ex->getMessage());
            return false;
        }
    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendDDShopRoleType() method.
        return 0;
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getDDTradesOrder() method.
    }


    /**
     * @see https://open.taobao.com/api.htm?spm=a219a.7386797.0.0.1999669aUc7uoo&source=search&docId=55747&docType=2
     * <AUTHOR>
     * @param $sec_token
     * @return mixed
     * @throws OrderException
     */
    public function getOnceToken($sec_token)
    {
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new CustomRequest();
        $req->setApiMethodName('taobao.top.once.token.get');

        $req->putOtherTextParam('sec_token', $sec_token);

        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);

        return $resp['token'];
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new CustomRequest();
        $req->setApiMethodName('taobao.areas.get');
        $req->setFields('id,type,name,parent_id,zip');

        $resp = $topClient->execute($req, $token);
        $this->handleResp($resp);
        $resp = json_decode(json_encode($resp), true);
        $list = $this->formatAddress($resp['areas']['area']);
        return $list;
    }

    protected function formatAddress(array $list, $parentId = 1, $level = 1)
    {
        $resArr = [];
        foreach ($list as $index => $item) {
            if ($item['parent_id'] == $parentId) {
                if ($item['name'] == '海外') {
                    continue;
                }
                $arr = [
                    'name' => $item['name'],
                    'code' => $item['id'],
                    'parent_code' => $parentId,
                    'level' => $level,
                ];
                // 减少后面递归数据量
                unset($list[$index]);
                $arr['sub'] = $this->formatAddress($list, $item['id'], $level + 1);
                $resArr[] = $arr;
            }
        }
        return $resArr;
    }

    public function getQueryTradeOrderId(string $type, string $search)
    {
        if (empty($search)) {
            return [];
        }
        $typeMapping = [
            'receiver_name' => 'receiver_name',
            'receiver_phone' => 'receiver_mobile',
        ];
        $field = $typeMapping[$type];
        $token = $this->getAccessToken();
        $topClient = $this->getClient();
        $req = new CustomRequest();
        $req->setApiMethodName('taobao.trades.sold.query');
        $query_list['start_created'] = Carbon::now()->subDay(90)->toDateTimeString();
        $query_list['end_created'] = Carbon::now()->toDateTimeString();
        $query_list[$field] = $search;
        $list = [];
        foreach ($query_list as $index => $item) {
            if (!in_array($index, ['receiver_phone', 'receiver_mobile', 'receiver_name', 'start_created', 'end_created'])) {
                continue;
            }
            $list[$index] = $item;
        }
        $req->putOtherTextParam('query_list', json_encode($list));
        $req->putOtherTextParam('scene', '1002');

        $resp = $topClient->execute($req, $token);
        $resp = $this->handleResp($resp);
        \Log::info('sendQueryTradeTid:', $resp['tid_list']);

        if (!empty($resp['tid_list'])) {
            $idArr = Order::query()->select('id')->whereIn('tid', $resp['tid_list'])->pluck('id')->toArray();
            return $idArr;
        }
        return [];
    }

    private function formatToPlatformOrder($shop, $item)
    {
//        $item['item_code'] // ts-26066-4
        $version = UserExtra::VERSION_STANDARD;

        $result = [
            'order_no' => $item['item_code'] . '_0', // 没有订单号
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'platform_type' => $this->platformType,
            'cycle_end_at' => $item['deadline'],
            'sku_id' => $item['item_code'],
            'version' => $version,
            'version_desc' => array_get(UserExtra::VERSION_MAP_ARR, $version, ''),
            'pay_amount' => 0,
            'status' => PlatformOrder::STATUS_SUCCESS,
        ];
        return $result;
    }

    public function reportBatchLogByEvent(Event $event): bool
    {
        $list = [];

        if ($event instanceof BaseRequestEvent) {
            if (empty($event->ati)) {
//                \Log::debug('reportBatchLogByEvent:no ati', [$event]);
                return false;
            }
        }
        $ychClient = $this->getYchClient();
        if ($event instanceof UserLoginEvent) {
            $method = 'login';
            $list[] = [
                'userId' => $event->shop->id,
                'userIp' => $event->clientIp,
                'ati' => $event->ati,
                'topAppKey' => $ychClient->getAppKey(),
                'appKey' => $ychClient->getYchAppKey(),
                'appName' => $ychClient->getAppName(),
                'time' => $event->time * 1000,

                'tid' => $event->shop->shop_name,
                'sellerNick' => $event->shop->shop_name,
                'loginResult' => $event->loginStatus == $event::LOGIN_STATUS_SUCCESS ? 'success' : 'fail',
                'loginMessage' => '',
            ];
        } elseif ($event instanceof OrderQueryEvent) {
            $method = 'order';
            foreach ($event->orderIds as $orderId) {
                $list[] = [
                    'userId' => $event->shop->id,
                    'userIp' => $event->clientIp,
                    'ati' => $event->ati,
                    'topAppKey' => $ychClient->getAppKey(),
                    'appKey' => $ychClient->getYchAppKey(),
                    'appName' => $ychClient->getAppName(),
                    'time' => $event->time * 1000,

                    'url' => $event->clientUrl,
                    'operation' => '查看订单',
                    'tradeIds' => $orderId,
                ];
            }

        } elseif ($event instanceof OrderPrintEvent) {
            $method = 'order';
            foreach ($event->orderIds as $orderId) {
                $list[] = [
                    'userId' => $event->shop->id,
                    'userIp' => $event->clientIp,
                    'ati' => $event->ati,
                    'topAppKey' => $ychClient->getAppKey(),
                    'appKey' => $ychClient->getYchAppKey(),
                    'appName' => $ychClient->getAppName(),
                    'time' => $event->time * 1000,

                    'url' => $event->clientUrl,
                    'operation' => '打印订单',
                    'tradeIds' => $orderId,
                ];
            }
        } elseif ($event instanceof OrderDecryptEvent) {
            $method = 'decrypt';
            foreach ($event->orderIds as $orderId) {
                $list[] = [
                    'userId' => $event->shop->id,
                    'userIp' => $event->clientIp,
                    'ati' => $event->ati,
                    'topAppKey' => $ychClient->getAppKey(),
                    'appKey' => $ychClient->getYchAppKey(),
                    'appName' => $ychClient->getAppName(),
                    'time' => $event->time * 1000,

                    'decryptTime' => $event->decryptTime ?: ($event->time * 1000),
                    'logTime' => $event->time * 1000,
                    'url' => $event->clientUrl,
                    'action' => '手动解密',
                    'topRequestId' => $event->decryptRequestId,
                    'orderId' => $orderId,
                ];
            }
        } elseif ($event instanceof OrderUpdateEvent) {
            return true;
        } elseif ($event instanceof SqlLogEvent) {
            return true;
        } else {
            return true;
        }
//        \Log::debug('reportBatchLogByEvent:' . $method, $list);
        if ($method == "order") { // order 事件不上报
            return true;
        }
        $ychClient->batchLogByMethod($method, $list);
        return true;
    }

    /**
     * 上报订单日志
     * 目前就淘宝要求每个订单都上报，所以放到淘宝的实现里
     * @param array $orders
     * @throws \Exception
     * <AUTHOR>
     */
    public function ychBatchLogByOrders(array $orders)
    {
        $ychClient = $this->getYchClient();
        $method = 'order';
        $list = [];
        foreach ($orders as $order) {
            $list[] = [
                'userId' => '系统',
                'userIp' => config('app.ych.user_ip'),
                'ati' => '0000000000',
                'topAppKey' => $ychClient->getAppKey(),
                'appKey' => $ychClient->getYchAppKey(),
                'appName' => $ychClient->getAppName(),
                'time' => time() . '000',
                'url' => 'SyncOrders',
                'operation' => '同步订单',
                'tradeIds' => $order['tid'],
            ];
        }
        $ychClient->batchLogByMethod($method, $list);
    }

    /**
     * @param $tid
     * <AUTHOR>
     */
    protected function getTradeFullInfoReq($tid)
    {
        $req = new TradeFullinfoGetRequest;
        $req->setFields($this->orderInfoFields);
        $req->setTid($tid);

        $shop = $this->getShop();
        $appid = \request('appId', -1);
//        \Log::info('getTradeFullInfoReq:' . $shop->id . ':' . $appid);

        return $req;
    }

    /**
     * @inheritDoc
     * 整单发
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $tbClient = $this->getTbClient();
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $req = new AlibabaAscpLogisticsOfflineSendRequest;
            $req->setTid($tid);
            $req->setConsignType("1");
            $consign_pkgs = [];
            $consignStatus = [];
            foreach ($orderDeliveryRequest->packs as $pack) {
                $consignStatus[] = [
                    'sub_tid' => $pack['oid'],
                    //子订单是否部分发货，true：部分发货；false：全部发货
                    'is_part_consign' => false,
                ];
            }
            $consign_pkg = new TopConsignPkgRequest;
            $consign_pkg->out_sid = $orderDeliveryRequest->expressNo;
            $consign_pkg->company_code = ArrayUtil::getArrayValue($this->expressCodeMap, $orderDeliveryRequest->expressCode, $orderDeliveryRequest->expressCode);
            $goods = [];
            foreach ($orderDeliveryRequest->packs as $pack) {
                $good = new TopConsignGoodsRequest();
                $good->sub_tid = $pack['oid'];
                $good->item_type = "0";
                $good->num = $pack['shippedNum'];
                $goods[] = $good;
            }
            $consign_pkg->goods = $goods;
            $consign_pkgs[] = $consign_pkg;
            //整单发
            $req->setSubTid(collect($consignStatus)->pluck('sub_tid')->implode(','));
            $req->setConsignPkgs(json_encode($consign_pkgs));
            $req->setConsignStatus(json_encode($consignStatus));
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
            Log::info('send taobao full delivery tid:' . $tid . '，request:', [$requestUrl, $apiParams]);
            $requestData[$index] = [
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $this->handleResp($result);
                $result = json_decode(json_encode($result), true);
                if (isset($result['alibaba_ascp_logistics_offline_send_response']['result']['success'])) {
                    $commonResponse->setSuccess($result['alibaba_ascp_logistics_offline_send_response']['result']['success']);
                } else {
                    $commonResponse->setSuccess(false);
                }
            } catch (\Exception $e) {
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }


    public function orderMultiPackagesDelivery(int $shopId, array $orderDeliveryRequests): array
    {
        $tbClient = $this->getTbClient();
        $requestData = [];
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest['tid'];
            $req = new AlibabaAscpLogisticsOfflineSendRequest;
            $req->setTid($tid);
            $req->setConsignType("1");
            $oidArr = [];
            $consign_pkgs = [];
            $waybillCodes = [];
            foreach ($orderDeliveryRequest['packs'] as $waybill) {
                $consign_pkg = new TopConsignPkgRequest;
                $consign_pkg->out_sid = $waybill['waybillCode'];
                $consign_pkg->company_code = ArrayUtil::getArrayValue($this->expressCodeMap, $waybill['expressCode'], $waybill['expressCode']);
                $goods = [];
                $packArr = [];
                foreach ($waybill['goods'] as $pack) {
                    $good = new TopConsignGoodsRequest();
                    $good->sub_tid = $pack['oid'];
                    $good->item_type = "0";
                    $good->num = $pack['shippedNum'];
                    $oidArr[] = $pack['oid'];
                    $goods[] = $good;
                    $packArr[] = ["oid" => $pack['oid'], "shippedNum" => $pack['shippedNum']];
                }
                $waybillCodes[] = ["waybillCode" => $waybill['waybillCode'], "expressCode" => $waybill['expressCode'], "packs" => $packArr];
                $consign_pkg->goods = $goods;
                $consign_pkgs[] = $consign_pkg;
            }
            $consignStatus = [];
            $orderItemStatusArr = $orderDeliveryRequest['orderItemStatusArr'] ?? [];
            foreach ($orderItemStatusArr as $item) {
                // 是否部分发货
                $is_part_consign = $item['status'] == Order::ORDER_STATUS_PART_DELIVERED;
                $consignStatus[] = [
                    'sub_tid' => $item['oid'],
                    //子订单是否部分发货，true：部分发货；false：全部发货
                    'is_part_consign' => $is_part_consign,
                ];
            }
            $req->setSubTid(collect($oidArr)->unique()->implode(','));
            $req->setConsignPkgs(json_encode($consign_pkgs));
            $req->setConsignStatus(json_encode($consignStatus));
            list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
            Log::info('send taobao split delivery tid: ' . $tid . '，params:', [$requestUrl, $apiParams]);
            $requestData[$index] = [
                'waybillCodes' => $waybillCodes,
                'params' => $apiParams,
                'url' => $requestUrl
            ];
        }
        $responses = $this->poolCurl($requestData, 'post_form');
        $successes = [];
        $failures = [];
        foreach ($responses as $index => $response) {
            $request = $orderDeliveryRequests[$index];
            $waybillCodes = $requestData[$index]['waybillCodes'];
            $tid = $request['tid'];
            $shopId = $request['shopId'];
            Log::info('taobao split delivery response', [$request, $response]);
            try {
                $this->handleResp($response);
                $response = json_decode(json_encode($response), true);
//                $waybillCodes = $this->generateWaybillCodes($request['waybills']);
                if ($response['alibaba_ascp_logistics_offline_send_response']['result']['success']) {
                    $successes[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes];
                    Log::info('taobao split delivery success', [$tid, $request, $response]);
                } else {
                    $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => '发货失败'];
                    Log::info('taobao split delivery error', [$tid, $request, $response]);
                }
            } catch (\Exception $e) {
                //进入了异常情况，返回的是一个数组
                $subMsg = $e->getMessage();
                Log::info('taobao split delivery error', [$e->getMessage(), $tid, $request, $response]);
                $failures[] = ['tid' => $tid, "shopId" => $shopId, "waybillCodes" => $waybillCodes, 'msg' => $subMsg];
            }
        }
        return ["successes" => $successes, "failures" => $failures];
    }

    private function generateWaybillCodes($waybills): array
    {
        $waybillCodes = [];
        $packs = [];
        foreach ($waybills as $waybill) {
            foreach ($waybill['goods'] as $good) {
                $packs[] = [
                    'oid' => $good['oid'],
                    'shippedNum' => $good['shippedNum'],
                ];
            }
            $waybillCodes[] = ["waybillCode" => $waybill['expressNo'], "expressCode" => $waybill['expressCode'], "packs" => $packs];
        }
        return $waybillCodes;
    }

    public function getAccessToken()
    {
//        if (!empty($this->accessToken)) {
//            return $this->accessToken;
//        }
        // 每次都重新获取token
        $shop = $this->getShop();
        return $this->accessToken = $shop->access_token;
    }

    /**
     * 订购记录导出
     * @return mixed
     * <AUTHOR>
     */
    public function vasSubscSearch()
    {
        $shop = $this->getShop();
        $client = $this->getClient();
        $req = new VasSubscSearchRequest;
        $req->setArticleCode(config('taobaotop.connections.app.article_code'));
        $req->setNick($shop->name);
        $resp = $client->execute($req);
        $resp = $this->handleResp($resp);

        return $resp['article_subs']['article_sub'] ?? [];
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    public function sendNumberExtension($data)
    {
        $client = $this->getClient();
        $req = new TopSecretExtendRequest();
        $extend_request = new SecretNoExtendRequest;
        $extend_request->extend_days = $data['extend_days'] ?? '7';
        $extend_request->oaid = $data['order']['orderCipherInfo']['oaid'] ?? '';
        $extend_request->order_id = $data['order']['tid'] ?? '';
        $extend_request->scene = $data['scene'] ?? '1005';
        $req->setExtendRequest(json_encode($extend_request));
        $resp = $client->execute($req, $this->getAccessToken());
        Log::info('sendNumberExtension', ['request' => json_decode(json_encode($extend_request), true), 'response' => json_decode(json_encode($resp), true)]);
        $resp = $this->handleResp($resp);
        return $resp['result'] ?? [];
    }

    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams, $order)
    {
        $tbClient = $this->getTbClient();
        $req = new CustomRequest();
        $apiMethod = str_replace('/', '.', $apiMethod);
        $req->setApiMethodName($apiMethod);
        foreach ($apiParams as $key => $apiParam) {
            if (!is_array($apiParam) && !is_object($apiParam)) {
                $apiParam = (string)$apiParam;
            }
            $req->putOtherTextParam($key, $apiParam);
        }
        list($requestUrl, $apiParams) = $tbClient->getApiParamsAndUrl($req);
        return $tbClient->executeByCustom($requestUrl, $apiParams);
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        switch ($apiMethod) {
            case 'cainiao.waybill.ii.get';
                if (empty($apiParams['param_waybill_cloud_print_apply_new_request']) || !is_string($apiParams['param_waybill_cloud_print_apply_new_request'])) {
                    return $apiParams;
                }
                $request = json_decode($apiParams['param_waybill_cloud_print_apply_new_request'], true);
                if (empty($request['trade_order_info_dtos']) || !is_array($request['trade_order_info_dtos'])) {
                    return $apiParams;
                }
                $taoBaoApi = new TaoBaoApi();
                if (empty($request['trade_order_info_dtos'][0])) {
                    throw new ApiException([ErrorConst::PARAM_ERROR[0], 'trade_order_info_dtos 值应为数组']);
                }
                foreach ($request['trade_order_info_dtos'] as $index => $trade_order_info_dto) {
                    //收件人信息
                    $receiver = $taoBaoApi->setReceiver($order);
                    $request['trade_order_info_dtos'][$index]['recipient'] = json_decode(json_encode($receiver), true);
                }
                $apiParams['param_waybill_cloud_print_apply_new_request'] = json_encode($request);
                break;
            default:
                break;
        }
        return $apiParams;
    }

    public function buildAddressMd5($order, $cipherInfo, $isCrossShopMergeOrder): string
    {

        $addressArr = [
            $order['shop_id'],
            $order['buyer_id'],
            $cipherInfo['oaid'],
        ];
        if ($isCrossShopMergeOrder) {
            array_shift($addressArr); // 跨店合单，不需要店铺id
        }
        return md5(implode(',', $addressArr));
    }

    /**
     * @throws OrderException
     */
    public function orderAppendPackages(string $tid, string $wpCode, string $waybillCode, array $goodsList = []): bool
    {
        $req = new AlibabaAscpLogisticsOfflineSendRequest;
        $req->setTid($tid);
        $req->setConsignType('3'); // 3：补发；默认为0
        $consign_pkgs = [];
        $consign_pkg = new TopConsignPkgRequest;
        $consign_pkg->out_sid = $waybillCode;
        $consign_pkg->company_code = ArrayUtil::getArrayValue($this->expressCodeMap, $wpCode, $wpCode);
        $goods = [];
        foreach ($goodsList as $pack) {
            $good = new TopConsignGoodsRequest();
            $good->sub_tid = $pack['oid'];
            $good->item_type = "0";
            $good->num = $pack['num'];
            $goods[] = $good;
        }
        $consign_pkg->goods = $goods;
        $consign_pkgs[] = $consign_pkg;
        $req->setConsignPkgs(json_encode($consign_pkgs));
        $tbClient = $this->getClient();
        $resp = $tbClient->execute($req, $this->getAccessToken());
        $resp = $this->handleResp($resp);
        if (isset($resp->result->success) && $resp->result->success) {
            return true;
        } else if (isset($resp['result']) && isset($resp['result']['success']) && $resp['result']['success']) {
            return true;
        }
        return false;
    }

    protected function deliverySellerOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
        $req = new AlibabaAscpLogisticsConsignModifyRequest();
        $req->setTid($orderDeliverAgainRequest->tid);
        $req->setNewCompanyCode(ArrayUtil::getArrayValue($this->expressCodeMap, $orderDeliverAgainRequest->wpCode, $orderDeliverAgainRequest->wpCode));
        $req->setOldCompanyCode(ArrayUtil::getArrayValue($this->expressCodeMap, $orderDeliverAgainRequest->oldWpCode, $orderDeliverAgainRequest->oldWpCode));
        $req->setNewOutSid($orderDeliverAgainRequest->waybillCode);
        $req->setOldOutSid($orderDeliverAgainRequest->oldWaybillCode);
        Log::info('taobao replace waybillCode,tid:' . $req->getTid(),[$req->getApiParas()]);
        $tbClient = $this->getClient();
        $resp = $tbClient->execute($req, $this->getAccessToken());
        $resp = $this->handleResp($resp);
        if (isset($resp->result->success) && $resp->result->success) {
            return true;
        } else if (isset($resp['result']['success']) && $resp['result']['success']) {
            return true;
        }
        return false;
    }

    protected function deliverySellerOrdersAgain_new(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
        $req = new AlibabaAscpLogisticsConsignResendRequest();
        $req->setTid($orderDeliverAgainRequest->tid);
        //如果是拆单发货的包裹重新发货，需要传subTids
        if (!empty($orderDeliverAgainRequest->subTids)) {
            $req->setSubTids($orderDeliverAgainRequest->subTids);
        }
        $consign_pkgs = [];
        foreach ($orderDeliverAgainRequest->waybills as $waybill) {
            $consign_pkgs[] = [
                'company_code' => ArrayUtil::getArrayValue($this->expressCodeMap, $waybill['wpCode'], $waybill['wpCode']),
                'out_sid' => $waybill['waybillCode']
            ];
        }
        $req->setConsignPkgs(json_encode($consign_pkgs));
        Log::info('taobao replace waybillCode,tid:' . $req->getTid(),[$req->getApiParas()]);
        $tbClient = $this->getClient();
        $resp = $tbClient->execute($req, $this->getAccessToken());
        $resp = $this->handleResp($resp);
        if (isset($resp->result->success) && $resp->result->success) {
            return true;
        } else if (isset($resp['result']['success']) && $resp['result']['success']) {
            return true;
        }
        return false;
    }

    private function formatRefundSubStatus($orderItem)
    {
        if (empty($orderItem['refund_status'])){
            return RefundSubStatusConst::NONE;
        }
        // 退款状态。可选值 WAIT_SELLER_AGREE(买家已经申请退款，等待卖家同意) WAIT_BUYER_RETURN_GOODS(卖家已经同意退款，等待买家退货) WAIT_SELLER_CONFIRM_GOODS(买家已经退货，等待卖家确认收货) SELLER_REFUSE_BUYER(卖家拒绝退款) CLOSED(退款关闭) SUCCESS(退款成功)
        switch ($orderItem['refund_status']) {
            case 'WAIT_SELLER_AGREE': // 买家已经申请退款，等待卖家同意
                return RefundSubStatusConst::MERCHANT_PROCESSING;
            case 'WAIT_BUYER_RETURN_GOODS': // 卖家已经同意退款，等待买家退货
                return RefundSubStatusConst::WAIT_BUYER_RETURN;
            case 'WAIT_SELLER_CONFIRM_GOODS': // 买家已经退货，等待卖家确认收货
                return RefundSubStatusConst::RETURN_WAIT_MERCHANT;
            case 'SELLER_REFUSE_BUYER': // 卖家拒绝退款
                return RefundSubStatusConst::MERCHANT_REFUSE_REFUND;
            case 'CLOSED': // 退款关闭
                return RefundSubStatusConst::REFUND_CLOSE;
            case 'SUCCESS': // 退款成功
                return RefundSubStatusConst::REFUND_COMPLETE;
            default:
                return RefundSubStatusConst::NONE;
        }
    }
}
