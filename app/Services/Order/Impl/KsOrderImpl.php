<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/18
 * Time: 20:50
 */

namespace App\Services\Order\Impl;


use App\Constants\ErrorConst;
use App\Constants\RefundSubStatusConst;
use App\Events\Event;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\SqlLogEvent;
use App\Events\Users\UserLoginEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Http\StatusCode\StatusCode;
use App\Models\Address;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\Shop;
use App\Models\OrderItem;
use App\Models\UserExtra;
use App\Services\Bo\LogisticsDataBo;
use App\Services\Bo\LogisticsDataProductBo;
use App\Services\Client\KsClient;
use App\Services\CommonResponse;
use App\Services\Order\AbstractOrderService;
use App\Services\Order\OrderCipherInterface;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Utils\ArrayUtil;
use App\Utils\RandomUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use Symfony\Component\Translation\Exception\NotFoundResourceException;

//class KsOrderImpl extends AbstractOrderService
class KsOrderImpl extends AbstractOrderService implements OrderCipherInterface
{
    const LOGISTICS_NEW_CREATE_ORDER = 'logistics.newCreateOrder';
    private $shopId;

    protected $platformType = Shop::PLATFORM_TYPE_KS;

    protected $orderStatusMap = [
        0 => Order::ORDER_STATUS_UNKNOWN,
        10 => Order::ORDER_STATUS_PADDING,
        30 => Order::ORDER_STATUS_PAYMENT,
        40 => Order::ORDER_STATUS_DELIVERED,
        50 => Order::ORDER_STATUS_RECEIVED,
        70 => Order::ORDER_STATUS_SUCCESS,
        80 => Order::ORDER_STATUS_FAILED,
    ];

    const OTHER_CODE = 9999;  //其他

    const CODE_UNBIND_AUTH = 24; //取消授权错误码

    protected $pageSize = 50;

    //错误码映射
    protected $errorCodeMap = [
        self::CODE_UNBIND_AUTH => self::ERROR_CODE_UNBIND,
    ];

    //对应平台快递公司
    protected $expressCodeList
        = [
            'POST_DSBK' => 139,
            'HT' => 1,
            'SF' => 4,
            'ZTO' => 9,
            'YTO' => 6,
            'STO' => 3,
            'EMS' => 2,
            'JD' => 14,
            'TT' => 5,
            'ZJS' => 10,
            'YZXB' => 8,
            'YS' => 15,
            'DB' => 53,
            'AIR' => 95,
            'HTKY' => 1,
            'POSTB' => 8,
            'YDKY' => 7,
            'ZTOKY' => 12,
            'HOAU' => 88,
            'BESTQJT' => 48,
            'RRS' => 79,
            'KYE' => 25,
            'SDSD' => 57,
            'ANKY' => 43,
            'OTP' => 51,
            'AXWL' => 45,
            'SZKKE' => 74,
            'SXJD' => 86,
            'DEBANGWULIU' => 17,
            'SFKY' => 4,
            'ZTOINTER' => 9,
            'YDGJ' => 11,
            'YUNDA' => 11,
            'STOINTER' => 85,
            'JIUYE' => 71,
            'GJ' => 63,
            'CN7000001021040' => 7,
            'CN7000001003751' => 25,
            '100007887' => self::OTHER_CODE,
            'SURE' => 31,
            'CP570969' => self::OTHER_CODE,
            '2608021499_235' => 40,
            'CP468398' => 6,
            'FAST' => 19,
            '3108002701_1011' => 12,
            'DBKD' => 53,
            'CN7000001000869' => 43,
            'CP471906' => 86,
            'GTO' => 23,
            'QFKD' => 16,
            'EYB' => 2,
            'CP457538' => self::OTHER_CODE,
            'SNWL' => 81,
            'CN7000001017817' => 3,
            '100004928' => 29,
            'CP443514' => 1,
            'FEDEX' => 77,
            '5000000007756' => 8,
            'TTKDEX' => 5,
            'UC' => 15,
            'JTSD' => 109,
            'JT' => 109,
            'FENGWANG' => 127,
        ];
    /**
     * 来自快手 https://open.kwaixiaodian.com/zone/new/solution/detail?pageSign=be565dfc5ca82ebb4683bc208f7929051642596657058#section-6
     * @var int[]
     */
    protected $expressCodeListByKs = [
        'ZTO' => 9, // 中通快递
        'YUNDA' => 11, // 韵达快递
        'YTO' => 6, // 圆通快递
        'STO' => 3, // 申通快递
        'JT' => 109, // 极兔快递
        'SF' => 4, // 顺丰
        'POSTB' => 8, // 邮政快递包裹
        'EMS' => 2, // EMS
        'JD' => 14, // 京东物流
        'DBKD' => 53, // 德邦快递
        'FENGWANG' => 127, // 丰网速运
        'YDKY' => 7, // 韵达快运
        'DANNIAO' => 113, // 菜鸟速递
        'KUAIDILAILE' => 129, // 快弟来了
        'ANNTO' => 44, // 安得物流
        'BEST800_LOGISTICS' => 48, // 百世快运
        'YIMIDIDA' => 100, // 壹米滴答
        'ZTOKY' => 12, // 中通快运
        'JDKY' => 136, // 京东快运
        'ANKY' => 43, // 安能快运
        'ZHONGYOUKD' => 112, // 众邮快递
        'DSU' => 57, // D速快递
        'SHUNXIN' => 86, // 顺心捷达
        'JINGGUANG' => 74, // 京广速递
        'TJS' => 111, // 特急送
        'ZHAIJISONG' => 10, // 宅急送
        'HAIXIN_LOGISTICS' => 126, // 海信物流
        'JDDJ' => 137, // 京东大件
        'POST_DSBK' => 139, // 邮政电商标快
        'JIAYUNMEI' => 72, // 加运美
        'NEZHA' => 128, // 一站通速运
        'ZTOCC' => 135, // 中通冷链
        'STE' => 141, // 速腾物流
        'PAD' => 142, // 平安达腾飞快递
        'JAX' => 145, // 捷安信物流
        'JSD' => 153, // 吉时达
    ];


    protected $refundStatusMap = [
        0 => OrderItem::REFUND_STATUS_NO,
        10 => OrderItem::REFUND_STATUS_WAIT_SELLER, // 买家仅退款申请
        11 => OrderItem::REFUND_STATUS_WAIT_SELLER, // 买家退货退款申请
        20 => OrderItem::REFUND_STATUS_PLATFORM_IN, // 平台介入-买家仅退款申请
        21 => OrderItem::REFUND_STATUS_PLATFORM_IN, // 平台介入-买家退货退款申请
        22 => OrderItem::REFUND_STATUS_PLATFORM_IN, // 平台介入-已确认退货退款
        30 => OrderItem::REFUND_STATUS_PROCESSING, // 商品回寄信息待买家更新
        40 => OrderItem::REFUND_STATUS_WAIT_SELLER, // 商品回寄信息待卖家确认
        50 => OrderItem::REFUND_STATUS_REFUNDING, // 退款执行中
        60 => OrderItem::REFUND_STATUS_SUCCESS, // 退款成功
        70 => OrderItem::REFUND_STATUS_CLOSED, // 退款失败
    ];

    //退款失败
    const REFUND_STATUS_CLOSE = 70;
    //  1：RED; 2：YELLOW; 3：GREEN; 4：BLUE; 5：PURPLE; 6：GREY;
    protected $orderFlagMap = [
        6 => Order::FLAG_GRAY,
        1 => Order::FLAG_RED,
        2 => Order::FLAG_YELLOW,
        3 => Order::FLAG_GREEN,
        4 => Order::FLAG_BLUE,
        5 => Order::FLAG_PURPLE,
    ];
    // 不知道为什么快手有2种标记
    protected $orderFlagMap2 = [
        'purple_flag_tag_order' => Order::FLAG_PURPLE,
        'red_flag_tag_order' => Order::FLAG_RED,
        'yellow_flag_tag_order' => Order::FLAG_YELLOW,
        'green_flag_tag_order' => Order::FLAG_GREEN,
        'blue_flag_tag_order' => Order::FLAG_BLUE,
        'grey_flag_tag_order' => Order::FLAG_GRAY,
    ];



    /**
     * 每次拉取订单间隔的分钟
     * @var int
     */
    public $orderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 120;

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;
    /**
     * 临时的游标
     * @var
     */
    private $tmp_cursor = '';
    /**
     * @var string
     */
    private $gatewayUrl = 'https://open.kwaixiaodian.com';

    public function formatToAfterSale(array $trade)
    {

    }

    /**
     * @inheritDoc
     */
    public function formatToOrder(array $trade): array
    {
        //退款状态 有退款并且非退款失败
        $refundStatus = 0;
        $orderBaseInfo = $trade['orderBaseInfo'];
        if ($orderBaseInfo['refundTime'] > 0 && isset($trade['orderRefundList'][0]['refundStatus']) &&
            $trade['orderRefundList'][0]['refundStatus'] != self::REFUND_STATUS_CLOSE) {
            $refundStatus = 1;
        }
        $orderItems = [];
        $orderItemInfo = $trade['orderItemInfo'];
        $total = $orderItemInfo['price'] * $orderItemInfo['num'];
        $status = $this->formatOrderStatus($orderBaseInfo['status']);
        if ($status == Order::ORDER_STATUS_DELIVERED) { // 已发货包括部分发货，需要区分
            $deliveryStatus = $trade['orderDeliveryInfo']['deliveryStatus']; // 发货状态，[10:部分发货]、[40:全部发货]
            if ($deliveryStatus == 10) { // [10:部分发货]
                $status = Order::ORDER_STATUS_PART_DELIVERED;
            }
        }
        $payAt = !empty($orderBaseInfo['payTime']) ? date('Y-m-d H:i:s', $orderBaseInfo['payTime'] / 1000) : null;
        if ($orderBaseInfo['validPromiseShipmentTimeStamp'] > 0) {
            $promise_ship_at = date('Y-m-d H:i:s', $orderBaseInfo['validPromiseShipmentTimeStamp'] / 1000);
        } elseif (isset($orderBaseInfo['promiseTimeStampOfDelivery']) && $orderBaseInfo['promiseTimeStampOfDelivery'] > 0) {
            $promise_ship_at = date('Y-m-d H:i:s', $orderBaseInfo['promiseTimeStampOfDelivery'] / 1000);
        } else {
            $promise_ship_at = date('Y-m-d H:i:s', strtotime('+72 hour', strtotime($payAt)));
        }
        $skuValue = $orderItemInfo['skuDesc'] ?? '';
        $skuValueArr = explode(',', $skuValue); // 黑色背心,150cm
        $skuValue = str_replace(',', ';', $skuValue);
        $skuValue1 = $skuValueArr[0] ?? '';
        $skuValue2 = $skuValueArr[1] ?? '';

        $orderItems[] = [
            "tid" => (string)$orderBaseInfo['oid'], //主订单
            "oid" => (string)$orderBaseInfo['oid'], //子订单号
            "type" => $this->platformType, //订单类型
            "payment" => formatToYuan($total), //实付金额
            "total_fee" => formatToYuan($total), //总金额
            "discount_fee" => formatToYuan($orderItemInfo['discountFee']), //优惠金额
            "goods_pic" => $orderItemInfo['itemPicUrl'], //商品图片
            "goods_title" => $orderItemInfo['itemTitle'], //商品标题
            "goods_price" => formatToYuan($orderItemInfo['price']), //商品单价
            "goods_num" => $orderItemInfo['num'], //商品数量
            "num_iid" => $orderItemInfo['itemId'], //商品id
            "sku_id" => $orderItemInfo['skuId'] ?? '', //sku id
            "sku_value" => $skuValue,
            "sku_value1" => $skuValue1,
            "sku_value2" => $skuValue2,
            "outer_iid" => '', //商家外部商品编码
            "outer_sku_iid" => $orderItemInfo['skuNick'] ?? '', //商家外部sku编码
            "order_created_at" => date('Y-m-d H:i:s', $orderBaseInfo['createTime'] / 1000), //订单创建时间
            "order_updated_at" => date('Y-m-d H:i:s', $orderBaseInfo['updateTime'] / 1000), //订单修改时间
            "promise_ship_at" => $promise_ship_at, //承诺发货时间
            "refund_id" => $trade['orderRefundList'][0]['refundId'] ?? null, //
            "status" => $status,
            "refund_status" => $refundStatus, //退款状态
            "refund_sub_status" => $this->formatSubRefundStatus($trade),
        ];
        $sendAt = !empty($orderBaseInfo['sendTime']) ? date('Y-m-d H:i:s', $orderBaseInfo['sendTime'] / 1000) : null;

        // 包裹信息
        $logistics_data = [];
        $expressCodeList = array_flip($this->expressCodeListByKs);
        foreach ($trade['orderLogisticsInfo'] as $logistics_info) {
            $product_list = [];
            $logisticsDataProductBo = new LogisticsDataProductBo();
            $logisticsDataProductBo->oid = (string)$orderBaseInfo['oid'];
            $logisticsDataProductBo->sku_id =$orderItemInfo['skuId'] ?? '';
            $logisticsDataProductBo->outer_sku_id = $orderItemInfo['skuNick'] ?? '';
            $logisticsDataProductBo->num_iid = $orderItemInfo['itemId'];
            $logisticsDataProductBo->num = $orderItemInfo['num'];
            $logisticsDataProductBo->goods_title =$orderItemInfo['itemTitle'];
            $logisticsDataProductBo->sku_value =  $orderItemInfo['skuDesc'] ?? '';
            $product_list[] = $logisticsDataProductBo;

            $logisticsDataBo = new LogisticsDataBo();
            $logisticsDataBo->waybill_code = $logistics_info['expressNo']; // 运单号
            $logisticsDataBo->wp_code = array_get($expressCodeList, $logistics_info['expressCode'],'other');
            $logisticsDataBo->wp_name = '';
//            $logisticsDataBo->delivery_at = $sendAt;
            $logisticsDataBo->delivery_id = $logistics_info['logisticsId'];// 包裹 id
            $logisticsDataBo->product_list = $product_list;
            $logistics_data[] = $logisticsDataBo;
        }

        $addressArr = $trade['orderAddress'];
        // 从密文里提取搜索串
//        preg_match('/#(.*?)#/',$addressArr['encryptedConsignee'] ,$arr);
//        $receiver_name = $arr[1]??'';
//        preg_match('/\$(.*?)\$/',$addressArr['encryptedMobile'] ,$arr);
//        $receiver_phone = $arr[1]??'';
//        preg_match('/~(.*?)~/',$addressArr['encryptedAddress'] ,$arr);
//        $receiver_address = $arr[1]??'';
        $seller_memo = '[]';
        if (!empty($trade['orderNote']['orderNoteInfo'])){
            $seller_memo = json_encode(array_column($trade['orderNote']['orderNoteInfo'],'note'), JSON_UNESCAPED_UNICODE);
        }
        $orderData = [
            "tid" => (string)$orderBaseInfo['oid'], //主订单
            "type" => $this->platformType, //订单类型
//                "user_id" => $trade[''], //用户ID
            "express_no" => $trade['orderLogisticsInfo'][0]['expressNo'] ?? null, //快递单号
            "buyer_id" => $orderBaseInfo['buyerOpenId'] ?? '', //买家ID
            "buyer_nick" => $orderBaseInfo['buyerNick'] ?? '', //买家昵称
            "seller_nick" => $orderBaseInfo['sellerNick'], //卖家昵称
            "order_status" => $status, //订单状态
            "refund_status" => $refundStatus, //退款状态
//                "print_status" => $trade[''], //打印状态
//            "shop_title" => '', //店铺名
            "receiver_state" => $addressArr['province'], //收货人省份
            "receiver_city" => $addressArr['city'], //收货人城市
            "receiver_district" => $addressArr['district'], //收货人地区
            "receiver_town" => $addressArr['town']??'', //收货人地区
//            "receiver_name" => $addressArr['consignee']??'', //收货人名字
//            "receiver_phone" => $addressArr['mobile']??'', //收货人手机
//            "receiver_zip" => 0, //收件人邮编
//            "receiver_address" => $addressArr['address']??'', //收件人详细地址
            "payment" => formatToYuan($orderBaseInfo['totalFee']), //实付金额
            "total_fee" => formatToYuan($orderBaseInfo['totalFee']), //总金额
            "discount_fee" => formatToYuan($orderBaseInfo['discountFee']), //优惠金额
            "post_fee" => formatToYuan($orderBaseInfo['expressFee']), //运费
            "seller_flag" => isset($trade['orderFlag']['flag'][0]) ? $this->formatOrderFlag($trade['orderFlag']['flag'][0]) : Order::FLAG_NONE, //卖家备注旗帜
            "seller_memo" => $seller_memo, //卖家备注
            "buyer_message" => $orderBaseInfo['remark'], //买家留言
            "has_buyer_message" => empty($orderBaseInfo['remark']) ? 0 : 1, //买家留言
//                "express_code" => $trade[''], //快递公司代码
            "order_created_at" => date('Y-m-d H:i:s', $orderBaseInfo['createTime'] / 1000), //订单创建时间
            "order_updated_at" => date('Y-m-d H:i:s', $orderBaseInfo['updateTime'] / 1000), //订单修改时间
            "send_at" => $sendAt, //发货时间
//            "finished_at" => , //订单完成时间
            "pay_at" => $payAt, //支付时间
//                "refund_id" => $trade[''], //退款id
            'num' => array_sum(array_column($orderItems, 'goods_num')), // 商品数量
            'sku_num' => count($orderItems), // SKU数量
            'is_pre_sale' => $orderBaseInfo['preSale'], // 是否预售1是0否
            'promise_ship_at' => $promise_ship_at, // 承诺发货时间
//            'district_code' => $addressArr['districtCode'],
            'items' => $orderItems,
            'order_extra' => [
                'logistics_data' => jsonEncode($logistics_data),
            ],
        ];
        $orderData['district_code'] = $this->getDistrictCodeByAddress($orderData['receiver_state'], $orderData['receiver_city'], $orderData['receiver_district']);

        //发货状态的时候已锁单 locked_at 为null
        if (in_array($this->formatOrderStatus($orderBaseInfo['status']), [Order::ORDER_STATUS_DELIVERED])) {
            $orderData['locked_at'] = null;
        }

        if (empty($orderData['express_no'])) {
            unset($orderData['express_no']);
        }
        $cipher_info = [
            'receiver_phone_ciphertext' => $addressArr['encryptedMobile'] ?? '',
            'receiver_name_ciphertext' => $addressArr['encryptedConsignee'] ?? '',
            'receiver_address_ciphertext' => $addressArr['encryptedAddress'] ?? '',
            'receiver_phone_mask' => $addressArr['desensitiseMobile'] ?? '',
            'receiver_name_mask' => $addressArr['desensitiseConsignee'] ?? '',
            'receiver_address_mask' => $addressArr['desensitiseAddress'] ?? '',
        ];
        $orderData['cipher_info'] = $cipher_info;

        //补充订单旗标
//        $orderInfo = $this->sendGetOrderInfo($trade['oid']);
//        $orderData['seller_flag'] = isset($orderInfo['flagTagCode']) ? $this->formatOrderFlag($orderInfo['flagTagCode']) : Order::FLAG_NONE;

        //加密改造
//        $shop = $this->getShop();
//        if (!empty($shop) && ksEncryptSwitch($shop->id)) {
        $orderData['receiver_name'] = $addressArr['encryptedConsignee'] ?? '';
        $orderData['receiver_phone'] = $addressArr['encryptedMobile'] ?? '';
        $orderData['receiver_address'] = $addressArr['encryptedAddress'] ?? '';
//        }
        return $orderData;
    }

    /**
     * @inheritDoc
     */
    public function formatToOrders(array $orders): array
    {
        $list = [];
        foreach ($orders as $index => $order) {
            $list[] = $this->formatToOrder($order);
        }
        return $list;
    }

    /**
     * 商品构建
     * @param array $good
     * @return array
     */
    public function formatToGood(array $good): array
    {
        $skus = [];
        foreach ($good['skuList'] as $index => $item) {
            $skus[] = [
                "type" => $this->platformType,
                "sku_id" => $item['kwaiSkuId'],
                "sku_value" => $item['specification'],
                "outer_id" => $item['relSkuId'],
                "outer_goods_id" => $good['relItemId'],
                "sku_pic" => $item['imageUrl'],
                "is_onsale" => 0,
            ];
        }

        return [
            "type" => $this->platformType,
            'num_iid' => $good['kwaiItemId'],
            'outer_goods_id' => $good['relItemId'],
            'goods_title' => $good['title'],
            'goods_pic' => $good['mainImageUrl'],
            'is_onsale' => $good['shelfStatus'] ? Goods::IS_ONSALE_YES : Goods::IS_ONSALE_NO,
            'goods_created_at' => date('Y-m-d H:i:s', $good['createTime'] / 1000),
            'goods_updated_at' => date('Y-m-d H:i:s', $good['updateTime'] / 1000),
            'skus' => $skus
        ];
    }

    /**
     * 商品批量格式转换
     * @param array $goods
     * @return array
     */
    public function formatToGoods(array $goods): array
    {
        $list = [];
        foreach ($goods['items'] as $index => $good) {
            $list[] = $this->formatToGood($good);
        }

        return $list;
    }

//    /**
//     * @param mixed $shopId
//     */
//    public function setShopId($shopId): void
//    {
//        $this->shopId = $shopId;
//    }

//    /**
//     * @return mixed
//     */
//    public function getShopId()
//    {
//        if (empty($this->shopId)) {
//            throw new \InvalidArgumentException('shopId 不能空');
//        }
//        return $this->shopId;
//    }

    /**
     * @inheritDoc
     * @throws ClientException
     */
    protected function sendGetTradesOrders(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $type = 1;
        if ($isFirstPull) {
            $type = 3;
        }

        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());
        $params = [
            'beginTime' => $startTime * 1000,
            'endTime' => $endTime * 1000,
            'orderViewStatus' => $type, // 订单状态，0未知 1 全部 2 待付款 3 待发货 4 待收货（已发货）5 已收货 6 交易成功订单 7 已关闭订单
//            'sellerId' => $this->getShopId(),
            'pageSize' => $this->pageSize,
//            'pageSize' => 10,
            'sort' => 1, // 1时间降序 2时间升序
            'queryType' => 1, // 1按创建时间查找 2按更新时间查找
            'cursor' => $this->page_cursor,
        ];
//        $response = $client->execute('get', '/open/seller/order/pcursor/list', $params);
        $response = $client->execute('get', '/open/order/cursor/list', $params); // v2
        $this->handleErrorCode($response);
        $list = $this->sendBatchGetOrderInfoByTidArr(array_pluck($response['data']['orderList'], 'orderBaseInfo.oid'));

        if ($response['data']['cursor'] != 'nomore') {
            $this->hasNext = true;
            $this->tmp_cursor = $response['data']['cursor'];
        } else {
            $this->hasNext = false;
        }

        return $list;
    }

    /**
     * 订单详情
     * @param string $tid
     * @return mixed
     * @throws ClientException
     */
    protected function sendGetOrderInfo(string $tid)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());
        $params = [
//            'sellerId' => $this->getShopId(),
//            'orderId'  => $tid,
        ];
        $params['oid'] = $tid;
        $url = '/open/order/detail'; // v2
//        $url = '/open/seller/order/detail';
        $response = $client->execute('get', $url, $params);

        return $response['data'] ?? [];
    }

    protected function sendGetAfterSaleOrderInfo(string $tid)
    {

    }

    /**
     * 获取商品列表
     * @param int int $pageSize,
     * @param int $currentPage
     * @return array
     * @throws ClientException
     */
    protected function sendGetGoods(int $pageSize, int $currentPage = 1)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());
        $params = [
//            'sellerId' => $this->getShopId(),
            'pageSize' => $pageSize,
            'itemStatus' => 1,
            'itemType' => 1,
            'pageNumber' => $currentPage,
        ];
        $goods = $client->execute('get', '/open/item/list/get', $params);

        if (isset($goods['data']['totalItemCount'])) {
            $this->goodsTotalCount = $goods['data']['totalItemCount'];
        } else {
            $this->goodsTotalCount = 0;
        }

        if (isset($goods['data']['totalItemCount'])) {
            $this->goodsTotalCount = $goods['data']['totalItemCount'];
        } else {
            $this->goodsTotalCount = 0;
        }

        \Log::info('ks_goods_result', [$goods]);
        if (!isset($goods['result']) || $goods['result'] != 1 || !isset($goods['data'])) {
            if (!isset($goods['result']) || $goods['result'] == 100026 || $goods['error_msg'] == '页数不合法') {
                return [];
            }
            \Log::error('获取商品列表接口失败!', [$goods, $this->pageSize, $currentPage]);
            return [];
        } elseif ($goods['data']['currentPageNumber'] < $goods['data']['totalPage']) {
            $this->hasNext = true;
        }
        return $goods['data'];
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @param array $orderItemOId
     * @param bool $silent
     * @return mixed
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function deliverySellerOrders(string $tid, string $expressCode, string $expressNo, array $orderItemOId
    = [],bool $silent=true)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());
        $params = [
//            'sellerId'    => $this->getShopId(),
            'orderId' => $tid,
            'expressNo' => $expressNo,
            'expressCode' => array_get($this->expressCodeList, $expressCode, self::OTHER_CODE),
        ];
        $result = $client->execute('post', '/open/seller/order/goods/deliver', $params);

        //发货失败重试3次
        if (!isset($result['result']) || $result['result'] != 1) {
            $num = 1;
            while ($num <= 3) {
                $result = $client->execute('post', '/open/seller/order/goods/deliver', $params);
                \Log::info('订单发货失败重试_' . $num, [$result, $this->platformType, $tid, $expressCode, $expressNo]);
                if (isset($result['result']) && $result['result'] == 1) {
                    break;
                }
                $num++;
            }
        }

        if (!isset($result['result']) || $result['result'] != 1) {
            \Log::error('订单发货失败!', [$result, $this->platformType, $tid, $expressCode, $expressNo]);
            $this->handleErrorCode($result);
            return false;
        }

        return true;
    }

    /**
     * 订单发货
     * @param string $tid
     * @param string $expressCode
     * @param string $expressNo
     * @return bool|mixed
     * @throws ClientException
     */
    protected function deliverySellerOrdersForOpenApi(string $tid, string $expressCode, string $expressNo, array $orderItemOId = [])
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());
        $params = [
//            'sellerId'    => $this->getShopId(),
            'orderId' => $tid,
            'expressNo' => $expressNo,
            'expressCode' => array_get($this->expressCodeList, $expressCode, self::OTHER_CODE),
        ];
        $result = $client->execute('post', '/open/seller/order/goods/deliver', $params);
        $this->handleErrorCode($result);
        if (!isset($result['result']) || $result['result'] != 1) {
            \Log::error('订单发货失败!', [$result, $this->platformType, $tid, $expressCode, $expressNo]);
            return $result['error_msg'] ?? false;
        }

        return true;
    }

    /**
     * @inheritDoc
     * @throws ClientException|ApiException
     */
    protected function sendGetTradesOrdersByIncr(int $startTime, int $endTime, bool $isFirstPull = false): array
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());

        $type = 1;
        if ($isFirstPull) {
            $type = 3;
        }
        $params = [
            'beginTime' => $startTime * 1000,
            'endTime' => $endTime * 1000,
            'orderViewStatus' => $type, // 订单状态，0未知 1 全部 2 待付款 3 待发货 4 待收货（已发货）5 已收货 6 交易成功订单 7 已关闭订单
//            'sellerId' => $this->getShopId(),
            'pageSize' => $this->pageSize,
//            'pageSize' => 10,
            'sort' => 1, // 1时间降序 2时间升序
            'queryType' => 2, // 1按创建时间查找 2按更新时间查找
            'cursor' => $this->page_cursor,
        ];
//        $response = $client->execute('get', '/open/seller/order/pcursor/list', $params);
        $response = $client->execute('get', '/open/order/cursor/list', $params); // v2
        $this->handleErrorCode($response);
        //用户取消授权
//	    if (isset($response['result']) && in_array($response['result'], array_keys($this->errorCodeMap))) {
//		    $this->errorInfo['code']  = array_get($this->errorCodeMap, $response['result'], 0);
//		    $this->errorInfo['info']  = $response;
//		    return $this->errorInfo;
//	    }
        $list = $this->sendBatchGetOrderInfoByTidArr(array_pluck($response['data']['orderList'], 'orderBaseInfo.oid'));
        \Log::info("coursor=" . $response['data']['cursor']);
        if ($response['data']['cursor'] != 'nomore') {
            $this->hasNext = true;
            $this->tmp_cursor = $response['data']['cursor'];
        } else {
            $this->hasNext = false;
        }

        return $list;
    }

    /**
     * @return KsClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        $appKey = config('socialite.ks.client_id');
        $secretKey = config('socialite.ks.client_secret');
        return new KsClient($appKey, $secretKey);
    }

    /**
     * @inheritDoc
     */
    public function openSubscribeMsg():bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    public function consumeSubscribeMsg()
    {
        // TODO: Implement consumeSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function confirmSubscribeMsg(array $idArr)
    {
        // TODO: Implement confirmSubscribeMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function handleSubscribeMsg($data)
    {
        // TODO: Implement handleOrderMsg() method.
    }

    /**
     * @inheritDoc
     */
    public function createWebsocketConnection()
    {
        // TODO: Implement createWebsocketServer() method.
    }

    public function pageTurning()
    {
//        $this->hasNext = false;
        $this->page_cursor = $this->tmp_cursor;
        $this->tmp_cursor = '';
    }

    /**
     * @return string
     */
    public function getPageCursor(): string
    {
        return $this->tmp_cursor;
    }

    /**
     * 获取token
     * @return mixed
     * <AUTHOR>
     */
    public function getAccessToken()
    {
        if (!empty($this->accessToken)) {
            return $this->accessToken;
        }
        $shop = $this->getShop();
        if (empty($shop)) {
            throw new NotFoundResourceException('未找到授权');
        }
//        $this->setShopId($shop->identifier);
        return $this->accessToken = $shop->access_token;
    }

    /**
     * @inheritDoc
     */
    public function sendServiceInfo()
    {
        $result = [];
        $shop = $this->getShop();

        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'buyerOpenId' => $shop->auth_user_id
        ];
        $response = $client->execute('get', '/open/service/market/buyer/service/info', $params);
        \Log::info("ks shop_id:$shop->id 查询用户订购关系：", [$response]);
        if ($response['result'] != 1 || !isset($response['data']) || empty($response['data'])) {
            return $result;
        }
        $expired_at = !empty($response['data']['endTime']) ? date('Y-m-d H:i:s', $response['data']['endTime'] / 1000) :
            Carbon::now()->addDays(30)->toDateTimeString();
        if (empty($response['data']['inService'])) {
            $expired_at = null;
        }

        $packageName = $response['data']['packageName'];
        return [
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'platform_type' => $this->platformType,
            'expired_at' => $expired_at,
            'version' => UserExtra::VERSION_NAME_MAP[$packageName],
            'version_desc' => $packageName,
            'pay_amount' => 0,
        ];
    }

    /**
     * @see https://docs.qq.com/doc/DQ3hyU3F4b0p6TENX
     * @inheritDoc
     * @throws ClientException
     */
    protected function sendRefundOrders(int $startTime, int $endTime)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
//        $client->setShopId($this->getShopId());
        $params = [
            'beginTime' => $startTime * 1000,
            'endTime' => $endTime * 1000,
            'type' => 9, // 退款单请求类型，8 等待退款 9 全部退款订单
//            'sellerId' => $this->getShopId(),
            'pageSize' => $this->pageSize,
//            'pageSize' => 10,
            'currentPage' => 1, // 1时间降序 2时间升序
            'queryType' => 2, // 1按创建时间查找 2按更新时间查找
            'pcursor' => $this->page_cursor,
            'negotiateStatus' => 1, // 协商状态 1,:"待商家处理" 2: "商家同意" 3: "商家驳回，等待买家修改"
        ];
        $response = $client->execute('get', '/open/seller/order/refund/pcursor/list', $params);
        //用户取消授权
        if (isset($response['result']) && in_array($response['result'], array_keys($this->errorCodeMap))) {
            $this->errorInfo['code'] = array_get($this->errorCodeMap, $response['result'], 0);
            $this->errorInfo['info'] = $response;
            return $this->errorInfo;
        }

        if ($response['data']['pcursor'] != 'nomore') {
            $this->hasNext = true;
            $this->tmp_cursor = $response['data']['pcursor'];
        } else {
            $this->hasNext = false;
        }

        $data = [];
        //售后订单处理 重复订单取最新一条
        if (!empty($response['data']['refundOrderInfoList'])) {
            foreach ($response['data']['refundOrderInfoList'] as $item) {
                if (!array_key_exists($item['oid'], $data)) {
                    $data[$item['oid']] = $item;
                } else {
                    if ($item['createTime'] > $data[$item['oid']]['createTime']) {
                        $data[$item['oid']] = $item;
                    }
                }
            }
        }

        return $data;
    }

    /**
     * @inheritDoc
     */
    protected function formatRefundOrder($order)
    {
        return [
            'tid' => (string)$order['oid'],
            'oid' => (string)$order['oid'],
            'refund_id' => $order['refundId'],
//            'express_no' => $order['tracking_number'],
            'refund_created_at' => date('Y-m-d H:i:s', $order['submitTime'] / 1000),
            'refund_updated_at' => date('Y-m-d H:i:s', $order['updateTime'] / 1000),
            'refund_status' => $this->formatRefundStatus($order['status']),
        ];
    }

    protected function sendGetTradesOrdersForOpenApi(int $startTime, int $endTime, bool $isFirstPull = false)
    {
        $type = 1;
        if ($isFirstPull) {
            $type = 3;
        }

        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'beginTime' => $startTime * 1000,
            'endTime' => $endTime * 1000,
            'type' => $type, // 订单状态，0未知 1 全部 2 待付款 3 待发货 4 待收货（已发货）5 已收货 6 交易成功订单 7 已关闭订单
            'pageSize' => $this->pageSize,
            'currentPage' => $this->getPage(), // 当前页
            'queryType' => 2, // 1按创建时间查找 2按更新时间查找
            'pcursor' => $this->page_cursor,
        ];
        $response = $client->execute('get', '/open/seller/order/pcursor/list', $params);
        if (!isset($response['result']) || $response['result'] != 1) {
            return throw_error_code_exception(StatusCode::SYSTEM_ERROR);
        }

        return $response['data'];
    }

    public function formatToOrdersForOpenApi($data)
    {
        $result = [
            'page' => $data['currentPage'],
            'pageSize' => $data['pageSize'],
            'totalPage' => $data['totalPage'],
            'totalSize' => $data['totalSize'],
            'pcursor' => $data['pcursor'],
            'orderList' => []
        ];
        $orderList = [];
        if (!empty($data['orderInfoList'])) {
            foreach ($data['orderInfoList'] as $item) {
                $receiver = json_decode($item['address'], true);
                $orderList[] = [
                    'receiverName' => dataDesensitization($receiver['consignee'], 0, -1), //收件人姓名脱敏处理
                    'receiverPhone' => dataDesensitization($receiver['mobile'], 3, 4), //收件人手机脱敏处理
                    'province' => $receiver['province'],
                    'city' => $receiver['city'] == "市辖区" ? $receiver['province'] : $receiver['city'],
                    'town' => $receiver['district'],
                    'address' => dataDesensitization($receiver['address'], 4),
                    'orderSn' => $item['oid'],
                    'goodsName' => $item['orderProductInfoList'][0]['itemTitle'],
                    'goodsImg' => $item['orderProductInfoList'][0]['itemPicUrl'],
                    'goodsSpec' => $item['orderProductInfoList'][0]['skuDesc'],
                    'goodsNum' => $item['num'],
                    'payAmount' => $item['totalFee'],
                    'remark' => $item['remark'],//买家留言
                    'orderStatus' => $item['status'],//订单状态 0未知 10代付款 30已付款 40已发货 50已签收 70订单成功 80订单失败
                ];
            }
        }
        $result['orderList'] = $orderList;

        return $result;
    }

    public function formatToOrderInfoForOpenApi($data)
    {
        $result = [];
        if (!empty($data['address'])) {
            $receiver = json_decode($data['address'], true);
            $result = [
                'receiverName' => dataDesensitization($receiver['consignee'], 0, -1), //收件人姓名脱敏处理
                'receiverPhone' => dataDesensitization($receiver['mobile'], 3, 4), //收件人手机脱敏处理
                'province' => $receiver['province'],
                'city' => $receiver['city'] == "市辖区" ? $receiver['province'] : $receiver['city'],
                'town' => $receiver['district'],
                'address' => dataDesensitization($receiver['address'], 4),
                'orderSn' => $data['oid'],
                'goodsName' => $data['orderProductInfoList'][0]['itemTitle'],
                'goodsImg' => $data['orderProductInfoList'][0]['itemPicUrl'],
                'goodsSpec' => $data['orderProductInfoList'][0]['skuDesc'],
                'goodsNum' => $data['num'],
                'payAmount' => $data['totalFee'],
                'remark' => $data['remark'],//买家留言
                'orderStatus' => $data['status'],//订单状态 0未知 10代付款 30已付款 40已发货 50已签收 70订单成功 80订单失败
            ];
        }
        return $result;
    }

    /**
     * @throws ClientException
     */
    public function sendEditSellerRemark($tid, $sellerFlag, $sellerMemo):bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $params = [
            'orderId' => $tid,
            'note' => $sellerMemo
        ];
        // 旗标
        if ($sellerFlag) {
            $star = [
                'flag' => array_flip($this->orderFlagMap)[$sellerFlag],
            ];
            $params = array_merge($params, $star);
        }
        $response = $client->execute('post', '/open/seller/order/note/add', $params);

        Log::info('ks_add_remark_result', [$response, $params]);
        if (isset($response['result']) && $response['result'] == 1) {
            return true;
        }

        return false;
    }

    public function sendMarketOrderDetail($oid)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $params = [
            'oid' => $oid
        ];
        $response = $client->execute('get', '/open/service/market/order/detail', $params);

        \Log::info('ks_get_market_order_info result:', [$response]);
        if (!isset($response['result']) || $response['result'] != 1) {
            $this->errorInfo['code'] = $response['result'];
            $this->errorInfo['info'] = $response;
            return $this->errorInfo;
        }

        return $response['data'];
    }

    public function sendPlatformOrder($startTime, $endTime)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $params = [
            'startTime' => strtotime($startTime) * 1000,
            'endTime' => strtotime($endTime) * 1000,
            'queryType' => 2, //1创建时间 2支付时间
            'status' => 30, //10待付款，30已付款，80已关闭
            //'pageSize' => $this->pageSize,
            'pageSize' => 10,
            'pageNum' => $this->getPage()
        ];

        $response = $client->execute('get', '/open/service/market/order/list', $params);
        \Log::info('ks_get_platform_order_info params:' . json_encode($params) . ' result:', [$response]);

        if (!isset($response['result']) || $response['result'] != 1) {
            $this->errorInfo['code'] = $response['result'];
            $this->errorInfo['info'] = $response;
            return $this->errorInfo;
        }

        //if (ceil($response['data']['totalCount'] / $response['data']['pageSize']) > $this->getPage()) {
        if (ceil($response['data']['totalCount'] / 10) > $this->getPage()) {
            $this->hasNext = true;
        } else {
            $this->hasNext = false;
        }

        return $response['data']['orderList'] ?? [];
    }

    protected function sendRefundOrderInfo($afterSaleId)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'refundId' => $afterSaleId
        ];
        $response = $client->execute('get', '/open/seller/order/refund/detail', $params);
        \Log::info('获取售后订单详情:', [$response]);
        //用户取消授权
        if (isset($response['result']) && in_array($response['result'], array_keys($this->errorCodeMap))) {
            $this->errorInfo['code'] = array_get($this->errorCodeMap, $response['result'], 0);
            $this->errorInfo['info'] = $response;
            return $this->errorInfo;
        }

        return $response['data'] ?? [];
    }

    public function sendBatchGetOrderInfo($orders,$safe=false)
    {
        //并行异步请求
        $data = $result = [];
//        $url   = '/open/seller/order/detail';
        $url = '/open/order/detail'; // v2
//        $url = '/open/seller/order/detail';
        foreach ($orders as $order) {
            $idStr = handleOrderIdStr($order);
            $params = ['oid' => $order['tid']]; // v2
//            $params = ['order_id' => $order['tid']];
            $data[$idStr] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . $url
            ];
        }

        $response = $this->poolCurl($data, 'get');
        foreach ($response as $orderId => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['data'] ?? [];
            if (empty($trade)) {
                \Log::warning("获取订单内容失败",[$orderId,$orderInfo,$this->getAccessToken()]);
                continue;
            }
            $result[$orderId] = $trade;
//            $result[$orderId] = $this->formatToOrder($trade);
        }

        return $result;
    }

    /**
     * 批量获取订单详情
     * @param $tidArr
     * @return array
     * <AUTHOR>
     */
    public function sendBatchGetOrderInfoByTidArr($tidArr)
    {
        //并行异步请求
        $data = $result = [];
//        $url   = '/open/seller/order/detail';
        $url = '/open/order/detail'; // v2
//        $url = '/open/seller/order/detail';
        foreach ($tidArr as $tid) {
            $params = ['oid' => $tid];
            $data[] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . $url
            ];
        }
        $response = $this->poolCurl($data, 'get');
        foreach ($response as $index => $orderInfo) {
            $orderInfo = json_decode(json_encode($orderInfo), true);
            $trade = $orderInfo['data'] ?? [];
            if (empty($trade)) {
                continue;
            }
            $result[$index] = $trade;
        }

        return $result;
    }

    /**
     * 生成请求数据
     * @param $url
     * @param array $params
     * @return array
     */
    public function getRequestData($url, array $params): array
    {
        if (strpos($url, '/') === 0) {
            $url = substr($url, 1);
        }
        $method = str_replace('/', '.', $url);
        $request = [
            'appkey' => config('socialite.ks.client_id'),
            'access_token' => $this->getAccessToken(),
            'version' => 1,
            'param' => json_encode($params, 320),
            'method' => $method,
            'signMethod' => 'MD5',
        ];
        $request['sign'] = $this->generateSign($request);
        return $request;
    }

    /**
     * KS有些接口需要用post，所以把参数拼接到url上，但post去调用
     * @param $url
     * @param array $params
     * @param array $excludes
     * @return string
     */
    public function getRequestUrl($url, array $params,array $excludes=[]): string
    {
        if (strpos($url, '/') === 0) {
            $method = substr($url, 1);
        }
        $method = str_replace('/', '.', $method);
        $request = [
            'appkey' => config('socialite.ks.client_id'),
            'access_token' => $this->getAccessToken(),
            'version' => 1,
            'param' => json_encode($params, 320),
            'method' => $method,
            'signMethod' => 'MD5',
        ];
        $request['sign'] = $this->generateSign($request);
        /**
         * 排除掉指定字段不加入到url中
         */
        foreach ($excludes as $exclude) {
            unset($request[$exclude]);
        }

        return http_build_query($request);
    }


    public function generateSign($params)
    {
        ksort($params);
        $sign = '';
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k=$v&";
            }
        }
        $sign .= "signSecret=" . env('KS_SIGN_SECRET', 'cf87e89ac34dc9dacfc73d6f7039ba0a');
        unset($k, $v);

        return md5($sign);
    }

    public function sendServiceOrderList($beginAt, $endAt)
    {
        // TODO: Implement sendServiceOrderList() method.
    }

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sentBatchDecrypt($order)
    {
        // TODO: Implement sentBatchDecrypt() method.
    }

    /**
     * 批量解密
     * @param $order
     * @return mixed
     */
    protected function sendDecrypt($order)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params['batchDecryptList'][] = [
            'bizId' => $order['tid'],
            'encryptedData' => $order['text'],
        ];
        $text = '';
        try {
            $result = $client->execute('post', '/open/order/decrypt/batch', $params);
            $this->handleErrorCode($result);
            $text = $result['batchDecryptResultList'][0]['decryptedData'] ?? '';
        } catch (\Exception $e) {
            Log::error('sentDecrypt error:' . $e->getMessage());
        }
        return [['text' => $text]];
    }

    /**
     * 批量加密
     * @param $order
     * @return mixed
     */
    protected function sendBatchEncrypt($list)
    {
        if (empty($list)) {
            return [];
        }
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $response = $client->execute('post', '/open/order/encrypt/batch', [
            'batchEncryptList' => $list,
        ]);
        $returnData = [];
        if (!isset($response['batchEncryptResultList']) && empty($response['batchEncryptResultList'])) {
            return [];
        }
        foreach ($response['batchEncryptResultList'] as $index => $item) {
            $returnData[] = [
                'tid' => $item['bizId'],
                'cipher_text' => $item['encryptedData'],
                'decrypt_text' => $item['decryptedData'],
            ];
        }
        return $returnData;
    }

    /**
     * 日志批量上传
     * @see https://open.kwaixiaodian.com/docs/api?apiName=open.security.log.batch&categoryId=62&version=1
     * @param string $method
     * @param array $data
     * @return bool
     * @throws ClientException
     * @throws OrderException
     * <AUTHOR>
     */
    public function sendLogUploadBatch(string $method, array $data): bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $data = json_encode($data, JSON_UNESCAPED_UNICODE);
        $params = [
            'method' => $method,
            'data' => $data,
        ];
        $response = $client->execute('post', '/open/security/log/batch', $params);
        if (!isset($response['result']) || $response['result'] != 1) {
            throw new OrderException('日志批量上传:' . $response['error_msg']);
        }
        Log::debug('sendLogUploadBatch', compact('params', 'response'));
        return true;
    }

    /**
     * 日志批量上传
     * @see operation 看 https://open.kwaixiaodian.com/docs/dev?pageSign=51312989e4795471e052d1d5dba730d71625457048192
     * @param Event $event
     * @return bool
     */
    public function reportBatchLogByEvent(Event $event): bool
    {
        $data = [];
        if ($event instanceof UserLoginEvent) {
            $method = 'login';
            $data[] = [
                'sellerId' => $event->shop->identifier,
                'userIp' => $event->clientIp,
                'loginResult' => $event->loginStatus == $event::LOGIN_STATUS_SUCCESS ? 'success' : 'fail',
                'time' => $event->time * 1000,
            ];
        } elseif ($event instanceof OrderQueryEvent) {
            $method = 'order';
            $data[] = [
                'sellerId' => $event->shop->identifier,
//            'orderIds' => '[]',
                'url' => $event->clientUrl,
                'operation' => 7,
                'userIp' => $event->clientIp,
                'data' => json_encode($event->queryConditions, JSON_UNESCAPED_UNICODE),
                'orderTotal' => $event->orderTotal,
                'time' => $event->time * 1000,
            ];
        } elseif ($event instanceof OrderPrintEvent) {
            $method = 'order';
            foreach (array_chunk($event->orderIds, 100) as $orderIdArr) {
                $data[] = [
                    'sellerId' => $event->shop->identifier,
                    'orderIds' => json_encode($orderIdArr),
                    'url' => $event->clientUrl,
                    'operation' => 8,
                    'userIp' => $event->clientIp,
//                'data' => ,
                    'orderTotal' => count($orderIdArr),
                    'time' => $event->time * 1000,
                ];
            }
        } elseif ($event instanceof OrderDecryptEvent) {
            $method = 'order';
            foreach ($event->orderIds as $orderId) {
                $data[] = [
                    'sellerId' => $event->shop->identifier,
                    'orderIds' => "[$orderId]",
                    'url' => $event->clientUrl,
                    'operation' => 2,
                    'userIp' => $event->clientIp,
//            'data' => ,
                    'orderTotal' => 1,
                    'time' => $event->time * 1000,
                ];
            }
        } elseif ($event instanceof OrderUpdateEvent) {
            $method = 'order';
            foreach ($event->orderIds as $orderId) {
                $data[] = [
                    'sellerId' => $event->shop->identifier,
                    'orderIds' => "[$orderId]",
                    'url' => $event->clientUrl,
                    'operation' => 6,
                    'userIp' => $event->clientIp,
//            'data' => ,
                    'orderTotal' => 1,
                    'time' => $event->time * 1000,
                ];
            }
        } elseif ($event instanceof SqlLogEvent) {
            $method = 'sql';
            $data[] = [
                'type' => 'mysql',
                'sql' => $event->sql,
                'time' => $event->time * 1000,
            ];
        } else {
            return true;
        }
        return $this->sendLogUploadBatch($method, $data);
    }

    /**
     * 处理错误码
     * @see https://open.kwaixiaodian.com/docs/dev?pageSign=5de448fecaddd4c58104e8aea442695b1614263998209
     * <AUTHOR>
     * @param $result
     * @throws ApiException
     * @throws OrderException
     */
    private function handleErrorCode($result)
    {
        if (!isset($result['result'])) {
            throw new OrderException('快手服务异常');
        }
        switch ($result['result']) {
            case 1: // 正确
                break;
            case 24:
            case 28:
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                Log::error('快手服务异常', $result);
                throw new OrderException('快手服务异常：' . $result['error_msg']);
        }
    }

    /**
     * @inheritDoc
     */
    public function checkAuthStatus(): bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'pageSize' => 1,
            'itemStatus' => 1,
            'itemType' => 1,
            'pageNumber' => 1,
        ];

        try {
            $goods = $client->execute('get', '/open/item/list/get', $params);
            $this->handleErrorCode($goods);
        } catch (ApiException $e) {
            if (ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0] == $e->getCode()) {
                return false;
            }
        } catch (\Exception $e) {

        }
        return true;

    }

    /**
     * @inheritDoc
     */
    public function sendFactoryShopRoleType(): int
    {
        // TODO: Implement sendDDShopRoleType() method.
        return 0;
    }

    /**
     * @inheritDoc
     */
    public function getFactoryTradesOrder(int $startTime, int $endTime)
    {
        // TODO: Implement getDDTradesOrder() method.
    }

    /**
     * 重新订单发货
     * @param OrderDeliverAgainRequest $orderDeliverAgainRequest
     * @return bool
     * @throws ApiException
     * @throws ClientException
     * @throws OrderException
     */
    protected function deliverySellerOrdersAgain(OrderDeliverAgainRequest $orderDeliverAgainRequest): bool
    {
        $tid = $orderDeliverAgainRequest->tid;
        $expressCode = $orderDeliverAgainRequest->wpCode;
        $expressNo = $orderDeliverAgainRequest->waybillCode;
        $orderItemOId = [];
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'orderId' => $tid,
            'expressNo' => $expressNo,
            'logisticsId' => $orderDeliverAgainRequest->deliveryId,
            'expressCode' => array_get($this->expressCodeList, $expressCode, self::OTHER_CODE),
        ];
        $result = $client->execute('post', '/open/seller/order/logistics/update', $params);

//        //发货失败重试3次
//        if (!isset($result['result']) || $result['result'] != 1) {
//            $num = 1;
//            while ($num <= 3) {
//                $result = $client->execute('post', '/open/seller/order/goods/deliver', $params);
//                Log::info('订单重新发货失败重试_' . $num, [$result, $this->platformType, $tid, $expressCode, $expressNo]);
//                if (isset($result['result']) && $result['result'] == 1) {
//                    break;
//                }
//                $num++;
//            }
//        }

        if (!isset($result['result']) || $result['result'] != 1) {
            Log::error('订单重新发货失败!', [$result, $this->platformType, $tid, $expressCode, $expressNo]);
            $this->handleErrorCode($result);
            return false;
        }

        return true;
    }

    /**
     * @see https://open.kwaixiaodian.com/docs/api?apiName=open.order.search.index.batch&categoryId=43&version=1
     * <AUTHOR>
     * @param string $text
     * @param string $type
     * @return string
     * @throws ClientException
     */
    public function getSearchIndex(string $text, string $type): string
    {
        // 临时处理，等快手电子面单上了
        return $text;
        if ($type == 'phone' && !isPhoneNumber($text)) {
            return '';
        }
        $typeMap = [
            'name' => 2,
            'phone' => 3,
            'address' => 1,
        ];
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params['indexParamList'][] = [
            'plainText' => $text,
            'type' => $typeMap[$type],
        ];

        try {
            $result = $client->execute('post', '/open/order/search/index/batch', $params);
            $this->handleErrorCode($result);
            if (isset($result['indexResultList'][0]['searchIndex'])) {
                return $result['indexResultList'][0]['searchIndex'];
            }
        } catch (\Exception $e) {
            Log::error('getSearchIndex error:' . $e->getMessage());
        }
        return "";
    }

    /**
     * @inheritDoc
     */
    public function batchGetFactoryOrderInfo($orders)
    {
        // TODO: Implement batchGetFactoryOrderInfo() method.
    }

    /**
     * @inheritDoc
     */
    public function batchReturnFactoryOrder($orders)
    {
        // TODO: Implement batchReturnFactoryOrder() method.
    }

    public function sendGetSellerList()
    {
        // TODO: Implement sendGetSellerList() method.
    }

    /**
     * 请求查询交易订单的 Tid
     * @param array $query_list
     * <AUTHOR>
     */
    public function sendQueryTradeTid(array $query_list)
    {
        // TODO: Implement sendQueryTradeTid() method.
    }

    /**
     * @inheritDoc
     */
    public function sendAddress(): array
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $params = [
            'districtVersion' => 0,
        ];
        $result = $client->execute('post', '/open/address/district/list', $params);
        if (!isset($result['result'])) {
            return [];
        }

        $list = $this->formatAddress($result['data']['districts']);

        return $list;
    }

    protected function formatAddress(array $list, $parent_code = 1, $level = 1)
    {
        $resArr = [];
        foreach ($list as $item) {
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parent_code,
                'level' => $level,
            ];
            if (!empty($item['children']) && is_array($item['children'])) {
                $arr['sub'] = $this->formatAddress($item['children'], $item['code'], $level + 1);
            }
            $resArr[] = $arr;
        }
        return $resArr;
    }

    public function getQueryTradeOrderId(string $type, string $search)
    {
        $shop = $this->getShop();
        if (ksEncryptSwitch($shop->id)) {
            $searchType = $type == 'receiver_name' ? 2 : ($type == 'receiver_phone' ? 3 : 1);
            if (empty($search) || ($searchType == '3' && !isPhoneNumber($search))) {
                return [];
            }
            $client = $this->getClient();
            $client->setAccessToken($this->accessToken);
            $params = [
                [
                    'plainText' => $search,
                    'type' => $searchType
                ]
            ];

            $response = $client->execute('post', '/open/order/search/index/batch', ['indexParamList' => $params]);
            if (isset($response['result']) && $response['result'] == 1 && !empty($response['indexResultList'])) {
                $conditions = [];
                $beginAt = \request()->input('begin_at');
                $endAt = \request()->input('end_at');
                $timeField = \request()->input('timeField');
                if ($timeField && $beginAt && $endAt) {
                    $conditions[] = [$timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
                    $conditions[] = [$timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
                }
                $keyword = $response['indexResultList'][0]['searchIndex'];
                $shop = $this->getShop();
                $query = Order::query()->select('id')
                    ->where('shop_id', $shop->id)
                    ->where($conditions)
                    ->where($type, $keyword);

                if (ksEncryptSwitch($shop->id)) {
                    $query->where($type, $keyword);
                } else {
                    $query->where($type, $search);
                }
                $idArr = $query->get()->pluck('id')->toArray();

                return $idArr;
            }
        } else {
            $conditions = [];
            $beginAt = \request()->input('begin_at');
            $endAt = \request()->input('end_at');
            $timeField = \request()->input('timeField');
            if ($timeField && $beginAt && $endAt) {
                $conditions[] = [$timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
                $conditions[] = [$timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
            }
            $shop = $this->getShop();
            $idArr = Order::query()->select('id')
                ->where('shop_id', $shop->id)
                ->where($conditions)
                ->where($type, $search)
                ->get()
                ->pluck('id')
                ->toArray();

            return $idArr;
        }

        return [];
    }

    /**
     * 批量数据解密
     * @see  https://open.pinduoduo.com/application/document/api?id=pdd.open.decrypt.batch
     * <AUTHOR>
     * @return mixed
     */
    public function cipherDecryptBatch()
    {
        // TODO: Implement cipherDecryptBatch() method.
    }

    /**
     * 批量数据解密脱敏
     * @see https://open.pinduoduo.com/application/document/api?id=pdd.open.decrypt.mask.batch
     * <AUTHOR>
     * @param array $list
     * @return array
     */
    public function cipherDecryptMaskBatch(array $list): array
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());
        $cipher_infos = [];
        foreach ($list as $item) {
            $cipher_infos[] = [
                'bizId' => $item['tid'],
                'encryptedData' => $item['text'],
            ];
        }
        $response = $client->execute('post', '/open/order/decrypt/batch', [
            'batchDecryptList' => $cipher_infos,
        ]);
        $returnData = [];
        foreach ($response['batchDecryptResultList'] as $index => $item) {
            $returnData[] = [
                'tid' => $item['bizId'],
                'text' => $item['decryptedData'],
            ];
        }
        return $returnData;
    }

    /**
     * 剥离数据检索字符串
     * @param string $encryptedData
     * @return string
     * <AUTHOR>
     */
    public function cipherExtractSearch(string $encryptedData): string
    {
        if (empty($encryptedData)) {
            return '';
        }
        $separator = $encryptedData[0];
        $explodeArr = explode($separator, $encryptedData);
        return $explodeArr[1];
    }

    /**
     * @inheritDoc
     */
    public function batchDeliveryOrders(array $orderDeliveryRequests): array
    {
        $requestData = [];
        $url = '/open/seller/order/goods/deliver';
        foreach ($orderDeliveryRequests as $index => $orderDeliveryRequest) {
            $tid = $orderDeliveryRequest->tid;
            $expressCode = $orderDeliveryRequest->expressCode;
            $expressNo = $orderDeliveryRequest->expressNo;
            $orderItemOId = $orderDeliveryRequest->orderItemOId;
            $params = [
                'orderId' => $tid,
                'expressNo' => $expressNo,
                'expressCode' => array_get($this->expressCodeList, $expressCode, self::OTHER_CODE),
            ];
            $requestData[$index] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . $url
            ];
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'get');
        $results = [];
        \Log::info("发货返回",[$responses]);
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $orderDeliveryRequest = $orderDeliveryRequests[$index];
                $commonResponse->setRequest($orderDeliveryRequest);
                $commonResponse->setRequestId($orderDeliveryRequest->getRequestId());
                $result = json_decode(json_encode($result), true);

                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            } catch (\Exception $e) {
                \Log::info("处理异常".$e->getMessage());
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            } finally {
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    public function batchWaybillRecoveryFactoryOrder($waybills)
    {
        // TODO: Implement batchWaybillRecoveryFactoryOrder() method.
    }

    /**
     * @inheritDoc
     */
    public function sendByCustom($requestMethod, $apiMethod, $apiParams,$order)
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $apiMethod = str_replace('.', '/', $apiMethod);
        if (strpos($apiMethod, '/') !== 0) {
            $apiMethod = '/' . $apiMethod;
        }
        $response = $client->executeByCustom($requestMethod, $apiMethod, $apiParams);
        return $response;
    }
    /**
     * @param $apiMethod
     * @param $apiParams
     * @param $order
     * @param null $orderShop
     * @inheritDoc
     */
    public function fillApiParamByOrder($apiMethod, $apiParams, $order, $orderShop = null): array
    {
        switch ($apiMethod) {
            case 'open.express.ebill.get';
                foreach ($apiParams['getEbillOrderRequest'] as &$apiParam) {
                    if (empty($apiParam["merchantCode"]) && !empty($orderShop)){
                        $apiParam["merchantCode"] = $orderShop['identifier'];
                    }
                    if (empty($apiParam["merchantName"]) && !empty($orderShop)){
                        $apiParam["merchantName"] = $orderShop['shop_name'];
                    }
                }
                if (!empty($order)){
                    $receiverContract = [
                        'name' => $order['order_cipher_info']['receiver_name_ciphertext'],
                        'mobile' => $order['order_cipher_info']['receiver_phone_ciphertext'],
                    ];
                    $detailAddress = $order['order_cipher_info']['receiver_address_ciphertext'];
                    $apiParams['getEbillOrderRequest'][0]['receiverContract'] = $receiverContract;
                    $apiParams['getEbillOrderRequest'][0]['receiverAddress']['detailAddress'] = $detailAddress;
                }
                break;
        }
//        \Log::info("快手请求参数",[$apiParams]);
        return $apiParams;
    }

    /**
     * 转换订单旗帜
     * @param $flag
     * @return int
     * <AUTHOR>
     */
    public function formatOrderFlag($flag)
    {
        if (isset($this->orderFlagMap2[$flag])) {
            return $this->orderFlagMap2[$flag];
        }
        if (!isset($this->orderFlagMap[$flag])) {
            throw new InvalidArgumentException('未定义的订单旗帜：' . $flag);
        }
        return $this->orderFlagMap[$flag];
    }

    /**
     * 快手的发货分两部分，第一部分是发货，第二部分是追加包裹
     * 订单对应的第一个快递单是发货，其他的号是追加包裹
     * @param int $shopId
     * @param array $orderDeliveryRequests
     * @return array
     * @see AbstractOrderService::orderMultiPackagesDelivery()
     */
    public function orderMultiPackagesDelivery($shopId, array $orderDeliveryRequests): array
    {
        $requestData = [];
//        $appendRequestData = [];
//        $appendPackageUrl = '/open/seller/order/goods/logistics/append';
//        $deliverOrderUrl = '/open/seller/order/goods/deliver';
        $deliverOrderUrl = '/open/order/goods/split/deliver'; // 订单拆单发货 支持整单发
        $this->poolCurlAbnormalOutputOriginalData = true;
        foreach ($orderDeliveryRequests as  $orderDeliveryRequest) {


            $tid = $orderDeliveryRequest['tid'];
            $requestItems = [];
            $waybillCodes = [];
            foreach ($orderDeliveryRequest['packs'] as $index=> $pack) {
                $expressCode=$pack['expressCode'] ;
                $waybillCode = strval($pack['waybillCode']);
                $expressCode = array_get($this->expressCodeList, $expressCode, self::OTHER_CODE);
                $deliveryGoodsInfoList = [];
                $packs = [];
                foreach ($pack['goods'] as $goods) {
                    $oid = $goods['oid'];
                    $deliveryGoodsInfoList[] = [
                        "oid" => $oid,
                        "deliveryNum" => $goods['shippedNum']
                    ];
                    $packs[] = ["oid" => $oid, "shippedNum" => $goods['shippedNum']];
                }
                $requestItem = [];
//                if($index==0){
//                if (true) {
                //第一个包裹是发货
//                $requestItem['orderId'] = $tid;
                $requestItem['expressNo'] = $waybillCode;
                $requestItem['expressCode'] = $expressCode;
                $requestItem['deliveryGoodsInfoList'] = $deliveryGoodsInfoList;

                $requestItems[] = $requestItem;
                $waybillCodes[] = ["waybillCode" => $waybillCode, "expressCode" => $expressCode, "packs" => $packs];

//                }
//                else {
//                    $appendRequestItem = [];
//                    //其他包裹是追加包裹
//                    $appendRequestItem['expressCode'] =$expressCode;
//                    $appendRequestItem['oid'] = $tid;
//                    $appendRequestItem['expressNo'] = $waybillCode;
//                    $appendRequestData[] = [
//                        'tid' => $tid,
//                        'waybillCode' => $waybillCode,
//                        'expressCode' => $expressCode,
//                        'packs'=>$packs,
//                        'params' => ["param"=>json_encode($appendRequestItem)],
//                        'url' => $this->gatewayUrl . $appendPackageUrl."?".$this->getRequestUrl($appendPackageUrl,
//                                $appendRequestItem,['param'])
//                    ];
//                }
            }
            $orderItemStatusArr = $orderDeliveryRequest['orderItemStatusArr'] ?? [];
            $deliveryStatusArr = [];
            foreach ($orderItemStatusArr as $orderItemStatusItem) {
                // 由商家确认的发货状态。10-部分发货、40-全部发货
                $status = $orderItemStatusItem['status'] == Order::ORDER_STATUS_DELIVERED ? 40 : 10;
                $deliveryStatusArr[] = [
                    'oid' => $orderItemStatusItem['oid'],
                    'confirmDeliveryStatus' => $status,
                ];
            }
            $param = [
                'mainOrderId' => $tid,
                'deliveryItemInfoList' => $requestItems,
                'deliveryStatus' => $deliveryStatusArr
            ];
            $requestData[$tid] = [
                'waybillCodes' => $waybillCodes,
                'shopId'=>$shopId,
                'params' => $this->getRequestData($deliverOrderUrl, $param),
                'url' => $this->gatewayUrl .  $deliverOrderUrl
            ];
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'post_form',10,true);
        Log::info('orderMultiPackagesDelivery', [$requestData, $responses]);
        $successes = [];
        $failures = [];
        //先处理订单发货
        foreach ($responses as $tid=> $response) {
            $requestItem = $requestData[$tid];
            try {
                $result = json_decode(json_encode($response), true);

                $this->handleErrorCode($result);
//                $successes[] = ['tid' => $tid,"shopId"=>$shopId,    'expressCode' => $expressCode, "waybillCode"=> $waybillCode,"packs"=>$packs];
                $successes[] = ['tid' => $tid, "shopId"=>$shopId,  "waybillCodes" => $requestItem['waybillCodes']];
            } catch (\Exception $e) {
//                $failures[] = ['tid' => $tid, "shopId"=>$shopId,'expressCode' => $expressCode,"waybillCode"=>
//                    $waybillCode,"packs"=>$packs,'msg'
//                => $e->getMessage()];
                $subMsg = $e->getMessage();
                Log::info('拆单发货失败', [$tid, $requestItem, $response]);
                $failures[] = ['tid' => $tid,"shopId"=>$shopId, "waybillCodes" => $requestItem['waybillCodes'], 'msg' => $subMsg];
            }
        }
//        $responses = $this->poolCurl($appendRequestData, 'post_form');
//        //再处理追加包裹
//        foreach ($responses as $index=>$response) {
//            $appendRequestItem = $appendRequestData[$index];
//            $tid = $appendRequestItem['tid'];
//            $waybillCode = $appendRequestItem['waybillCode'];
//            $packs = $appendRequestItem['packs'];
//            $expressCode = $appendRequestItem['expressCode'];
//            try {
//                $result = json_decode(json_encode($response), true);
//                $this->handleErrorCode($result);
//                $successes[] = ['tid' => $tid,"shopId"=>$shopId,'expressCode' => $expressCode,"waybillCode"=> $waybillCode,"packs"=>$packs];
//            } catch (\Exception $e) {
//                $failures[] = ['tid' => $tid,"shopId"=>$shopId,'expressCode' => $expressCode,"waybillCode"=> $waybillCode,"packs"=>$packs, 'msg' =>
//                    $e->getMessage()];
//            }
//        }
        //现在快手的发货接口是一次发一个包裹，所以这里是拉平的，需要按订单号分组
        \Log::info("原始的成功数据", $successes);
        \Log::info("原始的失败数据", $failures);
//        $successes = empty($successes)?[]: ArrayUtil::array_group_by($successes, 'tid');
//        $failures = empty($failures)?[]:ArrayUtil::array_group_by($failures, 'tid');
        /**
         * 返回的原始数据格式
         * [
         *  ['tid' => $tid,"waybillCodes"=> [
         * "waybillCode"=>"6919675406348588608",
         * "expressCode"=>"xxx",
         *  "packs"=>[
         *      ["oid"=>111,"shippedNum"=>1],
         *  ]
         * ]
         * ]
         */
//        $changeSuccesses = [];
//        foreach ($successes as $tid => $success) {
//
//            $waybillCodes=[];
//            foreach ($success as $item) {
//                $waybillCodes[] = ["waybillCode"=>$item["waybillCode"],"expressCode"=>$item["expressCode"],"packs"=>$item["packs"]];
//            }
//            $changeSuccesses[]= ['tid' => $tid, "shopId"=>$shopId,  "waybillCodes"=> $waybillCodes];
//
//        }
//        $changeFailures = [];
//        foreach ($failures as  $tid => $failure) {
//            $waybillCodes=[];
//            foreach ($failure as $item) {
//                $waybillCodes[] = ["waybillCode"=>$item["waybillCode"],"expressCode"=>$item["expressCode"],"packs"=>$item["packs"]];
//            }
//            $msg=implode(",",array_column($failure, 'msg'));
//            $changeFailures[]= ['tid' => $tid,"shopId"=>$shopId,"msg"=>$msg, "waybillCodes"=> $waybillCodes];
//        }
//        Log::info("转换后的数据",[ $changeSuccesses, $changeFailures]);
        return ["successes" => $successes, "failures" => $failures];
    }

    /**
     * @throws ClientException
     * @throws \Exception
     */
    public function orderAppendPackages(string $tid, string $wpCode, string $waybillCode, array $goodsList = []): bool
    {
        $client = $this->getClient();
        $client->setAccessToken($this->getAccessToken());

        $params = [
            'oid' => $tid,
            'expressNo' => $waybillCode,
            'expressCode' => array_get($this->expressCodeList, $wpCode, self::OTHER_CODE),
        ];
        $response = $client->execute('post', '/open/seller/order/goods/logistics/append',$params);
        $client::handleErrorCode($response);
        return true;
    }

    private function formatSubRefundStatus(array $trade)
    {
        $orderRefundInfo = array_first($trade['orderRefundList']);

        // [10, "买家已经申请退款，等待卖家同意"]
        // [12, "卖家已拒绝，等待买家处理"]
        // [20, "协商纠纷，等待平台处理"]
        // [30, "卖家已经同意退款，等待买家退货"]
        // [40, "买家已经退货，等待卖家确认收货"]
        // [45, "卖家已经发货，等待买家确认收货"]
        // [50, "卖家已经同意退款，等待系统执行退款"]
        // [60, "退款成功"]
        // [70, "退款关闭"]
        $refundStatus = $orderRefundInfo['refundStatus'] ?? 0;
        $handlingWay = $orderRefundInfo['handlingWay'] ?? 0; // 退款方式，枚举： [1, "退货退款"] [10, "仅退款"] [3, "换货"]

        $status = 0;
        switch ($refundStatus){
            case 10:// 买家已经申请退款，等待卖家同意
                $status = RefundSubStatusConst::MERCHANT_PROCESSING;
                break;
            case 12:// 卖家已拒绝，等待买家处理
                $status = RefundSubStatusConst::MERCHANT_REFUSE_REFUND;
                break;
            case 20:// 协商纠纷，等待平台处理
                $status = RefundSubStatusConst::PLATFORM_PROCESSING;
                break;
            case 30:// 卖家已经同意退款，等待买家退货
                $status = RefundSubStatusConst::WAIT_BUYER_RETURN;
                break;
            case 40:// 买家已经退货，等待卖家确认收货
                $status = RefundSubStatusConst::RETURN_WAIT_MERCHANT;
                break;
            case 45:// 卖家已经发货，等待买家确认收货
                $status = RefundSubStatusConst::WAIT_BUYER_EXCHANGE_RECEIVE;
                break;
            case 50:// 卖家已经同意退款，等待系统执行退款
                $status = RefundSubStatusConst::PLATFORM_PROCESSING;
                break;
            case 60:// 退款成功
                $status = RefundSubStatusConst::REFUND_COMPLETE;
                break;
            case 70:// 退款关闭
                $status = RefundSubStatusConst::REFUND_CLOSE;
                break;
        }

        return $status;
    }
}
