<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/3/17
 * Time: 16:33
 */

namespace App\Services\Order;

use App\Constants\ErrorConst;
use App\Constants\OrderErrorConst;
use App\Exceptions\ApiException;
use App\Constants\PlatformConst;
use App\Exceptions\OrderException;
use App\Jobs\Orders\SaveFactoryOrdersJob;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\Company;
use App\Models\Order;
use App\Models\Shop;
use App\Models\Waybill;
use App\Utils\Environment;
use Closure;
use GuzzleHttp\DefaultHandler;
use Illuminate\Support\Facades\Log;
use phpDocumentor\Reflection\Types\Integer;
use Swoole\Coroutine;
use Swoole\Coroutine\WaitGroup;
use Swoole\Runtime;
use Yurun\Util\Swoole\Guzzle\SwooleHandler;
use function Swoole\Coroutine\run;

/**
 * 订单相关逻辑的服务提供
 * Class OderLogicService
 * @package App\Services\Order
 */
class OderLogicService
{
    /**
     * @var AbstractOrderService
     */
    private $orderService;

    /** 订单总量限制 */
    protected $orderTotalLimit = 50000;
    protected $isFirstPull = true;
    /** 是否保存最后修改时间 */
    protected $isUpdateLastSyncAt = false;
    /** 是否保存最后修改时间 */
    protected $isSaveLastSync = false;
    /** @var bool 是否要更新 last_operated_at */
    protected $isUpdateLastOperatedAt = true;
    protected $requestCounter = 0;
    protected $shop = null;
    protected $beginAt = null;
    protected $endAt = null;
    /** 订单数 */
    protected $orderCount = 0;
    protected $callClassName = '';
    protected $isFactory = false;
    protected $action = '';
    protected $updateShopData = [];
    /**
     * @var
     */
    private $exception;
    /**
     * 是否同步积食订单
     * @var bool
     */
    private $isSyncLegacyOrder = false;

    /**
     * 使用 swoole 的协程来同步订单
     * @param $shop
     * @param string $beginTime
     * @param string $endTime
     * <AUTHOR>
     */
    public function handleSyncOrders($shop, string $beginTime, string $endTime)
    {
        $this->setShop($shop);
        $this->setBeginAt($beginTime);
        $this->setEndAt($endTime);
        // 兼容 swoole 和 Guzzle
        DefaultHandler::setDefaultHandler(SwooleHandler::class);
        Coroutine::set(['hook_flags' => SWOOLE_HOOK_ALL]);
        run(function () {
            // 通道 协程间通信队列
            $ch = new Coroutine\Channel();
            // 协程并发组
            $wg = new WaitGroup();

            $this->orderCount = 0;
            $userId = $this->shop->user_id;
            $this->orderService->setUserId($userId);
            $this->orderService->setShop($this->shop);
            $orderIncrTimeInterval = $this->orderService->orderTimeInterval;
            if ($this->isFactory()) {
                $orderIncrTimeInterval = $this->orderService->factoryOrderTimeInterval;
            }
            // 根据时间间隔 计算循环次数
            $len = $this->orderService->calcLenByTimeRange($this->beginAt, $this->endAt, $orderIncrTimeInterval);
            $shop_id = $this->shop->id;
            Log::info('OderLogicService run', [
                'shop_id' => $this->shop->id,
                'message' => 'start',
                'begin_at' => $this->beginAt,
                'end_at' => $this->endAt,
                'len' => $len,
                'callClassName' => $this->getCallClassName(),
            ]);

            if ($this->isUpdateLastOperatedAt) {
                $field = 'last_operated_at';
                if ($this->isFactory()) {
                    $field = 'last_factory_operated_at';
                }
                Shop::updateLastSyncIfNewest($shop_id, date('Y-m-d H:i:s'), $field);
//                Shop::updateLastOperatedIfNewest($shop_id, date('Y-m-d H:i:s'), $field);
            }

            $redis = redis('cache');
            if (!empty($this->action)) {
                // 初始化进度条
                // 当前同步的数量
                $key = "$this->action:sync_current:$shop_id";
                $redis->setex($key, 86400, 0);
                // 需要同步总数量
                $key = "$this->action:sync_total:$shop_id";
                $redis->setex($key, 86400, 0);
                // 脚本是否执行完成
                $key = "$this->action:sync_finish:$shop_id";
                $redis->setex($key, 86400, 0);
            }
            try {
                // 消费者
                go(function () use (&$ch, $redis, $shop_id) {
                    // 订单出列
                    while ($orders = $ch->pop()) {
//                        echo 'dispatch' . PHP_EOL;
                        Log::info(class_basename($this).':newSyncSaveOrders');
                        $obj = (new SyncSaveOrders(
                            $this->shop->user_id,
                            $this->shop->id,
                            $orders
                        ))->setAction($this->action);
                        if ($this->isFactory()) {
                            $obj = (new SaveFactoryOrdersJob(
                                $this->shop->id,
                                $orders
                            ))->setAction($this->action);
                        }
                        dispatch($obj);
                        if (!empty($this->action)) {
                            $key = "$this->action:sync_total:$shop_id";
                            $redis->incrby($key, count($orders));
                        }
                    }
                });
                // 生产者 根据时间拉订单
                for ($i = 0; $i < $len; $i++) {
                    $this->requestOrder($wg, $i, $ch);
//                    echo "requestOrder end" . PHP_EOL;
                    $chunkNum = 50; // 并发数
                    // 每 $chunkNum 个一组，并发拉取
                    if (Environment::isKs() ) {
                        $chunkNum = 30;
                    }
                    if (Environment::isXhs()) {
                        $chunkNum = 5;
                    }
                    if ($i % $chunkNum == 0) {
//                        echo "wait $i" . PHP_EOL;
                        $wg->wait();
                        sleep(1);
                        if (!empty($this->exception)) {
                            break;
                        }
                    }
                }
                // 生产者 积食订单处理
                if ($this->isSyncLegacyOrder) {
                    $this->handleLegacyOrder($wg, $ch);
                }
                // 跑完再修改最后同步时间
                if ($this->isSaveLastSync) {
                    Shop::updateLastSync($shop_id, $this->endAt);
                }

            } catch (OrderException | \Exception $exception) {
                $this->exception = $exception;
            } finally {
                $wg->wait();
                $ch->close();

                $key = "$this->action:sync_finish:$shop_id";
                $redis->setex($key, 86400, 1);
                // 先不修改
//                if ($this->isSaveLastSync && !empty($this->updateShopData)) {
//                    Shop::query()->where('id', $this->shop->id)
//                        ->update($this->updateShopData);
//                }
            }

//            echo "wait over" . PHP_EOL;
        });
        if (!empty($this->exception)) {
            Log::error('OderLogicService exception', [
                'msg' => $this->exception->getMessage(),
                'shop_id' => $this->shop->id,
                'beginAt' => $this->beginAt,
                'endAt' => $this->endAt,
                'callClassName' => $this->getCallClassName(),
            ]);
            if ($this->exception instanceof OrderException) {

            } else {
                throw $this->exception;
            }
        }
    }

    /**
     * 请求订单数据 并 push 到队列 $ch
     * @param WaitGroup $wg
     * @param int $i
     * @param Coroutine\Channel $ch
     * @return bool
     */
    public function requestOrder(WaitGroup $wg, int $i, Coroutine\Channel &$ch): bool
    {

        $wg->add();
        $orderService = clone $this->orderService;
        return go(function () use ($wg, $i, $orderService, &$ch) {
            try {
                $orderService->initPage();
                $orderIncrTimeInterval = $this->orderService->orderTimeInterval;
                if ($this->isFactory()) {
                    $orderIncrTimeInterval = $this->orderService->factoryOrderTimeInterval;
                }
                list($startTime, $endTime) = $orderService->calcTimeInterval($i, $orderIncrTimeInterval, $this->beginAt, $this->endAt);
                if ($startTime > time()) {
                    return true;
                }
                Log::info('OderLogicService do', [
                    'shop_id' => $this->shop->id,
                    'message' => 'before_order',
                    'startTime' => date('Y-m-d H:i:s', $startTime),
                    'endTime' => date('Y-m-d H:i:s', $endTime),
                    'i' => $i,
                    'callClassName' => $this->getCallClassName(),
                ]);

                $counter = 0;
                do {
                    $counter++;
                    if (!empty($this->exception)) {
                        Log::info("奇怪的终止1",$this->exception);
                        return false;
                    }
                    if ($this->isFactory()) {
                        $orders = $orderService->getFactoryTradesOrder($startTime, $endTime);
                    }else{
                        try {
                            $orders = $orderService->getTradesOrder($startTime, $endTime, $this->isFirstPull);
                        }catch (\GuzzleHttp\Exception\ConnectException $exception){
                            // 超时重试一次
                            try {
                                $orders = $orderService->getTradesOrder($startTime, $endTime, $this->isFirstPull);
                            }catch (\Exception $exception){
                                Log::error('OderLogicService getTradesOrder2 exception', [$exception]);
                                // 出错要终止
                                break;
                            }
                        }catch (\Exception $exception){
                            Log::error('OderLogicService getTradesOrder exception', [$exception]);
                            // 出错不终止
                            continue;
                        }
                        $code = $orders['code'] ?? '';
                        //取消授权终止
                        if (in_array($code, $this->orderService->errorCodeArr) && $this->isUpdateLastSyncAt) {
                            //还原时间，修改授权状态
                            if ($code == $this->orderService::ERROR_CODE_UNBIND) {
                                Log::info("授权改成失效",[$this->shop->id]);
                                $this->updateShopData = [
//                                    'last_sync_at' => $this->beginAt,
                                    'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE,
                                ];
                                $err = OrderErrorConst::AUTH_EXPIRE;
                                throw new OrderException($err[1], $err[0]);
                                // 授权过期直接跳出
                            }
                            if ($code == $this->orderService::ERROR_CODE_SERVER) {
                                $this->updateShopData = [
//                                    'last_sync_at' => $this->beginAt,
                                ];

                                // 出错直接跳出
                                $err = OrderErrorConst::ERROR;
                                throw new OrderException($err[1], $err[0]);
                            }

                        }
                    }


                    if (empty($orders)) {
                        Log::info("orders empty continue",[$this->shop->id]);
                        continue;
                    }
                    // 翻页
                    $orderService->pageTurning();
                    // 订单入列
                    $ch->push($orders);
                    $this->orderCount += count($orders);
                    if ($this->orderCount > $this->orderTotalLimit) {
//                        $this->updateShopData = [
//                            'last_sync_at' => $this->beginAt,
//                        ];
                        $err = OrderErrorConst::ORDER_UPPER_LIMIT;
                        throw new OrderException($err[1], $err[0]);
                    }
                    $this->requestCounter++;
//                    echo 'push:' . PHP_EOL;
                    Log::info(class_basename($this) .':while', [
                        'shop_id' => $this->shop->id,
                        'message' => 'do',
                        'startTime' => date('Y-m-d H:i:s', $startTime),
                        'endTime' => date('Y-m-d H:i:s', $endTime),
                        'i' => $i,
                        'counter' => $counter,
                        'orderCount'=>sizeof($orders),
                        'hasNext'=>$orderService->hasNext
                    ]);
                } while ($orderService->hasNext);
            }catch (\Exception $exception) {
                Log::error('OderLogicService requestOrder exception', [
                    'msg' => $exception->getMessage(),
                    'shop_id' => $this->shop->id,
                    'beginAt' => $this->beginAt,
                    'endAt' => $this->endAt,
                    'callClassName' => $this->getCallClassName(),
                ]);
                if ($exception instanceof ApiException) {
                    if (ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED == $exception->getCode()) {
//                        $syncAtField = 'last_sync_at';
                        if ($this->isFactory()) {
//                            $syncAtField = 'last_factory_sync_at';
                        }
                        $this->updateShopData = [
//                            $syncAtField => $this->beginAt,
                            'auth_status'  => Shop::AUTH_STATUS_EXPIRE,
                        ];
                    }
                }
                $this->exception = $exception;
            } finally {
                $wg->done();
            }
            return true;
        });

    }

    /**
     * @param $template
     * @param $company
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|null|object
     * @throws ApiException
     */
    public static function getWaybillAuth($template, $company)
    {
        if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY,Waybill::AUTH_SOURCE_KS, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
            if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
                $waybillAuth = Shop::query()->where('identifier', $company->owner_id)->first();
            } else {
                $waybillAuth = Shop::find($template['shop_id']);
            }
            $waybillAuth->auth_source = $template['auth_source'];
        } else {
            $waybillAuth = Waybill::where([
                'owner_id' => $template['owner_id'],
                'auth_source' => $template['auth_source']
            ])->orderBy('id', 'desc')->first();
        }

        if (!$waybillAuth) {
            throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
        }
        return $waybillAuth;
    }

    /**
     * @param bool $isSaveLastSync
     */
    public function setIsSaveLastSync(bool $isSaveLastSync): void
    {
        $this->isSaveLastSync = $isSaveLastSync;
    }

    /**
     * @param bool $isFirstPull
     * @return OderLogicService
     */
    public function setIsFirstPull(bool $isFirstPull): OderLogicService
    {
        $this->isFirstPull = $isFirstPull;
        return $this;
    }

    /**
     * @param bool $isUpdateLastSyncAt
     */
    public function setIsUpdateLastSyncAt(bool $isUpdateLastSyncAt): void
    {
        $this->isUpdateLastSyncAt = $isUpdateLastSyncAt;
    }

    /**
     * @param string $action
     */
    public function setAction(string $action): void
    {
        $this->action = $action;
    }


    /**
     * @param null $shop
     * @return OderLogicService
     */
    public function setShop($shop)
    {
        $this->shop = $shop;
        return $this;
    }

    /**
     * @param string $beginAt
     * @return OderLogicService
     */
    public function setBeginAt($beginAt)
    {
        $this->beginAt = $beginAt;
        return $this;
    }

    /**
     * @param string $endAt
     * @return OderLogicService
     */
    public function setEndAt($endAt)
    {
        $this->endAt = $endAt;
        return $this;
    }

    /**
     * @param AbstractOrderService $orderService
     */
    public function setOrderService(AbstractOrderService $orderService): void
    {
        $this->orderService = $orderService;
    }

    /**
     * @return int
     */
    public function getOrderCount(): int
    {
        return $this->orderCount;
    }

    /**
     * @return int
     */
    public function getOrderTotalLimit(): int
    {
        return $this->orderTotalLimit;
    }

    /**
     * @param bool $isUpdateLastOperatedAt
     */
    public function setIsUpdateLastOperatedAt(bool $isUpdateLastOperatedAt): void
    {
        $this->isUpdateLastOperatedAt = $isUpdateLastOperatedAt;
    }

    /**
     * @return string
     */
    public function getCallClassName(): string
    {
        return $this->callClassName;
    }

    /**
     * @param string $callClassName
     */
    public function setCallClassName(string $callClassName): void
    {
        $this->callClassName = $callClassName;
    }

    /**
     * @return bool
     */
    public function isFactory(): bool
    {
        return $this->isFactory;
    }

    /**
     * @param bool $isFactory
     */
    public function setIsFactory(bool $isFactory): void
    {
        $this->isFactory = $isFactory;
    }


    public function setIsSyncLegacyOrder(bool $isSyncLegacyOrder)
    {
        $this->isSyncLegacyOrder = $isSyncLegacyOrder;
    }

    /**
     * @param WaitGroup $wg
     * @param Coroutine\Channel $ch
     * <AUTHOR>
     */
    public function handleLegacyOrder(WaitGroup $wg, Coroutine\Channel $ch)
    {
        \Log::info(get_class($this).':handleLegacyOrder:start');
        $orderService = clone $this->orderService;
        $counter = 0;
        \App\Models\Fix\Order::query()
            ->where([
                ['shop_id', $this->shop->id],
                ['order_status', Order::ORDER_STATUS_PAYMENT],
                ['refund_status', Order::REFUND_STATUS_NO],
            ])
            ->select(['id','tid', 'user_id', 'shop_id'])
            ->chunkById(10000, function ($orders) use ($orderService, $wg, $ch,&$counter) {

//                \Log::info('handleLegacyOrder:count', [count($orders)]);
                $array_chunk = array_chunk($orders->toArray(), 100);
                foreach ($array_chunk as $idx => $chunkOrders) {
                    try {
                        $wg->add();
//                        \Log::info('handleLegacyOrder:send:'.$counter, [count($chunkOrders)]);
                        $orderInfos = $orderService->batchGetOrderInfo($chunkOrders);
//                        \Log::info('handleLegacyOrder:send_over:'.$counter, [count($chunkOrders)]);
                        $ch->push($orderInfos);
                    } catch (\Exception $exception){
                        \Log::error('handleLegacyOrder:exception', [
                            'msg' => $exception->getMessage(),
                            'shop_id' => $this->shop->id,
                        ]);
                        $this->exception = $exception;
                    } finally {
                        $wg->done();
                    }
                }
                $wg->wait();
                $counter++;
            });
    }

}
