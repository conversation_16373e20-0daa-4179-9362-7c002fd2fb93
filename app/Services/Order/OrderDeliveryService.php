<?php

namespace App\Services\Order;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\RedisKeyConst;
use App\Events\Orders\OrderDeliveryEvent;
use App\Events\Orders\OrderDeliveryFailEvent;
use App\Events\Orders\OrderPreshipmentFailEvent;
use App\Events\Orders\OrderPreshipmentJoinEvent;
use App\Events\Orders\OrderRedeliveryEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\DeliveryRecord;
use App\Models\Order;
use App\Models\OrderExtra;
use App\Models\OrderItem;
use App\Models\OrderItemExtra;
use App\Models\OrderTraceList;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\PtLogistics;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\WaybillHistory;
use App\Services\BusinessException;
use App\Services\Common\OrderResult;
use App\Services\Order\Request\OrderDeliverAgainRequest;
use App\Services\Order\Request\OrderDeliveryRequest;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\ArrayUtil;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use App\Utils\ObjectUtil;
use App\Utils\OrderUtil;
use App\Utils\WaybillUtil;
use Carbon\Carbon;
use DateTime;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

/**
 * 订单发货
 */
class OrderDeliveryService
{

    /**
     * 记录“固定时间发货的任务”最后一次执行的时间的KEy
     */
    const LOGISTICS_SCHEDULE_DELIVER_LAST_TIME = 'logistics_schedule_deliver_last_time';

    /**
     * 记录“有物流以后XX时间发货的任务”最后一次执行的时间的KEy
     */
    const LOGISTICS_AFTER_DELIVER_LAST_TIME = 'logistics_after_deliver_last_time';
    /**
     * @var PackageService
     */
    private $packageService;

    /**
     * @var OrderPreshipmentService
     */
    private $orderPreshipmentService;


    public function __construct()
    {
        $this->packageService = new PackageService();
        $this->orderPreshipmentService = new OrderPreshipmentService();
    }


    /**
     * 扫描发货,因为合单的原因,可能会有多个订单
     * [
     * $delivers[] = [
     * 'id' => $order['id'],
     * 'express_no' => $waybillCode,
     *'express_code' => $wp_code,
     * ];
     * ]
     * @param  array  $delivers
     * @return OrderResult[]  成功和失败的订单
     */
    public static function orderScanDelivery(array $delivers): array
    {

        $results = [];

        foreach ($delivers as $deliver) {
            $order = Order::find($deliver['id']);
            $tid = $order->tid;
            try {
                $shop = $order->shop;
                $userId = $order->user_id;
                $shopId = $order->shop_id;
                $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                $orderService->setUserId($userId);
                $orderService->setShop($shop);
                //多包情况,取第一个快递号发货
                $expressNo = explode(',', $deliver['express_no'])[0];
                //发货前校验订单是否已发货 非待发货状态不处理
                //$orderInfo = $orderService->batchGetOrderInfo([$order]);
                //if ($orderInfo[0]['order_status'] == Order::ORDER_STATUS_PAYMENT) {
                $res = $orderService->deliveryOrders($tid, $deliver['express_code'], $expressNo, [], false);
                if (!$res) {
                    $results[] = OrderResult::fail($tid, '发货失败,原因未知');
                }
                //}

                //订阅物流信息
                if ($order->template &&
                    in_array($order->template->auth_source, [PlatformConst::WAYBILL_PDD, PlatformConst::WAYBILL_PDD_WB])
                ) {
                    $waybillService = WaybillServiceManager::init($order->template->auth_source);
                    $waybillService->sendLogisticsTraceMsg($order);
                }
                $order->order_status = Order::ORDER_STATUS_DELIVERED;
                $order->send_at = date('Y-m-d H:i:s');
                if (is_null($order->express_code)) {
                    $order->express_code = $deliver['express_code'];
                }
                if (is_null($order->express_no)) {
                    $order->express_no = $deliver['express_no'];
                }

                if (!$order->save()) {
                    throw new ApiException(ErrorConst::ORDER_STATUS_UPDATE_FAIL);
//                    throw new \Exception('订单发货状态修改失败');

                }

                OrderItem::query()->where([
                    'order_id' => $deliver['id'],
                    'waybill_code' => $deliver['express_code']
                ])->update([
                    'send_at' => date('Y-m-d H:i:s'),
                    'status' => Order::ORDER_STATUS_DELIVERED
                ]);

                $history = WaybillHistory::where([
                    'shop_id' => $shopId,
                    'waybill_code' => $expressNo,
                    'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
                ])->first();
                $ret = DeliveryRecord::create([
                    'user_id' => $userId,
                    'shop_id' => $shopId,
                    'history_id' => $history ? $history->id : 0,
                    'order_id' => $order->id,
                    'order_no' => $tid,
                    'waybill_code' => $expressNo,
                    'wp_code' => $deliver['express_code'],
                    'result' => $res,
                ]);
                if (!$ret) {
                    throw new ApiException(ErrorConst::DELIVERY_RECORD_EXCEPTION);
//                    throw new \Exception('发货记录异常');
                }
                $results[] = OrderResult::success($tid);
            } catch (\Throwable $e) {
                $results[] = OrderResult::fail($tid, $e->getMessage());
                Log::error($e->getMessage(), ['deliver' => $deliver, 'orders' => $order->toArray()]);
                continue;
            }
        }
        return $results;
    }

    private static function saveInLocal($url, $ext)
    {
        $fileName = substr(md5(rand(100, 999).time()), 10).'.'.$ext;
        $path = '/tmp/excel_files';
        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }
        $fullName = $path.'/'.$fileName;
        $file = file_get_contents($url);
        $fp = fopen($fullName, 'x');
        fwrite($fp, $file);
        fclose($fp);

        return $fullName;
    }

    private static function delInLocal($path)
    {
        return unlink($path);
    }

    public static function import(string $fileName, string $path)
    {
        $ext = explode('.', $fileName);
        $ext = $ext[count($ext) - 1];
        $file = self::saveInLocal($path, $ext);
        try {
            $excel = Excel::toArray(null, $file);
        } catch (\Exception $e) {
            Log::error('Excel load failed !', ['msg' => $e->getMessage(), 'file_name' => $file, 'file' => $file]);
        }
        if (empty($excel)) {
            return [];
        }
        self::delInLocal($file);
        //删除表头
        unset($excel[0][0]);

        return $excel[0];
    }

    /**
     * 上传发货
     * @param  array  $shopIds
     * @param  string  $fileName
     * @param  string  $path
     * @param  string  $userId
     * @param  int  $shopId
     * @param $request
     * @return array |bool
     * @throws ApiException
     */
    public function uploadDelivery(array $shopIds, string $fileName, string $path, $userId, $shopId, $request)
    {
        $data = self::import($fileName, $path);
        if (empty($data) || !is_array($data)) {
            Log::warn('数据解析错误', [$shopIds, $path, $data]);
            throw new \Exception('数据解析错误');
        }

        $tids = OrderUtil::batchAppendOrderSuffix(array_column($data, 0));


        $orders = Order::query()->whereIn('tid', $tids)->get()->keyBy('tid');
        \Log::info("批量发货", ["tid" => $tids, "匹配的订单数" => sizeof($orders), "shopIds" => $shopIds]);
        $index = 1;
        $delivers = [];
        $errors = [];
        foreach ($data as $datum) {
            $index++;  //删除了表头从第二行算起
            $tid = trimBlankChars($datum[0]);
            $expressNo = trimBlankChars($datum[1]);
            $expressCode = trimBlankChars($datum[2]);
            $errorInfo = $this->buildErrorOrder($tid, $expressCode, $expressNo);
            if (!$tid || strlen($tid) == 0) {
                $errorInfo->error_msg = StatusCode::ORDER_NO_ERROR[1];
                $errors[$index] = $errorInfo;
                continue;
            }
            //快递公司简称验证
            if (!OrderUtil::validateExpressCode($expressCode)) {
                $errorInfo->error_msg = StatusCode::EXPRESS_CODE_ERROR[1];
                $errors[$index] = $errorInfo;
                continue;
            }
            $tid = OrderUtil::appendOrderSuffix($tid);
            //过滤掉退款订单
            $order = $orders[$tid] ?? null;
            if (!isset($order)) {
                $errorInfo->error_msg = StatusCode::ORDER_NOT_FOUND[1];
                $errors[$index] = $errorInfo;
                continue;
            }
            if (!in_array($order->shop_id, $shopIds)) {
                $errorInfo->error_msg = StatusCode::ORDER_NOT_THIS_SHOP[1];
                $errors[$index] = $errorInfo;
                continue;
            }
            $delivers[] = [
                "idStr" => $order->id,
                "waybillCodeStr" => $expressNo,
                "express_code" => $expressCode,
            ];
        }
//        $ret = $this->batchDelivery($delivers, $userId, $shopId, $request);
        $ret = $this->batchDeliveryV4($delivers, $shopId);
        return [
            'orderTotal' => $ret["orderTotal"],
            'orderSuccess' => $ret['orderSuccess'],
            'orderFail' => $ret['orderFail'] + sizeof($errors),
            'failOrders' => array_merge($ret['failOrders'], $errors),
        ];
    }

    /**
     * 包裹拆单发货
     *
     * @param  array  $delivers
     * @param  int  $userId
     * @param $mode
     * @return array
     * @throws ErrorCodeException
     */
    public function orderMultiPackagesDeliver(array $delivers, int $userId, $mode): array
    {
        /**
         * {
         * "delivers": [
         * {
         * "idStr": "1356091",
         * "waybillCodeStr": "9745570861180",
         * "express_code": "youzhengguonei"
         * },
         * {
         * "idStr": "1356091",
         * "waybillCodeStr": "9745570863980",
         * "express_code": "youzhengguonei"
         * },
         * {
         * "idStr": "1356091",
         * "waybillCodeStr": "9745570862580",
         * "express_code": "youzhengguonei"
         * },
         * {
         * "idStr": "1356091",
         * "waybillCodeStr": "9745570864280",
         * "express_code": "youzhengguonei"
         * }
         * ],
         * "currentTab": "0"
         * }
         */

        //从delivers里面把所有的快递单号都取出来
        //从delivers里面把面单号
        $waybillExpressCodes = array_column($delivers, 'express_code', 'waybillCodeStr');
        //从delivers里面把所有的订单id都取出来用面单号做key
        $orderIdsKeyByWaybillCode = array_column($delivers, 'idStr', 'waybillCodeStr');
        $deliversKeyByWaybillCode = array_column($delivers, null, 'waybillCodeStr');
        if ($mode == 3) {
            $orderItemIdArr = collect($delivers)->pluck('packages.*.orderItemId')->flatten()->toArray();
            $orderItemList = OrderItem::query()->whereIn('id', $orderItemIdArr)->get();
        }
        $waybillCodes = array_keys($orderIdsKeyByWaybillCode);
        //可能被误认为是数字，需要转换成字符串
        $waybillCodes = array_map(function ($item) {
            return strval($item);
        }, $waybillCodes);
        //利用面单好，把所有的包裹都查处理
        $packages = Package::query()->with(['packageOrders.orderItem', 'template'])->whereIn('waybill_code',
            $waybillCodes)->get();

        $waybillCodesGroupByShopId = ArrayUtil::array_group_by($packages->map(function ($package) {
            return ['waybill_code' => $package['waybill_code'], 'shop_id' => $package['shop_id']];
        })->toArray(), 'shop_id');
        //把$package里面的面单号和goods_info的包裹信息，拉平成一个一维数组
        $packageGoodsInfos = [];
        $successCount = 0;
        $failureCount = 0;
        /**
         * @var $package Package
         */
        foreach ($packages as $package) {
            // 分包发货
            if ($mode == 3) {
                $packs = $deliversKeyByWaybillCode[$package->waybill_code]['packages'];
                foreach ($packs as $pack) {
                    $orderItem = $orderItemList->where('id', $pack['orderItemId'])->first();
                    $goodsInfo = [];
                    $goodsInfo['waybillCode'] = $package->waybill_code;
                    $goodsInfo['expressCode'] = $waybillExpressCodes[$package->waybill_code];
                    $goodsInfo['shopId'] = $package->shop_id;
                    $goodsInfo['oid'] = $orderItem->oid;
                    $goodsInfo['shippedNum'] = $pack['num'];
                    $goodsInfo['num_iid'] = $orderItem->num_iid;
                    $goodsInfo['sku_id'] = $orderItem->sku_id;
                    $goodsInfo['tid'] = $orderItem->tid;
                    $packageGoodsInfos[] = $goodsInfo;
                }

            } //这个判断是为了防止有些包裹没有goods_info，这种情况是按固定数量来一单多包的
            elseif (empty($package->goods_info)) {
                foreach ($package->packageOrders as $packageOrder) {
                    $goodsInfo = [];
                    $goodsInfo['waybillCode'] = $package->waybill_code;
                    $goodsInfo['expressCode'] = $waybillExpressCodes[$package->waybill_code];
                    $goodsInfo['shopId'] = $package->shop_id;
                    $goodsInfo['oid'] = $packageOrder->orderItem->oid;
                    $goodsInfo['shippedNum'] = $packageOrder->num;
                    $goodsInfo['num_iid'] = $packageOrder->orderItem->num_iid;
                    $goodsInfo['sku_id'] = $packageOrder->orderItem->sku_id;
                    $goodsInfo['tid'] = $packageOrder->orderItem->tid;
                    $packageGoodsInfos[] = $goodsInfo;
                }
//                $goodsInfo = [];
//                $goodsInfo['waybillCode'] = $package->waybill_code;
//                $goodsInfo['expressCode'] = $waybillExpressCodes[$package->waybill_code];
//                $goodsInfo['shopId'] = $package->shop_id;
//                $packageGoodsInfos[] = $goodsInfo;
            } else {
                //如果走了按规格（规格种类或者规格件数拆单）就会在package里面有goods_info，走这个分支
                $goodsInfos = json_decode($package->goods_info, true);
                //这里把包裹里面的面单号和快递公司简称加进去
                foreach ($goodsInfos as $goodsInfo) {
                    $goodsInfo['waybillCode'] = $package->waybill_code;
                    $goodsInfo['expressCode'] = $waybillExpressCodes[$package->waybill_code];
                    $goodsInfo['shopId'] = $package->shop_id;
                    //$goodsInfo的格式为 {"tid":"6919675406348523072A","oid":"6919675406348588608","orderId":1356091,
                    //"orderItemId":1368118,"shippedNum":1,"waybillCode":"9745570861180","expressCode":"youzhengguonei"}
                    $packageGoodsInfos[] = $goodsInfo;
                }
            }
        }
        \Log::info("所有的包裹信息", $packageGoodsInfos);

        $packageGoodsInfosGroupByTid = ArrayUtil::array_group_by($packageGoodsInfos, 'tid');
        //开始一个订单一个订单的发货
        $successOrders = [];
        $failOrders = [];
        \Log::info("按订单号分组", [$packageGoodsInfosGroupByTid]);


        //先按店铺ID分组，店铺ID相同的订单一起发货，不同的店铺分开发货,店铺间是串行的
        $packageGoodsInfosGroupByShopId = ArrayUtil::array_group_by($packageGoodsInfos, 'shopId');
        //把店铺ID都提取出来，然后检查这个店铺的授权
        $shopIds = array_keys($packageGoodsInfosGroupByShopId);
        // 追加包裹数组
        $appendPackageArr = [];
        foreach ($packageGoodsInfosGroupByShopId as $shopId => $shopPackageGoodsInfos) {
            /**
             * @var $shop Shop
             */
            $shop = Shop::query()->find($shopId);
            $deliversRequests = [];
            $shopPackageGoodsInfosGroupByTid = ArrayUtil::array_group_by($shopPackageGoodsInfos, 'tid');
            if (!$shop->isAuthOk()) {
                \Log::info("店铺授权失效", [$shopId]);
                foreach ($shopPackageGoodsInfosGroupByTid as $tid => $tidPackageGoodsInfos) {
                    $authFailOrders[] = [
                        'tid' => $tid,
                        'shopId' => $shopId,
                        'waybillCodeStr' => implode(",", array_column($tidPackageGoodsInfos, 'waybillCode')),
                        'error_msg' => $shop->shop_name.'店铺授权失效',
                    ];
                    $failOrders = array_merge($failOrders, $authFailOrders);
                }
            } else {
                foreach ($shopPackageGoodsInfosGroupByTid as $tid => $tidPackageGoodsInfos) {
                    $tidPackageGoodsInfosGroupByWaybillCode = ArrayUtil::array_group_by($tidPackageGoodsInfos,
                        'waybillCode');
                    $deliversRequest = [];
                    $deliversRequest['tid'] = $tid;
                    $deliversRequest['shopId'] = $shopId;
                    $waybillCodeCounter = 0;
                    foreach ($tidPackageGoodsInfosGroupByWaybillCode as $waybillCode => $packageGoodsInfos) {
                        $waybillCodeCounter++;
                        if ($waybillCodeCounter == 1) { // 单订单多包裹，第一个走拆单
                            $deliversRequest['packs'][] = [
                                'waybillCode' => $waybillCode,
                                'expressCode' => $packageGoodsInfos[0]['expressCode'],
                                'goods' => array_map(function ($packageGoodsInfo) {
                                    return [
                                        'oid' => $packageGoodsInfo['oid'],
                                        'shippedNum' => $packageGoodsInfo['shippedNum'],
                                        'num_iid' => $packageGoodsInfo['num_iid'],
                                        'sku_id' => $packageGoodsInfo['sku_id'],
                                    ];
                                }, $packageGoodsInfos)
                            ];
                        } else { // 后面的走追加
                            $appendPackageArr[] = [
                                'shopId' => $shopId,
                                'tid' => $tid,
                                'wpCode' => $packageGoodsInfos[0]['expressCode'],
                                'waybillCode' => $waybillCode,
                                'goodsList' => array_map(function ($packageGoodsInfo) {
                                    return [
                                        'oid' => $packageGoodsInfo['oid'],
                                        'num' => $packageGoodsInfo['shippedNum'],
                                    ];
                                }, $packageGoodsInfos)
                            ];
                        }

                    }
                    $deliversRequests[] = $deliversRequest;
                }
                $orderService = OrderServiceManager::create();
                $orderService->setShop($shop);
                $deliverResult = $orderService->orderMultiPackagesDelivery($shopId, $deliversRequests);

                list($successOrders, $failOrders) = $this->handleSuccessFailOrders($deliverResult, $successOrders,
                    $failOrders);

                $this->handleOrderMultiPackagesDeliverResponse($successOrders, $packages);
                $this->handleOrderDeliverEvent($shop, $successOrders, $failOrders);
            }

        }
        $successCount = collect($successOrders)->pluck('waybillCodes.*.waybillCode')->flatten()->filter()->unique()->count();
        $failureCount = collect($failOrders)->pluck('waybillCodes.*.waybillCode')->flatten()->filter()->unique()->count();
        return [
            'orderSuccess' => $successCount,
            'orderFail' => $failureCount,
            'orderTotal' => $successCount + $failureCount,
            'failOrders' => $failOrders,
//            'successOrders'=>$successOrders
        ];

        //dd($delivers);

        //把$packageGoodsInfos里面的订单id和订单项id拉平成一个一维数组


//        //把package里面的goods_info都取出来,goods_info是json格式的，所以要转换成数组,这里的goods_info是一个二维数组,需要把它转换成一维数组
//        $goodsInfos = array_flatten(array_map(function ($input) {
//            return json_decode($input, true);
//        }, array_column($packages->toArray(), 'goods_info')));
//    //[{"tid":"6919675406348523072A","oid":"6919675406348588608","orderId":1356091,"orderItemId":1368118,
//        //"shippedNum":1}]


    }

    /**
     * 批量发货
     * @param  array  $delivers
     * @param $userId
     * @param $shopId
     * @param  Request|null  $request
     * @return array{orderTotal:int, orderSuccess:int, orderFail:int, failOrders:Order[],deliverOrders:array{id : string,waybill_code:string,wp_code:string}}
     * @throws ApiException
     */
    public function batchDelivery(array $delivers, $userId, $shopId, Request $request = null): array
    {

        $failOrders = [];
//        if (!is_array($delivers)) {
//            throw new ApiException(ErrorConst::ORDER_DELIVERY_FAIL);
//        }
        $orderTotal = count($delivers);
        $orderSuccess = 0;
        //重新组装数据
        $newDelivers = [];
        foreach ($delivers as $deliver) {
            if (strstr($deliver['idStr'], '_')) {
                $idArr = explode('_', $deliver['idStr']);
                foreach ($idArr as $idStr) {
                    $deliver['idStr'] = $idStr;
                    $newDelivers[] = $deliver;
                }
            } else {
                $newDelivers[] = $deliver;
            }
        }
        $deliveryOrdersList = [];
        $deliveryOrdersAgainList = [];
        $deliveryOrdersQualityInspectionList = [];
        // 检查过店铺的列表，避免重复检测
        $checkShopList = [];
        $orderIdList = [];
        foreach ($newDelivers as $deliver) {
            $idStr = $deliver['idStr'];
            if (strstr($idStr, ":")) {
                $temp = explode(":", $idStr);
                $orderId = $temp[0];
            } else {
                $orderId = $idStr;
            }
            $orderIdList[] = $orderId;
        }

        $orderInfoList = \App\Models\Fix\Order::query()->whereIn('id', $orderIdList)
            ->with(['shop', 'orderExtra', 'orderItem.orderItemExtra'])->get()->keyBy('id');


        foreach ($newDelivers as $deliver) {
            $order = null;
            try {
                // 是否部分发货 取出部分发货
                $idStr = $deliver['idStr'];
                $orderItemOId = [];
                if (strstr($idStr, ":")) {
                    $temp = explode(":", $idStr);
                    $orderId = $temp[0];
                    // temp[1]没有数据就是没勾选子订单 前端做了校验不会出现直接跳过
                    if (!empty($temp[1])) {
                        //打印完立即发货 需要转换oid
                        if (isset($deliver['orderOid'])) {
                            $orderItemOIdArr = OrderItem::query()->whereIn('id',
                                explode(',', $temp[1]))->get()->toArray();
                            $orderItemOId = array_column($orderItemOIdArr, 'oid');
                        } else {
                            $orderItemOId = explode(',', $temp[1]);
                        }
                    } else {
                        continue;
                    }
                } else {
                    $orderId = $idStr;
                }

                $order = $orderInfoList[$orderId];
                /**
                 * @var Shop $shop
                 */
                $shop = $order->shop;
                $userId = $order->user_id;
                $shopId = $order->shop_id;
                if (isset($checkShopList[$shopId])) {
                    $checkAuthStatus = $checkShopList[$shopId];
                } else {
                    $orderService = OrderServiceManager::create();
                    $orderService->setUserId($userId);
                    $orderService->setShop($shop);
                    $checkAuthStatus = $orderService->checkAuthStatus();
                    $checkShopList[$shopId] = $checkAuthStatus;
                }
                if (!$checkAuthStatus) {
                    throw new ApiException([
                        ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0],
                        '【'.$shop->shop_name.'】'.ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[1]
                    ]);
                }

                //判断整单发还是部分发
                $orderItemArr = OrderItem::query()->where('order_id', $orderId)->get()->toArray();
                $orderItemOidArr = array_column($orderItemArr, 'oid');
                //没有差集就是全部发货 不传oid
                if (empty(array_diff($orderItemOidArr, $orderItemOId))) {
                    $orderItemOId = [];
                }

                //多包情况,取第一个快递号发货
                $expressNo = explode(',', $deliver['waybillCodeStr'])[0];
                //订单状态是大于等于发货状态不去再发货,如果再次发货，就把需要再次发货的发货信息放到$deliveryOrdersAgainList
                if ($order->order_status >= Order::ORDER_STATUS_DELIVERED
                    && (!isset($order->orderExtra->order_biz_type) || (isset($order->orderExtra->order_biz_type) && $order->orderExtra->order_biz_type != OrderExtra::BIZ_TYPE_QUALITY_INSPECTION))
                ) {

                    // 判断退款
                    if ($order->refund_status != Order::REFUND_STATUS_NO) {
                        $order->error_code = 0;
                        $order->error_msg = '订单有退款，发货失败！';
                        $order->waybillCodeStr = $deliver['waybillCodeStr'];
                        $failOrders[] = $order;
                    } else {
                        //todo 子订单重新发货需要存pack_id
                        if (count($orderItemOId) == 0) {
                            $order->express_no = $expressNo;
                            $order->express_code = $deliver['express_code'];
                            $order->save();
//                            $orderService->deliveryOrdersAgain($order->tid, $deliver['express_code'], $expressNo, $orderItemOId);
                            $deliveryOrdersAgainList[] = [
                                'userId' => $userId,
                                'shopId' => $shopId,
                                'shop' => $shop,
                                'order' => clone $order,
                                'tid' => $order->tid,
                                'expressCode' => $deliver['express_code'],
                                'expressNo' => $expressNo,
                                'orderItemOId' => $orderItemOId,
                            ];
                        }
//                        $orderSuccess++;
                    }
                    continue;
                }

                $package = Package::query()->where('waybill_code', $expressNo)->first();
                if (!empty($package) && !empty($package->recycled_at)) {
                    throw new ApiException(ErrorConst::ORDER_WAYBILL_RECYCLED_DELIVERED_FAIL);
                }
                //如果是质检订单，把发货信息放到$deliveryOrdersQualityInspectionList
                if (isset($order->orderExtra->order_biz_type) && $order->orderExtra->order_biz_type == OrderExtra::BIZ_TYPE_QUALITY_INSPECTION) {
                    $deliveryOrdersQualityInspectionList[] = [
                        'userId' => $userId,
                        'shopId' => $shopId,
                        'shop' => $shop,
                        'order' => clone $order,
                        'tid' => $order->tid,
                        'expressCode' => $deliver['express_code'],
                        'expressNo' => $expressNo,
                        'orderItemOId' => $orderItemOId,
                    ];
                    continue;
                }

//                $res = $orderService->deliveryOrders($order->tid, $deliver['express_code'], $expressNo, $orderItemOId);
                //正常的发货信息放到$deliveryOrdersList
                if (Environment::isTaoBao()) {
                    $packs = [];
                    foreach ($orderItemArr as $orderItem) {
                        $packs[] = [
                            'oid' => $orderItem['oid'],
                            'shippedNum' => $orderItem['send_remain_num'] > 0 ? $orderItem['send_remain_num'] : 1
                        ];
                    }
                    $waybills[] = [
                        'expressCode' => $deliver['express_code'],
                        'expressNo' => $expressNo,
                        'packs' => $packs,
                    ];
                    $deliveryOrdersList[] = [
                        'userId' => $order->user_id,
                        'shopId' => $order->shop_id,
                        'shop' => $order->shop,
                        'order' => clone $order,
                        'tid' => $order->tid,
                        'orderItemOId' => [],
                        'waybills' => $waybills
                    ];
                } else {
                    $deliveryOrdersList[] = [
                        'userId' => $userId,
                        'shopId' => $shopId,
                        'shop' => $shop,
                        'order' => clone $order,
                        'tid' => $order->tid,
                        'expressCode' => $deliver['express_code'],
                        'expressNo' => $expressNo,
                        'orderItemOId' => $orderItemOId,
                    ];
                }
            } catch (\Exception $e) {
                Log::error($e->getMessage(), ['deliver' => $deliver, 'orders' => $order->toArray()]);
                $error_code = $e->getCode();
                if (method_exists($e, 'getErrorCode')) {
                    $error_code = $e->getErrorCode();
                }
                $order->error_code = $error_code;
                $order->error_msg = $e->getMessage();
                $order->waybillCodeStr = $deliver['waybillCodeStr'];
                $failOrders[] = $order;
            }
        }
        // 处理重新发货
        Log::info('处理重新发货', [$deliveryOrdersAgainList]);
        $this->handleRedelivery($deliveryOrdersAgainList, $orderSuccess, $failOrders);
//        Log::info('处理普通发货', [$deliveryOrdersList]);
        $this->handleDelivery($deliveryOrdersList, $orderSuccess, $failOrders);
        Log::info('处理质检发货', [$deliveryOrdersQualityInspectionList]);
        self::handleQualityInspectionDelivery($deliveryOrdersQualityInspectionList, $orderSuccess, $failOrders);

        $orderFail = count($failOrders);
        $failOrderIds = array_pluck($failOrders, "id");
        \Log::info("失败的订单ID", $failOrderIds);
        $successDelivers = array_filter($delivers, function ($item) use ($failOrderIds) {
            if (sizeof($failOrderIds) == 0) {
                return true;
            } else {
                return !in_array(explode(':', $item['idStr'])[0], $failOrderIds);
            }
        });


        $deliverOrders = array_map(function ($item) {
            return [
                'id' => explode(':', $item['idStr'])[0],
                'waybill_code' => explode(':', $item['waybillCodeStr'])[0],
                'wp_code' => $item['express_code'],
            ];
        }, $successDelivers);

        $this->publishDeliveryEvent($userId, $shopId, $deliverOrders, $request);
        return [
            'orderTotal' => $orderTotal,
            'orderSuccess' => $orderSuccess,
            'orderFail' => $orderFail,
            'failOrders' => $failOrders,
            'deliverOrders' => $deliverOrders
        ];
    }

    /**
     * @param  array  $deliveryOrdersAgainList
     * @param  int  $orderSuccess
     * @param  array  $failOrders
     * @return void
     * <AUTHOR>
     */
    protected
    function handleRedelivery(
        array $deliveryOrdersAgainList,
        int &$orderSuccess,
        array &$failOrders
    ) {
        // 订单可能包含多个店铺
        $deliveryOrdersAgainListGroup = collect($deliveryOrdersAgainList)->groupBy('shopId')->toArray();
        $deliveryOrderData = array_pluck($deliveryOrdersAgainList, null, 'tid');
        // 重新发货
        foreach ($deliveryOrdersAgainListGroup as $list) {
            $orderService = OrderServiceManager::create();
            $first = array_first($list);
            $orderService->setUserId($first['userId']);
            $orderService->setShop($first['shop']);
            foreach ($list as $item) {
                try {
                    $expressCode = $item['expressCode'];
                    $waybillCodeStr = $item['waybillCodeStr'] ?? '';
                    $orderService->deliveryOrdersAgain($item['tid'], $expressCode, $item['expressNo'],
                        $item['orderItemOId']);

                    $order = $item['order'];
                    $shop = $order->shop;
                    $userId = $order->user_id;
                    $shopId = $order->shop_id;
                    $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                    $orderService->setUserId($userId);
                    $orderService->setShop($shop);
                    $checkAuthStatus = $orderService->checkAuthStatus();
                    if (!$checkAuthStatus) {
                        throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                    }

                    // todo 后面要改成异步
                    $history = WaybillHistory::where([
                        'shop_id' => $shopId,
                        'waybill_code' => $item['expressNo'],
                        'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
                    ])->first();
                    $ret = DeliveryRecord::create([
                        'user_id' => $userId,
                        'shop_id' => $shopId,
                        'history_id' => $history ? $history->id : 0,
                        'order_id' => $order->id,
                        'order_no' => $order->tid,
                        'waybill_code' => $item['expressNo'],
                        'wp_code' => $expressCode,
                        'result' => true,
                    ]);
                    if (!$ret) {
                        throw new ApiException(ErrorConst::DELIVERY_RECORD_EXCEPTION);
//                    throw new \Exception('发货记录异常');
                    }
                    $orderSuccess++;
                } catch (\Exception $e) {
                    Log::error($e->getMessage(), ['trace' => $e->getTrace(), 'item' => $item]);
                    $error_code = $e->getCode();
                    $message = $e->getMessage();
                    if ($e instanceof ApiException) {
                        $error_code = $e->getErrorCode();
                    } elseif ($e instanceof ConnectException) {
                        $message = '请求平台发货超时，请稍后重试！';
                    }
                    $order = $item['order'];
                    $order->error_code = $error_code;
                    $order->error_msg = $message;
                    $order->waybillCodeStr = $waybillCodeStr;
                    $failOrders[] = $order;
                }
            }
        }
        return;
    }

    /**
     * @param  array  $deliveryOrdersList
     * @param  int  $orderSuccess
     * @param  array  $failOrders
     * @return void
     * <AUTHOR>
     */
    protected
    function handleDelivery(
        array $deliveryOrdersList,
        int &$orderSuccess,
        array &$failOrders
    ) {


        // 订单可能包含多个店铺
        $deliveryOrdersListGroup = collect($deliveryOrdersList)->groupBy('shopId')->toArray();
        // 发货
        foreach ($deliveryOrdersListGroup as $deliveryOrderData) {
            $orderService = OrderServiceManager::create();
            $first = array_first($deliveryOrderData);
            $userId = $first['userId'];
            $shopId = $first['shopId'];
            $orderService->setUserId($userId);
            $orderService->setShop($first['shop']);
            $orderDeliveryRequestList = ObjectUtil::batchMapToObject($deliveryOrderData, OrderDeliveryRequest::class);
//            Log::info('batchDeliveryOrders', [$deliveryOrderData]);
            $responseList = $orderService->batchDeliveryOrders($orderDeliveryRequestList);
//            Log::info('handleDeliveryResponse', [$responseList]);
            $this->handleDeliveryResponse($deliveryOrderData, $responseList, $shopId, $userId, $orderSuccess,
                $failOrders);
        }
        return;
    }

    /**
     * 处理质检发货
     * @param  array  $deliveryOrdersList
     * @param  int  $orderSuccess
     * @param  array  $failOrders
     * @return
     * <AUTHOR>
     */
    protected
    function handleQualityInspectionDelivery(
        array $deliveryOrdersList,
        int &$orderSuccess,
        array &$failOrders
    ) {
        // 订单可能包含多个店铺
        $deliveryOrdersListGroup = collect($deliveryOrdersList)->groupBy('shopId')->toArray();
        // 发货
        foreach ($deliveryOrdersListGroup as $deliveryOrderData) {
            $orderService = OrderServiceManager::create();
            $first = array_first($deliveryOrderData);
            $userId = $first['userId'];
            $shopId = $first['shopId'];
            $orderService->setUserId($userId);
            $orderService->setShop($first['shop']);
            $orderDeliveryRequestList = ObjectUtil::batchMapToObject($deliveryOrderData, OrderDeliveryRequest::class);
            $responseList = $orderService->deliveryQualityInspection($orderDeliveryRequestList);
            $this->handleDeliveryResponse($deliveryOrderData, $responseList, $shopId, $userId, $orderSuccess,
                $failOrders);
        }
        return;
    }

    public function publishDeliveryEvent($userId, $shopId, $deliveryList, ?Request $request)
    {
        $user = User::query()->where('id', $userId)->first();
        $shop = Shop::query()->where('id', $shopId)->first();

        event((new OrderDeliveryEvent($user, $shop, time(), $deliveryList))->setClientInfoByRequest($request));
    }

    /**
     * 主要是干几件事情
     * 1. 发送物流轨迹请求
     * 2. 更新订单状态和订单面单号
     * 3. 更新取号记录状态
     * 4. 插入发货记录
     * @param $deliveryOrderData
     * @param  array  $responseList
     * @param $shopId
     * @param $userId
     * @param  int  $orderSuccess
     * @param  array  $failOrders
     * @return array
     * <AUTHOR>
     */
    protected
    function handleDeliveryResponse(
        $deliveryOrderData,
        array $responseList,
        $shopId,
        $userId,
        int &$orderSuccess,
        array &$failOrders
    ): array {
        $deliveryOrderData = array_pluck($deliveryOrderData, null, 'tid');
        foreach ($responseList as $response) {
            try {
//                Log::info('handleDeliveryResponse ', [$response]);
                $requestOriginData = null;
                $wpCode = '';
                $expressNo = '';
                /** @var OrderDeliveryRequest $orderDeliveryRequest */
                $orderDeliveryRequest = $response->getRequest();
                $requestOriginData = $deliveryOrderData[$orderDeliveryRequest->tid];
                $wpCode = $orderDeliveryRequest->expressCode;
                $expressNo = $orderDeliveryRequest->expressNo;
                if (!$response->isSuccess()) {
                    throw new ApiException([$response->getCode(), $response->getMessage()]);
                }
                $order = $requestOriginData['order'];
                $orderId = $order->id;
                $orderItemOId = $requestOriginData['orderItemOId'];
                // todo 后面要改成异步
                //订阅物流信息
                if ($order->template &&
                    in_array($order->template->auth_source,
                        [PlatformConst::WAYBILL_PDD, PlatformConst::WAYBILL_PDD_WB]) &&
                    $wpCode != 'JTSD'
                ) {
                    $waybillService = WaybillServiceManager::init($order->template->auth_source);
                    $waybillService->sendLogisticsTraceMsg($order);
                }

                $sendAt = date('Y-m-d H:i:s');
                //修改orderItem发货状态
                $orderItemQuery = OrderItem::query()->where('order_id', $orderId);
                $orderItemUpdateData = ['status' => Order::ORDER_STATUS_DELIVERED, 'send_at' => $sendAt];
                $orderItemExtraUpdateData = [];
                if (isset($order->orderExtra->order_biz_type) && $order->orderExtra->order_biz_type == OrderExtra::BIZ_TYPE_QUALITY_INSPECTION) {
                    $orderItemExtraUpdateData['quality_delivery_status'] = OrderItemExtra::QUALITY_DELIVERY_STATUS_DELIVERY;
                }
                if ($orderItemOId) {
                    $orderItemQuery->whereIn('oid', $orderItemOId);
                    $orderItemUpdateData['waybill_code'] = $expressNo;
                }
                //更新最新发货字段 改到 batchSave 那边修改
//                $orderItemUpdateData['send_num'] = DB::raw('goods_num');
//                $orderItemUpdateData['send_remain_num'] = 0;

                $orderItemQuery->update($orderItemUpdateData);
                OrderItem::query()->where('order_id', $orderId)->update(['waybill_code' => $expressNo]);
                if (!empty($orderItemExtraUpdateData)) {
                    $orderItemQuery->get()->map(function ($item) use ($orderItemExtraUpdateData) {
                        OrderItemExtra::whereOrderItemId($item->id)->update($orderItemExtraUpdateData);
                    });
                }
                //更新首次发货时间和运单号
                $orderItemQuery->whereNull('first_send_at')->update([
                    'first_send_at' => $sendAt, 'first_send_waybill_code' => $expressNo
                ]);
                \Log::info("订单发货", ["id" => $orderId, "express_no" => $expressNo, "send_at" => $sendAt]);

                $order->order_status = Order::ORDER_STATUS_DELIVERED;
                //修改orders订单状态 未传子订单修改为已发货，传了子订单判断是否已经全部发货
                if (!empty($orderItemOId)) {
                    //子订单中是否还有未发货的
                    $orderItem = OrderItem::query()->where(['order_id' => $orderId])->get()->toArray();
                    $orderItemHasNoDeliver = array_filter($orderItem, function ($t) {
                        return $t['status'] == Order::ORDER_STATUS_PAYMENT;
                    });
                    if (!empty($orderItemHasNoDeliver)) {
                        $order->order_status = Order::ORDER_STATUS_PART_DELIVERED;
                    }
                    //取过号的子订单是否还有未发货的
                    $orderItemHasNoDeliver = array_filter($orderItem, function ($t) {
                        return $t['waybill_code'] != null;
                    });
                    if ($order->order_status == Order::ORDER_STATUS_DELIVERED) {
                        $order->print_status = Order::PRINT_STATUS_YES;
                    } else {
                        $order->print_status = Order::PRINT_STATUS_NO;
                        foreach ($orderItemHasNoDeliver as $val) {
                            if (in_array($val['oid'],
                                    $orderItemOId) && $val['status'] != Order::ORDER_STATUS_DELIVERED) {
                                $order->print_status = Order::PRINT_STATUS_PART;
                                break;
                            }
                        }
                    }
                } else {
                    $order->order_status = Order::ORDER_STATUS_DELIVERED;
                }
                $order->send_at = $sendAt;
                $order->express_no = $expressNo;
                $order->express_code = $wpCode;
                \Log::info("订单发货",
                    ["id" => $order->id, "order_status" => $order->order_status, "send_at" => $sendAt]);
                if (!$order->save()) {
                    \Log::info("订单失败发货",
                        ["id" => $order->id, "order_status" => $order->order_status, "send_at" => $sendAt]);
                    throw new ApiException(ErrorConst::ORDER_STATUS_UPDATE_FAIL);
                }

                // todo 后面要改成异步
                $history = WaybillHistory::where([
                    'shop_id' => $shopId,
                    'waybill_code' => $expressNo,
                    'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
                ])->first();
                $ret = DeliveryRecord::create([
                    'user_id' => $userId,
                    'shop_id' => $shopId,
                    'history_id' => $history ? $history->id : 0,
                    'order_id' => $order->id,
                    'order_no' => $order->tid,
                    'waybill_code' => $expressNo,
                    'wp_code' => $wpCode,
                    'result' => true,
                ]);
                if (!$ret) {
                    throw new ApiException(ErrorConst::DELIVERY_RECORD_EXCEPTION);
//                    throw new \Exception('发货记录异常');
                }
                if ($order->factory_id > 0) {
                    OrderExtra::whereOrderId($order->id)->update([
                        'is_factory_shipped' => 1,
                    ]);
                }
                $orderSuccess++;
            } catch (\Exception $e) {
                Log::error($e->getMessage(), ['trace' => $e->getTrace(), 'item' => $requestOriginData]);
                $error_code = $e->getCode();
                $message = $e->getMessage();
                if ($e instanceof ApiException) {
                    $error_code = $e->getErrorCode();
                } elseif ($e instanceof ConnectException) {
                    $message = '请求平台发货超时，请稍后重试！';
                }
                $order = clone $requestOriginData['order'] ?? [];
                $order->error_code = $error_code;
                $order->error_msg = $message;
                $order->waybillCodeStr = $expressNo;
                $failOrders[] = $order;
            }
        }
        return [];
    }

    /**
     * 发货成功的订单处理
     *      * 主要是干几件事情
     * 1. 发送物流轨迹请求
     * 2. 更新订单状态和订单面单号
     * 3. 更新取号记录状态
     * 4. 插入发货记录
     *          入参的原始数据格式
     * [
     *  ['tid' => $tid,"shopId"=>$shopId,  "waybillCodes"=> [
     * "waybillCode"=>"6919675406348588608",
     * "expressCode"=>"xxx",
     *  "packs"=>[
     *      ["oid"=>111,"shippedNum"=>1],
     *  ]
     * ]
     * ]
     *
     * @param  array{tid:string,shopId:int,waybillCodes:array{waybillCode:string,expressCode:string,packs:array{
     *     oid:int,shippedNum:int
     * }[]}[]  $orderSuccesses
     * @param  Collection  $packages
     * @param  array  $failOrders
     * @return void
     */
    public function handleOrderMultiPackagesDeliverResponse(array $orderSuccesses, $packages, array &$failOrders = [])
    {
        $tidArr = array_column($orderSuccesses, 'tid');
        $tidArr = batchAddA($tidArr);
        $orderList = \App\Models\Fix\Order::query()->with('shop.user')->whereIn('tid', $tidArr)->get();
        foreach ($orderSuccesses as $successes) {
            Log::info("handleOrderMultiPackagesDeliverResponse", $successes);
            $tid = $successes['tid'];
//            $shopId = $successes['shopId'];
            $waybillCodes = $successes['waybillCodes'];
            $waybillArray = array_pluck($waybillCodes, 'waybillCode');
            //单号可能被识别成数字，转成字符串
            $waybillArray = array_map(function ($item) {
                return strval($item);
            }, $waybillArray);
            $waybillCodeStr = implode(",", $waybillArray);

            $firstWaybillCode = $waybillCodes[0];
            $expressCode = $firstWaybillCode['expressCode'];
            $expressNo = $firstWaybillCode['waybillCode'];
            try {
                /**
                 * @var Order $order
                 */
//                $order = Order::firstByTid($tid);

                $order = $orderList->firstWhere('tid', tidAddA($tid));
                $shop = $order->shop;
                $packs = array_flatten(array_pluck($waybillCodes, 'packs'), 1);
                //1.把packs按oid分组
                //2.统计每个oid的发货数量
                $packsGroupByOid = ArrayUtil::array_group_by($packs, 'oid');
                $sendNumsKeyByOid = array_map(function ($packs) {
                    return array_sum(array_column($packs, 'shippedNum'));
                }, $packsGroupByOid);


                $orderItemOIdArr = array_pluck($packs, 'oid');

                // todo 后面要改成异步
                //订阅物流信息
                $package = $packages->whereIn('waybill_code', $expressNo)->first();
                if (!empty($package) && $package->template &&
                    in_array($package->template->auth_source,
                        [PlatformConst::WAYBILL_PDD, PlatformConst::WAYBILL_PDD_WB]) &&
                    $expressCode != 'JTSD'
                ) {
                    $waybillService = WaybillServiceManager::init($package->template->auth_source);
                    $waybillService->sendLogisticsTraceMsg($order);


                }

                $sendAt = date('Y-m-d H:i:s');
                //修改orderItem发货状态
                $orderId = $order->id;
//                $orderItemQuery = OrderItem::query()->where('order_id', $orderId);
//
//                $orderItemUpdateData = ['status' => Order::ORDER_STATUS_DELIVERED, 'send_at' => $sendAt];
//
//                if (!empty($orderItemOIdArr)) {
//                    $orderItemQuery->whereIn('oid', $orderItemOIdArr);
//                }
//                $orderItemQuery->update($orderItemUpdateData);
                //更新子订单里面的发货数量，
                Log::info("批量更新子订单状态", ["sendNumsKeyByOid" => $sendNumsKeyByOid]);
                foreach ($sendNumsKeyByOid as $oid => $shippedNum) {
                    $orderItem = OrderItem::query()->where(['order_id' => $order->id, 'oid' => $oid])->first();
                    $orderItem->send_at = $sendAt;
                    $orderItem->send_num = min(($orderItem->send_num ?? 0) + $shippedNum, $orderItem->goods_num);
                    $orderItem->send_remain_num = $orderItem->goods_num - $orderItem->send_num;
                    if ($orderItem->send_remain_num == 0) {
                        $orderItem->status = Order::ORDER_STATUS_DELIVERED;
                    } else {
                        $orderItem->status = Order::ORDER_STATUS_PART_DELIVERED;
                    }
                    Log::info("更新子订单状态", ["orderItemOIdArr" => $oid, "shippedNum" => $shippedNum]);
                    $orderItem->save();
                }


                Log::info("更新子订单状态", ["orderItemOIdArr" => $orderItemOIdArr]);
//                $order->order_status = Order::ORDER_STATUS_DELIVERED;
                //修改orders订单状态 未传子订单修改为已发货，传了子订单判断是否已经全部发货
                if (!empty($orderItemOIdArr)) {
                    //子订单中是否还有未发货的
                    $orderItem = OrderItem::query()->where(['order_id' => $orderId])->get()->toArray();
                    $orderItemHasNoDeliver = array_filter($orderItem, function ($t) {
                        return in_array($t['status'],
                            [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED]);
                    });
                    if (!empty($orderItemHasNoDeliver)) {
                        $order->order_status = Order::ORDER_STATUS_PART_DELIVERED;
                    } else {
                        $order->order_status = Order::ORDER_STATUS_DELIVERED;
                    }
                    //取过号的子订单是否还有未发货的
                    $orderItemHasNoDeliver = array_filter($orderItem, function ($t) {
                        return $t['waybill_code'] != null;
                    });
                    if ($order->order_status == Order::ORDER_STATUS_DELIVERED) {
                        $order->print_status = Order::PRINT_STATUS_YES;
                    } else {
//                        $order->print_status = Order::PRINT_STATUS_NO; // 部分发货导致打印状态变成 0 所以注释
                        foreach ($orderItemHasNoDeliver as $val) {
                            if (in_array($val['oid'],
                                    $orderItemOIdArr) && $val['status'] != Order::ORDER_STATUS_DELIVERED) {
                                $order->print_status = Order::PRINT_STATUS_PART;
                                break;
                            }
                        }
                    }
                } else {
                    $order->order_status = Order::ORDER_STATUS_DELIVERED;
                }
                $order->send_at = $sendAt;
                $order->express_no = $expressNo;
                $order->express_code = $expressCode;
                $order->abnormal_type = 0; // 清除异常状态
                Log::info("订单发货",
                    ["id" => $order->id, "order_status" => $order->order_status, "send_at" => $sendAt]);
                if (!$order->save()) {
                    Log::info("订单失败发货",
                        ["id" => $order->id, "order_status" => $order->order_status, "send_at" => $sendAt]);
                    throw new ApiException(ErrorConst::ORDER_STATUS_UPDATE_FAIL);
                }
                // 清除其他订单的异常状态
                Order::query()
                    ->where('address_md5', $order->address_md5)
                    ->where('shop_id', $order->shop_id)
                    ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])
                    ->where('abnormal_type', '!=', 0)
                    ->update(['abnormal_type' => 0]);

                // 这个地方只取了第一个面单的信息，并记录到发货记录里面，多个面单也只记录一个，逻辑上有问题，但需要考虑兼容性
                $history = WaybillHistory::where([
                    'shop_id' => $shop->id,
                    'waybill_code' => strval($expressNo),
                    'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
                ])->first();
                $ret = DeliveryRecord::create([
                    'user_id' => 0,
                    'shop_id' => $order->shop_id,
                    'history_id' => $history ? $history->id : 0,
                    'order_id' => $order->id,
                    'order_no' => $order->tid,
                    'waybill_code' => $expressNo,
                    'wp_code' => $expressCode,
                    'result' => true,
                ]);

                $packagesKeyByWaybillCode = $this->packageService->getPackagesByWaybillCodes($waybillArray)->keyBy('waybill_code');

                /**
                 * @var Package $package
                 */
                $packagesKeyByWaybillCode->each(function ($package, $waybillCode) use ($tid, $orderItemOIdArr) {
//                    $packageOrders = $package->packageOrders;
//                    $packageOrders->each(function ($packageOrder) use ($package, $tid, $orderItemOIdArr) {
//                        if (!in_array($packageOrder->oid, $orderItemOIdArr)) {
//                            return;
//                        }
////                        $packageOrder->status = Order::ORDER_STATUS_DELIVERED;
////                        $packageOrder->save();
//
//                        PackageOrder::create([
//                            'package_id' => $packageOrder->package_id,
//                            'order_id' => $packageOrder->order_id,
//                            'order_item_id' => $packageOrder->order_item_id,
//                            'num' => $packageOrder->num,
//                            'version' => 3,
//                            'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
//                            'tid' => $packageOrder->tid,
//                            'oid' => $packageOrder->oid,
//                            'num_iid' => $packageOrder->num_iid,
//                            'sku_id' => $packageOrder->sku_id,
//                            'status' => Order::ORDER_STATUS_DELIVERED,
//                        ]);
//                        \Log::info("更新包裹子订单状态", ["packageId" => $package->id, "orderItemOIdArr" => $packageOrder->oid]);
//                    });
                    //查找还有没有未发货的packageOrder
//                    $wholeDelivered = $packageOrders->filter(function ($packageOrder) {
//                        return $packageOrder->status != Order::ORDER_STATUS_DELIVERED;
//                    })->isEmpty();

                    $package->update([
                        'status' => Order::ORDER_STATUS_DELIVERED,
                        'delivery_type' => Package::DELIVERY_TYPE_FIRST,
                        'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                        'send_at' => date('Y-m-d H:i:s'),
                    ]);

                    Log::info("更新包裹状态", ["packageId" => $package->id, "status" => $package->status]);
                });
                if (!empty($orderItemOIdArr)) { //部分发货
                    $order->updateOrderPrintStatus();
                }


                if (!$ret) {
                    throw new ApiException(ErrorConst::DELIVERY_RECORD_EXCEPTION);
//                    throw new \Exception('发货记录异常');
                }
                if ($order->factory_id > 0) {
                    OrderExtra::whereOrderId($order->id)->update([
                        'is_factory_shipped' => 1,
                    ]);
                }


            } catch (\Exception $e) {
                Log::error($e->getMessage(), ['trace' => $e->getTrace()]);
                $message = $e->getMessage();
                if ($e instanceof ApiException) {
                    $error_code = $e->getErrorCode();
                } elseif ($e instanceof ConnectException) {
                    $message = '请求平台发货超时，请稍后重试！';
                }

                $order = [
                    "tid" => $tid,
                    "error_msg" => $message,
                    "waybillCodeStr" => $waybillCodeStr
                ];
                $failOrders[] = $order;
            }

        }

    }

    /**
     * @param  string  $tid
     * @param  string|null  $expressCode
     * @param  string  $message
     * @return Order
     */
    public
    function buildErrorOrder(
        string $tid,
        string $expressCode = null,
        string $message = ""
    ): Order {
        $errorOrder = new Order();
        $errorOrder->tid = $tid;
        $errorOrder->error_msg = $message;;
        $errorOrder->waybillCodeStr = $expressCode;
        return $errorOrder;
    }

    /**
     * 扫码发货
     * @param  array  $shopIds
     * @param  string  $waybillCode
     * @param  int  $userId
     * @return OrderResult[]
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function scanDeliver(array $shopIds, string $waybillCode, int $userId = 0): array
    {
        $failList = [];
        if (empty($shopIds)) {
            throw_error_code_exception(StatusCode::UNSELECT_SHOP);
        }
        $waybillCode = trim($waybillCode);
        /**
         * @var WaybillHistory $waybillHistory
         */
        $waybillHistory = WaybillHistory::query()->where('waybill_code', $waybillCode)->whereIn('shop_id',
            $shopIds)->first();
        if (empty($waybillHistory)) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：'.$waybillCode.'未识别');
        }
        if ($waybillHistory->waybill_status == WaybillHistory::WAYBILL_RECOVERY_YES) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：'.$waybillCode.'已经回收');
        }
        $delivers = [];
        $wpCode = $waybillHistory->wp_code;
        $packageOrders = $waybillHistory->packageOrders()->get()->all();
        foreach ($packageOrders as $packageOrder) {
            $delivers[] = [
                'idStr' => strval($packageOrder->order_id),
                'waybillCodeStr' => $waybillCode,
                'express_code' => $wpCode,
            ];
        }
        //对

        Log::info("扫码发货", $delivers);

        return $this->orderMultiPackagesDeliver($delivers, $userId, 2);
    }
////        $waybillHistory = WaybillHistory::query()->where([
////            'shop_id' => $request->auth->shop_id,
////            'waybill_code' => $waybillCode,
////        ])->get();
//        //这个已经是拉平到订单维度的了
//        if (!empty($waybillHistory)) {
//
//            foreach ($waybillHistory as $item) {
//                if ($item['waybill_status'] == WaybillHistory::WAYBILL_RECOVERY_YES) {
//                    $failList[] = strval(WaybillResult::fail($item['waybill_code'], '该单号已经回收'));
//                    break;
//                }
//                $order = Order::findOrFail($item['order_id'])->toArray();
//                $wp_code = $item['wp_code'];
//                if (!empty($order)) {
//                    //有退款
//                    if (in_array($order['refund_status'], [Order::REFUND_STATUS_YES])) {
//                        $failList[] = strval(OrderResult::fail($order['tid'], '订单有退款'));
//                        continue;
//                    }
//                    //已发货
//                    if (in_array($order['order_status'], [Order::ORDER_STATUS_DELIVERED])) {
//                        $failList[] = strval(OrderResult::fail($order['tid'], '订单已经发货'));
//                        continue;
//                    }
//
//                    //待发货
//                    if (in_array($order['order_status'], [Order::ORDER_STATUS_PAYMENT])) {
//                        $delivers = [[
//                            'id' => $order['id'],
//                            'express_no' => $waybillCode,
//                            'express_code' => $wp_code,
//                        ]];
//                        $ret = OrderDeliveryService::orderScanDelivery($delivers);
//                        foreach ($ret as $orderResult) {
//                            if (!$orderResult->success) {
//                                $failList[] = strval($orderResult);
//                            }
//                        }
//                        //把失败的记录返回
//
//
//                    }
//                } else {
//                    $failList[] = '订单号：' . $item['order_id'] . '未识别';
//                }
//            }
//
//        } else {
//            $failList[] = '单号：' . $waybillCode . '未识别';
//        }


    /**
     * 重新发货
     * @param $shopId
     * @param  array  $data
     * @return array
     */
    public function redelivery($shopId, array $data): array
    {
        $type = data_get($data, 'type'); // 1补发 2换货 3变更单号 9其他
        $list = data_get($data,
            'list'); // [{"tid":"6937150097245804464","orderId":2456415,"waybillCode":"9440299340783","beforePtLogisticsIdArr":[1319682],"wpCode":"youzhengguonei"}]
        $shopIds = ShopBind::getAllRelationShopIds($shopId);
        $shops = Shop::query()->whereIn('id', $shopIds)->get()->keyBy('id');

//        $beforeWaybillCodeArr = array_pluck($list, 'beforeWaybillCode');
        $beforePtLogisticsIdArr = array_flatten(array_pluck($list, 'beforePtLogisticsIdArr'));
        $waybillCodeArr = collect($list)->pluck('packArr.*.waybillCode')->flatten()->toArray();

        $orderTotal = count($list);
        $beforePtLogisticsList = PtLogistics::with(['package', 'ptLogisticsItems'])->whereIn('id',
            $beforePtLogisticsIdArr)->get();


        $orderIdArr = $beforePtLogisticsList->pluck('ptLogisticsItems.*.order_id')->flatten()->unique()->toArray();
        $orderList = \App\Models\Fix\Order::query()->with(['orderExtra', 'orderItem'])->whereIn('id',
            $orderIdArr)->get();

        $packageList = Package::query()->with(['printPackageOrders'])->whereIn('waybill_code', $waybillCodeArr)->get();

        $orderSuccess = 0;
        $orderFail = 0;
        $failOrderArr = [];
        $tid2ListMap = [];
        $hasHandleTid = [];
        $sendPackages = [];
//        if ($type == 3 && Environment::isTaoBao()) {
//            //淘宝换单号，根据tid聚合下
//            $tid2ListMap = collect($list)->groupBy("tid")->toArray();
//            //读取所有发货的包裹，用于换单号
//            $sendPackages = PtLogistics::with(['ptLogisticsItems'])->whereIn('order_id', array_pluck($list, 'orderId'))->get()->groupBy("order_id")->toArray();
//        }
        foreach ($list as $index => $item) {
//            if (in_array($item['tid'], $hasHandleTid) && Environment::isTaoBao()) {
//                continue;
//            }
            $hasHandleTid[] = $item['tid'];
//            $writePackageCounter = 0; // 用于合单判断是否重复写入包裹
            $writePackageCounterArr = []; // 用于合单判断是否重复写入包裹
            // 合单情况
            foreach ($item['beforePtLogisticsIdArr'] as $beforePtLogisticsIndex => $beforePtLogisticsId) {
                $beforePtLogistics = $beforePtLogisticsList->where('id', $beforePtLogisticsId)->first();
                if (!$beforePtLogistics) {
                    Log::warning('重新发货异常:发货记录ID没有:'.$beforePtLogisticsId);
                    continue;
                }
                $orderId = $beforePtLogistics->order_id;
                $oldPackage = $beforePtLogistics->package;
                $masterWaybillCode = $item['waybillCode'];
                // 需要重新发货的包裹数
                $beforePtLogisticsCount = count($item['beforePtLogisticsIdArr']);
                $packArr = $item['packArr'];
                $packArrCount = count($packArr);
                $isSplitOrderMany = false;
                if ($type == 3) {
//                    &&} in_array($oldPackage->send_waybill_type, [Package::SEND_WAYBILL_TYPE_SPILT_MAIN, Package::SEND_WAYBILL_TYPE_SPILT_SUB])) { // 变更单号
                    $isSplitOrderMany = true;
                    // 老单号比新单号多
                    if ($beforePtLogisticsCount >= $packArrCount) {
                        // 包裹一一对应，找不到的取第一个
                        $packItem = $item['packArr'][$beforePtLogisticsIndex] ?? $item['packArr'][0];
                        $packArr = [$packItem];
                    } else {// 剩下就是 老单号比新单号少
                        // 最后一个把剩下的都塞进去
                        if ($beforePtLogisticsIndex == ($beforePtLogisticsCount - 1)) {
                            $packArr = array_slice($item['packArr'], $beforePtLogisticsIndex);
                        } else {
                            $packItem = $item['packArr'][$beforePtLogisticsIndex];
                            $packArr = [$packItem];
                        }
                    }
                }
                // 重新发货多个包裹
                foreach ($packArr as $packIndex => $packItem) { //packArr =[["waybillCode"="123","wpCode"="EMS"]]
//                    $firstWaybillCode = $item['packArr'][0]['waybillCode'];
                    $waybillCode = $packItem['waybillCode'];
                    $wpCode = $packItem['wpCode'];
                    $newPackage = $packageList->where('waybill_code', $waybillCode)->first();
                    $ptLogisticsItems = $beforePtLogistics->ptLogisticsItems;
                    Log::info('重新发货', [$beforePtLogistics]);
                    $order = $orderList->whereIn('id', $orderId)->first();
                    $isContinue = false;
                    $deliveryType = Package::DELIVERY_TYPE_NO;
                    try {
//                    $shop = $shops[$order['shop_id']];
                        $shop = Shop::query()->where('id', $order['shop_id'])->first();
                        $orderService = OrderServiceManager::create();
                        $orderService->setShop($shop);
                        switch ($type) {
                            case 1: // 补发
                                if (Environment::isSupportAppendPlatform()) { // 有补发接口
                                    $goodsList = [];
                                    if (Environment::isDy() || Environment::isTaoBao()) {
                                        foreach ($order['orderItem'] as $order_item) {
                                            $goodsList[] = [
                                                'oid' => $order_item['oid'],
                                                'num' => $order_item['goods_num'],
                                            ];
                                        }
                                    }
                                    // 平台补发接口
                                    $orderService->orderAppendPackages($order['tid'], $wpCode, $waybillCode,
                                        $goodsList);
                                } else {
                                    $newMemo = '补发单号:'.$waybillCode.';';
                                    $this->setOrderSellerMemo($order, $newMemo, $orderService, $order['tid']);
                                }
                                $deliveryType = Package::DELIVERY_TYPE_REPLACEMENT;
                                if (empty($writePackageCounterArr[$packIndex])) { // 合单只写入一次
                                    $this->writePackage($newPackage, $deliveryType);
                                    $writePackageCounterArr[$packIndex] = 1;
                                }
                                break;
                            case 2: // 换货
                                $newMemo = '换货单号:'.$waybillCode.';';
                                $this->setOrderSellerMemo($order, $newMemo, $orderService, $order['tid']);
                                $deliveryType = Package::DELIVERY_TYPE_RETURN;
                                if (empty($writePackageCounterArr[$packIndex])) { // 合单只写入一次
                                    $this->writePackage($newPackage, $deliveryType);
                                    $writePackageCounterArr[$packIndex] = 1;
                                }
                                break;
                            case 3: // 变更单号
                                // 取打印包裹的订单id 数组
                                $newPackageOrderIds = $newPackage->printPackageOrders->pluck('order_id')->unique()->toArray();
                                // 如果没打印 则不处理
                                if (!in_array($beforePtLogistics->order_id, $newPackageOrderIds)) {
                                    $isContinue = true;
                                    break;
                                }

                                if (Environment::isWxsp() && !empty($oldPackage->is_split)) {
                                    throw new BusinessException('平台限制:拆单订单不能变更单号');
                                }
                                if (Environment::isWxsp() && in_array($order->order_status,
                                        [Order::ORDER_STATUS_PART_DELIVERED])) {
                                    throw new BusinessException('平台限制:未完整发货订单不允许变更');
                                }
                                $deliveryType = Package::DELIVERY_TYPE_CHANGE;
                                $isRequest = false;
                                if ($packIndex == 0 && $isSplitOrderMany) {
                                    // 第一个请求，后面备注
                                    $isRequest = true;
                                } elseif ($waybillCode == $masterWaybillCode) {
                                    // 变更单号多包裹只变更主单号
                                    $isRequest = true;
                                }

                                if (Environment::isAlbb() || Environment::isAlC2M()) {
                                    // 不支持变更单号
                                    $newMemo = '变更单号:'.$waybillCode.';';
                                    $this->setOrderSellerMemo($order, $newMemo, $orderService, $order['tid']);
                                    if (empty($writePackageCounterArr[$packIndex])) { // 合单只写入一次
                                        $this->writePackage($newPackage, $deliveryType);
                                        $writePackageCounterArr[$packIndex] = 1;
                                    }
                                    break;
                                }
                                if ($isRequest) {
                                    $orderDeliverAgainRequest = new OrderDeliverAgainRequest();
                                    $orderDeliverAgainRequest->tid = $order['tid'];
                                    $orderDeliverAgainRequest->waybillCode = $waybillCode;
                                    $orderDeliverAgainRequest->wpCode = $wpCode;
                                    $orderDeliverAgainRequest->isSplit = $newPackage->is_split;
                                    $orderDeliverAgainRequest->isMerge = count($item['beforePtLogisticsIdArr']) > 1;

                                    if (!empty($beforePtLogisticsId)) {
                                        if (empty($beforePtLogistics)) {
                                            // 运单号未找到
                                            throw new BusinessException('运单号未找到:'.$beforePtLogisticsId);
                                        }
                                        $orderDeliverAgainRequest->deliveryId = $beforePtLogistics->delivery_id;
                                        $orderDeliverAgainRequest->oldWpCode = $beforePtLogistics->wp_code;
                                        $orderDeliverAgainRequest->oldWaybillCode = $beforePtLogistics->waybill_code;
                                        $orderDeliverAgainRequest->oldPackageOrders = $oldPackage->printPackageOrders ?? [];

                                        if (Environment::isWxsp() && $orderDeliverAgainRequest->isSplit) {
                                            throw new BusinessException('平台限制：拆单不允许修改单号');
                                        }
//                                        if (Environment::isDy() || Environment::isJd() || Environment::isTaoBao()) {
//                                            $orderService->deliveryOrdersAgain($orderDeliverAgainRequest);
//                                        }
                                        $orderService->deliveryOrdersAgain($orderDeliverAgainRequest);
                                    } else {
                                        $orderService->deliveryOrdersAgain($orderDeliverAgainRequest);
                                    }

//                                $waybillHistory = $waybillHistoryList->where('waybill_code', $item['waybillCode'])->first();
                                    if (empty($writePackageCounterArr[$waybillCode])) { // 合单只写入一次
                                        $this->writePackage($newPackage, $deliveryType);
                                        $writePackageCounterArr[$waybillCode] = 1;
                                    }
                                    if ($oldPackage) {
                                        $newPackage->update([
                                            'send_waybill_type' => $oldPackage->send_waybill_type,  // 重新发货原来要保持一致
                                        ]);
                                    }
                                    // 拆单的一单多包需要记录主订单号
                                    if ($isSplitOrderMany && $beforePtLogisticsIndex > 0) {
                                        $newPackage->update(['multi_package_main_waybill_code' => $masterWaybillCode]);
                                    }
                                } else {
                                    // 一单多包裹的时候设置主包裹
                                    $newPackage->update(['multi_package_main_waybill_code' => $masterWaybillCode]);
                                    $newMemo = '重新发货:'.$waybillCode.';';
                                    $this->setOrderSellerMemo($order, $newMemo, $orderService, $order['tid']);
//                                    if ($isSplitOrderMany){
//                                        // 拆单标记成空包裹
//                                        $newPackage->update([
//                                            'send_waybill_type' => Package::SEND_WAYBILL_TYPE_EMPTY,
//                                        ]);
//                                    }
                                }

                                break;
                            case 9: // 其他
                                $newMemo = '其他:'.$waybillCode.';';
                                $this->setOrderSellerMemo($order, $newMemo, $orderService, $order['tid']);
                                $deliveryType = Package::DELIVERY_TYPE_OTHER;
                                if (empty($writePackageCounterArr[$packIndex])) { // 合单只写入一次
                                    $this->writePackage($newPackage, $deliveryType);
                                    $writePackageCounterArr[$packIndex] = 1;
                                }
                                break;
                            default:
                                throw new ApiException(ErrorConst::PARAM_ERROR);
                                break;
                        }
                        if ($isContinue) {
                            continue;
                        }
                        $orderSuccess++;
                        $eventOrderList = [];
                        $eventOrderList[] = [
                            'id' => $order['id'],
                            'tid' => $order['tid'],
                            'deliveryType' => $deliveryType,
                            'oldWaybillCode' => $beforePtLogistics->waybill_code,
                            'newWaybillCode' => $waybillCode,
                        ];
                        event((new OrderRedeliveryEvent($shop->user, $shop, time(),
                            $eventOrderList))->setClientInfoByRequest(\request()));
                    } catch (\Exception $e) {
                        Log::error('重新发货失败:'.$e->getMessage(), [$e->getTraceAsString()]);

                        $orderTmp = $order->toArray();
                        $orderFail++;
                        $orderTmp['error_code'] = $e->getCode();
                        $orderTmp['error_msg'] = $e->getMessage();
                        $orderTmp['waybillCodeStr'] = $waybillCode;
                        $failOrderArr[] = $orderTmp;
                    }
                }

            }

        }
        $orderInfos = Order::batchGetOrderInfo($orderList->toArray());
        $orderGroupArr = collect($orderInfos)->groupBy('shop_id')->toArray();
        foreach ($orderGroupArr as $item) {
            Order::batchSave($item, $item[0]['user_id'], $item[0]['shop_id']);
        }
        return [
            'orderTotal' => $orderTotal,
            'orderSuccess' => $orderSuccess,
            'orderFail' => $orderFail,
            'failOrders' => $failOrderArr
        ];

    }

    /**
     * @param $order
     * @param  string  $newMemo
     * @param  AbstractOrderService  $orderService
     * @param $tid
     */
    public function setOrderSellerMemo($order, string $newMemo, AbstractOrderService $orderService, $tid)
    {
        $orderSellerMemo = json_decode($order['seller_memo'], true);
        if (empty($orderSellerMemo)) {
            $orderSellerMemo[] = $newMemo;
        } else {
            $newMemo = $orderSellerMemo[0].$newMemo;
        }
//        $orderSellerMemo = json_encode($orderSellerMemo,JSON_UNESCAPED_UNICODE);
        $orderService->sendEditSellerRemark($tid, $order['seller_flag'] ?? Order::FLAG_GRAY, $newMemo);
    }


    /**
     * 获取上一次执行（PreshipmentLogisticScheduleTimerDeliverCmd）的时间,从系统配置项中读取
     * 如果取不到就返回今天的0点
     * @return DateTime
     */
    public function getLatestScheduleTimerDeliverTime(): DateTime
    {
        $logisticsDeliverLastTime = SystemConfig::query()->where('key',
            self::LOGISTICS_SCHEDULE_DELIVER_LAST_TIME)->first();
        $strDate = DateTimeUtil::strNow(DateTimeUtil::ymdHms);
        if (isset($logisticsDeliverLastTime)) {
            $strDate = $logisticsDeliverLastTime->value;
        }
        return DateTimeUtil::date($strDate);
    }

    /**
     * 获取上一次执行（PreshipmentLogisticAfterTimerDeliverCmd）的时间,从系统配置项中读取
     * 如果取不到就返回今天的0点
     * @return DateTime
     */
    public function getLatestAfterTimerDeliverTime(): DateTime
    {
        $logisticsDeliverLastTime = SystemConfig::query()->where('key',
            self::LOGISTICS_AFTER_DELIVER_LAST_TIME)->first();
        $strDate = DateTimeUtil::strNow(DateTimeUtil::ymdHms);
        if (isset($logisticsDeliverLastTime)) {
            $strDate = $logisticsDeliverLastTime->value;
        }
        return DateTimeUtil::date($strDate);
    }

//    /**
//     * 获取上一次执行（LogisticTimerDeliverTask）的时间,从系统配置项中读取
//     * 如果取不到就返回今天的0点
//     * @return DateTime
//     */
//    public function getLatestTimerDeliverTime(): DateTime
//    {
//        $logisticsDeliverLastTime = SystemConfig::query()->where('key', 'logistics_deliver_last_time')->first();
//        $strDate = DateTimeUtil::strNow(DateTimeUtil::ymdHms);
//        if (isset($logisticsDeliverLastTime)) {
//            $strDate = $logisticsDeliverLastTime->value;
//        }
//        return DateTimeUtil::date($strDate);
//    }


    /**
     * 用于记录上一次执行（PreshipmentLogisticScheduleTimerDeliverCmd）的绝对时间
     * @param  DateTime  $dateTime
     * @return void
     */
    public function setScheduleLatestDeliverTime(DateTime $dateTime): void
    {
        $logisticsDeliverLastTime = SystemConfig::query()->where('key',
            self::LOGISTICS_SCHEDULE_DELIVER_LAST_TIME)->first();
        if (!isset($logisticsDeliverLastTime)) {
            $logisticsDeliverLastTime = new SystemConfig();
            $logisticsDeliverLastTime->name = "物流定时发送最近执行时间";
            $logisticsDeliverLastTime->key = self::LOGISTICS_SCHEDULE_DELIVER_LAST_TIME;
        }
        $logisticsDeliverLastTime->value = DateTimeUtil::ymdHms($dateTime);
        $logisticsDeliverLastTime->save();
    }

    /**
     * 用于记录上一次执行（PreshipmentLogisticAfterTimerDeliverCmd）的绝对时间
     * @param  DateTime  $dateTime
     * @return void
     */
    public function setAfterLatestDeliverTime(DateTime $dateTime): void
    {
        $logisticsDeliverLastTime = SystemConfig::query()->where('key',
            self::LOGISTICS_AFTER_DELIVER_LAST_TIME)->first();
        if (!isset($logisticsDeliverLastTime)) {
            $logisticsDeliverLastTime = new SystemConfig();
            $logisticsDeliverLastTime->name = "有物流就发货最近执行时间";
            $logisticsDeliverLastTime->key = self::LOGISTICS_AFTER_DELIVER_LAST_TIME;
        }
        $logisticsDeliverLastTime->value = DateTimeUtil::ymdHms($dateTime);
        $logisticsDeliverLastTime->save();
    }


    /**
     * @param  int  $shop_id
     * @param $list array [{"waybillCode":"运单号","wpCode":"快递公司 code","items":[{"orderId":"订单id","orderItemId":"子订单id","num":"数量"}]}]
     * @param $consignStatus  [{oid:'xx',isPartConsign: false}} 用于淘宝确认是否发完
     * @param  bool  $preshipment
     * @param  int|null  $userId
     * @return array
     * @throws ErrorCodeException
     * @throws \Throwable
     */
    public function customDelivery(
        int $shop_id,
        array $list,
        $consignStatus,
        bool $preshipment = false,
        ?int $userId = null
    ): array {

        $successOrders = [];
        $failOrders = [];
        $orderIds = [];
        $orderItemIds = [];
        $waybillCodes = [];
        foreach ($list as $item) {
            $waybillCodes[] = $item['waybillCode'];
            foreach ($item['items'] as $item2) {
                $orderIds[] = $item2['orderId'];
                $orderItemIds[] = $item2['orderItemId'];
            }
        }
        $waybillCodes = array_unique($waybillCodes);
        $orderIds = array_unique($orderIds);
        $orderItemIds = array_unique($orderItemIds);
        $orderList = Order::query()->with('shop')->whereIn('id', $orderIds)->get();
        $orderItemList = OrderItem::query()->whereIn('id', $orderItemIds)->get();
        $operatingShop = \App\Models\Fix\Shop::find($shop_id);


        //利用面单好，把所有的包裹都查处理
        $packages = Package::query()->with(['packageOrders', 'template'])->whereIn('waybill_code',
            $waybillCodes)->get();

        list($successWaybillCodeArr, $successOrders, $failOrders) = $this->splitOrderDelivery($operatingShop, $list,
            $packages, $orderList, $orderItemList, $preshipment, $consignStatus);

        Log::info('customDelivery:$success', [$successWaybillCodeArr, $successOrders, $failOrders]);
        Log::info('customDelivery:$successWaybillCodeArr', [$successWaybillCodeArr, $successOrders, $failOrders]);
        $successMainWaybillCodeArr = [];
        foreach ($successOrders as $index => $successOrder) {
            $tid = $successOrder['tid'];
            $isSplitCount = $packages->whereIn('waybill_code', $successOrder['waybillCodeArr'])->filter(function ($item
            ) use ($tid) {
                return $item['is_split'];
            })->count();
            foreach ($successOrder['waybillCodes'] as $waybillCodeItem) {
                $waybillCode = $waybillCodeItem['waybillCode'];
                $package = $packages->firstWhere('waybill_code', $waybillCode);
                $requestCount = collect($list)->where('waybillCode', $waybillCode)->count();
                $successCount = collect($successOrders)->filter(function ($item) use ($waybillCode) {
                    return in_array($waybillCode, $item['waybillCodeArr']);
                })->count();
                $packageStatus = Package::ORDER_STATUS_DELIVERED;
                if ($requestCount != $successCount) { // 请求过来的运单号数量要和成功的运单号数量对上
                    // 运单号没有全部成功，设置成部分发货
                    Log::info('运单号没有全部成功，设置成部分发货:'.$waybillCode);
                    $packageStatus = Package::ORDER_STATUS_PART_DELIVERED;
                }
                $packItems = $waybillCodeItem['packs'];
                $mainWaybillCode = '';
                $sendWaybillType = Package::SEND_WAYBILL_TYPE_NORMAL;
                if (!empty($waybillCodeItem['isEmptyPack'])) { // 空包裹
                    $mainWaybillCode = $waybillCodeItem['parentWaybillCode'];
                    $sendWaybillType = Package::SEND_WAYBILL_TYPE_EMPTY;
                } else {
                    if ($isSplitCount > 0) { // 有拆单订单就正常拆单
                        $sendWaybillType = Package::SEND_WAYBILL_TYPE_NORMAL;
                    } elseif (count($successOrder['waybillCodes']) > 1) {
                        // 没有走主从逻辑
                        if (empty($successMainWaybillCodeArr[$tid])) {
                            $sendWaybillType = Package::SEND_WAYBILL_TYPE_SPILT_MAIN;
                            $successMainWaybillCodeArr[$tid] = $waybillCode; // 保存主单号
                        } else {
                            $sendWaybillType = Package::SEND_WAYBILL_TYPE_SPILT_SUB;
                            $mainWaybillCode = $successMainWaybillCodeArr[$tid];
                        }
                    }
                }


                Log::info('$sendWaybillType',
                    [$isSplitCount, $waybillCode, $successMainWaybillCodeArr, $sendWaybillType]);

                if (empty($package)) {
                    $order = $orderList->firstWhere('tid', $tid);
                    $createArr = [
                        'tids' => '',
                        'waybill_code' => $waybillCode,
                        'waybill_status' => 0,
                        'user_id' => 0,
                        'shop_id' => $order['shop_id'],
                        'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                        "delivery_type" => Package::DELIVERY_TYPE_FIRST,
                        'version' => 3,
                        'total_num' => collect($packItems)->sum('shippedNum'),
                        'send_scene' => Package::SEND_SCENE_MULTI,
                        'send_waybill_type' => $sendWaybillType,
                        'multi_package_main_waybill_code' => $mainWaybillCode,
                    ];
                    if (!$preshipment) {
                        $createArr['status'] = $packageStatus;
                        $createArr['send_at'] = date('Y-m-d H:i:s');
                    }
                    $package = Package::create($createArr);
                } else {
                    $updateArr = [
                        'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                        'send_scene' => Package::SEND_SCENE_MULTI,
                        "delivery_type" => Package::DELIVERY_TYPE_FIRST,
                        'send_waybill_type' => $sendWaybillType,
                        'multi_package_main_waybill_code' => $mainWaybillCode,
                    ];
                    if (!$preshipment) {
                        $updateArr['status'] = $packageStatus;
                        $updateArr['send_at'] = date('Y-m-d H:i:s');
                    }
                    $package->update($updateArr);
                }

                foreach ($packItems as $item) {
                    $orderItem = $orderItemList->firstWhere('oid', $item['oid']);
                    PackageOrder::create([
                        'package_id' => $package->id,
                        'order_id' => $orderItem['order_id'],
                        'order_item_id' => $orderItem['id'],
                        'num' => $item['shippedNum'],
                        'version' => 3,
                        'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                        'tid' => $orderItem['tid'],
                        'oid' => $orderItem['oid'],
                        'num_iid' => $orderItem['num_iid'],
                        'sku_id' => $orderItem['sku_id'],
                        'status' => Package::ORDER_STATUS_DELIVERED,
                    ]);
                }
            }
//            $waybillCode = $successOrder['waybillCodeStr'];
//            if (!in_array($waybillCode, $successWaybillCodeArr)) {
//                Log::info('customDelivery:失败的不保存', [$waybillCode]);
//                // 失败的不保存
//                continue;
//            }


        }

        $orderInfos = Order::batchGetOrderInfo($orderList->toArray());
        $orderGroupArr = collect($orderInfos)->groupBy('shop_id')->toArray();
        foreach ($orderGroupArr as $item) {
            Order::batchSave($item, $item[0]['user_id'], $item[0]['shop_id']);
        }
        Log::info("发货结果", [$successOrders, $failOrders]);
        $successOrdersGroupByShopId = ArrayUtil::array_group_by($successOrders, 'shopId');
        $orderTraceArr = [];
        foreach ($successOrdersGroupByShopId as $shopId => $successOrders) {
            foreach ($successOrders as $successOrder) {
                $tid = $successOrder['tid'];
                $shopId = $successOrder['shopId'];
                foreach ($successOrder['waybillCodes'] as $waybillCode) {
                    $orderTraceArr[] = [
                        'tid' => $tid,
                        'user_id' => $userId,
                        'shop_id' => $shopId,
                        'express_code' => $waybillCode['expressCode'],
                        'express_no' => $waybillCode['waybillCode'],
                        'send_at' => DateTimeUtil::strNow(),
                        'status' => OrderTraceList::STATUS_SHIPPED,
                        'latest_updated_at' => DateTimeUtil::strNow(),
                        'auth_source' => WaybillUtil::currentWaybillAuthSource(),
                    ];
                }
            }
            OrderTraceList::batchSave($orderTraceArr, $userId, $shopId);
        }

        $successCount = sizeof($successOrders);
        $failureCount = sizeof($failOrders);
        return [
            'orderSuccess' => $successCount,
            'orderFail' => $failureCount,
            'orderTotal' => $successCount + $failureCount,
            'failOrders' => $failOrders
        ];
    }

    /**
     * 处理成功和失败的订单
     * @param  array  $deliverResult  配送结果数组，包含所有的配送订单信息
     * @param  array  $successOrders  成功订单的数组引用，用于存放处理成功的订单
     * @param  array  $failOrders  失败订单的数组引用，用于存放处理失败的订单
     * @return array 返回一个数组，表示处理结果
     */
    public
    function handleSuccessFailOrders(
        array $deliverResult,
        $successOrders,
        $failOrders
    ): array {
        $successOrders = array_merge($successOrders, Arr::get($deliverResult, 'successes', []));
        $failOrders = array_merge($failOrders, Arr::get($deliverResult, 'failures', []));
        Log::info("发货结果", [$successOrders, $failOrders]);
        /**
         * 返回的原始数据格式
         * [
         *  ['tid' => $tid,"waybillCodes"=> [
         * "waybillCode"=>"6919675406348588608",
         * "expressCode"=>"xxx",
         *  "packs"=>[
         *      ["oid"=>111,"shippedNum"=>1],
         *  ]
         * ]
         * ]
         */
        $oidArr = collect($successOrders)->pluck('waybillCodes.*.packs.*.oid')->flatten()->filter()->unique()->toArray();
        $orderItemList = \App\Models\Fix\OrderItem::query()->whereIn('oid', $oidArr)->get();
        //先把可能的空的过滤掉，然后把有值的成功的结果的订单号和面单号拼接成字符串
        $successOrders = array_map(function ($success) use ($orderItemList) {
            $items = [];
            foreach ($success["waybillCodes"] as $index => $waybillCode) {
                foreach ($waybillCode['packs'] as $index2 => $pack) {
                    $orderItem = $orderItemList->firstWhere('oid', $pack['oid']);
                    $items[] = [
                        'orderId' => $orderItem['order_id'],
                        'orderItemId' => $orderItem['id'],
                        'num' => $pack['shippedNum'],
                    ];
                }
            }
            return [
                "tid" => (string) $success["tid"],
                "shopId" => $success["shopId"],
                "waybillCodes" => $success["waybillCodes"],
                "waybillCodeStr" => implode(",", array_column($success["waybillCodes"], "waybillCode")),
                "waybillCodeArr" => array_column($success["waybillCodes"], "waybillCode"),
                "packs" => $success["packs"] ?? [],
                "items" => $items,
//                "expressCode" => $success["expressCode"],
            ];
        }, array_filter($successOrders, function ($item) {
            return !empty($item);
        }));
//        Log::info("成功的订单", $successOrders);
        $failOrders = array_map(function ($failure) {
            return [
                "tid" => (string) $failure["tid"],
                "shopId" => $failure["shopId"],
                "waybillCodes" => $failure["waybillCodes"],
                "waybillCodeStr" => implode(",", array_column($failure["waybillCodes"], "waybillCode")),
                "waybillCodeArr" => array_column($failure["waybillCodes"], "waybillCode"),
                "error_msg" => $failure["msg"],
//                "packs" => $failure["packs"],
//                "expressCode" => $failure["expressCode"],
            ];
        }, array_filter($failOrders, function ($item) {
            return !empty($item);
        }));
        // 失败标记
        foreach ($failOrders as $failOrder) {
            redis('cache')->setex(sprintf(RedisKeyConst::ORDER_DELIVERY_FAIL, $failOrder['tid']), 86400 * 3,
                json_encode($failOrder));
        }
        return array($successOrders, $failOrders);
    }

    /**
     * 批量发货 V4
     * @param  array<array{
     *    idStr: string,
     *    waybillCodeStr: string,
     *    express_code: string
     *  }>  $delivers
     * @param $currentShopId
     * @param  bool  $preshipment  预发货标志
     * @return array
     * @throws ErrorCodeException
     * @throws \Throwable
     */
    public
    function batchDeliveryV4(
        array $delivers,
        $currentShopId,
        bool $preshipment = false
    ): array {
        // 逻辑：
        // 通过 idStr 分组
        // 判断订单状态是部分发货，请求拆单发货。
        // 判断大于运单数 > 1 的，进行追加发货
        // 成功的写入已发货包裹数据
        $deliversByIdStrArr = collect($delivers)->groupBy('idStr')->toArray();
        $idStrArr = collect($delivers)->pluck('idStr')->toArray();
        $waybillCodeArr = collect($delivers)->pluck('waybillCodeStr')->toArray();
        $orderList = \App\Models\Fix\Order::query()->with('shop', 'orderItem')->whereIn('id', $idStrArr)->get();
        $packageList = Package::query()->whereIn('waybill_code', $waybillCodeArr)->get();
        $packageOrderList = PackageOrder::query()->whereIn('package_id',
            $packageList->pluck('id')->toArray())->get();
        $operatingShop = Shop::query()->find($currentShopId);
        $failOrders = $successOrders = [];
        $orderSuccessCounter = 0;
        $successArr = [];

        list($successArr, $failOrders) = $this->fullOrderDelivery($delivers, $orderList, $preshipment);
        Log::info("发货结果", [$successArr, $failOrders]);
        $successResponseArr = [];
        foreach ($successArr as $successItem) {
            $thisOrder = $orderList->firstWhere('tid', tidAddA($successItem['tid']));
            $successResponseArr[] = [
                'tid' => $successItem['tid'],
                'shopId' => $thisOrder->shop_id,
                'waybillCodes' => [
                    [
                        'waybillCode' => $successItem['waybillCode'],
                        'expressCode' => $successItem['wpCode'],
                        'packs' => $successItem['packs'],
                    ]
                ],
            ];
        }
        // 处理发货后
        if ($preshipment) {
            $this->handleOrderDeliverEvent($operatingShop, $successResponseArr, $failOrders, $preshipment);
        } else {
            $this->handleOrderMultiPackagesDeliverResponse($successResponseArr, collect());
            $this->handleOrderDeliverEvent($operatingShop, $successResponseArr, $failOrders);
        }

        Log::info('保存 $successArr');
        $successMainWaybillCodeArr = []; // 成功的主单号 tid => waybillCode
        foreach ($successArr as $item) {
            $waybillCode = $item['waybillCode'];
            $package = $packageList->firstWhere('waybill_code', $waybillCode);
            $thisTid = tidAddA($item['tid']);
            $order = $orderList->firstWhere('tid', $thisTid);
            $packageStatus = Package::ORDER_STATUS_DELIVERED;
            $requestCount = collect($delivers)->where('waybillCodeStr', '=', $waybillCode)->count();
            // 这次请求成功的数量
            $successCount = collect($successArr)->where('waybillCode', '=', $waybillCode)->count();
            if ($requestCount != $successCount) { // 请求过来的运单号数量要和成功的运单号数量对上
                // 运单号没有全部成功，设置成部分发货
                Log::info('运单号没有全部成功，设置成部分发货:'.$waybillCode);
                $packageStatus = Package::ORDER_STATUS_PART_DELIVERED;
            }
            if (!empty($package->id)) {
                $thisPackageOrderList = $packageOrderList->where('package_id', $package->id);
                // 未发货的 order_id 数量
                $packageOrderUnshippedCount = $thisPackageOrderList->whereIn('status',
                    [30, null])->pluck('order_id')->unique()->count();
                // 已发货的 order_id 数量
                $packageOrderDeliveredCount = $thisPackageOrderList->where('status',
                    40)->pluck('order_id')->unique()->count();
                // 如果已发货的数量 + 成功的数量 < 未发货的数量，设置成部分发货
                if (($packageOrderDeliveredCount + $successCount) < $packageOrderUnshippedCount) {
                    Log::info('运单号没有全部成功，设置成部分发货2:'.$waybillCode,
                        [$package->id, $packageOrderDeliveredCount, $successCount, $packageOrderUnshippedCount]);
                    $packageStatus = Package::ORDER_STATUS_PART_DELIVERED;
                }
            }

            $orderWaybillCodeCount = collect($delivers)->where('idStr', $order->id)->count();
            $sendWaybillType = Package::SEND_WAYBILL_TYPE_NORMAL; // 默认是正常发货
            $mainWaybillCode = '';
            switch ($item['action']) {
                case 'full':
                case 'split':
                default:
                    if ($orderWaybillCodeCount > 1) { // 一单多包
                        $sendWaybillType = Package::SEND_WAYBILL_TYPE_MULTI_MAIN;
                        $successMainWaybillCodeArr[$thisTid] = $waybillCode; // 保存主单号
                    }
                    break;
                case 'append':
                    $sendWaybillType = Package::SEND_WAYBILL_TYPE_MULTI_SUB;
                    if ($orderWaybillCodeCount > 1) { // 一单多包
                        $mainWaybillCode = $successMainWaybillCodeArr[$thisTid]; // 子单号记录主单号
                    }
                    break;
            }
            if (empty($package)) {
                $createArr = [
                    'tids' => '',
                    'waybill_code' => $waybillCode,
                    'waybill_status' => 0,
                    'user_id' => 0,
                    'shop_id' => $order['shop_id'],
                    'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                    "delivery_type" => $item['deliveryType'] ?? Package::DELIVERY_TYPE_FIRST,
                    'status' => $packageStatus,
                    'version' => 3,
                    'total_num' => 0,
                    'send_scene' => Package::SEND_SCENE_SINGLE,
                    'send_waybill_type' => $sendWaybillType,
                    'multi_package_main_waybill_code' => $mainWaybillCode,
                ];
                if (!$preshipment) {
                    $createArr['send_at'] = date('Y-m-d H:i:s');
                }
                $package = Package::create($createArr);
            } else {

                $updateArr = [
                    'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                    'status' => $packageStatus,
                    'send_scene' => Package::SEND_SCENE_SINGLE,
                    'send_waybill_type' => $sendWaybillType,
                    'multi_package_main_waybill_code' => $mainWaybillCode,
                ];
                if (!$preshipment) {
                    $updateArr['send_at'] = date('Y-m-d H:i:s');
                }
                Package::query()->where('id', $package->id)->update($updateArr);
            }
            // $goodsItem = {orderItemId, num}
            foreach ($item['packs'] as $pack) {
                $orderItem = $order->orderItem->firstWhere('oid', $pack['oid']);
                PackageOrder::create([
                    'package_id' => $package->id,
                    'order_id' => $order['id'],
                    'order_item_id' => $orderItem['id'],
                    'num' => $pack['shippedNum'],
                    'version' => 3,
                    'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                    "delivery_type" => $item['deliveryType'] ?? Package::DELIVERY_TYPE_FIRST,
                    'tid' => $orderItem['tid'],
                    'oid' => $orderItem['oid'],
                    'num_iid' => $orderItem['num_iid'],
                    'sku_id' => $orderItem['sku_id'],
                    'status' => $packageStatus,

                ]);
            }
        }
        Log::info('更新平台订单');
        if (!$preshipment) {
            sleep(1); // 平台状态来不及更新
            // 更新平台订单
            $orderInfos = Order::batchGetOrderInfo($orderList->toArray());
            $orderGroupArr = collect($orderInfos)->groupBy('shop_id')->toArray();
            foreach ($orderGroupArr as $item) {
                Order::batchSave($item, $item[0]['user_id'], $item[0]['shop_id']);
            }
        }

        $orderSuccessCounter = collect($successArr)->whereIn('action', ['split', 'full'])->count();
//        $orderFail = count($failOrders);
        $orderFail = collect($failOrders)->count(); // ->whereIn('action', ['split', 'full'])
        $orderTotal = $orderSuccessCounter + $orderFail;
        if ($preshipment) {
            //如果是预发货，要处理预发货的处理逻辑，
            //把运单号都提取出来

            $this->orderPreshipmentService->handlePreshipment($successResponseArr);

        } else {
            $waybillCodes = collect($successResponseArr)->pluck('waybillCodes')->collapse()->pluck('waybillCode')->toArray();
            $this->orderPreshipmentService->generateOrderTrace($waybillCodes);
        }
        return [
            'orderTotal' => $orderTotal,
            'orderSuccess' => $orderSuccessCounter,
            'orderFail' => $orderFail,
            'failOrders' => $failOrders,
        ];

    }

    /**
     * 整单发货
     * @param  array{
     *     0: array{
     *         idStr: string,
     *         waybillCodeStr: string,
     *         express_code: string
     * }
     * }  $delivers
     * @param  Collection  $orderList
     * @param  bool  $preshipment
     * @return array
     * @throws ErrorCodeException
     */
    public
    function fullOrderDelivery(
        array $delivers,
        $orderList,
        bool $preshipment = false
    ): array {
        Log::info('开始整单发货', $delivers);

        $failOrders = $successArr = [];
        $fullDeliveryList = []; // 正常整单发货列表
        $appendDeliveryList = []; // 追加发货列表
        $splitDeliveryList = []; // 拆分发货列表
        // 先按照 idStr 分组，区分出同一个订单多包裹,要考虑合单的情况
//        $deliversByIdStrArr = [];
//        foreach ($delivers as $deliver) {
//            foreach ($deliver['orderIdArr'] as $orderId) {
//                if (isset($deliversByIdStrArr[$orderId])) {
//                    $deliversByIdStrArr[$orderId] = array_merge($deliversByIdStrArr[$orderId], [$deliver]);
//                } else {
//                    $deliversByIdStrArr[$orderId] = [$deliver];
//                }
//            }
//        }
        $deliversByIdStrArr = collect($delivers)->groupBy('idStr')->toArray();
        // 构造发货请求数据列表
        // 根据订单号分组
        foreach ($deliversByIdStrArr as $orderIdStr => $deliversByIdStr) {
            $order = $orderList->where('id', $orderIdStr)->first();
            //如果是淘宝的话，不用追加，直接发货，不区分，剔除售后，已发货的
            foreach ($deliversByIdStr as $index => $item) {
                $packs = [];
                foreach ($order->orderItem as $orderItem) {
//                    if($orderItem['refund_status']==Order::REFUND_STATUS_YES){
//                        //只保留有售后的
//                        Log::info('订单有售后，不发货', ['orderId' => $order['id'], 'orderItemOid' => $orderItem->oid]);
//                        continue;
//                    }
                    $send_remain_num = $orderItem['send_remain_num'];
                    if ($send_remain_num <= 0) {
                        Log::info('订单数量为0，不发货',
                            ['send_remain_num' => $send_remain_num, 'orderItems' => $orderItem]);
                        continue;
                    }
                    $packs[] = [
                        'oid' => $orderItem->oid,
                        'shippedNum' => $send_remain_num,
                        //重复这个目的是为了兼容追加发货时候的问题
                        'num' => $send_remain_num,
                    ];
                }

                // 首次发货
                if ($index == 0) {
                    // 拆单发货 部分发货的订单 和 有售后的订单
                    if ($order->order_status == Order::ORDER_STATUS_PART_DELIVERED
                        || $order->refund_status > Order::REFUND_STATUS_NO
                        || $order->pre_shipment_status > Order::PRE_SHIPMENT_STATUS_NO) {
                        $orderItems = $order->orderItem
                            ->whereIn('status',
                                [OrderItem::ORDER_STATUS_PART_DELIVERED, OrderItem::ORDER_STATUS_PAYMENT])
                            ->where('refund_status', OrderItem::REFUND_STATUS_NO)
                            ->all();
                        $deliversRequest = [];
                        $deliversRequest['tid'] = $order['tid'];
                        $deliversRequest['shopId'] = $order['shop_id'];
                        $goods = $packs = [];
                        // 未退款的非赠品子订单数量
                        $noRefundOrderItemCount = $order->orderItem
                            ->where('goods_type', OrderItem::GOODS_TYPE_NORMAL)
                            ->where('refund_status', OrderItem::REFUND_STATUS_NO)
                            ->count();
                        if ($noRefundOrderItemCount == 0) {
                            $orderTmp = clone $order;
                            $orderTmp->error_code = 1;
                            $orderTmp->error_msg = '没有可发的商品（非赠品）';
                            $orderTmp->waybillCodeStr = $item['waybillCodeStr'];
                            $orderTmp->wpCode = $item['express_code'];
                            $failOrders[] = $orderTmp;
                            continue;
                        }
                        foreach ($orderItems as $orderItem) {
                            if ($orderItem['refund_status'] > Order::REFUND_STATUS_NO) { // 是否忽略退款订单
                                //只保留有售后的
                                Log::info('订单有售后，不发货',
                                    ['orderId' => $order['id'], 'orderItemOid' => $orderItem->oid]);
                                continue;
                            }
                            if (in_array($order->pre_shipment_status,
                                [Order::PRE_SHIPMENT_STATUS_YES, Order::PRE_SHIPMENT_STATUS_PAUSE])) {
                                // 预发货订单取剩余数量-预发货数量
//                                    $shippedNum = $orderItem['send_remain_num'] - $orderItem['pre_send_num'];
                                $shippedNum = $orderItem['goods_num'] - $orderItem['send_num'] - $orderItem['pre_send_num'];
                            } else {
                                $shippedNum = $orderItem['goods_num'] - $orderItem['send_num'];
                            }
                            if ($shippedNum <= 0) {
                                Log::info('订单数量为0，不发货',
                                    ['shippedNum' => $shippedNum, 'orderItems' => $orderItem]);
                                continue;
                            }
                            $goods[] = [
                                'oid' => $orderItem['oid'],
                                'shippedNum' => $shippedNum,
                                'num_iid' => $orderItem['num_iid'],
                                'sku_id' => $orderItem['sku_id'],
                                'sku_uuid' => $orderItem['sku_uuid'],
                            ];
                            $packs[] = [
                                'oid' => $orderItem->oid,
                                'shippedNum' => $shippedNum
                            ];
                        }
                        $deliversRequest['packs'][] = [
                            'waybillCode' => $item['waybillCodeStr'],
                            'expressCode' => $item['express_code'],
                            'goods' => $goods,
                        ];
                        // 拆单发货
                        $splitDeliveryList[] = [
                            'shopId' => $order['shop_id'],
                            'shop' => $order->shop,
                            'order' => clone $order,
                            'request' => $deliversRequest,
                            'tid' => $order->tid,
                            'expressCode' => $item['express_code'],
                            'expressNo' => $item['waybillCodeStr'],
                            'packs' => $packs,
                        ];

                    } else {
                        // 整单发货
                        $fullDeliveryList[] = [
                            'userId' => $order['user_id'],
                            'shopId' => $order['shop_id'],
                            'shop' => $order->shop,
                            'order' => clone $order,
                            'tid' => $order->tid,
                            'expressCode' => $item['express_code'],
                            'expressNo' => $item['waybillCodeStr'],
                            'orderItemOId' => [],
                            'packs' => $packs,
                        ];
                    }
                } else { // 一单多包，追加发货
                    $appendDeliveryList[] = [
                        'shopId' => $order['shop_id'],
                        'shop' => $order->shop,
                        'order' => clone $order,
                        'tid' => $order->tid,
                        'expressCode' => $item['express_code'],
                        'expressNo' => $item['waybillCodeStr'],
                        'packs' => $packs,
                    ];
                }
            }
        }
        Log::debug('整单发货', [$fullDeliveryList]);
        // 批量整单发货
        // 订单可能包含多个店铺
        $deliveryOrdersListGroup = collect($fullDeliveryList)->groupBy('shopId')->toArray();
        foreach ($deliveryOrdersListGroup as $deliveryOrderData) {
            $orderService = OrderServiceManager::create();
            $first = array_first($deliveryOrderData);
            $orderService->setUserId($first['userId']);
            $orderService->setShop($first['shop']);
            $orderDeliveryRequestList = ObjectUtil::batchMapToObject($deliveryOrderData,
                OrderDeliveryRequest::class);
            if (!$preshipment) {
                $responseList = $orderService->batchDeliveryOrders($orderDeliveryRequestList);
            } else {
                $responseList = $orderService->mockBatchDeliveryOrdersSuccess($orderDeliveryRequestList);
            }
            foreach ($responseList as $response) {
                /** @var OrderDeliveryRequest $orderDeliveryRequest */
                $orderDeliveryRequest = $response->getRequest();
                if (!$response->isSuccess()) {
                    $order = clone $orderDeliveryRequest->order;
                    $order->error_code = $response->getCode();
                    $order->error_msg = $response->getMessage();
                    if (!empty($orderDeliveryRequest->waybills)) {
                        //像淘宝这种，特殊处理，因为面单号是在waybills里面的
                        foreach ($orderDeliveryRequest->waybills as $waybill) {
                            $order->waybillCodeStr = $waybill['expressNo'];
                            $order->wpCode = $waybill['expressCode'];
                            $failOrders[] = $order;
                        }
                    } else {
                        $order->waybillCodeStr = $orderDeliveryRequest->expressNo;
                        $order->wpCode = $orderDeliveryRequest->expressCode;
                        $failOrders[] = $order;
                    }
                    continue;
                }
                if (!empty($orderDeliveryRequest->waybills)) {
                    foreach ($orderDeliveryRequest->waybills as $waybill) {
                        $successArr[] = [
                            'action' => 'full', // 整单发货
                            'waybillCode' => $waybill['expressNo'],
                            'wpCode' => $waybill['expressCode'],
                            'tid' => $orderDeliveryRequest->tid,
                            'packs' => $waybill['packs'],
                        ];
                    }
                } else {
                    $fullDelivery = collect($fullDeliveryList)->where('expressNo', $orderDeliveryRequest->expressNo)
                        ->where('tid', $orderDeliveryRequest->tid)->first();
                    $successArr[] = [
                        'action' => 'full', // 整单发货
                        'waybillCode' => $orderDeliveryRequest->expressNo,
                        'wpCode' => $orderDeliveryRequest->expressCode,
                        'tid' => $orderDeliveryRequest->tid,
                        'packs' => $fullDelivery['packs'],
                    ];
                }

            }
//            // 修改成已发货
//            Order::query()->where('tid', $orderDeliveryRequest->tid)->update(['order_status' => Order::ORDER_STATUS_DELIVERED]);
        }
        Log::debug('拆单发货', [$splitDeliveryList]);

        // 批量拆单发货
        $splitDeliveryListGroupByShopId = collect($splitDeliveryList)->groupBy('shopId')->toArray();
        foreach ($splitDeliveryListGroupByShopId as $list) {
            $firstItem = $list[0];
            // 拆单发货
            $orderService = OrderServiceManager::create();
            $orderService->setShop($firstItem['shop']);
            $requestList = collect($list)->pluck('request')->toArray();
            if (!$preshipment) {
                $deliverResult = $orderService->orderMultiPackagesDelivery($firstItem['shop']['id'], $requestList);
            } else {
                $deliverResult = $orderService->mockOrderMultiPackagesDeliverySuccess($firstItem['shop']['id'],
                    $requestList);
            }
            list($successOrdersTmp, $failOrdersTmp) = $this->handleSuccessFailOrders($deliverResult, [], []);
            foreach ($successOrdersTmp as $successOrder) {
                foreach ($successOrder['waybillCodes'] as $waybillCodeItem) {
                    $successArr[] = [
                        'action' => 'split', // 拆分发货
                        'waybillCode' => $waybillCodeItem['waybillCode'],
                        'wpCode' => $waybillCodeItem['expressCode'],
                        'tid' => $successOrder['tid'],
                        'packs' => $waybillCodeItem['packs'],
                    ];
                }
            }
            foreach ($failOrdersTmp as $failOrder) {
                foreach ($failOrder['waybillCodes'] as $waybillCodeItem) {
                    $orderTemp = $orderList->where('tid', $failOrder['tid'])->first();
                    $order = clone $orderTemp;
                    $order->error_code = 1;
                    $order->error_msg = $failOrder['error_msg'];
                    if (!empty($waybillCodeItem['waybillCode'])) {
                        $order->waybillCodeStr = $waybillCodeItem['waybillCode'];
                        $order->wpCode = $waybillCodeItem['expressCode'];
                    } else {
                        if (!empty($waybillCodeItem['expressNo'])) {
                            $order->waybillCodeStr = $waybillCodeItem['expressNo'];
                            $order->wpCode = $waybillCodeItem['expressCode'] ?? '';
                        }
                    }
                    $failOrders[] = $order;
                }

            }
//            $this->handleOrderMultiPackagesDeliverResponse($successOrders2, collect());
        }

        Log::debug('追加发货', [$appendDeliveryList]);
        // 循环追加发货（因为追加情况比较少，所以直接循环请求）
        $appendDeliveryListGroupByShopId = collect($appendDeliveryList)->groupBy('shopId')->toArray();
        foreach ($appendDeliveryListGroupByShopId as $list) {

            $firstItem = $list[0];
            $orderService = OrderServiceManager::create();
            $orderService->setShop($firstItem['shop']);
            foreach ($list as $item) {
                try {
                    if (!$preshipment) {
                        // 不请求接口
//                        $newMemo =  $item['expressNo'] . ';';
//                        $this->setOrderSellerMemo($item['order'], $newMemo, $orderService, $item['order']['tid']);
//                        $orderService->orderAppendPackages($item['tid'], $item['expressCode'], $item['expressNo'], $item['packs']);
                    }
                    $successArr[] = [
                        'action' => 'append', // 追加发货
                        'waybillCode' => $item['expressNo'],
                        'wpCode' => $item['expressCode'],
                        'tid' => $item['tid'],
                        "deliveryType" => Package::DELIVERY_TYPE_REPLACEMENT,
                        'packs' => $item['packs'],
                    ];
                } catch (\Exception $exception) {
                    Log::error('追加发货失败', [$exception->getMessage(), $exception->getTraceAsString()]);
                    $order = clone $item['order'] ?? [];
                    $order->error_code = $exception->getCode();
                    $order->error_msg = $exception->getMessage();
                    $order->waybillCodeStr = $item['expressNo'];
                    $order->wpCode = $item['expressCode'];
                    $failOrders[] = $order;
                }
            }
        }

        return array($successArr, $failOrders);
    }

    /**
     * @param $operatingShop
     * @param  array  $list
     * @param $packages
     * @param $orderList
     * @param $orderItemList
     * @param  bool  $preshipment
     * @param  array  $consignStatusArr
     * @return array
     * @throws ErrorCodeException
     */
    public
    function splitOrderDelivery(
        $operatingShop,
        array $list,
        $packages,
        $orderList,
        $orderItemList,
        bool $preshipment = false,
        $consignStatusArr = []
    ): array {
        Log::info('进入拆单发货', ["list" => $list]);
        $successOrders = [];
        $failOrders = [];
        // 一个订单可能有多个运单号
        $groupByOrderIdList = collect($list)->groupBy('orderId')->toArray();
        $splitDeliverList = [];
        $appendDeliverList = [];
        $emptyOrderList = [];
        foreach ($groupByOrderIdList as $groupList) {
            $orderItemSurplus = []; // 子订单剩余可发货数量
            foreach ($groupList as $value) {
                // 有可能一个运单号有不同的主订单进来，所以要根据主订单拆开
                $groupByOrderIdItems = collect($value['items'])->groupBy('orderId')->toArray();
                foreach ($groupByOrderIdItems as $orderId => $items) {
                    $order = $orderList->firstWhere('id', $orderId);
                    $deliversRequest = [];
                    $deliversRequest['tid'] = $order['tid'];
                    $deliversRequest['shopId'] = $order['shop_id'];
                    $goods = [];
                    foreach ($items as $item) {
                        $orderItem = $orderItemList->firstWhere('id', $item['orderItemId']);
                        $goods[] = [
                            'oid' => $orderItem['oid'],
                            'shippedNum' => $item['num'],
                            'num_iid' => $orderItem['num_iid'],
                            'sku_id' => $orderItem['sku_id'],
                            'sku_uuid' => array_get($orderItem, 'sku_uuid'),
                        ];
                    }
                    $deliversRequest['packs'][] = [
                        'waybillCode' => $value['waybillCode'],
                        'expressCode' => $value['wpCode'],
                        'goods' => $goods,
                        'items' => $items,
                    ];

                    $isSplit = true;
//                        // 判断可发商品数量，发货数量 <= 可发商品数量，走拆单，否则走追加
//                        foreach ($items as $item) {
//                            $orderItem = $orderItemList->firstWhere('id', $item['orderItemId']);
//                            if (!isset($orderItemSurplus[$item['orderItemId']])) {
//                                $send_remain_num = $orderItem['goods_num'] - $orderItem['send_num'];
//                                $orderItemSurplus[$item['orderItemId']] = $send_remain_num;
//                            }
//                            if ($item['num'] > $orderItemSurplus[$item['orderItemId']]) {
//                                // 发货数量大于剩余数量，追加发货
//                                $isSplit = false;
//                                break;
//                            }
//                            $orderItemSurplus[$item['orderItemId']] = $orderItemSurplus[$item['orderItemId']] - $item['num'];
//                        }

                    $orderItemIdArr = collect($items)->pluck('orderItemId')->unique()->toArray();
                    // 这里计算子订单状态，有的平台需要手动指定子订单状态
                    $orderItemStatusArr = [];
                    foreach ($orderItemIdArr as $orderItemId) {
                        $orderItem = $orderItemList->firstWhere('id', $orderItemId);
                        $consignStatus = collect($consignStatusArr)->where('orderItemId', $orderItemId)->first();
                        $isPartConsign = $consignStatus['isPartConsign'] ?? 0;
                        $orderItemStatusArr[] = [
                            'oid' => $orderItem['oid'],
                            'status' => $isPartConsign ? Order::ORDER_STATUS_PART_DELIVERED : Order::ORDER_STATUS_DELIVERED,
                        ];
                    }
                    $deliversRequest['orderItemStatusArr'] = $orderItemStatusArr;

                    if ($isSplit) {
                        // 拆单发货列表
                        $splitDeliverList[] = $deliversRequest;
                    } else {
                        // 追加发货列表
                        $appendDeliverList[] = $deliversRequest;
                    }
                }
            }
        }
        // 通过对 $splitDeliverList groupBy tid,然后合并里面的 packs
        $splitDeliverListTemp = [];
        collect($splitDeliverList)->groupBy('tid')->each(function ($valueArr, $tid) use (&$splitDeliverListTemp) {
            foreach ($valueArr as $index => $value) {
                if (empty($splitDeliverListTemp[$tid])) {
                    $splitDeliverListTemp[$tid] = $value;
                } else {
                    $splitDeliverListTemp[$tid]['packs'] = array_merge($splitDeliverListTemp[$tid]['packs'],
                        $value['packs']);
                }
            }
        });
        $splitDeliverList = $splitDeliverListTemp;

        Log::info('拆单发货列表', $splitDeliverList);
        Log::info('追加发货列表', $appendDeliverList);

        // 批量拆单发货
        $shopIds = collect($splitDeliverList)->pluck('shopId')->toArray();
        $shopList = Shop::query()->whereIn('id', $shopIds)->get();
        $deliversRequestByShopId = collect($splitDeliverList)->groupBy('shopId')->toArray();
        foreach ($deliversRequestByShopId as $shopId => $deliversRequestItems) {
            $shop = $shopList->firstWhere('id', $shopId);
            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);
            if (!$preshipment) {
                $deliverResult = $orderService->orderMultiPackagesDelivery($shopId, $deliversRequestItems);
            } else {
                $deliverResult = $orderService->mockOrderMultiPackagesDeliverySuccess($shopId,
                    $deliversRequestItems);
            }
            list($successOrders, $failOrders) = $this->handleSuccessFailOrders($deliverResult, $successOrders,
                $failOrders);

        }
        // 批量追加发货（循环）
        foreach ($appendDeliverList as $appendDeliver) {
            $order = $orderList->firstWhere('tid', $appendDeliver['tid']);
            Log::debug('$appendDeliver', [$appendDeliver]);

            $orderService = OrderServiceManager::create();
            $orderService->setShop($order->shop);

            foreach ($appendDeliver['packs'] as $pack) {
                //预发货不支持追加发货
                if ($preshipment) {

//                    $failOrders[] = [
//                        "tid" => $order['tid'],
//                        "shopId" => $order['shop_id'],
//                        "waybillCodes" => ['waybillCode' => $pack['waybillCode']],
//                        "waybillCodeStr" => $pack['waybillCode'],
//                        "waybillCodeArr" => [$pack['waybillCode']],
//                        'error_msg' => ErrorConst::ERROR_MSG_PRESHIPMENT_UNSUPPORT_APPEND];
                    continue;
                }
                try {
                    $goods = array_map(function ($item) {
                        return [
                            'oid' => $item['oid'],
                            'num' => $item['shippedNum']
                        ];
                    }, $pack['goods']);
                    $orderService->orderAppendPackages($order['tid'], $pack['expressCode'], $pack['waybillCode'],
                        $goods);
                    $successOrders[] = [
                        "tid" => $order['tid'],
                        "shopId" => $order['shop_id'],
                        "deliveryType" => Package::DELIVERY_TYPE_REPLACEMENT,
                        "waybillCodes" => ['waybillCode' => $pack['waybillCode']],
                        "waybillCodeStr" => $pack['waybillCode'],
                        "waybillCodeArr" => [$pack['waybillCode']],
                        "items" => $pack['items'],
                    ];
                } catch (\Exception $exception) {
                    Log::error('批量追加发货失败', [$exception->getMessage(), $exception->getTrace()]);
                    $failOrders[] = [
                        "tid" => $order['tid'],
                        "shopId" => $order['shop_id'],
                        "waybillCodes" => ['waybillCode' => $pack['waybillCode']],
                        "waybillCodeStr" => $pack['waybillCode'],
                        "waybillCodeArr" => [$pack['waybillCode']],
                        'error_msg' => $exception->getMessage(),
                    ];
                }
            }


        }
        if (!$preshipment) {
            $this->handleOrderMultiPackagesDeliverResponse($successOrders, $packages);
            if (!empty($emptyOrderList)) {
                foreach ($successOrders as $index => $successOrder) {
                    Log::info('$emptyOrderList', [$emptyOrderList, $successOrder]);
                    // 添加空包裹
                    collect($emptyOrderList)->where('tid', tidAddA($successOrder['tid']))->each(function (
                        $emptyOrder
                    ) use (&$successOrders, $index) {
                        $successOrders[$index]['waybillCodeArr'][] = $emptyOrder['waybillCode'];
                        $successOrders[$index]['waybillCodeStr'] .= ','.$emptyOrder['waybillCode'];
                        $successOrders[$index]['waybillCodes'][] = [
                            'waybillCode' => $emptyOrder['waybillCode'],
                            'parentWaybillCode' => $emptyOrder['parentWaybillCode'],
                            'expressCode' => $emptyOrder['wpCode'],
                            'isEmptyPack' => 1,
                            'packs' => [],
                        ];
                    });
                }
            }
            $this->handleOrderDeliverEvent($operatingShop, $successOrders, $failOrders);
        } else {

            $this->orderPreshipmentService->handlePreshipment($successOrders);
        }
        $successWaybillCodeArr = collect($successOrders)->pluck('waybillCodes.*.waybillCode')->flatten()->filter()->unique()->toArray();


        return [$successWaybillCodeArr, $successOrders, $failOrders];
    }

    /**
     * @param $package
     * @param  int  $deliveryType
     * @return void
     */
    public
    function writePackage(
        $package,
        int $deliveryType
    ): void {
        $package->update([
            'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
            'delivery_type' => $deliveryType,
            'status' => Package::ORDER_STATUS_DELIVERED,
            'send_at' => Carbon::now(),
        ]);
        foreach ($package['printPackageOrders'] as $printPackageOrder) {
            PackageOrder::create([
                'package_id' => $package->id,
                'order_id' => $printPackageOrder['order_id'],
                'order_item_id' => $printPackageOrder['order_item_id'],
                'num' => $printPackageOrder['num'],
                'version' => 3,
                'source_type' => Package::SOURCE_TYPE_INTERNAL_DELIVERY,
                "delivery_type" => $deliveryType,
                'tid' => $printPackageOrder['tid'],
                'oid' => $printPackageOrder['oid'],
                'num_iid' => $printPackageOrder['num_iid'],
                'sku_id' => $printPackageOrder['sku_id'],
                'status' => Package::ORDER_STATUS_DELIVERED,
            ]);
        }
    }

    /**
     * 处理发货事件
     * @param $operatingShop
     * @param $successOrders
     * @param $failOrders
     * @param $preshipment
     * @return void
     */
    private
    function handleOrderDeliverEvent(
        $operatingShop,
        $successOrders,
        $failOrders,
        $preshipment = false
    ) {
        Log::info("handleOrderDeliverEvent", [$successOrders, $failOrders]);

//        $successOrders = [
//                "tid" => (string)$success["tid"],
//                "shopId" => $success["shopId"],
//                "waybillCodes" => $success["waybillCodes"],
//                "waybillCodeStr" => implode(",", array_column($success["waybillCodes"], "waybillCode")),
//                "waybillCodeArr" => array_column($success["waybillCodes"], "waybillCode"),
//                "packs" => $success["packs"] ?? [],
//        ];
        $tidArr = array_column($successOrders, 'tid');
        $tidArr2 = array_column($failOrders, 'tid');
        $tidArr = array_merge($tidArr, $tidArr2);
        $tidArr = batchAddA($tidArr);
        $orderList = \App\Models\Fix\Order::query()->with('shop.user')->whereIn('tid', $tidArr)->get();
        foreach ($successOrders as $successes) {
            $tid = tidAddA($successes['tid']);
            $order = $orderList->firstWhere('tid', $tid);
            $waybillCodes = $successes['waybillCodes'];
            // 触发发货事件
            $deliveryList = [];
            $tidArr = [$tid];
            foreach ($waybillCodes as $item) {
                $deliveryList[] = [
                    'id' => $order->id,
                    'tid' => $order->tid,
                    'waybill_code' => $item['waybillCode'],
                    'wp_code' => $item['expressCode'],
                    'packs' => $item['packs'],
                ];
            }

            if ($preshipment) {
                event((new OrderPreshipmentJoinEvent($operatingShop->user, $operatingShop, time(),
                    $deliveryList))->setClientInfoByRequest(\request()));
            } else {
                event((new OrderDeliveryEvent($operatingShop->user, $operatingShop, time(), $deliveryList,
                    $tidArr))->setClientInfoByRequest(\request()));
            }
        }
//        $failOrders =  [
//            "tid" => (string)$failure["tid"],
//            "shopId" => $failure["shopId"],
//            "waybillCodes" => $failure["waybillCodes"],
//            "waybillCodeStr" => implode(",", array_column($failure["waybillCodes"], "waybillCode")),
//            "waybillCodeArr" => array_column($failure["waybillCodes"], "waybillCode"),
//            "error_msg" => $failure["msg"],
        foreach ($failOrders as $fail) {
            $deliveryList = [];
            if (is_object($fail)) {
                $order = $fail;
                $deliveryList[] = [
                    'id' => $order->id,
                    'tid' => $order->tid,
                    'waybill_code' => $order->waybillCodeStr,
                    'wp_code' => $order->wpCode,
//                    'packs' => $item['packs'],
                ];
                $errorMsg = $order->error_msg;

            } else {
                $tid = $fail['tid'];
                $tid = tidAddA($tid);
                $order = $orderList->firstWhere('tid', $tid);
                $waybillCodes = $fail['waybillCodes'];
                $tidArr = [$tid];
                $errorMsg = $fail['error_msg'];
                foreach ($waybillCodes as $item) {
                    $deliveryList[] = [
                        'id' => $order->id,
                        'tid' => $tid,
                        'waybill_code' => $item['waybillCode'],
                        'wp_code' => $item['expressCode'],
//                        'packs' => $item['packs'],
                    ];
                }
            }
            if ($preshipment) {
                event((new OrderPreshipmentFailEvent($operatingShop->user, $operatingShop, time(),
                    $deliveryList))->setClientInfoByRequest(\request()));
            } else {
                event((new OrderDeliveryFailEvent($operatingShop->user, $operatingShop, time(), $deliveryList,
                    $tidArr,
                    $errorMsg))->setClientInfoByRequest(\request()));
            }


        }
    }

    public
    function scanDeliverV2(
        $shopId,
        array $shopIdList,
        string $waybillCode
    ) {
        // 待发货订单 含退款商品、已发货订单、交易关闭、拆单打印 的订单不支持使用扫描发货

        $package = Package::query()->with('printPackageOrders')->whereIn('shop_id',
            $shopIdList)->where('waybill_code',
            $waybillCode)->first();
        if (empty($package)) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：'.$waybillCode.' 未找到');
        }
        if (!is_null($package->recycled_at)) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null, '单号：'.$waybillCode.' 已经回收');
        }
        if ($package->is_split) {
            throw_error_code_exception(StatusCode::PARAMS_ILLEGAl, null,
                '单号：'.$waybillCode.' 是拆单，不支持扫描发货');
        }

        $orderIdArr = $package->printPackageOrders->pluck('order_id')->toArray();
        $orderList = Order::query()->whereIn('id', $orderIdArr)->get();

        $delivers = [];
        // 有可能有合单，按订单拆分
        $package->printPackageOrders->each(function ($order) use ($package, $orderList, &$delivers) {
            $first = collect($delivers)->where('idStr', $order->order_id)->first();
            if (!empty($first)) {
                // 订单已经有了 跳过
                return;
            }
            $order = $orderList->where('id', $order->order_id)->first();
            if ($order->order_status != Order::ORDER_STATUS_PAYMENT) {
                throw new \Exception('订单状态不是待发货，不支持扫描发货');
            }
            if ($order->refund_status != Order::REFUND_STATUS_NO) {
                throw new \Exception('订单有售后，不支持扫描发货');
            }
            $delivers[] = [
                'idStr' => $order->id,
                'waybillCodeStr' => $package->waybill_code,
                'express_code' => $package->wp_code,
            ];
        });
        //idStr


        $ret = $this->batchDeliveryV4($delivers, $shopId);
        return $ret;
    }

    public
    function redeliveryV2(
        $shop_id,
        array $data
    ) {

    }

}
