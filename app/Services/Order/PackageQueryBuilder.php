<?php

namespace App\Services\Order;

use App\Constants\OrderIndexTabConst;
use App\Constants\PlatformConst;
use App\Http\Controllers\Order\PackageSearchRequest;
use App\Models\Fix\Order;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\ShopBind;
use App\Services\Order\Request\OrderSearchRequest;
use App\Utils\OrderUtil;
use App\Utils\StrUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PackageQueryBuilder
{

    public function buildDeliveryStatisticsQuery(PackageSearchRequest $packageSearchRequest): Builder
    {
        $ownerIdList = ShopBind::getValidIdentifierByRelation($packageSearchRequest->authShopId, $packageSearchRequest->ownerIdList, 1);
        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $shopIds = $shops->pluck('id')->toArray();
        $query = PackageOrder::query()->with(['orderItem','orderItem.customGoods','orderItem.customGoodsSkus', 'fixOrder'])->join('packages', "packages.id", "=", "package_orders.package_id")
            ->where('packages.source_type', '=', 1)
            ->where('package_orders.source_type', '=', 1)
            ->whereIn('packages.shop_id', $shopIds);
//            ->whereIn('packages.send_waybill_type', [Package::SEND_WAYBILL_TYPE_NORMAL, Package::SEND_WAYBILL_TYPE_MULTI_MAIN]);
        $this->buildByTime($query, $packageSearchRequest);
        $this->buildByPackageTypes($query, $packageSearchRequest);
        $this->buildWpCodes($query, $packageSearchRequest);
//        $this->buildSelectItem($query, $packageSearchRequest);

        return $query;

    }

    /**
     * 构建查询类型
     * @param Builder $query
     * @param PackageSearchRequest $packageSearchRequest
     * @return void
     */
    public function buildByPackageTypes(Builder $query, PackageSearchRequest $packageSearchRequest): void{

        if(!empty($packageSearchRequest->packageTypes)){
            $query->whereIn('packages.delivery_type', $packageSearchRequest->packageTypes);
        }


    }

    public function buildWpCodes(Builder $query, PackageSearchRequest $packageSearchRequest): void{
        if(!empty($packageSearchRequest->wpCodes)){
            $wpCodes= explode(',', $packageSearchRequest->wpCodes);
            $query->whereIn('packages.wp_code', $wpCodes);
        }
    }

    private function buildByTime(Builder $query, PackageSearchRequest $packageSearchRequest): void
    {
        $beginAt = $packageSearchRequest->beginAt;
        $endAt = $packageSearchRequest->endAt;
        $timeField = $packageSearchRequest->timeField;
        if ($timeField == "send_at" && $beginAt && $endAt) {
            $query->where('packages.' . $timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt)));
            $query->where('packages.' . $timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt)));
        }
    }

//    private function buildSelectItem(Builder $query, PackageSearchRequest $packageSearchRequest)
//    {
//        $search = $packageSearchRequest->search;
//        if ($search) {
//           $orderSn=OrderUtil::appendOrderSuffix($search);
//           $query->where('package_orders.tid', $orderSn);
//        }
//    }

    private function buildByOrderSelectItem(Builder $query, PackageSearchRequest $orderSearchRequest, $shops)
    {
//        if(empty($orderSearchRequest->search)){
//            return;
//        }
        $goods_include = $orderSearchRequest->goodsInclude;
        $sku_include = $orderSearchRequest->skuInclude;
        $selectItem = $orderSearchRequest->selectItem;

        $search = $orderSearchRequest->search;
        //规格名称，颜色，尺寸搜索
//        if ($orderSearchRequest->selectItem == 8) {
//            if ($orderSearchRequest->goodsColor || $orderSearchRequest->goodsSize) {
//                $search = $orderSearchRequest->goodsColor . "," . $orderSearchRequest->goodsSize;
//            }
//        }

        $includeOrNot = $orderSearchRequest->includeOrNot;
        $isInclude =
        $accurateWhere = [];

        $orderService = OrderServiceManager::create(config('app.platform'));
//        $shops = \App\Models\Shop::query()->whereIn('id', $orderSearchRequest->shopIds)->get();

        if ($selectItem > 0 && ($search || ($goods_include || $sku_include))) {
            switch ($selectItem) {
                case '1':
                    if (PlatformConst::DY == config('app.platform')) {
                        $tid = strpos($search, 'A') ? $search : ($search . 'A');
                    } else {
                        $tid = $search;
                    }
                    $accurateWhere[] = [
                        'func' => 'where',
                        'args' => ['orders.tid', $tid]
                    ];
                    break;
                case '2':
                    $query->where('express_no', $search);
                    break;
                case '3':
                    $orderIdArr = [];
                    foreach ($shops as $shop) {
                        $orderService->setShop($shop);
                        $orderService->setAccessToken($shop->access_token);
                        $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_name', $search));
                    }
                    //if (!empty($tidArr)) {
                    $query->whereIn('orders.id', $orderIdArr);
                    //}
                    break;
                case '4':
                    $orderIdArr = [];
                    foreach ($shops as $shop) {
                        $orderService->setShop($shop);
                        $orderService->setAccessToken($shop->access_token);
                        $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_phone', $search));
                    }
                    //if (!empty($tidArr)) {
                    $query->whereIn('orders.id', $orderIdArr);
                    //}
                    break;
                case '5': // 商品包含
                    if (!empty($goods_include)) {
//                        $isInclude = true;
                        $accurateWhere = Order::getAccurateWhereByGoods($goods_include, $orderSearchRequest->shopIds, $accurateWhere, true);
                    }
                    if (!empty($sku_include)) {
//                        $isInclude = true;
                        $accurateWhere = Order::getAccurateWhereByGoodsSku($sku_include, $orderSearchRequest->shopIds, $accurateWhere, true);
                    }
                    break;
                case '6': // 商品不包含
                    if (!empty($goods_include)) {
//                        $isInclude = false;
                        $accurateWhere = Order::getAccurateWhereByGoods($goods_include, $orderSearchRequest->shopIds, $accurateWhere, false);
                    }
                    if (!empty($sku_include)) {
//                        $isInclude = false;
                        $accurateWhere = Order::getAccurateWhereByGoodsSku($sku_include, $orderSearchRequest->shopIds, $accurateWhere, false);
                    }
                    break;
                case '7':
                    $query->whereHas('orderItem', function ($query) use ($search) {
                        $query->where('outer_sku_iid', $search);
                    });
                    break;
                case '8':
                    if ($orderSearchRequest->goodsColor || $orderSearchRequest->goodsSize) {
                        $search = $orderSearchRequest->goodsColor . "," . $orderSearchRequest->goodsSize;
                    }
                    //规格名称，颜色尺寸
                    $sortArr = explode(',', $search);
                    $search1 = $sortArr[0];
                    $search2 = $sortArr[1];
                    $condition = $includeOrNot == 1 ? 'like' : 'not like';

                    $query->whereHas('orderItem', function ($query) use ($search1, $condition) {
                        $query->where('sku_value', $condition, '%' . $search1 . '%');
                    });

                    $query->whereHas('orderItem', function ($query) use ($search2, $condition) {
                        $query->where('sku_value', $condition, '%' . $search2 . '%');
                    });
                    break;
                case '9':
                    $query->where('seller_memo', 'like', "%{$search}%");
                    break;
                case '13':
                    $query->where('buyer_message', 'like', "%{$search}%");
                    break;
                case '14':
                    $separator = ',';
                    strpos($search, '，') !== false && $separator = '，';
                    $arr = explode($separator, $search);
                    $func = $includeOrNot == 1 ? 'whereIn' : 'whereNotIn';

                    $accurateWhere[] = [
                        'func' => $func,
                        'args' => ['order_items.num_iid', $arr]
                    ];
                    break;
                case '15':
                    $separator = ',';
                    strpos($search, '，') !== false && $separator = '，';
                    $arr = explode($separator, $search);
                    $func = $includeOrNot == 1 ? 'whereIn' : 'whereNotIn';

                    $accurateWhere[] = [
                        'func' => $func,
                        'args' => ['order_items.sku_id', $arr]
                    ];
                    break;
                case '16': // 商品种类
                    if ($orderSearchRequest->displayMerge && $orderSearchRequest->isPreShipmentList != 1) { //
                        break;
                    }
                    $range = StrUtil::extractedNumOrNumArr($search);
                    if (is_array($range)) {
                        $query->whereBetween('orders.sku_num', $range);
                    } else {
                        $query->where('orders.sku_num', $range);
                    }
                    break;
                case '17': // 订单数量
                    if ($orderSearchRequest->displayMerge && $orderSearchRequest->isPreShipmentList != 1) { // 合并单走 merge_orders_num 逻辑
                        break;
                    }
                    $range = StrUtil::extractedNumOrNumArr($search);
                    if (is_array($range)) {
                        $query->whereBetween('orders.num', $range);
                    } else {
                        $query->where('orders.num', $range);
                    }
                    break;
                case '18':
                    // 批次号
                    $arr = explode('-', $search);
                    $tidsArray = Package::query()->where('batch_no', 'like', $arr[0] . '%')
                        ->select('tids')->get()->pluck('tids')->toArray();
                    $orderIdArr = [];
                    foreach ($tidsArray as $index => $item) {
                        $orderIdArr = array_merge($orderIdArr, explode(',', $item));
                    }
                    $query->whereIn('orders.tid', $orderIdArr);
                    break;
                case '19':
                    // 昵称
                    $query->where('orders.buyer_nick', $search);
                    break;
                case 'isRemoteTransit': // 偏远中转
                    $query->where('orders.is_remote_transit', Order::REMOTE_TRANSIT_YES);
                    break;
                default:
                    break;
            }
        } else {
            $orderIdArr = [];
            //关键词查询
            if ($search) {
                foreach ($shops as $shop) {
                    $orderService->setShop($shop);
                    $orderService->setAccessToken($shop->access_token);
                    $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_name', $search));
                    if (isPhoneNumber($search)) {
                        $orderIdArr = array_merge($orderIdArr, $orderService->getQueryTradeOrderId('receiver_phone', $search));
                    }
                }
                $packageBuilder = Package::query()
                    ->leftJoin('package_orders', 'package_id', '=', 'packages.id')
                    ->where('waybill_code', $search);
                if ($orderSearchRequest->isUnshippedList){ // 未发货
                    $packageBuilder->where(DB::raw('IFNULL(packages.status, 0)'), '!=', Package::ORDER_STATUS_DELIVERED);
                }elseif ($orderSearchRequest->isShippedList){ // 已发货明细
                    $query->whereIn('packages.status', [Package::ORDER_STATUS_DELIVERED, Package::ORDER_STATUS_PART_DELIVERED]);
                    $query->where('packages.waybill_code', $search);
//                    $packageBuilder->whereIn('packages.status', [Package::ORDER_STATUS_DELIVERED, Package::ORDER_STATUS_PART_DELIVERED]);
//                    $packageBuilder->orWhere('packages.waybill_code',$search);

                }elseif ($orderSearchRequest->isResendList){ // 重新发货
                    $waybillCodeList = [$search];
                    $packageList = Package::query()->whereIn('waybill_code', $waybillCodeList)->get(['id','waybill_wp_index','multi_package_main_waybill_code']);
                    $multiPackageMainWaybillCodeArr = $packageList->pluck('multi_package_main_waybill_code')->toArray();
                    $waybillWpIndexArr = $packageList->pluck('waybill_wp_index')->toArray();  // 老单号
                    $packageIdArr = $packageList->pluck('id')->toArray();
                    $multiPackageList = Package::query()
                        ->whereIn('waybill_code', $multiPackageMainWaybillCodeArr)
                        ->get(['id']); // 一单多包

                    $packageIdArr = array_merge($packageIdArr, $multiPackageList->pluck('id')->toArray());

                    $query->where(function ($query) use ($waybillCodeList, $packageIdArr,$waybillWpIndexArr,$search) {
                        $query->whereIn('pt_logistics.waybill_code', $waybillCodeList);
                        $query->orWhereIn('pt_logistics.waybill_wp_index', $waybillWpIndexArr);
                        $query->orWhereIn('packages.id', $packageIdArr);
                        $query->orWhereIn('orders.tid', [$search, $search . 'A']);
                    });
                }
                $packageOrderIdArr = $packageBuilder
                    ->get(['order_id'])
                    ->pluck('order_id')
                    ->toArray();
                Log::info('packageOrderIdArr',[ $packageOrderIdArr]);
                $function = function ($query) use ($search, $packageOrderIdArr, $orderIdArr, $orderSearchRequest) {
                    $tid = $search;
                    if (PlatformConst::DY == config('app.platform')) {
                        $tid = strpos($search, 'A') ? $search : ($search . 'A');
                    }

//                    $query->whereRaw('2=2');
                    // 查询订单号和已打印逻辑冲突
                    if ($orderSearchRequest->displayMerge && $orderSearchRequest->isUnshippedList
                        && $orderSearchRequest->tabFlag == OrderIndexTabConst::PRINTED) {
                        $tempOrder = Order::query()->where('tid', $tid)->first(['address_md5']);

                        if (!empty($tempOrder)){
                            $query->orWhere('orders.address_md5',  $tempOrder->address_md5);
                        }else{
                            $query->orWhere('orders.tid', $tid); // 有可能查不到值，这个防止订单号查不到值
                        }
                    } else {
                        $query->orWhere('orders.tid', $tid);
                    }

                    if (in_array(config('app.platform'), [PlatformConst::KS, PlatformConst::JD])) {
                        $query->orWhere('buyer_nick', $search);
                    }
                    if (!empty($packageOrderIdArr)) {
                        $query->orWhereIn('orders.id', $packageOrderIdArr);
                    }
                    if (!empty($orderIdArr)) {
                        $query->orWhereIn('orders.id', $orderIdArr);
                    }
                };
                $accurateWhere[] = [
                    'func' => 'where',
                    'args' => $function,
                ];
            }
        }

        //实付金额
        if ($orderSearchRequest->selectItem == 10 && $search) {
            if (is_numeric($search)) {
                $this->amountArr['payment'] = $search;
            } else {
                $searchArr = explode('-', $search);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $this->amountArr['payment'] = $searchArr;
                }
            }
        }

        //订单金额
        if ($orderSearchRequest->selectItem == 11 && $search) {
            if (is_numeric($search)) {
                $this->amountArr['total_fee'] = $search;
            } else {
                $searchArr = explode('-', $search);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $this->amountArr['total_fee'] = $searchArr;
                }
            }
        }

        if ($orderSearchRequest->selectItem == 12 && $search) {
            if (is_numeric($search)) {
                $this->goodsNum = $search;
                if ($this->goodsNum == 1) {
                    $query->where('num', $this->goodsNum);
                }
            } else {
                $searchArr = explode('-', $search);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $this->goodsNum = $searchArr;
                }
            }
        }
        if ($orderSearchRequest->selectItem == 16 && $search) {
            // 商品种类
            if (strpos($search, '-')) {
                list($left, $right) = explode('-', $search);
                $this->ordersKind = [$left, $right];
            } else {
                $this->ordersKind = $search;
            }
        }
        if ($orderSearchRequest->selectItem == 17 && $search) {
            // 订单数量
            if (strpos($search, '-')) {
                list($left, $right) = explode('-', $search);
                $this->ordersNum = [$left, $right];
            } else {
                $this->ordersNum = $search;
            }
        }
        $this->accurateWhere = array_merge($this->accurateWhere, $accurateWhere);
    }
}
