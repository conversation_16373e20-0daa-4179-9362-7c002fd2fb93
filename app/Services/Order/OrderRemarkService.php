<?php

namespace App\Services\Order;

use App\Constants\PlatformConst;
use App\Events\Orders\OrderLockEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Http\StatusCode\StatusCode;
use App\Models\Order;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use App\Services\Shop\ShopService;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 订单备注相关的逻辑
 */
class OrderRemarkService
{


    private $orderCheckService;

    /**
     */
    public function __construct()
    {
        $this->orderCheckService = new OrderCheckService();
    }

    /**
     * 处理备注同步
     * @param $shop
     * @param  string  $beginTime
     * @param  string  $endTime
     * @return void
     */
    public function handleSyncRemarks($shop, string $beginTime, string $endTime)
    {
        if (!Environment::isPlatform(PlatformConst::JD)) {
            return;
        }
        $step = 1;
        try {
            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);
            $continue = true;
            $nextBeginTime = $beginTime;
            $shopId = $shop->id;
            while ($continue) {
//                \Log::info("getRemarks",["$nextBeginTime"=>$nextBeginTime,"end"])
                $remarkResult = $orderService->getRemarks($nextBeginTime, $endTime, 1, 1);
                $step++;
                $pageSize = $remarkResult['pageSize'];
                $totalNum = $remarkResult['totalNum'];
                if ($totalNum < $pageSize) {
                    $continue = false;
                }
                $remarks = $remarkResult['remarks'];

                if (sizeof($remarks) == 0 || $step > 100) {
                    break;
                }
                $remarkMapByOrderSn = array_column($remarks, null, "orderSn");
                $maxModified = max(array_column($remarks, 'modified'));
                $tids = array_column($remarks, "orderSn");
                $orders = Order::whereIn("tid", $tids)->get();
                \Log::info("remarks shopId".$shopId, [
                    "nextBeginTime" => $nextBeginTime, "end" => $endTime, "continue" => $continue,
                    "maxModified" => $maxModified, "orderCount" => sizeof($orders), "steps" => $step
                ]);
                foreach ($orders as $order) {
                    $remarkItem = $remarkMapByOrderSn[$order->tid];
                    $sellerFlag = $remarkItem['remarkFlag'];
                    $order->seller_flag = $sellerFlag ?? null;
                    $sellerMemo = $remarkItem['remark'];
                    $order->seller_memo = $sellerMemo ?? null;
                    $order->save();
                    \Log::info("save remark",
                        ["orderId" => $order->id, "sellerMemo" => $sellerMemo, "sellerFlag" => $sellerFlag]);
                }
            }
        } catch (\Exception $ex) {
            \Log::error($ex);
        }

    }

    /**
     * 批量备注
     * @throws \App\Exceptions\ShopCheckException
     */
    public function batchEditRemark($userId, $currentShopId, $tids, $flag, $sellerMemo, $lock, Request $request)
    {
        $columns = ['id', 'tid', 'seller_flag', 'seller_memo'];
        $data = [
            'seller_flag' => $flag
        ];
        $data['locked_at'] = null;
        if ($lock) {
            $data['locked_at'] = date('Y-m-d H:i:s');
        }
        if ($sellerMemo) {
            $data['seller_memo'] = json_encode([$sellerMemo], JSON_UNESCAPED_UNICODE);
        }
        $tids = OrderUtil::batchAppendOrderSuffix($tids);
        $allRelationShops = ShopBind::getAllRelationShop($currentShopId);
        $shopIds = array_pluck($allRelationShops, 'id'); //ShopBind::getAllRelationShopIds($request->auth->shop_id);
        $shopIdMap = ShopService::shopsByIds($shopIds)->keyBy('id');
        $beforeOrders = Order::query()->whereIn('tid', $tids)->get();
        //检查订单是不是都是这个店铺的
        $this->orderCheckService->authShopOrder($currentShopId, $beforeOrders);
        $beforeOrdersGroupByShopId = $beforeOrders->groupBy('shop_id');

//        \Log::info("绑定店铺",$shopIdMap);
        $successList = [];
        $failList = [];
        \Log::info("开始修改备注.", ["size" => sizeof($tids)]);
        foreach ($beforeOrdersGroupByShopId as $shopId => $orders) {
            $groupTids = $orders->map(function ($item) {
                return $item->tid;
            })->all();
            if (!$shopIdMap->has($shopId)) {
                foreach ($groupTids as $tid) {
                    $failList[] = ['tid' => $tid, "error" => StatusCode::SHOP_NOT_BOUND[1]];
                    \Log::info("没有匹配到店铺.", ['tid' => $tid, "shopId" => $shopId]);
                }
                continue;
            }

            $shop = $shopIdMap[$shopId];
            $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
            $orderService->setShop($shop);

            if (!$orderService->checkAuthStatus()) {
                foreach ($groupTids as $tid) {
                    $failList[] = ['tid' => $tid, "error" => StatusCode::SHOP_AUTH_ERROR[1]];
                    \Log::info("店铺授权不正确.", ['tid' => $tid, "shopId" => $shopId]);
                }
                continue;
            }


            //请求平台修改留言备注
            $failTids = $orderService->sendBatchEditSellerRemark($groupTids, $flag, $sellerMemo);
            $successList = $groupTids;
            \Log::info("修改备注.", ["tids" => $groupTids, "failTids" => $failTids]);
            if (sizeof($failTids) > 0) {
                foreach ($failTids as $tid) {
                    $failList[] = ['tid' => $tid, "error" => "修改失败"];
                }
                $successList = array_diff($successList, $failTids);
            }
        }
        \Log::info("结束修改备注.", ["successList" => $successList]);

        $orderList = Order::query()->whereIn('tid', $successList)->get();
        Order::query()
            ->whereIn('tid', $successList)
            ->update($data);
        $afterOrder = Order::query()->whereIn('tid', $tids)->whereIn('shop_id', $shopIds)->get($columns)->toArray();
        $user = User::query()->where('id', $userId)->first();
        $shop = Shop::query()->where('id', $currentShopId)->first();
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders->toArray(), $afterOrder,
            'flag'))->setClientInfoByRequest($request));
        if ($lock) {
            $orderArr = [];
            foreach ($orderList as $order) {
                $orderArr[] = [
                    'id' => $order['id'],
                    'tid' => $order['tid'],
                ];
            }
            event((new OrderLockEvent($shop->user, $shop, time(), $orderArr))->setClientInfoByRequest(\request()));
        }
        return $failList;
    }

    public function editRemark($id, $sellerFlag, $sellerMemo): bool
    {
        //修改商家留言备注
        $order = Order::find($id);
        if (!$order) {
            Log::error('修改留言备注未查询到订单 order_id：'.$id);
            return false;
        }
        $shop = $order->shop;
        $userId = $order->user_id;
        $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
        $orderService->setUserId($userId);
        $orderService->setShop($shop);

        //请求平台修改留言备注
        $res = $orderService->sendEditSellerRemark($order->tid, $sellerFlag, $sellerMemo);
        if (!$res) {
            return false;
        }
        sleep(Environment::isKs() ? 3 : 0);
        //快手因为修改以后里面同步这个订单的时候，这个备注信息回丢失，就先不重新获取了
        //修改留言备注
        $tradesOrder = $orderService->batchGetOrderInfo([$order]);
        Order::batchSave($tradesOrder, $userId, $shop->id);
//        $order->seller_flag = $sellerFlag;
//        $order->seller_memo = empty($sellerMemo) ? '[]' : json_encode([$sellerMemo]);
//        if (!$order->save()) {
//            return false;
//        }
        return true;
    }
}
