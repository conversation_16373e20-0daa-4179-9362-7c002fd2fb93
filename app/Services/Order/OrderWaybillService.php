<?php

namespace App\Services\Order;

use App\Constants\ErrorConst;
use App\Events\Waybills\WaybillRecycledEvent;
use App\Exceptions\ApiException;
use App\Models\Company;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\Shop;
use App\Models\Template;
use App\Models\User;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\WaybillUtil;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * 订单相关的电子面单服务
 */
class OrderWaybillService
{
    /**
     * 运单回收
     * @param array $ids
     * @param array $shopIds
     * @param int $userId
     * @param int $shopId
     * @return array
     */
    public static function recoveryByOrderId(array $ids, array $shopIds, int $userId = 0, int $shopId = 0): array
    {
        $failed = [];
        foreach ($ids as $item) {
//            $orderItemIdArr = [];
            if (strstr($item, ":")) {
                $temp = explode(":", $item);
                $orderId = $temp[0];
//                if (isset($temp[1]) && !empty($temp[1])) {
//                    $orderItemIdArr = explode(",", $temp[1]);
//                }
            } else {
                $orderId = $item;
            }
            $waybills = WaybillHistory::query()
                ->whereIn('shop_id', $shopIds)
                ->where(['waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO, 'order_id' => $orderId])
                ->get();
            if(sizeof($waybills)==0){
                $order = Order::find($orderId);

                if(!isset($order)){
                    $failed[] = [
                        'tid'=>$orderId,
                        'error_msg'    => '没有匹配到订单',
                    ];
                    continue;
                }
                $tid=OrderUtil::removeOrderSuffix($order['tid']);
                $failed[] = [
                    'tid'=>$tid,
                    'error_msg'    => '没有匹配到运单',
                ];
                continue;
            }
            foreach ($waybills as $waybill) {
                try {
                    log::info('waybill_code', [$waybill->waybill_code]);
                    $template = Template::withTrashed()->where('id', $waybill->template_id)->first();
                    if (!$template) {
                        throw new ApiException(ErrorConst::WAYBILL_TEMPLATE_NOT_FOUND);
                        //                        throw new \Exception('取号记录ID为' . $waybill->id . '电子面单模板不存在!');
                    }
                    $company = $template->company;
                    if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
                        $auth=WaybillUtil::findShopWaybillAuth($template['shop_id'],$template['auth_source'],
                            $template['owner_id']);
                        \Log::info('company', [$auth]);
//                        if (!Environment::isWxOrWxsp()&&in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY,
//                                Waybill::AUTH_SOURCE_KS,
//                                Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
//                            $auth = Shop::find($template['shop_id']);
//                        } else {
//                            $auth = Waybill::where(['owner_id' => $template['owner_id'], 'auth_source' => $template['auth_source']])->first();
//                        }
                    } else {
                        if (in_array($template['auth_source'], [ Waybill::AUTH_SOURCE_DY,Waybill::AUTH_SOURCE_TAOBAO])) {
                            $auth = Shop::where(['identifier' => $company['owner_id']])->first();
                        } else {
                            $auth = Waybill::where(['owner_id' => $company->owner_id, 'auth_source' => $company->auth_source])->first();
                        }
                    }
                    if (!$auth) {
                        throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
                        //                        throw new \Exception('电子面单授权信息丢失!');
                    }
                    $waybillService = WaybillServiceManager::init($template['auth_source'], $auth->access_token);
                    $waybillCode = $waybill->parent_waybill_code ? $waybill->parent_waybill_code : $waybill->waybill_code;
                    if($auth instanceof  Shop){
                        $waybillService->setShop($auth);
                    }
                    $waybillService->wayBillCancelDiscard($waybill->wp_code, $waybillCode,$waybill->platform_waybill_id??'');
                    Package::where('id', $waybill->package_id)
                        ->update([
                            'recycled_at' => Carbon::now()
                        ]);

                    $orders = Order::query()->where('id', $orderId)->get();
                    foreach ($orders as $order) {
                        $order->express_code = null;
                        $order->express_no = null;
                        $order->template_id = 0;
                        $order->printed_at = null;
                        $order->print_status = Order::PRINT_STATUS_NO;
                        $order->recycled_at = Carbon::now();
                        $order->merge_flag = '';
                        $order->save();

                        $orderItem = OrderItem::query()->where('order_id', $orderId)->get();
                        foreach ($orderItem as $item) {
                            if (empty($item->waybill_code)) {
                                $item->print_status = Order::PRINT_STATUS_NO;
                                $item->save();
                                continue;
                            }
                            $itemNoArr = explode(',', $item->waybill_code);
                            if (in_array($waybill->waybill_code, $itemNoArr)) {
                                $key = array_search($waybill->waybill_code, $itemNoArr);
                                unset($itemNoArr[$key]);
                            }
                            $item->waybill_code = !empty($itemNoArr) ? implode(',', $itemNoArr) : null;
                            $item->print_status = !empty($itemNoArr) ? Order::PRINT_STATUS_YES : Order::PRINT_STATUS_NO;
                            $item->save();
                        }
                    }

                    $waybill->waybill_status = WaybillHistory::WAYBILL_RECOVERY_YES;
                    if (!$waybill->save()) {
                        throw new ApiException(ErrorConst::WAYBILL_UPDATE_FAIL);
//                        throw new \Exception('回收状态修改失败');
                    }
                } catch (\Exception $e){
                    $failed[] = [
                        'tid'=>$orderId,
                        'waybill_code' => $waybillCode,
                        'error_msg'    => $e->getMessage()
                    ];
                }
            }
        }

        return $failed;
    }


    /**
     * 运单回收 这个是通过取号记录的id来回收的
     * @param array $ids
     * @param array $shopIds
     * @param int $userId
     * @param int $shopId
     * @return array
     */
    public static function recoveryByHistoryId(array $ids, array $shopIds, int $userId, int $shopId): array
    {
        $failed = [];

        $waybills = WaybillHistory::query()
            ->whereIn('id', $ids)
            ->where(function ($query) use ($shopIds, $shopId) {
                $query->whereIn('shop_id', $shopIds);
                $query->orWhere('to_shop_id', $shopId);
            })
            ->get();
        \Log::info("回收面单数量",["count"=>count($waybills),"shopIds"=>$shopIds,"ids"=>$ids]);
        foreach ($waybills as $waybill) {
            $template = Template::withTrashed()->where('id', $waybill->template_id)->first();
            $waybillCode = $waybill->parent_waybill_code ? $waybill->parent_waybill_code : $waybill->waybill_code;
            try {
                if (!$template) {
                    $auth = Shop::find($template['shop_id']);
                } else {
                    $company = $template->company;
                    if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
//                        if (!Environment::isWxOrWxsp()&&in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY,
//                                Waybill::AUTH_SOURCE_KS,
//                            Waybill::AUTH_SOURCE_JD, Waybill::AUTH_SOURCE_TAOBAO])) {
//                            $auth = Shop::find($template['shop_id']);
//                        } else {
//                            $auth = Waybill::where(['owner_id' => $template['owner_id'], 'auth_source' => $template['auth_source']])->first();
//                        }
                        $auth=WaybillUtil::findShopWaybillAuth($template['shop_id'],$template['auth_source'],
                            $template['owner_id']) ;
                    } else {
                        if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                            $auth = Shop::where(['identifier' => $company['owner_id']])->first();
                        } else {
                            $auth = Waybill::where(['owner_id' => $company->owner_id, 'auth_source' => $company->auth_source])->first();
                        }
                    }
                }

                if (!$auth) {
                    throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
                }
                $waybillService = WaybillServiceManager::init($template['auth_source'], $auth->access_token);
                if($auth instanceof Shop){
                    $waybillService->setShop($auth);
                }
                $ret = $waybillService->wayBillCancelDiscard($waybill->wp_code, $waybillCode,
                    $waybill->platform_waybill_id??'');
                if (!$ret) {
                    Log::error('waybill recovery failed !', [$waybill]);
                    throw new ApiException(ErrorConst::WAYBILL_INVALID_FAIL);
                }
                \Log::info('waybill recovery success !', [$waybill]);
                Package::where('id', $waybill->package_id)
                    ->where('waybill_code', $waybill->waybill_code)
                    ->update([
                        'recycled_at' => Carbon::now()
                    ]);
                $packageOrders = PackageOrder::where('package_id', $waybill->package_id)->get();

                $orders = Order::whereIn('id', collect($packageOrders)->pluck('order_id')->toArray())->get();
                $waybillHistory = WaybillHistory::query()
                    ->where('order_id', $waybill->order_id)
                    ->where('waybill_code', $waybill->waybill_code)
                    ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)->get();
                foreach ($orders as $order) {
                    if (empty($order->express_no)) {
                        if (collect($waybillHistory)->count() > 1 && !$waybill->parent_waybill_code
                            && !in_array($order->order_status, [Order::ORDER_STATUS_PAYMENT])) {
                            $order->recycled_at = Carbon::now();
                            $order->save();
                        } else {
                            $order->recycled_at = Carbon::now();
                            $order->template_id = 0;
                            $order->print_status = Order::PRINT_STATUS_NO;
                            $order->printed_at = null;
                            $order->save();

                            $orderItem = OrderItem::query()->where('order_id', $order->id)->get();
                            foreach ($orderItem as $item) {
                                if (empty($item->waybill_code)) {
                                    $item->print_status = Order::PRINT_STATUS_NO;
                                    $item->save();
                                    continue;
                                }
                                $itemNoArr = explode(',', $item->waybill_code);
                                if (in_array($waybill->waybill_code, $itemNoArr)) {
                                    $key = array_search($waybill->waybill_code, $itemNoArr);
                                    unset($itemNoArr[$key]);
                                }
                                $item->waybill_code = !empty($itemNoArr) ? implode(',', $itemNoArr) : null;
                                $item->print_status = !empty($itemNoArr) ? Order::PRINT_STATUS_YES : Order::PRINT_STATUS_NO;
                                $item->save();
                            }
                        }
                    } else {
                        //多个单号
                        $noArr = explode(',', $order->express_no);
                        if (in_array($waybill->waybill_code, $noArr)) {
                            $key = array_search($waybill->waybill_code, $noArr);
                            unset($noArr[$key]);
                        }
                        // 非待发货
                        if (!in_array($order->order_status, [Order::ORDER_STATUS_PAYMENT])){
                            $order->recycled_at = Carbon::now();
                            $order->save();
                        } else{
                            $order->recycled_at = Carbon::now();
                            $order->express_code = !empty($noArr) ? $order->express_code : null;
                            $order->express_no = !empty($noArr) ? implode(',', $noArr) : null;
                            $order->template_id = !empty($noArr) ? $order->template_id : 0;
                            $order->print_status = !empty($noArr) ? Order::PRINT_STATUS_YES : Order::PRINT_STATUS_NO;
                            $order->printed_at = !empty($noArr) ? $order->printed_at : null;
                            $order->save();

                            $orderItem = OrderItem::query()->where('order_id', $order->id)->get();
                            foreach ($orderItem as $item) {
                                if (empty($item->waybill_code)) {
                                    $item->print_status = Order::PRINT_STATUS_NO;
                                    $item->save();
                                    continue;
                                }
                                $itemNoArr = explode(',', $item->waybill_code);
                                if (in_array($waybill->waybill_code, $itemNoArr)) {
                                    $key = array_search($waybill->waybill_code, $itemNoArr);
                                    unset($itemNoArr[$key]);
                                }
                                $item->waybill_code = !empty($itemNoArr) ? implode(',', $itemNoArr) : null;
                                $item->save();
                            }
                        }

                    }
                }

                $res = WaybillHistory::query()
                    ->where('order_id', $waybill->order_id)
                    ->where('waybill_code', $waybill->waybill_code)
                    ->update([
                        'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_YES
                    ]);
                if (!$res) {
                    throw new ApiException(ErrorConst::WAYBILL_UPDATE_FAIL);
                }
            } catch (\Exception $e) {
                \Log::info('waybill recovery failed !', [$waybill]);
                $failed[] = [
                    'waybill_code' => $waybillCode,
                    'error_msg'    => $e->getMessage()
                ];
            }
        }

        return $failed;
    }


    /**
     * 通过面单号批量运单回收
     * @param array $waybillCodes
     * @param array $shopIds
     * @param int $userId
     * @param int $shopId
     * @return array
     * @throws ApiException
     */
    public static function batchRecoveryByWaybillCode(array $waybillCodes, array $shopIds, int $userId, int $shopId, int $isForcedRecovery = 0): array
    {
        $failed = [];
        $waybillHistories = WaybillHistory::query()
            ->where(function ($query)use ($shopIds){
                $query->whereIn('shop_id', $shopIds)
                    ->orWhereIn('to_shop_id', $shopIds);
            })
            ->whereIn('waybill_code', $waybillCodes)->get();

        if ($waybillHistories->isEmpty()) {
            throw new ApiException(ErrorConst::WAYBILL_CODE_NOT_FOUND);
        }

        foreach ($waybillHistories as $waybillHistory) {
            $waybillCode = $waybillHistory->parent_waybill_code ? $waybillHistory->parent_waybill_code : $waybillHistory->waybill_code;
            $template = Template::withTrashed()->where('id', $waybillHistory->template_id)->first();
            try {
                if (!$template) {
                    throw new ApiException(ErrorConst::WAYBILL_TEMPLATE_NOT_FOUND);
//                    throw new \Exception('取号记录ID为' . $waybillHistory->id . '电子面单模板不存在!');
                }
                $company = $template->company;
                if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
//                    if (in_array($template['auth_source'],[Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
//                        $auth = Shop::find($template['shop_id']);
//                    } else {
//                        $auth = Waybill::where(['owner_id' => $template['owner_id'], 'auth_source' => $template['auth_source']])->first();
//                    }
                    $auth=WaybillUtil::findShopWaybillAuth($template['shop_id'],$template['auth_source'],
                        $template['owner_id']) ;
                } else {
                    if (in_array($template['auth_source'],[Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD, Waybill::AUTH_SOURCE_XHS])) {
                        $auth = Shop::where(['identifier'=>$company['owner_id']])->first();
                    } else {
                        $auth = Waybill::where(['owner_id' => $company->owner_id, 'auth_source' => $company->auth_source])->first();
                    }
                }
                if (!$auth) {
                    throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
//                    throw new \Exception('电子面单授权信息丢失!');
                }
                $waybillService = WaybillServiceManager::init($template['auth_source'], $auth->access_token);
                if ($auth instanceof Shop) {
                    $waybillService->setShop($auth);
                }
                try {
                    $ret = $waybillService->wayBillCancelDiscard($waybillHistory->wp_code, $waybillCode,$waybillHistory->platform_waybill_id??'');
                    if (!$ret) {
                        Log::error('waybillHistory recovery failed !', [$waybillHistory]);
                        throw new ApiException(ErrorConst::WAYBILL_INVALID_FAIL);
//                    throw new \Exception('取消失败！');
                    }
                } catch (\Exception $e) {
                    if (!$isForcedRecovery){ // 非强制回收抛出异常
                        throw $e;
                    }
                }

                $waybillHistory->waybill_status = WaybillHistory::WAYBILL_RECOVERY_YES;
                if (!$waybillHistory->save()) {
                    throw new ApiException(ErrorConst::WAYBILL_UPDATE_FAIL);
                }
                // 平台订单
                if ($waybillHistory->order_type == WaybillHistory::ORDER_TYPE_GENERAL) {
                    Package::where('id', $waybillHistory->package_id)->update(['recycled_at' => Carbon::now()]);
                    $packageOrders = PackageOrder::where('package_id', $waybillHistory->package_id)->get();
                    $orders = Order::whereIn('id', collect($packageOrders)->pluck('order_id')->toArray())->get();
                    //
//                    $waybillHistory = WaybillHistory::query()->where('order_id', $waybillHistory->order_id)
//                        ->where('order_type',WaybillHistory::ORDER_TYPE_GENERAL)
//                        ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)->get();

                    /**
                     * @var Order $order;
                     */
                    foreach ($orders as $order) {
                        //判断这个订单有没有未回收的包裹数据，看包裹的回收时间是不是为null，如果不存在，这个订单就是未打印了
                        $orderPackagesNotRecycled=collect($order->packages)->filter(function ($item) {
                            // 判断未发货的=0
                            return is_null($item->recycled_at) && $item->status != Order::ORDER_STATUS_DELIVERED;
                        });
                        if (sizeof($orderPackagesNotRecycled) > 0
                            || !in_array($order->order_status, [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])) {
                            $order->recycled_at = Carbon::now();
                            $order->save();
                        } else {
                            $order->recycled_at = Carbon::now();
                            $order->template_id = 0;
                            $order->print_status = Order::PRINT_STATUS_NO;
                            $order->printed_at = null;
                            $order->take_waybill_at = null;
                            $order->is_split = 0; // 去掉拆分标记
                            $order->abnormal_type = 0; // 去掉异常标记
                            $order->save();

                            $orderItem = OrderItem::query()->where('order_id', $order->id)->get();
                            foreach ($orderItem as $item) {
                                if (empty($item->waybill_code)) {
                                    $item->print_status = Order::PRINT_STATUS_NO;
                                    $item->save();
                                    continue;
                                }
                                $itemNoArr = explode(',', $item->waybill_code);
                                if (in_array($waybillHistory->waybill_code, $itemNoArr)) {
                                    $key = array_search($waybillHistory->waybill_code, $itemNoArr);
                                    unset($itemNoArr[$key]);
                                }
                                $item->waybill_code = !empty($itemNoArr) ? implode(',', $itemNoArr) : null;
                                $item->print_status = !empty($itemNoArr) ? Order::PRINT_STATUS_YES : Order::PRINT_STATUS_NO;
                                $item->save();
                            }
                        }

                    }
                } elseif ($waybillHistory->order_type == WaybillHistory::ORDER_TYPE_FREE) { // 自由打印订单
                    $customizeOrder = CustomizeOrder::query()->where('id', $waybillHistory->order_id)->withTrashed()->first();
                    // 查询未回收的数量
                    $count = WaybillHistory::query()->where('order_id', $waybillHistory->order_id)
                        ->where('order_type',WaybillHistory::ORDER_TYPE_FREE)
                        ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)->count();
                    if($count == 0){
                        // 修改成未打印
                        $customizeOrder->print_status = CustomizeOrder::PRINT_STATUS_NO;
                        $customizeOrder->printed_at = null;
                        $customizeOrder->save();
                    }
                }

                $user = User::query()->find($userId);
                $shop = Shop::query()->find($shopId);
                event((new WaybillRecycledEvent($user, $shop, time(), $waybillHistory))->setClientInfoByRequest(request()));
            } catch (\Exception $e) {
                Log::error('waybill recovery failed !', [$e->getMessage(),$e->getTraceAsString()]);
                $failed[] = [
                    'waybill_code' => $waybillCode,
                    'error_msg'    => $e->getMessage()
                ];
            }
        }
        return $failed;
    }
}
