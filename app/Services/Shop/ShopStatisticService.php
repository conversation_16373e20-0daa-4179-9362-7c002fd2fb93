<?php

namespace App\Services\Shop;

use App\Models\Shop;
use App\Models\UserShopStatistic;
use Illuminate\Support\Facades\DB;

/**
 * 店铺统计的服务层
 */
class ShopStatisticService
{

    /**
     * 店铺使用情况统计
     * @param array $condition
     * @param string $keyword
     * @param int $offset
     * @param int $limit
     * @param string $orderBy
     * @param string $statAt
     * @return mixed
     */
    public static function shopUsageStatisticsSearch(array $condition, string $keyword, int $offset, int $limit,
                                                     string $orderBy = 'created_at desc', string $statAt = '', $openLiveMode = null)
    {
        $sortArr = explode(' ', $orderBy);

        $query = Shop::query()->where($condition);
        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $query->where('shops.identifier', $keyword)
                    ->orWhere('shops.name', 'like', '%' . $keyword . '%')
                    ->orWhere('shops.shop_name', 'like', '%' . $keyword . '%')
                    ->orWhere('shops.id', $keyword)
                    ->orWhere('shops.shop_code', $keyword);

            });
        }
        if (!is_null($openLiveMode)){
            $query->whereHas('shopExtra',function ($query) use ($openLiveMode){
                $query->where('open_live_mode', $openLiveMode);
            });
        }

        $query->leftJoin('user_shop_statistics', function ($join) use ($statAt) {
            $join->on('user_shop_statistics.shop_id', '=', 'shops.id')
                ->where('user_shop_statistics.interval', '=', UserShopStatistic::INTERVAL_DAY)
                ->where('user_shop_statistics.stat_at', '=', date('Y-m-d', strtotime($statAt)));
        })
            ->leftJoin('user_shop_statistics  AS  total_statistic', function ($join) {
                $join->on('total_statistic.shop_id', '=', 'shops.id')
                    ->where('total_statistic.interval', '=', UserShopStatistic::INTERVAL_IGNORE)
                    ->whereNull('total_statistic.stat_at');
            })
//            ->where('user_shop_statistics.stat_at', date('Y-m-d', strtotime($statAt)))
//            ->where('user_shop_statistics.interval', UserShopStatistic::INTERVAL_DAY)
            ->where($condition)
            ->with(['totalStatistic', 'userExtra','shopExtra'])
            ->selectRaw('shops.*,user_shop_statistics.print_count AS `today_print_count`,user_shop_statistics.order_count AS `today_order_count`, user_shop_statistics.delivery_count AS `today_delivery_count`,total_statistic.print_count AS `total_print_count`,total_statistic.order_count AS `total_order_count`, total_statistic.delivery_count AS `total_delivery_count`');
        $count = $query->count();
        $query->orderBy($sortArr[0], $sortArr[1])
            ->offset($offset)
            ->limit($limit);
        \Log::info($query->toSql());
        return [$query->get(), $count];
//

    }
}
