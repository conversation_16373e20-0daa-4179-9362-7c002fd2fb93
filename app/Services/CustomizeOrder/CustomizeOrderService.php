<?php

namespace App\Services\CustomizeOrder;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\CustomizeOrder;
use App\Models\CustomizeOrdersFailed;
use App\Models\PrintRecord;
use App\Models\ShippingAddress;
use App\Models\WaybillHistory;
use App\Utils\AddressUtil;
use App\Utils\FileUploadUtil;
use Illuminate\Http\UploadedFile;

/**
 * 自由订单服务类
 */
class CustomizeOrderService
{
    /**
     * 批量创建订单
     * @param  string  $userId
     * @param  int  $shopId
     * @param  string  $fileName
     * @param  string  $path
     * @return bool
     * @throws \Exception
     */
    public static function batchGenerate(string $userId, int $shopId, UploadedFile $file)
    {
//        $data = FileUploadUtil::uploadFileReadExcel($fileName, $path); // 导入某些 xlsx 文件的时候 会报内存溢出
        $data = FileUploadUtil::importByFastExcel($file);
        if (empty($data)) {
            throw new ApiException(ErrorConst::FILE_ERROR_OR_DATA_EMPTY);
        }

        //发件人信息
        $ship = ShippingAddress::findDefaultShippingAddress($userId, $shopId);

        if (empty($ship)) {
            throw new ApiException(ErrorConst::NOT_SET_DELIVERY_ADDRESS);
        }

        $successNum = $failedLength = 0;
        $index = 0;
        foreach ($data as $key => $line) {
            \Log::info("生成导入数据:".strval($index), $line);
            $index++;
            if (empty($line[0] ?? '') && empty($line[1] ?? '') && empty($line[2] ?? '')) {
                continue;
            }
            $addressInfo = null;
            if (!is_null($line[2])) {
                // 去掉空白字符
                $addrStr = trim($line[2]);
                $addrStr = removeNonChineseFirstChar($addrStr);
                $addrStr = removeNonChineseFirstChar($addrStr);
                $addrStr = trim($addrStr);
                $addressInfo = AddressUtil::smartParse($addrStr);
            }
            if ($addressInfo) {
                $province = $addressInfo->getProvince();
                $detail = $addressInfo->getDetail();
                $city = $addressInfo->getCity();
                $district = $addressInfo->getDistrict();
                $num = $line[4] ?? 1;
                if (!is_numeric($num)) {
                    self::makeCustomizeOrderFailed($line, $shopId, '商品数量格式错误',
                        CustomizeOrdersFailed::IMPORT_ERROR_GOODS_NUM);
                    ++$failedLength;
                    continue;
                }
                if (!$addressInfo->getPassed()) {
//                    throw new ApiException(ErrorConst::ADDRESS_INCOMPLETE);
                    self::makeCustomizeOrderFailed($line, $shopId, '地址解析异常',
                        CustomizeOrdersFailed::IMPORT_ERROR_ADDRESS);
                    ++$failedLength;
                    continue;
                }
                //手机号错误
//                if (!isPhoneNumber($line[1])) {
//                    self::makeCustomizeOrderFailed($line, $shopId,'手机号格式错误',CustomizeOrdersFailed::IMPORT_ERROR_PHONE);
//                    ++$failedLength;
//                    continue;
//                }
                //商品信息不完整
                /*if (!$line[3] || !$line[4]) {
                    self::makeCustomizeOrderFailed($line, $shopId,'商品信息不完整',CustomizeOrdersFailed::IMPORT_ERROR_GOODS);
                    ++$failedLength;
                    continue;
                }*/

                $goodsInfo = '[{"index":0,"value":"","title":"'.trim($line[3]).'","code":"","norm":"","num":"'.$line[4].'","price":"","weight":null,"volume":null}]';
                $order = [
                    'user_id' => $userId,
                    'shop_id' => $shopId,
                    'receiver_name' => trim($line[0]),
                    'order_type' => CustomizeOrder::ORDER_TYPE_BATCH,
                    'receiver_phone' => $line[1],
                    'receiver_province' => $province,
                    'receiver_city' => $city,
                    'receiver_district' => $district,
                    'receiver_address' => $detail,
                    'goods_info' => $goodsInfo,
                    'num' => trim($line[4]),
                    'seller_memo' => trim($line[5]),
                    'sender_name' => $ship->sender_name,
                    'sender_phone' => $ship->mobile,
                    'sender_tel' => $ship->tel,
                    'sender_province' => $ship->province,
                    'sender_city' => $ship->city,
                    'sender_district' => $ship->district,
                    'outer_order_no' => trim($line[6] ?? '') ?? "",
                    'sender_detailaddress' => $ship->address,
                ];
                CustomizeOrder::create($order);
                ++$successNum;
            } else {
                \Log::info("生成导入错误数据:".strval($index), $line);
                self::makeCustomizeOrderFailed($line, $shopId, '地址解析异常',
                    CustomizeOrdersFailed::IMPORT_ERROR_ADDRESS);
                ++$failedLength;
            }
        }

        return ['succcessLength' => $successNum, 'failedLength' => $failedLength];
    }

    /**
     * @param $line
     * @param  int  $shopId
     * @return void
     */
    protected static function makeCustomizeOrderFailed($line, int $shopId, $errMsg, $errCode): void
    {

        CustomizeOrdersFailed::query()->create([
            'receiver_name' => $line[0] ?? '',
            'receiver_phone' => $line[1] ?? '',
            'receiver_address' => $line[2] ?? '',
            'goods_name' => $line[3] ?? '',
            'goods_num' => is_numeric($line[4]) ? $line[4] : 0,
            'goods_memo' => $line[5] ?? '',
            'shop_id' => $shopId,
            'err_msg' => $errMsg,//'地址解析异常',
            'err_code' => $errCode// CustomizeOrdersFailed::IMPORT_ERROR_ADDRESS
        ]);
    }

    /**
     * @param $userId
     * @param $waybillShopId
     * @param $order
     * @param $template
     * @param $company
     * @param $package
     * @param  array  $printData
     * @param $batchNo
     * @param  int  $counter
     * @param  int  $total
     * @param  int  $version
     * @return mixed
     */
    public static function createWaybillHistory(
        $userId,
        $waybillShopId,
        $order,
        $template,
        $company,
        $package,
        array $printData,
        $batchNo,
        int $counter,
        int $total,
        int $version
    ) {
        $history = WaybillHistory::create([
            'user_id' => $userId,
            'shop_id' => $waybillShopId,//$waybillAuth->id,
            'order_id' => $order->id,
            'order_no' => $order['order_no'] ?? '',
            'template_id' => $template['id'],
            'auth_source' => $template['auth_source'],
            'source' => $company['source'] ?? '',
            'source_shopid' => $company['source_shopid'] ?? '',
            'company_id' => $company['id'],
            'parent_waybill_code' => $package['parent_waybill_code'],
            'waybill_code' => $package['waybill_code'],
            'wp_code' => $template['wp_code'],
            'print_data' => json_encode($printData),
            'receiver_province' => $order->receiver_province,
            'receiver_city' => $order->receiver_city,
            'receiver_district' => $order->receiver_district,
            'receiver_name' => $order->receiver_name,
            'receiver_phone' => $order->receiver_phone,
            'receiver_address' => $order->receiver_address,
            'print_data_items' => '',
            'batch_no' => $batchNo,
            'outer_order_no' => $order['outer_order_no'] ?? "",
            'to_shop_id' => $order['shop_id'],
            'waybill_index' => $counter,
            'waybill_count' => $total,
            'version' => $version,
            'order_type' => WaybillHistory::ORDER_TYPE_FREE,
        ]);
        return $history;
    }

    /**
     * @param $userId
     * @param $shopId
     * @param $order
     * @param $history
     * @param $waybill_code
     * @param $template
     * @param $batchNo
     * @param  array  $printData
     * @param  int  $counter
     * @param  int  $total
     * @param  int  $version
     * @return void
     */
    public static function createPrintRecord(
        $userId,
        $shopId,
        $order,
        $history,
        $waybill_code,
        $template,
        $batchNo,
        array $printData,
        int $counter,
        int $total,
        int $version
    ): void {
        PrintRecord::create([
            'user_id' => $userId,
            'shop_id' => $shopId,
            'order_id' => $order->id,
            'history_id' => $history->id,
            'order_no' => $order['order_no'] ?? '',
            'waybill_code' => $waybill_code,
            'wp_code' => $template['wp_code'],
            'receiver_province' => $order->receiver_province,
            'receiver_city' => $order->receiver_city,
            'receiver_district' => $order->receiver_district,
            'receiver_town' => $order->receiver_town,
            'receiver_address' => $order->receiver_address,
            'receiver_name' => $order->receiver_name,
            'receiver_zip' => $order->receiver_zip,
            'receiver_phone' => $order->receiver_phone,
            'buyer_remark' => $order->buyer_message,
            'batch_no' => $batchNo,
            'print_data' => json_encode($printData),
            'outer_order_no' => $order['outer_order_no'] ?? "",
            'to_shop_id' => $order['shop_id'],
            'template_id' => $template['id'],
            'template_name' => $template['name'],
            'print_index' => $counter,
            'print_count' => $total,
            'version' => $version,
            'order_type' => WaybillHistory::ORDER_TYPE_FREE,
        ]);
    }

}
