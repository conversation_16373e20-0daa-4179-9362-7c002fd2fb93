<?php
namespace App\Services\AfterSale;

use App\Constants\DyConst;
use App\Models\Order;

/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/9/15
 * Time: 10:35
 */

class WxAfterSaleServiceImpl extends AbstractAfterSaleService
{
    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60;
    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($accessToken, $data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * 获取单个的退款申请详情
     * @param $accessToken
     * @param $data
     * @return mixed
     */
    public function getRefundApply($accessToken, $data)
    {
        // TODO: Implement getRefundApply() method.
    }

    /**
     * 批量转换成售后请求
     * @param $applyList
     * @return mixed
     */
    public function formatToRefundApplyList($applyList)
    {
        // TODO: Implement formatToRefundApplyList() method.
    }

    /**
     * 单个转换售后请求
     * @param $apply
     * @return mixed
     */
    public function formatToRefundApply($apply)
    {
        // TODO: Implement formatToRefundApply() method.
    }

    /**
     * @param int $startTime
     * @param int $endTime
     * @return array
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\ClientException
     */
    public function getRefundOrdersList(int $startTime, int $endTime)
    {
        $this->hasNext = false;
        $client = wxClient();
        $client->setAccessToken($this->getAccessToken());

        $params = [
            'start_update_time' => date('Y-m-d H:i:s', $startTime),
            'end_update_time'   => date('Y-m-d H:i:s', $endTime),
            'status'            => 20, // 订单状态， 10 待付款 20 待发货 30 待收货 100 交易成功订单 200 全部商品售后之后，订单取消 250 用户主动取消或待付款超时取消
            'page_size'         => $this->pageSize,
            'page'              => $this->page,
        ];

        $response = $client->execute('post', '/product/order/get_list', $params);
        $count    = collect($response['orders'])->count();
        if ($count < $this->pageSize) {
            $this->hasNext = false;
        }
        else {
            $this->hasNext = true;
        }

        return $this->formatToRefundList($response['orders']);
    }

    private function formatToRefundList($data)
    {
        $list = [];
        foreach ($data as $index => $apply) {
            if ($apply['aftersale_detail']['on_aftersale_order_cnt'] > 0) {
                $list[] = $this->formatToRefund($apply);
            }
        }
        return $list;
    }

    private function formatToRefund($apply)
    {
        $extra = [
            'aftersale_status_text' => '未知', //售后状态说明
            'reason_text' => '未知', //退款原因
            'refund_amount' => formatToYuan($apply['order_detail']['price_info']['order_price']), //退款金额
        ];
        return [
            'tid'               => $apply['order_id'],
            'oid'               => $apply['order_id'],
            'refund_id'         => $apply['refundId'] ?? 0,
            'refund_created_at' => $apply['update_time'],
            'refund_updated_at' => $apply['update_time'],
            'refund_status'     => Order::REFUND_STATUS_YES,
            'aftersale_extra'   => json_encode($extra)
        ];
    }

    /**
     * 同意退款
     * @param $data
     * @return mixed
     */
    public function agreeRefund($data)
    {
        // TODO: Implement agreeRefund() method.
    }

    /**
     * 拒绝退款
     * @param $data
     * @return mixed
     */
    public function refuseRefund($data)
    {
        // TODO: Implement refuseRefund() method.
    }

    /**
     * 同意退货
     * @param $data
     * @return mixed
     */
    public function agreeReturn($data)
    {
        // TODO: Implement agreeReturn() method.
    }

    /**
     * 拒绝退货
     * @param $data
     * @return mixed
     */
    public function refuseReturn($data)
    {
        // TODO: Implement refuseReturn() method.
    }

    /**
     * 获取平台拒绝原因列表
     * @param $shopId
     * @param $refundId
     * @return mixed
     */
    public function getRefundRejectReason($shopId, $refundId)
    {
        // TODO: Implement getRefundRejectReason() method.
    }

    /**
     * 获取平台退款地址列表
     * @param $shopId
     * @return mixed
     */
    public function getRefundRejectAddress($shopId)
    {
        // TODO: Implement getRefundRejectAddress() method.
    }
}