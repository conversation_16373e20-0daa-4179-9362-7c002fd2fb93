<?php

namespace App\Services\AfterSale;

use App\Constants\DyConst;
use App\Constants\PlatformConst;
use App\Models\AftersaleOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Shop;
use GuzzleHttp\Pool;
use GuzzleHttp\Client;
use Illuminate\Support\Arr;
use GuzzleHttp\Psr7\Response;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;

abstract class  AbstractAfterSaleService
{
    private   $shop;
    protected $page = 1;
    protected $pageSize = 100;
    protected $pageTotal = 0;
    protected $accessToken = '';
    protected $tmp_cursor = ''; //游标

    protected $dataType = 'JSON';
    protected $concurrency = 50;  //并发数

    /**
     * curl 并发池异常输出原始数据
     * @var bool
     */
    protected $poolCurlAbnormalOutputOriginalData = false;

    /**
    * 每次拉取退款订单间隔的分钟
    * @var int
    */
    public $refundOrderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 0;

    /**
     * 是否存在下一页
     */
    public $hasNext = false;

    /**
     * 初始化页数
     */
    public function initPage()
    {
        $this->page = 1;
    }

    public function getPage()
    {
        return $this->page;
    }

    /**
     * 翻页
     */
    public function pageTurning()
    {
        $this->page++;
    }

    /**
     * @param $shop
     */
    public function setShop($shop): void
    {
        $this->shop = $shop;
        if (isset($shop->access_token)) {
            $this->setAccessToken($shop->access_token);
        }
    }

    /**
     * @return mixed
     * @throws ApiException
     */
    public function getShop()
    {
        if (empty($this->shop)) {
            throw new ApiException(ErrorConst::SHOP_NOT_FOUND);
        }
        return $this->shop;
    }

    /**
     * @param $accessToken
     * @return AbstractAfterSaleService
     */
    public function setAccessToken($accessToken): AbstractAfterSaleService
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @return string
     * @throws ApiException
     */
    public function getAccessToken()
    {
        if (!empty($this->accessToken)) {
            return $this->accessToken;
        }
        // 每次都重新获取token
        $shop = $this->getShop();
        return $this->accessToken = $shop->access_token;
    }

    /**
     * @param int $i
     * @param int $orderTimeInterval
     * @param $beginAt
     * @param $endAt
     * @return array
     * <AUTHOR>
     */
    public function calcTimeInterval(int $i, int $orderTimeInterval, $beginAt, $endAt): array
    {
        $addMinutes = $i * $orderTimeInterval;
        $startAt = date('Y-m-d H:i:s', strtotime("+$addMinutes minute", strtotime($beginAt)));

        $startTime = strtotime($startAt);
        $endTime = strtotime("+$orderTimeInterval minute", $startTime);
        //时间超出当前时间
        if ($endTime > strtotime($endAt)) {
            $endTime = strtotime($endAt);
        }
        return array($startTime, $endTime);
    }

        /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    abstract public function batchGetRefundApplyList($accessToken,$data);


    /**
     * 获取单个的退款申请详情
     * @param $accessToken
     * @param $data
     * @return mixed
     */
    abstract public function getRefundApply($accessToken,$data);

    /**
     * 批量转换成售后请求
     * @param $applyList
     * @return mixed
     */
    abstract public  function formatToRefundApplyList($applyList);


    /**
     * 单个转换售后请求
     * @param $apply
     * @return mixed
     */
    abstract public function formatToRefundApply($apply);

    /**
     * 拉取平台退款审核单列表(同步脚本使用)
     * @param $startTime
     * @param $endTime
     * @return array
     */
    abstract public function getRefundOrdersList(int $startTime, int $endTime);

    public function getRefundList($params)
    {
        $keyword     = $params['keyword'];
        $begin_at    = $params['begin_at'];
        $end_at      = $params['end_at'];
        $offset      = $params['offset'];
        $limit       = $params['limit'];
        $ownerIdList = $params['ownerIdList'];
//        $printStatus = $params['print_status'] ?? 0;
//        $orderStatus = $params['order_status'] ?? 30;


        $shops = Shop::getListByIdentifiers($ownerIdList);
        $shopIds = collect($shops)->pluck('id')->toArray();

        $query = AftersaleOrder::query()->whereIn('aftersale_order.shop_id', $shopIds)
            ->where('refund_created_at', '>=', $begin_at)
            ->where('refund_updated_at', '<=', $end_at);

        // 订单编号or退款编号
        if ($keyword) {
            $query->where(function ($query) use ($keyword) {
                $tid = $keyword;
                if (config('app.platform') == PlatformConst::DY) {
                    $tid = $keyword . 'A';
                }
                $query->orWhere('aftersale_order.tid', $tid)
                    ->orWhere('aftersale_order.refund_id', $keyword);
            });
        }
//        if ($printStatus > -1 || $orderStatus > -1) {
//            $query->rightJoin('orders', 'aftersale_order.tid', '=', 'orders.tid');
//            if ($printStatus) {
//                $query->where('orders.print_status', $printStatus);
//            }
//
//            if ($orderStatus) {
//                switch ($orderStatus) {
//                    case Order::ORDER_STATUS_PAYMENT:
//                        $query->whereIn('order_status', [
//                            Order::ORDER_STATUS_PAYMENT,
//                            Order::ORDER_STATUS_PART_DELIVERED,
//                        ]);
//                        break;
//                    case Order::ORDER_STATUS_DELIVERED:
//                        $query->whereNotNull('orders.send_at');
//                        $query->whereIn('order_status', Order::ORDER_STATUS_DELIVERED_ARRAY);
//                        break;
//                    default:
//                        $query->where('order_status', $orderStatus);
//                        break;
//                }
//            }
//        }

        $count = $query->count();
        $data = $query
            ->select(['aftersale_order.*'])
            ->orderBy('aftersale_order.refund_created_at', 'asc')
            ->offset($offset)
            ->limit($limit)
            ->get();

        $list = [];
        foreach ($data as $refund) {
            foreach ($refund->orderItem as $item) {
                if (($item['refund_status'] == Order::REFUND_STATUS_NO || $item['refund_id'] == 0) && config('app.platform') != PlatformConst::JD) {
                    continue;
                }
                $order = $item->order;
                $orderCipherInfo = $order->orderCipherInfo;
                $list[$item['refund_id']] = [
                    'shop_id'   => $item['shop_id'],
                    'tid'       => $item['tid'],
                    'oid'       => $item['oid'],
                    'receiver_state' => $order->receiver_state,
                    'receiver_city' => $order->receiver_city,
                    'receiver_district' => $order->receiver_district,
                    'receiver_town' => $order->receiver_town,
                    'receiver_name' => $orderCipherInfo ? $orderCipherInfo->receiver_name_mask : $order->receiver_name,
                    'receiver_phone' => $orderCipherInfo ? $orderCipherInfo->receiver_phone_mask : $order->receiver_phone,
                    'receiver_address' => $orderCipherInfo ? $orderCipherInfo->receiver_address_mask : $order->receiver_address,
                    'refund_id' => $item['refund_id'],
                    'refund_created_at' => $refund['refund_created_at'],
                    'refund_updated_at' => $refund['refund_updated_at'],
                    'refund_status'   => $refund['refund_status'],
                    'refund_status_text'   => DyConst::formatRefundStatusRemark($refund['refund_status']),
                    'refund_reason'   => $refund['refund_reason'],
                    'refund_price'   => $refund['refund_price'],
                    'goods_title' => $item['goods_title'],
                    'sku_value' => $item['sku_value'],
                    'seller_memo' => implode(',', json_decode($order->seller_memo, true)),
                    'seller_flag' => $order->seller_flag,
                    'wp_code' => $order->wp_code,
                    'waybill_code' => $order->express_no
                ];
            }
        }

        return [array_values($list), $count];
    }

    /**
     * 同意退款
     * @param $data
     * @return mixed
     */
    abstract public function agreeRefund($data);

    /**
     * 拒绝退款
     * @param $data
     * @return mixed
     */
    abstract public function refuseRefund($data);

    /**
     * 同意退货
     * @param $data
     * @return mixed
     */
    abstract public function agreeReturn($data);

    /**
     * 拒绝退货
     * @param $data
     * @return mixed
     */
    abstract public function refuseReturn($data);

    /**
     * 获取平台拒绝原因列表
     * @param $shopId
     * @param $refundId
     * @return mixed
     */
    abstract public function getRefundRejectReason($shopId, $refundId);

    /**
     * 获取平台退款地址列表
     * @param $shopId
     * @return mixed
     */
    abstract public function getRefundRejectAddress($shopId);


    public function poolCurl($params = [], $method = 'GET')
    {
        $headers = array();
        $keyArr = array_keys($params);
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'json';
                $headers = [
                    'Content-type' => 'application/json',
                    "Accept" => "application/json"
                ];
                break;
            case 'post_form':
                $method = 'post';
                $postKey = 'form_params';
                break;
            default:
                $postKey = 'json';
                break;
        }

        $client = new Client();
        $requests = function ($data) use ($client, $method, $headers, $postKey) {
            foreach ($data as $index => $datum) {
                yield function () use ($client, $method, $postKey, $headers, $datum) {
//                    Log::info('item', [$datum]);
                    return $client->requestAsync($method, $datum['url'], [
                        $postKey => $datum['params'],
                        'headers' => $headers
                    ]);
                };
            }
        };

        $result = [];
        $pool = new Pool($client, $requests($params), [
            'concurrency' => $this->concurrency, //并发数
            'fulfilled' => function (Response $response, $index) use (&$result, $params, $keyArr) {

                $orderIdStr = $keyArr[$index];
                $res = [
                    'http_code' => $response->getStatusCode(), // 200
                    'reason' => $response->getReasonPhrase(), // OK
                    //'data' => $response->getBody()->getContents()
                    'data' => $response->getBody()
                ];

                $content = $this->handleResponse($res, true);
                $result[$orderIdStr] = [
                    'sort' => $index,
                    'orderIdStr' => $orderIdStr,
                    'content' => $content,
                ];
                return $result;
            },
            'rejected' => function (\Exception $e, $index) {
                throw new \Exception($e->getMessage());
                Log::error(var_export($e->getRequest()->getMethod(), true));
            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();

        array_multisort(array_column($result, 'sort'), SORT_ASC, $result);
        //$result = Arr::pluck($result, 'content', 'sort');
        $result = Arr::pluck($result, 'content', 'orderIdStr');
        //Log::info('result', [$result]);

        return $result;
    }

    /**
     * 数据格式转换
     * @param array $response
     * @return bool|mixed|null
     * @throws \Exception
     */
    public function handleResponse(array $response, bool $isPoolCurl = false)
    {
        if ($response['http_code'] != 200) {
            return false;
        }
        $result = null;
        if ($this->dataType == 'JSON') {
            $response_ = preg_replace('/id":(\\d{11,})/', 'id":"\\1"', $response['data']);
            $respObject = json_decode($response_);
            //记录错误日志
            if (isset($respObject->err_no) && $respObject->err_no != 0) {
                Log::error('Order api请求错误====', [$response]);
            }
            $result = $respObject;

            //抛出异常
            if ((isset($result->sub_code) && !empty($result->sub_code)) && (isset($result->sub_msg) && !empty($result->sub_msg))) {
                //异步池请求，防止进程退出
                if ($isPoolCurl) {
                    if ($this->poolCurlAbnormalOutputOriginalData) {
                        return json_decode(json_encode($result), true);
                    } else {
                        return $result->sub_msg;
                    }
                } else {
                    throw new \Exception($result->sub_msg);
                }
            }
            return $result;
        }

        return $response['data'];
    }
}
