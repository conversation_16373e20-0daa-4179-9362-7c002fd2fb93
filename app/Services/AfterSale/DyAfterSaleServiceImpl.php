<?php
namespace App\Services\AfterSale;

use App\Constants\DyConst;
use App\Exceptions\ApiException;
use App\Models\Order;
use App\Models\Shop;
use App\Utils\ObjectUtil;
use App\Services\Client\DyClient;
use App\Services\CommonResponse;
use App\Services\AfterSale\Request\AfterSaleRequest;

/**
 * Created by PhpStorm.
 * User: xujianwei
 * Date: 2022/9/15
 * Time: 10:35
 */

class DyAfterSaleServiceImpl extends AbstractAfterSaleService
{
    protected $gatewayUrl = 'https://openapi-fxg.jinritemai.com';
//    protected $gatewayUrl = 'http://proxy-dy.mayiapps.cn';

    /**
     * 每次拉取退款订单间隔的分钟
     * @var int
     */
    public $refundOrderTimeInterval = 60;

    /**
     * 每次拉取增量订单间隔的分钟
     * @var int
     */
    public $orderIncrTimeInterval = 60;
    /**
     * 拉取平台退款审核单列表
     * @param $data
     * @return array
     */
    public function batchGetRefundApplyList($accessToken, $data)
    {
        // TODO: Implement batchGetRefundApplyList() method.
    }

    /**
     * 获取单个的退款申请详情
     * @param $accessToken
     * @param $data
     * @return mixed
     */
    public function getRefundApply($accessToken, $data)
    {
        // TODO: Implement getRefundApply() method.
    }

    /**
     * 批量转换成售后请求
     * @param $applyList
     * @return mixed
     */
    public function formatToRefundApplyList($applyList)
    {
        // TODO: Implement formatToRefundApplyList() method.
    }

    /**
     * 单个转换售后请求
     * @param $apply
     * @return mixed
     */
    public function formatToRefundApply($apply)
    {
        // TODO: Implement formatToRefundApply() method.
    }

    /**
     * @param int $startTime
     * @param int $endTime
     * @return array
     * @throws \App\Exceptions\ClientException
     */
    public function getRefundOrdersList(int $startTime, int $endTime)
    {
        $this->hasNext = false;
        $client = dyClient();
        $client->setAccessToken($this->accessToken);

        $params = [
            'update_start_time' => $startTime,
            'update_end_time' => $endTime,
            'size' => $this->pageSize,
            'order_by' => ['update_time desc'],
            'page' => $this->page -1,
        ];

        $response = $client->execute('afterSale/List', $params);

        if ($response['data']['has_more'] != 'true') {
            $this->hasNext = false;
        } else {
            $this->hasNext = true;
        }

        return $this->formatToRefundList($response['data']['items']);
    }

    private function formatToRefundList($items)
    {
        $list = [];
        foreach ($items as $index => $apply) {
            $list[] = $this->formatToRefund($apply);
        }
        return $list;
    }

    private function formatToRefund($apply)
    {
        return [
            'tid'               => $apply['order_info']['shop_order_id'] . 'A',
            'oid'               => (string)$apply['order_info']['related_order_info'][0]['sku_order_id'],
            'refund_id'         => $apply['aftersale_info']['aftersale_id'],
            'refund_price'      => formatToYuan($apply['aftersale_info']['refund_amount']), //退款金额
            'refund_status'     => DyConst::formatRefundStatus($apply['aftersale_info']['refund_status']), //退款状态
            'refund_reason'     => $apply['text_part']['reason_text'], //退款原因
            'refund_memo'       => $apply['aftersale_info']['remark'], //售后商家备注
            'refund_created_at' => date('Y-m-d H:i:s', $apply['aftersale_info']['apply_time']),
            'refund_updated_at' => date('Y-m-d H:i:s',$apply['aftersale_info']['update_time']),
        ];
    }

    /**
     * @param $data
     * @return array|mixed
     * @throws ApiException
     */
    public function agreeRefund($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchAgreeRefund($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $data
     * @return array|mixed
     * @throws ApiException
     */
    public function refuseRefund($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchRefuseRefund($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $data
     * @return array|mixed
     * @throws ApiException
     */
    public function agreeReturn($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchAgreeReturn($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    /**
     * @param $data
     * @return array|mixed
     * @throws ApiException
     */
    public function refuseReturn($data)
    {
        $failList = [];
        $successNum = 0;
        $total = count($data);
        // 订单可能包含多个店铺
        $agreeRefundListGroup = collect($data)->groupBy('shop_id')->toArray();
        foreach ($agreeRefundListGroup as $afterSaleOrderData) {
            $first = array_first($afterSaleOrderData);
            $shop = Shop::query()->find($first['shop_id']);
            $this->setShop($shop);
            $afterSaleRequestList = ObjectUtil::batchMapToObject($afterSaleOrderData, AfterSaleRequest::class);
            $responseList = $this->batchRefuseReturn($afterSaleRequestList);

            foreach ($responseList as $response) {
                if ($response->isSuccess()) {
                    $successNum++;
                } else {
                    $afterSaleRequest = $response->getRequest();
                    $error_code = $response->getCode();
                    $message = $response->getMessage();

                    $failList[] = [
                        'error_code' => $error_code,
                        'error_msg'  => $message,
                        'refund_id'  => $afterSaleRequest->refund_id
                    ];
                }
            }
        }

        return [
            'total' => $total,
            'success' => $successNum,
            'fail' => count($failList),
            'failData' => $failList
        ];
    }

    public function getRefundRejectReason($shopId, $refundId)
    {
        $list = [];
        $client = dyClient();
        $shop = Shop::query()->find($shopId);
        $this->setShop($shop);
        $client->setAccessToken($this->accessToken);

        $params = [

        ];

        $response = $client->execute('afterSale/rejectReasonCodeList', $params);

        if (!isset($response['code']) || $response['code'] !== 10000 || !isset($response['data']['items'])) {
            return $list;
        }

        foreach ($response['data']['items'] as $item) {
            $list[] = [
                'reason' => $item['reason'],
                'reject_reason_code' => $item['reject_reason_code']
            ];
        }

        return $list;
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchAgreeRefund($afterSaleRequestList)
    {
        $afterType = 201; // type说明: https://op.jinritemai.com/docs/api-docs/17/560
        $requestData = [];
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $params = [
                'type'  => $afterType,
                'items' => [
                    [
                        'aftersale_id' => $afterSaleRequest->refund_id
                    ]
                ]
            ];
            $url = 'afterSale/operate';
            $requestData[$index] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . '/' . $url
            ];
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'get');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchRefuseRefund($afterSaleRequestList)
    {
        $afterType = 202; // type说明: https://op.jinritemai.com/docs/api-docs/17/560
        $requestData = [];
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $params = [
                'type'  => $afterType,
                'items' => [
                    [
                        'aftersale_id' => $afterSaleRequest->refund_id,
                        'reason' => $afterSaleRequest->remark,
                        'reject_reason_code' => $afterSaleRequest->reject_reason_code,
                        'evidence' => [
                            [
                                'desc' => $afterSaleRequest->remark,
                                'type' => '4',
                            ]
                        ],
                    ],
                ]
            ];
            $url = 'afterSale/operate';
            $requestData[$index] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . '/' . $url
            ];
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'get');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchAgreeReturn($afterSaleRequestList)
    {
        $afterType = 111; // type说明: https://op.jinritemai.com/docs/api-docs/17/560
        $requestData = [];
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $params = [
                'type'  => $afterType,
                'items' => [
                    [
                        'aftersale_id' => $afterSaleRequest->refund_id
                    ]
                ]
            ];
            $url = 'afterSale/operate';
            $requestData[$index] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . '/' . $url
            ];
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'get');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $afterSaleRequestList
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    private function batchRefuseReturn($afterSaleRequestList)
    {
        $afterType = 202; // type说明: https://op.jinritemai.com/docs/api-docs/17/560
        $requestData = [];
        foreach ($afterSaleRequestList as $index => $afterSaleRequest) {
            $params = [
                'type'  => $afterType,
                'items' => [
                    [
                        'aftersale_id' => $afterSaleRequest->refund_id,
                        'reason' => $afterSaleRequest->remark,
                        'reject_reason_code' => $afterSaleRequest->reject_reason_code,
                        'evidence' => [
                            [
                                'desc' => $afterSaleRequest->remark,
                                'type' => '4',
                            ]
                        ],
                    ],
                ]
            ];
            $url = 'afterSale/operate';
            $requestData[$index] = [
                'params' => $this->getRequestData($url, $params),
                'url' => $this->gatewayUrl . '/' . $url
            ];
        }
        $this->poolCurlAbnormalOutputOriginalData = true;
        $responses = $this->poolCurl($requestData, 'get');
        $results = [];
        foreach ($responses as $index => $result) {
            $commonResponse = new CommonResponse();
            try {
                $afterSaleRequest = $afterSaleRequestList[$index];
                $commonResponse->setRequest($afterSaleRequest);
                $result = json_decode(json_encode($result), true);
                $this->handleErrorCode($result);
                $commonResponse->setSuccess(true);
            }catch (\Exception $e){
                $commonResponse->setSuccess(false);
                $commonResponse->setCode($e->getCode());
                $commonResponse->setMessage($e->getMessage());
            }finally{
                $results[] = $commonResponse;
            }
        }
        return $results;
    }

    /**
     * @param $apiMethod
     * @param array $apiParams
     * @return array
     * @throws \App\Exceptions\ApiException
     */
    public function getRequestData($apiMethod, array $apiParams)
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        $apiParams = array_map(function ($item) {
            // 内嵌数组也要排序 不然会签名失败
            if (is_array($item)) {
                $item = $this->sort($item);
            }
            return is_array($item) ? $item : (string)$item;
        }, $apiParams);

        ksort($apiParams);
        $method = str_replace('/', '.', $apiMethod);
        $timestamp = date('Y-m-d H:i:s', time());


        // 构造请求url
        $sign = $this->generateSign($method, $apiParams, $timestamp, $appKey, $secretKey);
        $request = [
            'app_key' => $appKey,
            'method' => $method,
            'access_token' => $this->getAccessToken(),
            'param_json' => json_encode($apiParams, 320),
            'timestamp' => $timestamp,
            'v' => '2',
            'sign' => $sign,
        ];
        return $request;
    }

    private function sort(array $item)
    {
        ksort($item);
        foreach ($item as $k => $v) {
            if (is_array($v)) {
                $item[$k] = $this->sort($v);
            }
        }

        return $item;
    }

    public function generateSign($method, $params, $timestamp, $appKey, $secretKey)
    {
        ksort($params);
        $param_json = json_encode($params, 320);

        // 计算签名
        $str = "app_key" . $appKey . "method" . $method . "param_json" . $param_json . "timestamp" . $timestamp . "v" . '2';
        $md5_str = $secretKey . $str . $secretKey;
        return md5($md5_str);
    }

    /**
     * @param $result
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\OrderException
     */
    private function handleErrorCode($result)
    {
        DyClient::handleErrorCode($result);
    }

    /**
     * 获取平台退款地址列表
     * @param $shopId
     * @return mixed
     */
    public function getRefundRejectAddress($shopId)
    {
        // TODO: Implement getRefundRejectAddress() method.
    }
}
