<?php

namespace App\Services\Payment;

use App\Constants\ErrorConst;
use App\Constants\PaymentConst;
use App\Events\Subscription\SubscriptionOrderRefunded;
use App\Events\Subscription\SubscriptionOrderSucceeded;
use App\Exceptions\ErrorCodeException;
use App\Models\Payment\PayOrder;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use App\Services\PlatformOrder\SelfPlatformOrderService;
use App\Utils\Environment;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yansongda\Pay\Exceptions\GatewayException;
use Yansongda\Pay\Exceptions\InvalidConfigException;
use Yansongda\Pay\Exceptions\InvalidSignException;
use Yansongda\Pay\Pay;

/**
 * 支付服务
 */
class PaymentService
{

    protected  $selfPlatformOrderService;
    protected $payOrderService;

    public function __construct(SelfPlatformOrderService $selfPlatformOrderService,PayOrderService $payOrderService){
        $this->selfPlatformOrderService = $selfPlatformOrderService;
        $this->payOrderService = $payOrderService;
    }

    /**
     * 支付平台订单成功回调
     * @param PaymentCallbackRequest $paymentCallbackRequest
     * @return bool
     */
    public function payPlatformOrderSuccess(PaymentCallbackRequest $paymentCallbackRequest): bool
    {
        try {
            DB::transaction(function () use ($paymentCallbackRequest) {
                $outTradeNo = $paymentCallbackRequest->outTradeNo;
                $actualAmount = $paymentCallbackRequest->actualAmount;
                $orderAmount = $paymentCallbackRequest->orderAmount;
                $businessOrderArray = self::parseBusinessOrder($outTradeNo);
                $orderType= $businessOrderArray['orderType'];
                if($orderType!=PaymentConst::PAYMENT_ORDER_TYPE_PT){
                    throw_error_code_exception(ErrorConst::PARAM_ERROR,null, '订单类型错误');
                }
                $businessOrderNo = $businessOrderArray['businessOrderNo'];
                $businessOrderId = $businessOrderArray['businessOrderId'];
                $platformOrder = $this->selfPlatformOrderService->getByOrderId($businessOrderNo);
                if($platformOrder->status == PlatformOrder::STATUS_SUCCESS){
                    Log::info("订单已经支付成功，无需重复处理",["orderNo"=>$businessOrderNo]);
                    return true;
                }
                $shopId = $platformOrder->shop_id;
                //目前只支持平台订单支付成功回调
                $this->selfPlatformOrderService->paySuccess($businessOrderNo, $orderAmount,$paymentCallbackRequest->payAt);
                $payOrderRequest = new PayOrderRequest();
                $paySetting = new PaySetting();
                $paymentType = $paymentCallbackRequest->payType;
                $paySetting->appId = $paymentCallbackRequest->appId;
                $paySetting->id = 1;
                $paySetting->appType=$paymentType;
                $payOrderRequest->userId = $platformOrder->user_id;
                $payOrderRequest->businessOrderNo = $businessOrderNo;
                $payOrderRequest->businessOrderId = $businessOrderId;
                $payOrderRequest->businessType = PaymentConst::BUSINESS_TYPE_MAP[$orderType];
                $payOrderRequest->tradeType =PaymentConst::TRADE_TYPE_NATIVE;
                $payOrderRequest->mchid = $paymentCallbackRequest->mchid;
                $payOrderRequest->openId = $paymentCallbackRequest->buyerId;
                $payOrderRequest->description = $paymentCallbackRequest->subject;
                $payOrderRequest->actualAmount = $actualAmount;
                $payOrderRequest->orderAmount = $orderAmount;
                $payOrderRequest->fee = $paymentCallbackRequest->fee;
                $payOrderRequest->orderStatus = PayOrder::ORDER_STATUS_SUCCESS;
                $payOrderRequest->refundStatus = PayOrder::REFUND_STATUS_UNREFUND;
                $payOrderRequest->payAt=$paymentCallbackRequest->payAt;
                $payOrderRequest->platformOrderNo=$paymentCallbackRequest->tradeNo;
                $this->payOrderService->createPayOrder($payOrderRequest, $paySetting);


                return true;



            });

            return true;
        }catch (\Exception $exception){
            Log::error("支付回调异常：",["messages"=>$exception->getMessage(),"trace"=>$exception->getTraceAsString()]);
            return false;
        }
    }

    /**
     * @throws InvalidSignException
     * @throws InvalidConfigException
     * @throws GatewayException
     * @throws ErrorCodeException
     */
    function platformOrderRefund(int $platformOrderId,string $refundAmount):void{
        $platformOrder = $this->platformOrderRefundValidate($platformOrderId);
        $payOrder = $this->payOrderService->findByBusinessOrderId(PaymentConst::BUSINESS_TYPE_SELF_PLATFORM, $platformOrderId);

        if(empty($payOrder)){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null, '支付订单不存在');
        }
        $order = [
            'trade_no' => $payOrder->platform_order_no,
            'refund_amount' => $refundAmount,
            'out_request_no'=>strrev($platformOrder->order_no)
        ];

        $result = Pay::alipay(self::alipayConfig())->refund($order);
        Log::info("退款结果：",["result"=>$result]);
        DB::transaction(function () use ($platformOrder,$payOrder,$result,$refundAmount){
            $platformOrder->status = PlatformOrder::STATUS_CLOSE;
            $platformOrder->refund_fee = bcmul($refundAmount,"100");
            $platformOrder->save();
            $shop = Shop::find($platformOrder->shop_id);
            $payOrder->refund_amount = $refundAmount;
            if(bccomp($payOrder->refundable_amount,$refundAmount,2)==0) {
                $payOrder->refund_status = PayOrder::REFUND_STATUS_ALL_REFUND;
            }else{
                $payOrder->refund_status = PayOrder::REFUND_STATUS_PART_REFUND;
            }
            $payOrder->save();
            event(new SubscriptionOrderRefunded($platformOrder));
        });


    }



    /**
     * 用_分割，第一位是订单类型，第二位是业务订单NO，第三位是支付平台订单Id
     * 解析业务订单Id或者订单NO
     * @param string $outTradeNo
     * @return array{orderType:string,businessOrderNo:string,businessOrderId:int}
     */
    public static function parseBusinessOrder(string $outTradeNo):array{

        $arr = explode("_", $outTradeNo);

        return [
            'orderType' => $arr[0],
            'businessOrderNo' => $arr[1]??'',
            'businessOrderId' => $arr[2]??''
        ];
    }

    public static function appId(string $paymentType){
        return  config("pay".$paymentType.'.app_id');
    }

    /**
     * @param string $orderType
     * @param string|null $businessOrderNo
     * @param string|null $payPlatformOrderId
     * @return string 生成支付平台订单号
     */
    public static function genOuterTradeNo(string $orderType, ?string $businessOrderNo="", ?string $payPlatformOrderId=""): string
    {

        $returnStr = $orderType . "_" . $businessOrderNo;
        if(!empty($payPlatformOrderId)){
            $returnStr .= "_" . $payPlatformOrderId;
        }
        return $returnStr;
    }

    public function findByBusinessOrderId(string $orderType,int $businessOrderId): ?PayOrder{

        return $this->payOrderService->findByBusinessOrderId(PaymentConst::BUSINESS_TYPE_MAP[$orderType],  $businessOrderId);
    }


    /**
     * 获取支付宝配置
     * @return array
     */
    public static function  alipayConfig():array {
        return   config('pay.alipay');
    }

    /**
     * 退款定价
     * 1. 检查当前时间和订单里面的服务订购的时间，如果小于7天
     * 比如 1号订购的  服务开始时间是2号 那么（服务开始时间-退款申请时间（在2-8号的时间段内））小于7都可以全额退款 2-8号都可以可以按全额退款
     *  可能是负数，比如提前10天订购了,退款的申请的时间还没开始服务，那么全额退款
     * 如果大于7，那么退款金额=支付金额*（退款申请时间-服务开始时间）/(服务结束时间-服务开始时间)，就是按剩余服务的时间换算以后退款
     *
     * @param  PlatformOrder  $platformOrder
     * @param  string  $refundApplyAt
     * @return string
     * @throws ErrorCodeException
     */
    public function platformOrderRefundPricing(PlatformOrder $platformOrder,string $refundApplyAt=""):string{
        $cycleStartAt = $platformOrder->cycle_start_at;
        if(empty($refundApplyAt)){
            $refundApplyAt=date("Y-m-d H:i:s");
        }
        $cycleStartEnd = $platformOrder->cycle_end_at;
        //比较结束时间和当前时间，如果当前时间大于结束时间，抛出异常
        if (strtotime($cycleStartEnd) < strtotime($refundApplyAt)) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null, '退款申请时间不能大于服务结束时间');
        }
        $refundApplyTime = strtotime($refundApplyAt);
        $serviceStartTime = strtotime($cycleStartAt);
        $refundFee = "0";

        //如果小于7天，全额退款
        if (($refundApplyTime - $serviceStartTime) < 7 * 24 * 60 * 60) {
            $refundFee =bcdiv($platformOrder->pay_fee,"100",2);
        }
        else{
            $serviceEndTime = strtotime($platformOrder->cycle_end_at);
            $refundFee=bcdiv(bcmul($platformOrder->pay_fee,  ($serviceEndTime - $refundApplyTime),2) ,100* ($serviceEndTime - $serviceStartTime));
        }
        Log::info("退款定价", ["refundApplyTime" => $refundApplyAt, "serviceStartTime" => $cycleStartAt, "refundFee" => $refundFee]);
        return $refundFee;




    }

    /**
     * 退款校验
     * @param int $platformOrderId
     * @param int|null $shopId
     * @return PlatformOrder
     * @throws ErrorCodeException
     */
    public function platformOrderRefundValidate(int $platformOrderId,?int $shopId=null): PlatformOrder
    {
        $platformOrder = $this->selfPlatformOrderService->getById($platformOrderId,$shopId);
        if (empty($platformOrder)) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR, null, '平台订单不存在');
        }
        if ($platformOrder->status != PlatformOrder::STATUS_SUCCESS) {
            throw_error_code_exception(ErrorConst::PARAM_ERROR, null, '订单未支付成功，不能退款');
        }
        return $platformOrder;
    }

    /**
     * 判断是否为开发模式
     * @return bool
     */
    public static function isDev(): bool
    {
        return config('pay.mode') == 'dev';
    }
}
