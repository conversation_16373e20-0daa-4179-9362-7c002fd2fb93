<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/3/15
 * Time: 17:10
 */

namespace App\Services;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Events\Orders\OrderAllocation4PrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\CustomGroup;
use App\Models\CustomizeOrder;
use App\Models\DeliveryRecord;
use App\Models\Order;
use App\Models\Package;
use App\Models\PrintRecord;
use App\Models\Shop;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\Client\DyClient;
use App\Services\Order\OrderServiceManager;
use App\Services\Waybill\WaybillServiceManager;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Models\ShopBind;

class OpenApiV3Service
{
    protected $appId = '';

    /**
     * 订单批量设置
     * @param $noList
     * @param $shopId
     * @param $flag
     * @param $remark
     * @param array $data
     * @return array
     * <AUTHOR>
     */
    public function orderBatchSet($noList, $shopId, $flag, $remark, array $data): array
    {
        $list = [];
        foreach ($noList as $tid) {
            try {
                $isSuccess = false;
                $errorMsg = '';
                $order = Order::query()
                    ->where('tid', $tid)
                    ->where('shop_id', $shopId)
                    ->orWhere('tid', $tid . 'A')->first();
                if (empty($order)) {
                    throw_error_code_exception(StatusCode::ORDER_NOT_FOUND);
                }
                $shop = $order->shop;
                $userId = $order->user_id;
                $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                $orderService->setUserId($userId);
                $orderService->setShop($shop);

                //请求平台修改留言备注
                $res = $orderService->sendEditSellerRemark($order->tid, $flag, $remark);
                if (!$res) {
                    $errorMsg = '请求平台失败';
                    continue;
                }

                Order::query()
                    ->where('id', $order->id)
                    ->update($data);
                $isSuccess = true;
            } catch (\Exception $exception) {
                $errorMsg = $exception->getMessage();
            } finally {
                $list[] = [
                    'orderSn' => $tid,
                    'errorMsg' => $errorMsg,
                    'isSuccess' => $isSuccess,
                ];
            }
        }
        return $list;
    }

    /**
     * @param $shop
     * @param $pageSize
     * @param $page
     * @return array
     * <AUTHOR>
     */
    public function getGoodsList($shop, $pageSize, $page): array
    {
        $orderService = OrderServiceManager::create();
        $orderService->setShop($shop);
        $list = $orderService->getGoodsList($pageSize, $page);
        $newList = [];

        foreach ($list as $index => $item) {
            $arr = array_only($item, [
                'num_iid',
                'outer_goods_id',
                'goods_title',
                'goods_pic',
                'is_onsale',
                'goods_created_at',
                'goods_updated_at',
            ]);
            foreach ($item['skus'] as $skus) {
                $arr['skus'][] = array_only($skus, [
                    'sku_id',
                    'sku_value',
                    'outer_id',
                    'outer_goods_id',
                    'sku_pic',
                    'is_onsale',
                ]);
            }
            $newList[] = $arr;
        }

        return $newList;
    }

    public function getOrderList($shop, $startTime, $endTime, $flag, $remark, $page, $pageSize, $orderStatus)
    {
        $shopId = $shop->id;

        $conditions = [];
        if ($flag) {
            $conditions[] = ['seller_flag', $flag];
        }
        if (!empty($remark)) {
            $conditions[] = ['seller_memo', 'like', '%' . $remark . '%'];
        }
        $conditions[] = ['shop_id', $shopId];
        $conditions[] = ['order_created_at', '>=', date('Y-m-d', $startTime)];
        $conditions[] = ['order_created_at', '<=', date('Y-m-d', $endTime) . ' 23:59:59'];
        switch ($orderStatus) {
            case 1: //未发货
                $conditions[] = ['order_status', Order::ORDER_STATUS_PAYMENT];
                $conditions[] = ['refund_status', Order::REFUND_STATUS_NO];
                break;
            case 2: //已发货
                $conditions[] = ['order_status', Order::ORDER_STATUS_DELIVERED];
                $conditions[] = ['refund_status', Order::REFUND_STATUS_NO];
                break;
            default:
                $conditions[] = ['order_status', Order::ORDER_STATUS_PAYMENT];
                $conditions[] = ['refund_status', Order::REFUND_STATUS_NO];
                break;
        }

        $count = Order::query()->where($conditions)->count('*');
        $data = [
            'page' => intval($page),
            'pageSize' => $pageSize,
            'totalPage' => 0,
            'totalSize' => 0,
            'orderList' => []
        ];
        if ($count <= 0) {
            return $data;
        }
        $offset = (intval($page) - 1) * $pageSize;
        $result = Order::query()->with('orderCipherInfo', 'orderItem', 'packages')->where($conditions)
            ->offset($offset)->limit($pageSize)->orderBy('order_updated_at', 'desc')->get();

        $data['totalSize'] = $count;
        $data['totalPage'] = ceil($count / $pageSize);
        foreach ($result as $order) {
            $data['orderList'][] = $this->formatOrderInfo($order);
        }


        return $data;
    }

    public function getOrderListByApi($shop, $startTime, $endTime, $page, $pageCursor, $pageSize, $orderStatus,$dateType="update")
    {
        $shopId = $shop->id;
        $redisLimitKey = 'getOrderListByApi:' . date('md') . ':' . $shopId;
        $incr = redis()->incr($redisLimitKey);
        if ($incr < 3) redis()->expire($redisLimitKey, 86400);
        if ($incr > 2000){
            throw_error_code_exception(StatusCode::SHOP_ORDER_GET_LIMIT, null, "该店铺当天获取订单次数不能超过2000次！");
        }
        $orderService = OrderServiceManager::create();
        $orderService->setShop($shop);
        $isFirstPull = true;
        if ($orderStatus != 1) { // 全部
            $isFirstPull = false;
        }
        $orderService->setPage($page);
        $orderService->setPageCursor($pageCursor);
        $orderService->setPageSize($pageSize);
        if($dateType=="update"){
            $list = $orderService->getTradesOrderByIncr($startTime, $endTime, $isFirstPull);
        }
        else{
            $list = $orderService->getTradesOrder($startTime, $endTime, $isFirstPull);
        }

        Log::info('batchSave before',['count'=>count($list)]);
        Order::batchSave($list, $shop->user_id, $shopId);
        Log::info('batchSave after',['count'=>count($list)]);

        $orderTids = array_pluck($list, 'tid');
        $data = [
            'page' => intval($page),
            'pageSize' => $pageSize,
            'pageCursor' => $orderService->getPageCursor(),
            'total' => $orderService->getOrderTotalCount(),
//            'hasNext' => false,
            'orderList' => []
        ];
        $result = \App\Models\Fix\Order::query()->with('orderCipherInfo', 'orderItem')->whereIn('tid', $orderTids)
            ->limit(100)->orderBy('order_updated_at', 'desc')->get();
        Log::info('select done', ['count' => count($result)]);

        foreach ($result as $order) {
            $data['orderList'][] = $this->formatOrderInfo($order);
        }
        $data['hasNext'] = $orderService->hasNext;
        return $data;
    }

    public function getOrderInfo($shop, array $orderSnsArr)
    {
        $shopId = $shop->id;
        $conditions[] = ['shop_id', $shopId];
        $result = \App\Models\Fix\Order::query()->with(['orderCipherInfo', 'orderItem', 'packages'])
            ->where($conditions)->whereIn('tid', $orderSnsArr)->get();
        $data = [];
        if (!empty($result)) {
            foreach ($result as $order) {
                $data[] = $this->formatOrderInfo($order);
            }
        }
        return $data;
    }

    /**
     * @param $order
     * @param array $data
     * @return array
     * <AUTHOR>
     */
    public  function formatOrderInfo($order): array
    {
        $items = [];
        $orderItems = $order['orderItem'] ?? '';
        if (empty($orderItems)) {
            $orderItems = $order['items'];
        }
        foreach ($orderItems as $index => $orderItem) {
            is_object($orderItem) && $orderItem = $orderItem->toArray();
            $items[] = array_only($orderItem, [
                'payment',
                'goods_price',
                'goods_pic',
                'goods_title',
                'goods_num',
                'num_iid',
                'sku_id',
                'sku_value',
                'outer_iid',
                'outer_sku_iid',
                'refund_status',
                'product_no',
            ]);
        }
        $oaid = '';
        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::TAOBAO, PlatformConst::JD,PlatformConst::KS]) && !empty($order['orderCipherInfo'])) {
            $receiverName = $order['orderCipherInfo']['receiver_name_mask'];
            $receiverPhone = $order['orderCipherInfo']['receiver_phone_mask'];
            $address = $order['orderCipherInfo']['receiver_address_mask'];
            $appid = $this->appId;
            $whitelist = explode(',', config('app.open_api_whitelist', ''));
            // 白名单不限制
            if (!empty($whitelist) && in_array($appid, $whitelist)) {
                $oaid = $order['orderCipherInfo']['oaid'] ?? '';
            }

        } else {
            $receiverName = dataDesensitizationForOpenApi($order['receiver_name'], 1);
            $receiverPhone = dataDesensitizationForOpenApi($order['receiver_phone'], 3, 4);
            $address = dataDesensitizationForOpenApi(addressDesensitization($order['receiver_address']), -4);
        }
        $packages = [];
//        if (!empty($order['packages'])) {
//            foreach ($order['packages'] as $index => $package) {
//                $packageArr = $package->toArray();
//                $arr = array_only($packageArr, [
//                    'id',
//                    'waybill_code',
//                    'wp_code',
////                'waybill_status',
//                    'error_info',
//                ]);
//                $arr['createdTime'] = $packageArr['created_at'];
//                $arr['recycledTime'] = $packageArr['recycled_at'];
//                $packages[] = $arr;
//            }
//        }

        $data = [
            'receiverName' => $receiverName, //收件人姓名脱敏处理
            'receiverPhone' => $receiverPhone, //收件人手机脱敏处理
            'province' => $order['receiver_state'],
            'city' => $order['receiver_city'],
            'district' => $order['receiver_district'],
            'town' => $order['receiver_town'],
            'address' => $address,
            'orderSn' => $order['tid'],
//                'goodsName'     => $order['goods_title'],
//                'goodsSpec'     => $order['sku_value'],
            'goodsNum' => $order['num'],
            'payAmount' => $order['payment'],
            'isPreSale' => $order['is_pre_sale'],// 是否预售
            'payTime' => $order['pay_at'],
            'lastShipTime' => $order['promise_ship_at'],// 最晚发货时间
            'confirmTime' => $order['pay_at'], //成团时间
            'remark' => implode(",", json_decode($order['seller_memo'], true)), // 卖家备注
            'remarkTag' => $order['seller_flag'], // 卖家旗帜
            'buyerMessage' => $order['buyer_message'], // 买家留言
            'buyerNick' => $order['buyer_nick'], // 买家昵称
            'orderStatus' => $order['order_status'],
            'createTime' => $order['order_created_at'],
            'updateTime' => $order['order_updated_at'],
            'refundStatus' => $order['refund_status'],
            'storeId' => $order['store_id'] ?? '',
            'idSopShipmentType' => $order['send_type'] ?? '',
            'postFee'=>$order['post_fee']??null,
            'discountFee'=>$order['discount_fee']??null,
            'orderCipherInfo'=>$order['orderCipherInfo'],
            'orderItems' => $items,
            'packages' => $packages,
        ];
        if (!empty($oaid)) {
            $data['oaid'] = $oaid;
        }
        if (empty($order['packages'])) {
            unset($data['packages']);
        }
        return $data;
    }


    /**
     * 抖音再次取号里面的面单号，必须是同一商家的。
     * @param $waybillPrintParams
     * @param $wpCode
     * @return mixed
     * @throws ErrorCodeException
     */
    public function getSingleShopByWaybillPrint($waybillPrintParams, $wpCode)
    {
        $waybillCodes = array_column($waybillPrintParams, 'waybillCode');
        if (empty($waybillCodes)) {
            return throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }
        $wayBillCollection = WaybillHistory::query()->where([
            'auth_source' => Waybill::AUTH_SOURCE_DY,
            'wp_code' => $wpCode
        ])->whereIn('waybill_code', $waybillCodes)->get();
        if ($wayBillCollection->isEmpty()) {
            return throw_error_code_exception(StatusCode::WAYBILL_OR_WP_CODE_NOT_FOUND);
        }
        $shopIds = $wayBillCollection->map(function ($item) {
            return $item['shop_id'];
        })->unique()->all();
        if (count($shopIds) > 1) {
            return throw_error_code_exception(StatusCode::NOT_SUPPORT_MULTI_SHOPS);
        }
        \Log::info("shopIds", $shopIds);
        $shopId = $shopIds[0];
        $shop = Shop::find($shopId);
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
        }
        return $shop;
    }

    /**
     * 过滤订单找到去全部的shopId
     * 1. 一批只能是一个shopId
     * 2. 可以没有shopId,但必须要存在
     * @param $orderSns
     * @return mixed
     * @throws ErrorCodeException
     */
    public function getSingleShopByOrderSns($orderSns)
    {
        $shopIds = Order::query()->whereIn('tid', $orderSns)->get()->map(function ($value, $key) {
            return $value['shop_id'];
        })->unique()->all();
        if (sizeof($shopIds) > 1) {
            return throw_error_code_exception(StatusCode::NOT_SUPPORT_MULTI_SHOPS);
        }
        $shop = null;
        if (sizeof($shopIds) == 1) {
            $shopId = $shopIds[0];
            $shop = Shop::find($shopId);
            if (empty($shop)) {
                return throw_error_code_exception(StatusCode::SHOP_UN_EXIST);
            }
        }
        return $shop;

    }


    /**
     * @param $waybillPrintParams
     * @param $shop
     * @param $wpCode
     * @return array
     * <AUTHOR>
     */
    public function handleWaybillPrint($waybillPrintParams, $shop, $wpCode): array
    {
        $printDataList = [];
        foreach ($waybillPrintParams as $waybillPrintParam) {
            $waybillPrintParam['wp_code'] = $wpCode;
            $waybillPrintParam['waybill_code'] = $waybillPrintParam['waybillCode'];
            $waybillCode = $waybillPrintParam['waybillCode'];
            $orderSn = '';
            $printData = '';
            $errConst = [];
            $hasHistory = WaybillHistory::query()->where([
                'auth_source' => Waybill::AUTH_SOURCE_DY,
                'waybill_code' => $waybillCode,
                'wp_code' => $wpCode
            ])->first();
            if (empty($hasHistory)) {
                $errConst = ErrorConst::WAYBILL_NOT_FOUND;
            } else {
                $printData = json_decode($hasHistory['print_data'], true);
                $waybillPrintParam['addData'] = $printData['addData'] ?? "";
                $waybillPrintParam['templateUrl'] = $printData['templateUrl'] ?? "";
                $waybillPrintParam['templateURL'] = $printData['templateURL'] ?? "";
                $waybillService = WaybillServiceManager::init(Waybill::AUTH_SOURCE_DY, $shop->access_token);
                \Log::info("waybillPrintParam=", [$waybillPrintParam]);
                $res = $waybillService->getPrintData($waybillPrintParam);

                $appKey = config('socialite.dy.client_id');
                $secretKey = config('socialite.dy.client_secret');
                $client = (new DyClient($appKey, $secretKey))->setAccessToken($shop->access_token);
                $requestData = $client->buildRequestData([], 'logistics/getShopKey');
                $paramsStr = urldecode(http_build_query($requestData));
                if (!array_key_exists('err_no', $res)) {
                    $print_data = [
                        'encryptedData' => $res['print_data'],
                        'templateUrl' => $waybillPrintParam['templateUrl'],
                        'templateURL' => $waybillPrintParam['templateURL'],
                        'addData' => $waybillPrintParam['addData'],
                        'signature' => $res['sign'],
                        'params' => $paramsStr
                    ];
                    $printData = json_encode($print_data);
                } else {
                    $printData = '';
                    $errConst = [0, json_encode($res)];
                }
            }
            $printDataList[] = [
                'orderSn' => $orderSn,
                'waybillCode' => $waybillCode,
                'wpCode' => $wpCode,
                'code' => $errConst[0] ?? 1,
                'printData' => $printData,
                'errorMsg' => $errConst[1] ?? '',
            ];
        }
        return $printDataList;
    }

    /**
     * @param array $params
     * @param Request $request
     * @param $waybill
     * @param $platform
     * @param $appId
     * @param $branchCode
     * @return array
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function handleFreeOrder(array $params, $shop, $waybill, $platform, $appId, $branchCode): array
    {
        // 创建自由打印订单
        $data = [
            "sender_name" => $params['senderName'],
            "sender_phone" => $params['senderMobile'],
            "sender_province" => $params['senderProvince'],
            "sender_city" => $params['senderCity'],
            "sender_district" => $params['senderTown'],
            "sender_detailaddress" => $params['senderDetail'],
            "receiver_name" => $params['receiverName'],
            "receiver_phone" => $params['receiverMobile']??'',
            "receiver_tel" => $params['receiverPhone']??'',
            "receiver_province" => $params['receiverProvince'],
            "receiver_city" => $params['receiverCity'],
            "receiver_district" => $params['receiverTown'] ??'',
            "receiver_town" => $params['receiverStreet'] ?? "",
            "receiver_address" => $params['receiverAddress'],
            'goods_info' => json_encode($params['goodsInfo'], JSON_UNESCAPED_UNICODE),
            'production_type' => '商品详细',
            'wp_code' => $params['wpCode'],
            'order_no' => $params['orderSn'] ?? null,
        ];

        if (empty($data['receiver_phone']) && empty($data['receiver_tel'])) {
            throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }
        if (!isset($data['goods_info']) || empty($data['goods_info'])) {
            throw_error_code_exception(StatusCode::PARAMS_ERROR);
        }

//        if ($data['order_no']) {
//            $result = CustomizeOrder::updateOrCreate(['order_no' => $data['order_no']], array_merge($data, [
////                'user_id'    => $request->user_id,
//                'shop_id' => $shop->id,
//                'order_type' => CustomizeOrder::ORDER_TYPE_SINGLE,
//            ]));
//        } else {
        $result = CustomizeOrder::create(array_merge($data, [
//                'user_id'    => $request->user_id,
            'shop_id' => $shop->id,
            'order_type' => CustomizeOrder::ORDER_TYPE_SINGLE,
        ]));
//        }

        if (!$result) {
            throw_error_code_exception(StatusCode::SYSTEM_ERROR);
        }
        if (!empty($params['packageId'])) {
            $result['request_id'] = $params['packageId'];
        }
        if (!empty($params['isChildParentOrder'])) {
            $result['is_child_parent_order'] = $params['isChildParentOrder'];
        }
        if (!empty($params['childParentPackagesCount'])) {
            $result['child_parent_packages_count'] = $params['childParentPackagesCount'];
        }

        $waybillType = $params['waybillType'];
        $printData = $this->getPrintDataAndWaybillForOpenApi(
            $shop->user_id,
            $shop->id,
            $result,
            $waybill,
            $waybillType,
            $platform,
            $appId,
            $branchCode
        );
        $printData['orderSn'] = (string)$printData['orderSn'];
        return $printData;
    }

    /**
     * @param $platform
     * @param $wpShop
     * @param $ownerId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function getWaybill($platform, $wpShop, $ownerId)
    {
        if ($platform == Waybill::OPEN_API_DEFAULT) {
            $waybill = $wpShop;
            if (empty($waybill) || $waybill->auth_status != Shop::AUTH_STATUS_SUCCESS) {
                throw_error_code_exception(StatusCode::SHOP_WAYBILL_ERROR);
            }
        } else {
            $params = ['shop_id' => $wpShop->id, 'auth_source' => $platform];
            if (!empty($ownerId)) {
                $params = ['shop_id' => $wpShop->id, 'auth_source' => $platform, 'owner_id' => $ownerId];
            }
            $waybill = Waybill::query()->where($params)->first();
        }

        if (empty($waybill)) {
            throw_error_code_exception(StatusCode::WAYBILL_UN_EXITS);
        }
        return $waybill;
    }

    public function checkApiShopBind($appId, $shopId, $checkIsWaybill = false)
    {
        $apiShopBind = ApiShopBind::firstByAppIdShopId($appId, $shopId);
        if (empty($apiShopBind)) {
            throw_error_code_exception(StatusCode::SHOP_NOT_BOUND);
        }
        if ($checkIsWaybill && $apiShopBind->is_waybill_shop == ApiShopBind::IS_WAYBILL_SHOP_NO) {
            throw_error_code_exception(StatusCode::SHOP_BOUND_NOT_WAYBILL);
        }
    }

    public function checkApiShopBindWithAuthed($appId, $shopId)
    {
        $appIds = ApiAuth::getAllAuthedAppId($appId);
        $appIds [] = $appId;
        \Log::info("allSignAppId and shopIds", [$appIds, $shopId]);
        $apiShopBind = ApiShopBind::firstByAppIdShopIds($appIds, $shopId);
        if (empty($apiShopBind)) {
            throw_error_code_exception(StatusCode::SHOP_NOT_BOUND);
        }
        return true;
    }

    public function handleEncryptionPrint(int $userId, int $shopId, $params, $list, $waybill, $waybillType, $platform,
                                              $appId, $branchCode)
    {
        // 发货地址 默认取订单里的
        $addressInfo = [
            'province' => $params['senderProvince'],
            'city' => $params['senderCity'],
            'district' => $params['senderTown'] ?? '',
            'address' => $params['senderDetail']
        ];

        // 查询网点信息 传了网点地址取网点里的
        if (!empty($branchCode)) {
//            $authSource = $platform == Waybill::OPEN_API_DEFAULT ? Waybill::AUTH_SOURCE_DY : $waybill->auth_source;
            $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $waybillData = $waybillService->waybillSubscriptionQuery();
            $addressInfo = Order::getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $params['wpCode']);
            if (empty($addressInfo)) {
                return throw_error_code_exception(StatusCode::COMPANY_ERROR);
            }
        }
        $addressInfo['mobile'] = $params['senderMobile'];
        $addressInfo['sender_name'] = $params['senderName'];
        $printDataList = [];
        foreach ($list as $index => $item) {
            // 创建加密打印订单
            $order = [
                "sender_name" => $params['senderName'],
                "sender_phone" => $params['senderMobile'],
                "sender_province" => $params['senderProvince'],
                "sender_city" => $params['senderCity'],
                "sender_district" => $params['senderTown'],
                "sender_detailaddress" => $params['senderDetail'],
                "receiver_name" => '',
                "receiver_phone" => '',
                "receiver_province" => $item['receiverProvince'],
                "receiver_city" => $item['receiverCity'],
                "receiver_district" => $item['receiverTown'],
                "receiver_town" => $item['receiverStreet'] ?? '',
                "receiver_address" => $item['receiverAddress'] ?? '',
                "receiver_zip" => '',
                "buyer_message" => '',
                'goods_info' => json_encode($item['goodsInfo'], JSON_UNESCAPED_UNICODE),
                'production_type' => '商品详细',
                'wp_code' => $params['wpCode'],
                'order_cipher_info' => [
                    'oaid' => $item['ciphertext'],
                    'receiver_address_ciphertext' => '',
                    'receiver_name_ciphertext' => '',
                    'receiver_phone_ciphertext' => '',
                ],
                'shop_id' => $shopId,
                'tid' => $item['orderSn'],
                'id' => $index,
            ];


            if (!isset($order['goods_info']) || empty($order['goods_info'])) {
                throw_error_code_exception(StatusCode::PARAMS_ERROR);
            }

            $printData = self::newWaybillHistoriesForOpenApi(
                $userId,
                $shopId,
                $order,
                $addressInfo,
                $order['wp_code'],
                $waybill,
                $waybillType,
                $platform,
                $appId
            );
            $printData['orderSn'] = (string)$printData['orderSn'];
            $printDataList[] = $printData;
        }

        return $printDataList;
    }

    public function newWaybillHistoriesForOpenApi($userId, $shopId, $order, $sender, $wpCode, $waybill, $waybillType, $platform, $appId)
    {
        // 查询标准模板
        $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
        $redisKey = $authSource . $wpCode;
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $waybillTemp = Cache::remember($redisKey, 10, function () use ($waybillService, $wpCode, $waybill) {
            return $waybillService->getCloudPrintStdTemplates($wpCode);
        });
        // 获取运单号
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $packages = $waybillService->assemWaybillPackagesForOpenApi($platform, $sender, [collect($order)], $wpCode, $waybillType, $waybillTemp, $packageNum = 1);

        //错误信息
        if (!is_string($packages[$order['id']][0]) && is_array($packages[$order['id']][0]) && (isset($packages[$order['id']][0]['err_no']) ? $packages[$order['id']][0]['err_no'] == 0 : true)
            && (($platform == Waybill::OPEN_API_DEFAULT && config('app.platform') == 'dy') ? (!isset($packages[$order['id']][0]['data']['ebill_infos'])) : true)
            && (($platform == Waybill::OPEN_API_DEFAULT && config('app.platform') == 'ks') ? (!isset($package[0]['result'])) : true)) {

            $history = WaybillHistory::create([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'order_id' => $order['id'],
                'order_no' => $order['order_no'] ?? '',
                'auth_source' => $authSource,
                'parent_waybill_code' => $packages[$order['id']][0]['parent_waybill_code'] ?? "",
                'waybill_code' => $packages[$order['id']][0]['waybill_code'],
                'wp_code' => $wpCode,
                'print_data' =>"{}",// $packages[$order['id']][0]['print_data'],
                'receiver_province' => $order['receiver_province'],
                'receiver_city' => $order['receiver_city'],
                'receiver_district' => $order['receiver_district'],
                'receiver_name' => $order['receiver_name'],
                'receiver_phone' => $order['receiver_phone'],
                'receiver_address' => $order['receiver_address'],
                'app_id' => $appId,
            ]);
//            PrintRecord::create([
//                'user_id' => $userId,
//                'shop_id' => $shopId,
//                'order_id' => $order['id'],
//                'history_id' => $history->id,
//                'order_no' => $order['order_no'] ?? '',
//                'waybill_code' => $packages[$order['id']][0]['waybill_code'],
//                'wp_code' => $wpCode,
//                'receiver_province' => $order['receiver_province'],
//                'receiver_city' => $order['receiver_city'],
//                'receiver_district' => $order['receiver_district'],
//                'receiver_town' => $order['receiver_town'],
//                'receiver_address' => $order['receiver_address'],
//                'receiver_name' => $order['receiver_name'],
//                'receiver_zip' => $order['receiver_zip'],
//                'receiver_phone' => $order['receiver_phone'],
//                'buyer_remark' => $order['buyer_message'],
//                'app_id' => $appId,
//            ]);

            $printData = [
                'orderSn' => $order['id'],
                'orderNo' => $order['order_no'] ?? "",
                'waybillCode' => $packages[$order['id']][0]['waybill_code'],
                'parentWaybillCode' => $packages[$order['id']][0]['parent_waybill_code'] ?? "",
                'printData' => $packages[$order['id']][0]['print_data'],
                'code' => 1,
                'errorMsg' => '',
            ];
        } else {
            //取号错误信息
            $printData = [
                'orderSn' => $order['id'],
                'orderNo' => $order['order_no'] ?? "",
                'waybillCode' => '',
                'parentWaybillCode' => '',
                'printData' => '',
                'code' => 0,
                'errorMsg' => $packages[$order['id']][0] ?? "取号错误",
            ];
        }

        return $printData;
    }

    /**
     * 回收单号
     * @param $waybillHistory
     * @param $waybill
     * @param $platform
     * @return bool|string
     * @throws \Throwable
     * <AUTHOR>
     */
    public function recoveryWaybill($waybillHistory, $waybill, $platform)
    {
        try {
            $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $ret = $waybillService->wayBillCancelDiscard($waybillHistory->wp_code, $waybillHistory->waybill_code,$waybillHistory->platform_waybill_id??'');
            if (!$ret) {
                Log::error('waybill recovery failed !');
                return '取消失败';
            }

            \DB::transaction(function () use ($waybillHistory) {
                WaybillHistory::query()
                    ->where('id', $waybillHistory->id)
                    ->update(['waybill_status' => WaybillHistory::WAYBILL_RECOVERY_YES]);

                Package::where('waybill_code', $waybillHistory->waybill_code)
                    ->update([
                        'recycled_at' => Carbon::now()
                    ]);
                if ($waybillHistory->order_type == WaybillHistory::ORDER_TYPE_GENERAL) {
                    Order::query()->where('id', $waybillHistory->order_id)
                        ->update(['print_status' => Order::PRINT_STATUS_NO]);
                }

            });
        } catch (\Exception $e) {
            Log::error('recovery failed !', ['msg' => $e->getMessage(), 'express_no' => $waybillHistory->waybill_code]);
            return $e->getMessage();

        }

        return true;
    }

    public function deliverOrder($appId, $orderSn, $waybillCode, $wpCode, $shop)
    {
        $orderInfo = Order::query()->where(['tid' => $orderSn, 'shop_id' => $shop->id])->first();
        if (empty($orderInfo)) {
            throw new ApiException(ErrorConst::ORDER_NOT_FOUND);
        }
        if($orderInfo->order_status==Order::ORDER_STATUS_CLOSE){
            Log::info('订单已取消，不能发货',[$orderInfo->tid]);
            throw new ApiException(StatusCode::ORDER_CANCELLED_NOT_DELIVER);
        }
        if($orderInfo->refund_status!=Order::REFUND_STATUS_NO){
            Log::info('订单有售后，不能发货',[$orderInfo->tid]);
            throw new ApiException(StatusCode::ORDER_HAS_REFUND_NOT_DELIVER);
        }
//        if (!empty($orderInfo->send_at)) {
//             已经发货成功不再做处理
//            throw new ApiException(ErrorConst::ORDER_SHIPPED)
//        }
        $shop = $orderInfo->shop;
        $userId = $orderInfo->user_id;
        $shopId = $orderInfo->shop_id;
        $orderService = OrderServiceManager::create();
        $orderService->setUserId($userId);
        $orderService->setShop($shop);
        //多包情况,取第一个快递号发货
        $expressNo = explode(',', $waybillCode)[0];
        $res = $orderService->deliveryOrdersForOpenApi($orderInfo->tid, $wpCode, $expressNo);
        if ($res !== true) {
            return $res;
        }

        //订阅物流信息
        if ($orderInfo->template &&
            in_array($orderInfo->template->auth_source, [PlatformConst::WAYBILL_PDD, PlatformConst::WAYBILL_PDD_WB])
        ) {
            $waybillService = WaybillServiceManager::init($orderInfo->template->auth_source);
            $waybillService->sendLogisticsTraceMsg($orderInfo);
        }

        DB::transaction(function () use ($orderInfo, $appId, $orderSn, $expressNo, $wpCode, $userId, $shopId) {
            $orderInfo->order_status = Order::ORDER_STATUS_DELIVERED;
            $orderInfo->send_at = date('Y-m-d H:i:s');
            if (is_null($orderInfo->express_code)) {
                $orderInfo->express_code = $wpCode;
            }
            if (is_null($orderInfo->express_no)) {
                $orderInfo->express_no = $expressNo;
            }
            if (!$orderInfo->save()) {
                throw new ApiException(ErrorConst::ORDER_SAVE_FAIL);
            }

            $history = WaybillHistory::where([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'waybill_code' => $expressNo,
                'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
            ])->first();
            $ret = DeliveryRecord::create([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'history_id' => $history ? $history->id : 0,
                'order_id' => $orderInfo->id,
                'order_no' => $orderInfo->tid,
                'waybill_code' => $expressNo,
                'wp_code' => $wpCode,
                'result' => true,
                'app_Id' => $appId,
            ]);
        });
        return true;
    }

    public function getPrintDataAndWaybill($userId, $shopId, $wpCode, $platform, $branchCode, $senderName, $sendMobile,
                                           $orderSnList, $waybillType, $batchNo, $packageNum, $waybillData, $waybill,
                                           $appId, $orderPrintParams = [],$productType=null)
    {
        $addressInfo = Order::getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $wpCode);
        if (empty($addressInfo)) {
            return throw_error_code_exception(StatusCode::COMPANY_ERROR);
        }
        StatisticsCost('获取 $addressInfo done');

        $addressInfo = array_merge($addressInfo, ['sender_name' => $senderName, 'mobile' => $sendMobile]);

        // 查询是否取过面单号
//        $hasWaybillHistories = self::hasWaybillHistoriesForOpenApi($userId, $shopId, $orderSnList, $wpCode, $waybill, $platform, $appId);
//        $newOrders = $hasWaybillHistories['new_orders'];
        $newOrders = [];
        $orderList = Order::query()->with('OrderCipherInfo')->whereIn('tid', $orderSnList)->get();
        $orderList = array_pluck($orderList, null, 'tid');
        foreach ($orderSnList as $orderSn) {
            $mainOrder = $orderList[$orderSn] ?? null;
            if (empty($mainOrder)) {
                \Log::warning("订单不存在", [$orderSn]);
                throw_error_code_exception(StatusCode::ORDER_NOT_FOUND,[ $orderSn]);
            }
            $mainOrder->mergeOrders = null;
            $newOrders[] = $mainOrder;
        }
        StatisticsCost('获取 $newOrders done');
        $failedData = $printData = [];

        if (!empty($newOrders)) {
            $packageIdArr = array_pluck($orderPrintParams, 'packageId', 'orderSn');
            $isChildParentOrderArr = array_pluck($orderPrintParams, 'isChildParentOrder', 'orderSn');
            $childParentPackagesCountArr = array_pluck($orderPrintParams, 'childParentPackagesCount', 'orderSn');
            foreach ($newOrders as $index => $newOrder) {
                if (isset($packageIdArr[$newOrder->tid])) {
                    $newOrder->request_id = $packageIdArr[$newOrder->tid];
                }
                if (isset($isChildParentOrderArr[$newOrder->tid])) {
                    $newOrder->is_child_parent_order = $isChildParentOrderArr[$newOrder->tid];
                }
                if (isset($childParentPackagesCountArr[$newOrder->tid])) {
                    $newOrder->child_parent_packages_count = $childParentPackagesCountArr[$newOrder->tid];
                }
            }
            //未取号取号处理
            $newWaybillHistories = Order::newWaybillHistoriesForOpenApi(
                $userId,
                $shopId,
                $platform,
                $newOrders,
                $addressInfo,
                $wpCode,
                $waybill,
                $batchNo,
                $waybillType,
                $packageNum,
                $appId,
                $productType
            );

            //取号失败信息
            if (count($newWaybillHistories['failed_data']) > 0) {
                $failedData = $newWaybillHistories['failed_data'];
            }
        }
        StatisticsCost('获取 $newWaybillHistories done');
        $printData = $newWaybillHistories['print_data'] ?? [];
        if (!$printData && !$failedData) {
            //throw new \Exception('订单获取打印数据异常！');
            return throw_error_code_exception(StatusCode::PRINT_ERROR);
        }

        return array_merge($printData, $failedData);
    }

    /**
     * @param $orderPrintParams
     * @param $platform
     * @param $waybill
     * @param $wpShop
     * @param $shop
     * @param $wpCode
     * @param string $branchCode
     * @param $senderInfo
     * @param $waybillType
     * @param $batchNo
     * @param $packageNum
     * @param string $appId
     * @return array
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Throwable
     * <AUTHOR>
     */
    public function handleOrderPrint($orderPrintParams, $platform, $waybill, $wpShop, $shop, $wpCode, string $branchCode,
                                     $senderInfo, $waybillType, $batchNo, $packageNum, string $appId,$productType=null): array
    {
        Log::info("处理订单取号");

        $orderSnList = array_pluck($orderPrintParams, 'orderSn');
        if (!empty($shop)) {
            $orderArray = array_map(function ($tid) {
                return ['tid' => $tid, 'id' => $tid]; // 这个 id 是索引用
            }, $orderSnList);
            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);
            $tradesOrder = $orderService->batchGetOrderInfo($orderArray);

            Order::batchSave($tradesOrder, $shop->user_id, $shop->id);
            $this->checkApiShopBindWithAuthed($appId, $shop->id);
        } else {
            $shopIds = Order::query()->whereIn('tid', $orderSnList)->get()->map(function ($value, $key) {
                return $value['shop_id'];
            })->unique()->all();
            if (sizeof($shopIds) > 1) {
                return throw_error_code_exception(StatusCode::NOT_SUPPORT_MULTI_SHOPS);
            }
            if (sizeof($shopIds) == 0) {
                return throw_error_code_exception(StatusCode::ORDER_NOT_FOUND_SHOP_ID);
            }
            $this->checkApiShopBindWithAuthed($appId, $shopIds[0]);
        }

        // 查询网点信息
        $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
        $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
        $waybillData = $waybillService->waybillSubscriptionQuery();
//        \Log::info("waybillData=", [$waybillData]);
//            $orderCount = Order::query()->whereIn('tid', $orderSnList)->whereIn('shop_id', $shopIds)->count();
//            if ($orderCount > 0) {
//                return throw_error_code_exception(StatusCode::ORDER_NOT_THIS_SHOP);
//            }

        $userId = $wpShop->user_id;
        $shopId = $wpShop->id;
        $printDataList = $this->getPrintDataAndWaybill($userId, $shopId, $wpCode, $platform, $branchCode,
            $senderInfo['senderName'], $senderInfo['senderMobile'], $orderSnList, $waybillType, $batchNo, $packageNum,
            $waybillData, $waybill, $appId, $orderPrintParams,$productType);
        return $printDataList;
    }

    /**
     * @param string $appId
     */
    public function setAppId(string $appId): void
    {
        $this->appId = $appId;
    }

    /**
     * 分配订单
     * @param $wpShop
     * @param $shop
     * @param array $orderList
     *<AUTHOR>
     */
    public function factoryAssign($wpShop, $shop, array $orderList)
    {
        $orderSnList = array_pluck($orderList, 'orderSn');
        $orderList = collect($orderList)->keyBy('orderSn');
        $orderInfoList = Order::query()->whereIn('tid', $orderSnList)->where('shop_id', $shop->id)->get();
        $factoryId = $wpShop->id;
        $successCounter = 0;
        foreach ($orderInfoList as $index => $orderInfo) {
            $orderInfo->factory_id = $factoryId;
            if (isset($orderList[$orderInfo->tid]['customPrintContent'])) {
                $orderInfo->custom_print_content = $orderList[$orderInfo->tid]['customPrintContent'];
            }
            if (isset($orderList[$orderInfo->tid]['customGroup'])) {
                $customGroup = $orderList[$orderInfo->tid]['customGroup'];
                CustomGroup::firstOrCreateByShopId($factoryId, $customGroup, $customGroup);
                $orderInfo->custom_group = $customGroup;
            }
            $orderInfo->save();
            $successCounter ++;
        }
        event(new OrderAllocation4PrintEvent($factoryId, $shop->id,ShopBind::TYPE_AGENT_PRINT_FACTORY_2_SHOP));
        return $successCounter;
    }

    /**
     * 分配订单取消
     * <AUTHOR>
     * @param $wpShop
     * @param $shop
     * @param array $orderList
     */
    public function factoryAssignCancel($wpShop, $shop, array $orderList)
    {
        $orderSnList = array_pluck($orderList, 'orderSn');
        $orderInfoList = Order::query()->whereIn('tid', $orderSnList)->where('shop_id', $shop->id)->get();
        $successCounter = 0;
        foreach ($orderInfoList as $index => $orderInfo) {
            $orderInfo->factory_id = 0;
            $orderInfo->custom_print_content = '';
            $orderInfo->custom_group = '';
            $orderInfo->save();
            $successCounter ++;
        }
        return $successCounter;
    }

    /**
     * 获取物流轨迹
     * @param $wpShop
     * @param array $waybillCodeList
     * <AUTHOR>
     */
    public function getTraceInfoList($wpShop, array $waybillCodeList)
    {
        $authSource = Waybill::getAuthSourceByPlatform(Waybill::OPEN_API_DEFAULT, null);
        $waybillService = WaybillServiceManager::init($authSource, $wpShop->access_token);
        $waybillHistoryList = WaybillHistory::query()->where('shop_id', $wpShop->id)
            ->whereIn('waybill_code', $waybillCodeList)->get()->keyBy('waybill_code');

        $traceInfoList = [];
        foreach ($waybillCodeList as $index => $waybillCode) {
            $trace_list = [];
            $code = 0;
            $errMsg = '';
            try {
                $waybillInfo = $waybillHistoryList[$waybillCode] ?? [];
                if (empty($waybillInfo)) {
                    throw_error_code_exception(StatusCode::WAYBILL_CODE_NOT_FOUND);
                }
                list($waybillData) = WaybillHistory::getWaybillDataByWaybillHistory($waybillInfo);
                $traceInfo = $waybillService->getOrderTraceList($waybillData);
                !empty($traceInfo['trace_list']) && $trace_list = json_decode($traceInfo['trace_list'], true);
                $code = 1;
            } catch (\Exception $e) {
                $code = $e->getCode();
                $errMsg = $e->getMessage();
            } finally {
                $traceInfoList[] = [
                    'waybill_code' => $waybillCode,
                    'trace_list' => $trace_list,
                    'code' => $code,
                    'err_msg' => $errMsg,
                ];
            }

        }
        return $traceInfoList;
    }
    public function getPrintDataAndWaybillForOpenApi(int $userId, int $shopId, $order, $waybill, $waybillType, $platform, $appId, $branchCode)
    {
        // 发货地址 默认取订单里的
        $addressInfo = [
            'province' => $order['sender_province'],
            'city'     => $order['sender_city'],
            'district' => $order['sender_district'],
            'address'  => $order['sender_detailaddress']
        ];
        // 查询网点信息 传了网点地址取网点里的
        if (!empty($branchCode)) {
//            $authSource = $platform == Waybill::OPEN_API_DEFAULT ? Waybill::AUTH_SOURCE_DY : $waybill->auth_source;
            $authSource = Waybill::getAuthSourceByPlatform($platform, $waybill);
            $waybillService = WaybillServiceManager::init($authSource, $waybill->access_token);
            $waybillData    = $waybillService->waybillSubscriptionQuery();
//            $addressInfo = self::getOpenApiAddressInfo($platform, $waybillData, $waybill, $branchCode, $order->wp_code);
            $addressInfo = Order::getOpenApiAddressInfoV3($platform, $waybillData, $waybill, $branchCode, $order->wp_code);
            if (empty($addressInfo)) {
                \Log::info("电子面单数据",[json_encode($waybillData)]);
                return throw_error_code_exception(StatusCode::COMPANY_ERROR);
            }
        }

        $addressInfo['mobile'] = $order['sender_phone'];
        $addressInfo['sender_name'] = $order['sender_name'];

        $result = CustomizeOrder::newWaybillHistoriesForOpenApi(
            $userId,
            $shopId,
            $order,
            $addressInfo,
            $order['wp_code'],
            $waybill,
            $waybillType,
            $platform,
            $appId
        );

        return $result;
    }

    public function getRemarks($shop,$startTime,$endTime,$page,$sortType){
        $orderService = OrderServiceManager::create();
        $orderService->setShop($shop);
        return $orderService->getRemarks($startTime,$endTime,$page,$sortType);
    }
}
