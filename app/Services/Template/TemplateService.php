<?php

namespace App\Services\Template;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\Company;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Template;
use App\Models\WaybillHistory;
use App\Models\WaybillShareAction;
use App\Services\Waybill\AbstractWaybillService;
use App\Services\Waybill\CompanyService;
use App\Services\Waybill\WaybillServiceManager;
use App\Services\WaybillHistory\WaybillHistoryQueryService;
use App\Utils\Environment;
use App\Utils\WaybillUtil;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Throwable;

class TemplateService
{
    /**
     * @var WaybillHistoryQueryService $waybillHistoryQueryService
     */
    protected $waybillHistoryQueryService;

    /**
     * @param WaybillHistoryQueryService $waybillHistoryQueryService
     */
    public function __construct(WaybillHistoryQueryService $waybillHistoryQueryService)
    {
        $this->waybillHistoryQueryService = $waybillHistoryQueryService;
    }

    /**
     * 获取范围定义内的模板
     * //1-商家店铺的模板
     * //2-》商家绑定的主店铺的模板
     * //3-》商家绑定的从店铺的模板
     * @param $scopes
     * @param $currentShopId
     * @param $userId
     * @return array
     * @throws ApiException
     */
    public function getScopeDefinedTemplates($currentShopId, $userId, $scopes): array
    {
        $myCompanies = CompanyService::getRelationCompanies($userId, $currentShopId, null);
        $brothShopIds = array_column(ShopBind::getAllRelationShop($currentShopId, [ShopBind::TYPE_ME_BIND], false), 'id');
        $childShopIds = array_column(ShopBind::getAllRelationShop($currentShopId, [ShopBind::TYPE_BIND_ME], false), 'id');
        Log::info("绑定的店铺", [$brothShopIds, $childShopIds, $scopes]);

        $resultTemplates = [];
        $templates = $this->getDefinedTemplates($myCompanies);
        foreach ($templates as $template) {
            //如果店铺id和模板的店铺id一样，并且模板的权限范围包含1，就加入到结果集
            if ($template['shop_id'] == $currentShopId && in_array(1, $scopes)) {
                $resultTemplates[] = $template;
            }
            if (in_array(2, $scopes) && in_array($template['shop_id'], $brothShopIds)) {
                //如果模板的店铺id在兄弟店铺id里面，就加入到结果集
                $resultTemplates[] = $template;
                continue;
            }

            if (in_array(3, $scopes) && in_array($template['shop_id'], $childShopIds)) {
                //如果模板的店铺id在子店铺id里面，就加入到结果集
                $resultTemplates[] = $template;
                continue;
            }
            if (!isset($template['belong_shop_id'])) {
                $template['belong_shop_id'] = $template['shop_id'];
                $template['belong_shop_name'] = $template['owner_name'];
            }


        }
        return $resultTemplates;
    }

    /**
     * 获取定义的模板（同时更新公司的电子面单余量）
     * @param $myCompanies
     * @return Collection<Template>
     */
    public function getDefinedTemplates($myCompanies): Collection
    {
        $companyIds = collect($myCompanies)->pluck('id')->toArray();
        Log::info("获取定义的模板", ["companyIds" => $companyIds]);

//        Log::info("获取定义的模板", ["companyIds"=>$companyIds]);
        $templates = Template::query()
            ->whereIn('company_id', $companyIds)
            ->get();
        /**
         * 这个是做电子面单查询的缓存，避免频繁查询，key是店铺的平台ID，value是查询结果
         */
        $waybillSubscriptionQueryResult = new Collection();
        /**
         * @var Company $company
         */
        foreach ($myCompanies as &$company) {
            try {
                $wpCode = $company->wp_code;
                /**
                 * 非三方共享面单
                 */
                if (!$company->isThirdShare()) {
                    $identifier = $company->owner_id;
                    $auth = WaybillUtil::findShopWaybillAuth($company->shop_id, $company->auth_source, $identifier,
                        true);
                    //会有目标绑定的电子面单账号被删除的情况，跳过
                    if (!$auth) {
                        $company->waybill_auth_status = 0;
                        continue;
                    }
                    //pdd站内
                    try {
                        $waybillService = WaybillServiceManager::init($company->auth_source, $auth->getAccessToken());
                        $waybillService->checkTokenExpired($auth, true);
                        if ($auth instanceof Shop) {
                            $waybillService->setShop($auth);
                        }
                        $waybills = $this->getWaybillsWithCache($waybillSubscriptionQueryResult, $identifier, $waybillService, $auth);
                        //                    $waybill = get_array_value($waybills,$wpCode,[]);
                        $waybill = collect($waybills)->where('wp_code', $wpCode)->toArray();


                    } catch (\Exception $e) {
                        Log::error("获取电子面单失败", [$e->getMessage()]);
                        $company->waybill_auth_status = 0;
                        continue;
                    }
                    Log::info("获取到电子面单信息", [$waybill]);
                    if (empty($waybill)) {
                        $company->waybill_auth_status = 0;
                        $company->available_status = 0;
                        continue;
                        //throw new ApiException(ErrorConst::WAYBILL_SERVICE_NOT_OPEN);
//                            throw new \Exception('未开通该公司电子面单服务!');
                    }
                    $company->waybill_auth_status = 1;

                    //这段代码的意图是更新公司的电子面单余量，也就是每次读取模板的时候，都会去更新一下公司的电子面单余量
                    foreach ($waybill as $item) {
                        if ($item['wp_code'] == $company->wp_code) {
                            $company->available_status = 0;
                            foreach ($item['branch_account_cols'] as $branch) {
                                //网点编码的情况下(加盟)，更新公司的电子面单余量,值匹配branch_code去掉branch_name的原因是branch会改名
                                if ($company->branch_code == $branch['branch_code']) {
                                    Log::info("开始匹配",["wpCode"=>$company->wp_code,"branch_code"=>$company->branch_code]);
                                    foreach ($branch['shipp_addresses'] as $address) {
                                        if (is_object($address)) {
                                            $address = json_decode(json_encode($address), true);
                                        }
                                        if ($company->isAddressMatch($address)) {
                                            $company->available_status = 1;
                                            if (!empty($branch['platform_account_id'])) {
                                                $company->platform_account_id = $branch['platform_account_id'];
                                                Log::info("platform_account_id", [$branch['platform_account_id']]);
                                            }
                                            $company->quantity = intval($branch['quantity']);
                                            $company->cancel_quantity = $branch['cancel_quantity'];
                                            $company->recycled_quantity = $branch['recycled_quantity'];
                                            $company->allocated_quantity = $branch['allocated_quantity'];
                                            if(Environment::isXhs()){
                                                $company->extended_info='{"billVersion":2}';
                                            }
                                            Log::info("更新公司电子面单余量", [$company]);
//
                                            if (!$company->save()) {
                                                Log::info("更新公司电子面单余量失败", [$company]);
                                                throw new ApiException(ErrorConst::COMPANY_BALANCE_UPDATE_FAIL);
                                            }
                                        } else {
                                            Log::info("地址不匹配", [$company, $address]);
                                        }
                                    }
                                } else {
                                    foreach ($branch['shipp_addresses'] as $address) {
                                        if ($company->isAddressMatch($address)) {
                                            //如果地址匹配了，但有结算号，结算号不匹配的情况，网点状态调整成异常
                                            if(!empty($company->settlement_code) && $company->settlement_code != $address['settlement_code']){
                                                Log::info("结算号不匹配", [$company, $address]);
                                                $company->available_status = 0;
                                            }else{
                                                $company->available_status = 1;
                                            }

                                        }else{

                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    //获取分享单号总数
                    $companyId = $company->id;
                    $shareCount = WaybillShareAction::getShareCount($companyId);

                    $redis = redis('cache');
                    $cacheKey = 'share_waybill_statistics_' . $companyId;
                    $historyUseCount = 0;
                    $historyCancelCount = 0;
                    $deadline = null;
                    if ($redis->exists($cacheKey)) {
                        $data = $redis->get($cacheKey);
                        $data = json_decode($data, true);
                        $historyUseCount = $data['useCount'];
                        $historyCancelCount = $data['cancelCount'];
                        $deadline = $data['deadline'];
                        Log::info("获取分享单号历史数据", ["data" => $data, "historyUseCount" => $historyUseCount, "historyCancelCount" => $historyCancelCount, "deadline" => $deadline, "companyId" => $companyId]);
                    } else {
                        Log::info("获取分享单号历史数据", ["不存在", "companyId" => $companyId]);
                    }

                    list($useCount, $cancelCount) = $this->waybillHistoryQueryService->getShareUseAndCancelCount($company, null, $deadline);
                    Log::info("获取分享单号近期数据", ["useCount" => $useCount, "cancelCount" => $cancelCount, "companyId" => $companyId, "deadline" => $deadline]);
                    $useCount = $useCount + $historyUseCount;
                    $cancelCount = $cancelCount + $historyCancelCount;
                    if ($shareCount == -1) {
                        Company::query()->where('id', $companyId)->update([
                            'quantity' => -1,
                            'cancel_quantity' => $cancelCount,
                            'allocated_quantity' => $historyCancelCount + $useCount + $cancelCount
                        ]);
                    } else {
                        //获取分享网点使用总数、取消总数
                        Log::info("更新分享单号总数", ["shareCount" => $shareCount, "companyId" => $companyId, "useCount" => $useCount, "cancelCount" => $cancelCount]);
                        Company::query()->where('id', $companyId)->update([
                            'quantity' => $shareCount - $useCount - $cancelCount,
                            'cancel_quantity' => $cancelCount,
                            'allocated_quantity' => $useCount + $cancelCount
                        ]);
                    }
                    $auth = WaybillUtil::findShopWaybillAuth($company->source_shopid, $company->auth_source, $company->owner_id,
                        true);
                    if (!$auth) {
                        $company->waybill_auth_status = 0;
                        continue;
                    }
                    $waybillService = WaybillServiceManager::init($company->auth_source, $auth->getAccessToken());
                    if ($auth instanceof Shop) {
                        $waybillService->setShop($auth);
                    }
                    $identifier = $company->sourceShop->identifier;
                    $waybills = $this->getWaybillsWithCache($waybillSubscriptionQueryResult, $identifier, $waybillService, $auth);
                    $waybill = collect($waybills)->where('wp_code', $wpCode)->toArray();
                    if (empty($waybill)) {
                        $company->waybill_auth_status = 0;
                        $company->available_status = 0;
                        continue;
                    }
                    foreach ($waybill as $item) {
                        if ($item['wp_code'] == $company->wp_code) {
                            $company->available_status = 0;
                            foreach ($item['branch_account_cols'] as $branch) {
                                //有网点名称和网点编码的情况下(加盟)，更新公司的电子面单余量
                                if ($company->branch_name == $branch['branch_name'] && $company->branch_code == $branch['branch_code']) {
                                    foreach ($branch['shipp_addresses'] as $address) {
                                        if (is_object($address)) {
                                            $address = json_decode(json_encode($address), true);
                                        }
                                        if ($company->isAddressMatch($address)) {
                                            $company->available_status = 1;
                                        }
                                    }
                                } else {
                                    foreach ($branch['shipp_addresses'] as $address) {
                                        if ($company->isAddressMatch($address)) {
                                            $company->available_status = 1;
                                        }
                                    }
                                }
                            }

                        }
                    }
                }
            } catch (Throwable $e) {
                Log::error("获取电子面单失败", [$e->getMessage(), $e->getTraceAsString()]);
            }
        }
        $myCompanies = array_column($myCompanies, null, 'id');
        foreach ($templates as $template) {
//            Log::info("获取模板", ["id" => $template->id]);
            $templateCompany = get_array_value($myCompanies, $template['company_id'], null);
            if (isset($templateCompany)) {
                $template['company'] = $templateCompany;
//                \Log::info("模板关联的公司", [$templateCompany,$template]);
            }
        }

        return $templates;
    }

    /**
     * 更新模板关联的网点
     * @param array $templateIds
     * @param int $companyId
     * @return int
     */
    public
    function updateCompany(array $templateIds, int $companyId): int
    {
        return Template::query()->whereIn('id', $templateIds)->update(['company_id' => $companyId]);

    }

    /**
     * 更新模板的增值服务
     * @param int $templateId
     * @param string $serviceList
     * @param string|null $timeDelivery
     * @return int
     */
    public
    function updateServiceList(int $templateId, string $serviceList, string $timeDelivery = null): int
    {
        return Template::query()->where('id', $templateId)->update(['service_list' => $serviceList, "time_delivery" => $timeDelivery]);
    }

    /**
     * @param Collection $waybillSubscriptionQueryResult
     * @param $identifier
     * @param AbstractWaybillService $waybillService
     * @param $auth
     * @return array|mixed
     */
    public
    function getWaybillsWithCache(Collection $waybillSubscriptionQueryResult, $identifier, \App\Services\Waybill\AbstractWaybillService $waybillService, $auth)
    {
//这个地方忽略掉wpCode目的是一次查出来全部快递公司的电子面单，加入缓存，然后再通过wpCode匹配
        $waybills = $waybillSubscriptionQueryResult->get($identifier);
//        Log::info("从缓存读取电子面单信息", ["identifier" => $identifier, $waybills]);
        if (!isset($waybills)) {
            $waybills = $waybillService->waybillSubscriptionQuery('',
                $auth->service_id ?? '');
//            Log::info("从接口读取电子面单信息", [$waybills]);
            $waybillSubscriptionQueryResult->put($identifier, $waybills);
//            Log::info("缓存未命中", ["identifier" => $identifier]);

        } else {
//            Log::info("缓存命中", ["identifier" => $identifier]);
        }
        return $waybills;
    }
}
