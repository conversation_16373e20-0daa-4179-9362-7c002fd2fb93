<?php

namespace App\Services\PlatformAddress;

use App\Services\BaseFromRequest;

class PlatformAddressRequest extends BaseFromRequest
{
    /**
     * @var int 店铺ID
     */
    public $shop_id;
    
    /**
     * @var int 地址库ID
     */
    public $address_id;
    
    /**
     * @var string 收/发件人
     */
    public $name;
    
    /**
     * @var string 默认联系方式
     */
    public $contact;
    
    /**
     * @var string|null 手机号码
     */
    public $phone;
    
    /**
     * @var string|null 普通座机号码
     */
    public $common_phone;
    
    /**
     * @var string|null 企业座机号码
     */
    public $company_phone;
    
    /**
     * @var string 邮编
     */
    public $postal_code;
    
    /**
     * @var string 省
     */
    public $province;
    
    /**
     * @var string 市
     */
    public $city;
    
    /**
     * @var string 区
     */
    public $district;
    
    /**
     * @var string 镇/街道
     */
    public $town;
    
    /**
     * @var string 详细地址
     */
    public $address;
    
    /**
     * @var int 是否为退货默认
     */
    public $is_default;
    
    /**
     * @var int 是否为发货默认
     */
    public $is_send_default;
    
    /**
     * @var int 联系方式类型
     */
    public $link_type;
    
    /**
     * @var string 地址备注信息
     */
    public $remark;
    
    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            'shop_id' => 'required|integer',
            'address_id' => 'required|integer',
            'name' => 'required|string|max:32',
            'contact' => 'required|string|max:50',
            'phone' => 'nullable|string|max:11',
            'common_phone' => 'nullable|string|max:50',
            'company_phone' => 'nullable|string|max:50',
            'postal_code' => 'required|string|max:32',
            'province' => 'required|string|max:32',
            'city' => 'required|string|max:32',
            'district' => 'required|string|max:32',
            'town' => 'required|string|max:32',
            'address' => 'required|string|max:64',
            'is_default' => 'nullable|integer|in:0,1',
            'is_send_default' => 'nullable|integer|in:0,1',
            'link_type' => 'nullable|integer|in:0,1,2',
            'remark' => 'nullable|string|max:255',
        ];
    }
}
