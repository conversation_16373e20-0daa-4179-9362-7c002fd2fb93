<?php

namespace App\Services\PlatformAddress;

use App\Models\PlatformAddress;
use App\Models\Shop;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PlatformAddressService
{
    /**
     * 创建平台地址
     * @param array $data 地址数据
     * @return PlatformAddress
     * @throws \Exception
     */
    public function createAddress(array $data): PlatformAddress
    {
        try {
            DB::beginTransaction();
            
            // 如果设置为默认退货地址，则将其他地址设为非默认
            if (isset($data['is_default']) && $data['is_default'] == 1) {
                PlatformAddress::where('shop_id', $data['shop_id'])
                    ->where('is_default', 1)
                    ->update(['is_default' => 0]);
            }
            
            // 如果设置为默认发货地址，则将其他地址设为非默认
            if (isset($data['is_send_default']) && $data['is_send_default'] == 1) {
                PlatformAddress::where('shop_id', $data['shop_id'])
                    ->where('is_send_default', 1)
                    ->update(['is_send_default' => 0]);
            }
            
            $address = PlatformAddress::create($data);
            
            DB::commit();
            return $address;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建平台地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data,
            ]);
            throw $e;
        }
    }

    /**
     * 更新平台地址
     * @param int $id 地址ID
     * @param array $data 更新数据
     * @return PlatformAddress
     * @throws \Exception
     */
    public function updateAddress(int $id, array $data): PlatformAddress
    {
        $address = PlatformAddress::find($id);
        if (!$address) {
            throw new \Exception('平台地址不存在');
        }
        
        try {
            DB::beginTransaction();
            
            // 如果设置为默认退货地址，则将其他地址设为非默认
            if (isset($data['is_default']) && $data['is_default'] == 1) {
                PlatformAddress::where('shop_id', $address->shop_id)
                    ->where('id', '!=', $id)
                    ->where('is_default', 1)
                    ->update(['is_default' => 0]);
            }
            
            // 如果设置为默认发货地址，则将其他地址设为非默认
            if (isset($data['is_send_default']) && $data['is_send_default'] == 1) {
                PlatformAddress::where('shop_id', $address->shop_id)
                    ->where('id', '!=', $id)
                    ->where('is_send_default', 1)
                    ->update(['is_send_default' => 0]);
            }
            
            $address->update($data);
            
            DB::commit();
            return $address;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新平台地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id,
                'data' => $data,
            ]);
            throw $e;
        }
    }

    /**
     * 删除平台地址
     * @param int $id 地址ID
     * @return bool
     * @throws \Exception
     */
    public function deleteAddress(int $id): bool
    {
        $address = PlatformAddress::find($id);
        if (!$address) {
            throw new \Exception('平台地址不存在');
        }
        
        try {
            return $address->delete();
        } catch (\Exception $e) {
            Log::error('删除平台地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id,
            ]);
            throw $e;
        }
    }

    /**
     * 设置默认退货地址
     * @param int $id 地址ID
     * @return bool
     * @throws \Exception
     */
    public function setDefaultAddress(int $id): bool
    {
        $address = PlatformAddress::find($id);
        if (!$address) {
            throw new \Exception('平台地址不存在');
        }
        
        try {
            DB::beginTransaction();
            
            // 将其他地址设为非默认
            PlatformAddress::where('shop_id', $address->shop_id)
                ->where('id', '!=', $id)
                ->where('is_default', 1)
                ->update(['is_default' => 0]);
            
            // 设置当前地址为默认
            $address->is_default = 1;
            $result = $address->save();
            
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置默认退货地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id,
            ]);
            throw $e;
        }
    }

    /**
     * 设置默认发货地址
     * @param int $id 地址ID
     * @return bool
     * @throws \Exception
     */
    public function setDefaultSendAddress(int $id): bool
    {
        $address = PlatformAddress::find($id);
        if (!$address) {
            throw new \Exception('平台地址不存在');
        }
        
        try {
            DB::beginTransaction();
            
            // 将其他地址设为非默认
            PlatformAddress::where('shop_id', $address->shop_id)
                ->where('id', '!=', $id)
                ->where('is_send_default', 1)
                ->update(['is_send_default' => 0]);
            
            // 设置当前地址为默认
            $address->is_send_default = 1;
            $result = $address->save();
            
            DB::commit();
            return $result;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置默认发货地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id,
            ]);
            throw $e;
        }
    }

    /**
     * 获取默认退货地址
     * @param int $shopId 店铺ID
     * @return PlatformAddress|null
     */
    public function getDefaultAddress(int $shopId)
    {
        return PlatformAddress::getDefaultAddress($shopId);
    }

    /**
     * 获取默认发货地址
     * @param int $shopId 店铺ID
     * @return PlatformAddress|null
     */
    public function getDefaultSendAddress(int $shopId)
    {
        return PlatformAddress::getDefaultSendAddress($shopId);
    }

    /**
     * 批量导入平台地址
     * @param int $shopId 店铺ID
     * @param array $addresses 地址数组
     * @return array 导入结果
     */
    public function batchImport(int $shopId, array $addresses): array
    {
        $success = 0;
        $failed = 0;
        $errors = [];
        
        foreach ($addresses as $index => $addressData) {
            try {
                $addressData['shop_id'] = $shopId;
                $this->createAddress($addressData);
                $success++;
            } catch (\Exception $e) {
                $failed++;
                $errors[] = [
                    'index' => $index,
                    'message' => $e->getMessage(),
                ];
            }
        }
        
        return [
            'success' => $success,
            'failed' => $failed,
            'errors' => $errors,
        ];
    }

    /**
     * 同步平台地址
     * @param int $shopId 店铺ID
     * @param array $platformData 平台数据
     * @return array 同步结果
     */
    public function syncFromPlatform(int $shopId, array $platformData): array
    {
        $created = 0;
        $updated = 0;
        $failed = 0;
        $errors = [];
        
        foreach ($platformData as $index => $data) {
            try {
                // 检查地址是否已存在
                $existingAddress = PlatformAddress::where('shop_id', $shopId)
                    ->where('address_id', $data['address_id'])
                    ->first();
                
                if ($existingAddress) {
                    // 更新现有地址
                    $data['shop_id'] = $shopId;
                    $this->updateAddress($existingAddress->id, $data);
                    $updated++;
                } else {
                    // 创建新地址
                    $data['shop_id'] = $shopId;
                    $this->createAddress($data);
                    $created++;
                }
            } catch (\Exception $e) {
                $failed++;
                $errors[] = [
                    'index' => $index,
                    'message' => $e->getMessage(),
                ];
            }
        }
        
        return [
            'created' => $created,
            'updated' => $updated,
            'failed' => $failed,
            'errors' => $errors,
        ];
    }
}
