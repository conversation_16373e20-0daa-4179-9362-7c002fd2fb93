<?php

namespace App\Services\Printing;

use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Constants\PrintModeConst;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderWaybillEvent;
use App\Events\Orders\OrderWaybillFailEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Models\AbnormalOrder;
use App\Models\Batch;
use App\Models\Company;
use App\Models\CustomizeOrder;
use App\Models\FactoryOrder;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PackageOrder;
use App\Models\PrintRecord;
use App\Models\PtLogistics;
use App\Models\ShippingAddress;
use App\Models\Shop;
use App\Models\ShopExtra;
use App\Models\TagTemplate;
use App\Models\Template;
use App\Models\User;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintOrderItemBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\SenderAddressBo;
use App\Services\Bo\WaybillBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\BusinessException;
use App\Services\PrintDataService;
use App\Services\Vo\PrintPackVo;
use App\Services\Waybill\DY\DYApi;
use App\Services\Waybill\PDDWB\PDDWBApi;
use App\Services\Waybill\Taobao\NewTBApi;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\ArrayUtil;
use App\Utils\Environment;
use App\Utils\WaybillUtil;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;

class OrderPrintService
{

    /**
     * 获取打印数据
     * @param int $userId
     * @param int $shopId
     * @param $isNewPrint
     * @param string $waybillCode 指定单号打印
     * @param int $templateId
     * @param array $orderIds
     * @param int $batchNo
     * @param int $packageNum
     * @param array $contents
     * @param bool $forcePrint
     * @param int $printMode
     * @param array $printConfig
     * @return array
     * @throws ApiException
     */
    public static function getPrintDataAndWaybill(int   $userId, int $shopId, $isNewPrint, $waybillCode, int $templateId,
                                                  array $orderIds, int $batchNo, int $packageNum = 1, $contents = [],
                                                        $forcePrint = false, $printMode = 1, array $printConfig = [], string $operatorName = ""): array
    {
        \Log::info('获取打印数据', ["operatorName" =>$operatorName ]);
        $templateObj = Template::query()->findOrFail($templateId);
        $company = $templateObj->company;
        $template = $templateObj->toArray();

        //网点地址与真实发货地址
        $addressInfo = self::getBranchAndSendAddress($userId, $shopId, $company, $template);
        StatisticsCost('获取真实发货地址');
        //判断是否为虚拟网点
        $waybillAuth = self::getWaybill($company, $template);

        if (!$waybillAuth) {
            throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
//            throw new \Exception('电子面单授权信息丢失!');
        }

        //查询是否去过号
        $hasWaybillHistories = self::hasWaybillHistories($userId, $shopId, $isNewPrint, $waybillCode, $orderIds,
            $addressInfo, $template, $waybillAuth, $batchNo, $contents, $packageNum, $forcePrint, $printConfig);
        StatisticsCost('查询是否已取号');
        //查询订单详情过滤掉不符合状态的订单
        $failedData = [];
        $newOrders = $hasWaybillHistories['new_orders'];
        $printData = $hasWaybillHistories['print_data'];
        $orderItem = $hasWaybillHistories['order_item'];
        $orderInfoArr = $hasWaybillHistories['orderInfoArr'];
        $failedOrders = $hasWaybillHistories['failedOrders'] ?? [];

        if (!empty($newOrders)) {
            //判断是否是虚拟分享网点，且余额不为0，状态是正常
            if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
                if ($company->source_status == Company::SOURCE_COMPANY_STATUS_CLOSED) {
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_FREEZE);
                }
            }
            //未取号取号处理
            $newWaybillHistories = self::newWaybillHistories(
                $userId,
                $shopId,
                $newOrders,
                $addressInfo,
                $template,
                $waybillAuth,
                $batchNo,
                $packageNum,
                $orderItem,
                $contents,
                $printMode,
                $printConfig,
                1,
                $operatorName
            );
            StatisticsCost('取号返回');

            $printData = array_merge($hasWaybillHistories['print_data'], $newWaybillHistories['print_data']);
            $orderInfoArr = array_merge($orderInfoArr, $newWaybillHistories['orderInfoArr']);
            //取号失败信息
            if (count($newWaybillHistories['failed_data']) > 0) {
                $failedData = $newWaybillHistories['failed_data'];
            }
        }
        if (!$printData && !$failedData && !$failedOrders) {
            throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
//            throw new \Exception('订单获取打印数据异常！');
        }

        //打印排序
        $sortOrders = [];
        foreach ($orderIds as $oIdArr) {
            foreach ($oIdArr as $key => $val) {
                if (!empty($orderItem) && array_key_exists($val, $orderItem)) {
                    $oIdArr[$key] = $val . ':' . implode(',', $orderItem[$val]);
                }
            }
            $orderIdStr = implode('_', collect($oIdArr)->sort()->all());
            $exist = collect($printData)->where('documentID', $orderIdStr)->values()->all();
            if ($exist) {
                $sortOrders = array_merge($sortOrders, $exist);
            }
        }

        return ['orders' => $sortOrders, 'failed' => array_merge($failedData, $failedOrders), 'orderInfoArr' => $orderInfoArr];
    }

    /**
     * 已经取号订单打印数据
     * @param $userId
     * @param $shopId
     * @param $isNewPrint
     * @param $waybillCode
     * @param $orderIds
     * @param $sender
     * @param $template
     * @param $waybillAuth
     * @param $batchNoKey
     * @param array $contents
     * @param int $packageNum
     * @param bool $forcePrint
     * @param array $printConfig
     * @return array
     * @throws ApiException
     */
    public static function hasWaybillHistories($userId, $shopId, $isNewPrint, $waybillCode, &$orderIds, $sender,
                                               $template, $waybillAuth, $batchNoKey, $contents = [], $packageNum = 1,
                                               $forcePrint = false, array $printConfig): array
    {
        $printRecords = [];
        $printData = [];
        $newOrders = [];
        $orderInfoArr = [];
        $failedOrders = [];

        $allOrderIds = [];
        //部分打印订单提取出来 [order_id=>[order_item_id_arr]]
        $orderItemIds = [];
        foreach ($orderIds as $key => $value) {
            foreach ($value as $k => $v) {
                if (strstr($v, ":")) {
                    $temp = explode(":", $v);
                    if (!empty($temp[1])) {
                        $orderIds[$key][$k] = $temp[0];
                        $orderItemIds[$temp[0]] = explode(",", $temp[1]);
                    } else {
                        unset($orderIds[$key][$k]);
                    }
                }
            }
        }
        array_map(function ($value) use (&$allOrderIds) {
            $allOrderIds = array_merge($allOrderIds, array_values($value));
        }, $orderIds);

        $query = Order::query()->with(['orderItem', 'orderCipherInfo'])
            ->where(['locked_at' => null])
            ->whereIn('id', $allOrderIds);
        if ($forcePrint) {
            $query->whereIn('refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_YES, Order::REFUND_STATUS_PART]);
        }
        $allOrders = $query->get();
        StatisticsCost('一把查出来全部的订单信息');
        //过滤退款订单
        $mergeOrderUnsetId = [];
        if (!$forcePrint) {
//            $tradesOrder = static::batchGetOrderInfo($allOrders->toArray());
            StatisticsCost('更新了订单信息');
//            $orderStatus = array_column($tradesOrder, null, 'tid');
            self::excludeRefund($allOrders, $orderItemIds, $mergeOrderUnsetId);
            StatisticsCost('对订单状态进行检查，剔除退款订单');
        }
        //干掉退款订单/锁定订单的id防止排序出错
        $checkOrderIds = array_column($allOrders->toArray(), 'id');
        foreach ($orderIds as $key => $value) {
            foreach ($value as $k => $item) {
                if (in_array($item, $mergeOrderUnsetId)) {
                    unset($orderIds[$key][$k]);
                }
                if (!in_array($item, $checkOrderIds)) {
                    unset($orderIds[$key][$k]);
                }
            }
            if (empty($orderIds[$key])) {
                unset($orderIds[$key]);
            }
        }
        $orderIds = array_values($orderIds);
        \Log::info('订单id', ["orderIds" => $orderIds]);
        if ($allOrders->isEmpty()) {
            throw new ApiException(ErrorConst::ORDER_PRINT_DATA_EMPTY);
        }

        $orders = [];
        foreach ($orderIds as $orderIdArr) {
            if (empty($orderIdArr)) {
                continue;
            }
            $orderIdArr = collect($orderIdArr)->sort()->all();
            //这个地方写的太隐晦了，用array_shift 把合单的第一个ID取处理，作为合单的Main Order，剩下的作为mergeOrders
            $mainOrder = collect($allOrders)->where('id', array_shift($orderIdArr))->first();
            $mainOrder->mergeOrders = null;
            //orderIdArr如果不是合单的情况，上面的array_shift会把第一个ID取出来，剩下的就是空的
            //如果是合单，第一个被array_shift,剩下的是被合的，把被合放到mergeOrders里面 o
            if (count($orderIdArr) > 0) {
                \Log::info('合单 ', ["orderIdArray" => $orderIdArr]);
                $mainOrder->mergeOrders = collect($allOrders)->whereIn('id', $orderIdArr)->all();
            }
            //获取每个订单真实获取面单数量
            $mainOrder->packageNum = $packageNum;
            if ($packageNum < 0) {
                $mainOrder->packageNum = self::getRelPackageNum($mainOrder->toArray(), $packageNum);
            }
            //就是要拆单
            if ($packageNum != 1) {
                $mainOrder->waybillPackages = self::multiPackages($mainOrder->toArray(), $packageNum);
            }
            $orders[] = $mainOrder;
        }
        StatisticsCost('计算包裹的数量');
//        unset($allOrders);

        //取新单号直接返回
        if ($isNewPrint) {
            return ['print_data' => $printData, 'new_orders' => $orders, 'order_item' => $orderItemIds, 'orderInfoArr' => $orderInfoArr, 'failedOrders' => $failedOrders];
        }

        //历史打印数据 考虑到部分取号的情况
        $allHistories = [];
        foreach ($orders as $order) {
            $extra = [];
            if (array_key_exists($order['id'], $orderItemIds)) {
                $extra[$order['id']] = $orderItemIds[$order['id']];
            }
            if (!is_null($order->mergeOrders)) {
                foreach ($order->mergeOrders as $item) {
                    if (array_key_exists($item['id'], $orderItemIds)) {
                        $extra[$item['id']] = $orderItemIds[$item['id']];
                    }
                }
            }

            $query = WaybillHistory::where([
                'waybill_status' => WaybillHistory::WAYBILL_RECOVERY_NO,
            ])
                ->where('order_id', $order['id'])
                ->where('wp_code', $template['wp_code'])
                ->where('auth_source', $template['auth_source']);
//                ->where(function ($query) use ($extra) {
//                    $query->where('extra', json_encode($extra));
//                    if (empty($extra))
//                        $query->orWhereNull('extra');
//                });
            //是否指定运单打印
            if (!empty($waybillCode)) {
                $waybillCode = array_column($waybillCode, null, 'order_id');
                if (array_key_exists($order['id'], $waybillCode)) {
                    $query->where(function ($query) use ($waybillCode, $order) {
                        $query->orWhere('waybill_code', $waybillCode[$order['id']]['waybill_code'])
                            ->orWhere('parent_waybill_code', $waybillCode[$order['id']]['waybill_code']);
                    });
                    \Log::info('指定运单打印', ["waybillCode" => $waybillCode[$order['id']]['waybill_code'], 'extra' => $extra]);
                }
            }

            $histories = $query->orderBy('id', 'desc')->first();
            \Log::info('历史打印数据', ["histories" => $histories, "order_id" => $order['id']]);
            if (empty($histories)) {
                \Log::info("没有历史打印数据 orderItemIds", [$orderItemIds]);

            }
            $allHistories[] = $histories;
        }
        StatisticsCost('获取面单的历史数据');

        $allHistories = array_filter($allHistories);
        //如果无历史取号记录，直接去取号
        if (empty($allHistories)) {
            return ['print_data' => $printData, 'new_orders' => $orders, 'order_item' => $orderItemIds, 'orderInfoArr' => $orderInfoArr];
        }
        $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
        foreach ($orderIds as $k => $orderIdArr) {
            $order = collect($orders)->whereIn('id', $orderIdArr)->first();
            $tidArr = collect($allOrders)->whereIn('id', $orderIdArr)->pluck('tid')->all();
            $oldTemplate = $order->template;
            if ($oldTemplate && $oldTemplate->auth_source != $template['auth_source']) {
                throw new ApiException(ErrorConst::CROSS_PLATFORM_PRINTING_NOT_SUPPORTED);
//                throw new \Exception('暂不支持跨平台模板打印！');
            }

            //$batchNo = $batchNoKey . '-' . ($k+1);
            $batchNo = $batchNoKey;
            $waybillHistory = collect($allHistories)->whereIn('order_id', $orderIdArr)->all();
            //有子运单号是子母件 要一起打印出来
            if (!empty($waybillHistory[0]) && $waybillHistory[0]->parent_waybill_code) {
                $waybillHistory = WaybillHistory::query()->where('package_id', $waybillHistory[0]->package_id)->get();
                $waybillHistory = collect($waybillHistory)->unique('waybill_code')->values()->all();
            }
            if (count($waybillHistory) > 0) {
                foreach ($waybillHistory as $key => $history) {
                    //取老单号切换面单账号提示错误
                    $historyTemplateInfo = Template::query()->where('id', $history['template_id'])->first();
                    if (!empty($historyTemplateInfo) && $template['shop_id'] != $historyTemplateInfo['shop_id']) {
                        $historyShop = Shop::query()->where('id', $historyTemplateInfo['shop_id'])->first();
                        //取号错误信息
                        $obj = new \stdClass();
                        $obj->text = '面单账号与原单号不匹配';
                        $obj->tid = [$order['tid'] ?? null];
                        $obj->info = "面单账号与原单号不匹配 原单号取号账号:" . $historyShop['name'];
                        $failedOrders[] = json_encode($obj, 320); //失败数据
                        continue;
                    }
                    // 请求电子面单更新接口 拿到新的打印数据 抖音以下快递公司不支持更新
                    if (!in_array($history['wp_code'], DYApi::NOUPDATE)) {
                        $waybillData = $waybillService->updateWaybillData($sender['sender_address'], $order, $template, $history['waybill_code']);
                        if (in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS])) {
                            $history['print_data'] = !empty($waybillData) ? json_encode($waybillData) : $history['print_data'];
                        } else {
                            $history['print_data'] = $waybillData['print_data'] ?? $history['print_data'];
                        }
                    }
//                    if($template['auth_source']==Waybill::AUTH_SOURCE_WXSP) {
//                        $tempPrintData = PrintDataService::wxspTemplateData($sender['sender_address'], $order, $template,
//                            $history, $orderItemIds, false, $key + 1, count($waybillHistory), $contents, $printConfig);
//                    }else {
                    $tempPrintData = PrintDataService::templateData($sender['sender_address'], $order, $template,
                        $history, $orderItemIds, false, $key + 1, count($waybillHistory), $contents, $printConfig);
//                    }
                    $print_data_items = '';
                    if ($template['auth_source'] != Waybill::AUTH_SOURCE_JD) {
                        if (ArrayUtil::is_indexed_array($tempPrintData)) {
//                            $printData[] = $tempPrintData[0];
//                            $printData[] = $tempPrintData[1];
                            foreach ($tempPrintData as $item) {
                                $printData[] = $item;
                            }
                            $print_data_items = json_encode($tempPrintData[1]['contents'][0]['data']['printNextItemBeans'] ?? '');
                        } else {
                            $printData[] = $tempPrintData;
                            $print_data_items = json_encode($tempPrintData['contents'][1]['data']['printNextItemBeans'] ?? '');
                        }
                    } else {
                        $printData[] = $tempPrintData;
                    }

                    $printRecords[] = [
                        'wp_code' => $history['wp_code'],
                        'waybill_code' => $history['waybill_code'],
                        'order' => $order->toArray(),
                        'user_id' => $history['user_id'],
                        'package_id' => $history['package_id'],
                        'history_id' => $history['id'],
                        'print_data' => json_encode($tempPrintData),
                        'name_index' => $history['name_index'],
                        'phone_index' => $history['phone_index'],
                        'batch_no' => $batchNo,
                        'to_shop_id' => $order['shop_id'],
                        'template_id' => $template['id'],
                        'template_name' => $template['name'],
                    ];

                    //重复打印如果是合并订单 也存一下
                    if (!empty($order->mergeOrders)) {
                        foreach ($order->mergeOrders as $item) {
                            $printRecords[] = [
                                'wp_code' => $history['wp_code'],
                                'waybill_code' => $history['waybill_code'],
                                'order' => $item->toArray(),
                                'user_id' => $history['user_id'],
                                'package_id' => $history['package_id'],
                                'history_id' => $history['id'],
                                'print_data' => json_encode($tempPrintData),
                                'name_index' => $history['name_index'],
                                'phone_index' => $history['phone_index'],
                                'batch_no' => $batchNo,
                                'to_shop_id' => $order['shop_id'],
                                'template_id' => $template['id'],
                                'template_name' => $template['name'],
                            ];
                        }
                    }

                    //修改取号记录中打印数据等
                    WaybillHistory::query()->where('id', $history['id'])->update([
                        'receiver_province' => $order->receiver_state,
                        'receiver_city' => $order->receiver_city,
                        'receiver_district' => $order->receiver_district,
                        'receiver_name' => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_name_mask : $order->receiver_name,
                        'receiver_phone' => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_phone_mask : $order->receiver_phone,
                        'receiver_address' => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_address_mask : $order->receiver_address,
                        'print_data' => $history['print_data'],
                        'print_data_items' => $print_data_items,
                    ]);
                    //打印次数加1
                    $order->increment('print_num', 1);
                    $orderInfoArr[] = [
                        'id' => $order->id,
                        'tid' => $order->tid,
                        'package_id' => $history['package_id'],
                        'waybill_code' => $history['waybill_code'],
                        'wp_code' => $history['wp_code'],
                        'tid_arr' => $tidArr,
                    ];
                }
                StatisticsCost('把历史的取号数据更新');
            } else {
                $newOrders[] = $order;
            }
        }
//        // 添加批次号
//        Batch::create([
//            'shop_id' => $shopId,
//            'batch_no' => $batchNoKey,
//            'type' => Batch::TYPE_PRINT,
//            'status' => Batch::STATUS_SUCCESS,
//            'total_num' => count($printData),
//            'success_num' => count($printData),
//            'ignore_num' => 0,
//            'fail_num' => 0,
//            'fail_msg' => '',
//        ]);
        //添加打印记录
        $records = PrintRecord::generate($printRecords, $userId, $shopId);
        if (!$records) {
            Log::error('打印记录添加失败!', ['data' => $printRecords]);
            throw new ApiException(ErrorConst::PRINTED_RECORD_ADD_FAIL);
//            throw new \Exception('打印记录添加失败');
        }
        StatisticsCost('生成打印记录');
        \Log::info('打印数据', ["order_item" => $orderItemIds]);
        return ['print_data' => $printData, 'new_orders' => $newOrders, 'order_item' => $orderItemIds, 'orderInfoArr' => $orderInfoArr, 'failedOrders' => $failedOrders];
    }

    /**
     * 未取号订单打印
     * @param $userId
     * @param $shopId
     * @param $orders
     * @param $sender
     * @param $template
     * @param $waybillAuth // 授权信息 可能是shop也可能是waybillAuth
     * @param $batchNoKey
     * @param int $packageNum
     * @param array $orderItemOId
     * @param array $contents
     * @param int $printMode
     * @param array $printConfig
     * @param int $maxPackageSkuNum
     * @param string $operatorName
     * @return array
     * @throws ApiException
     */
    public static function newWaybillHistories($userId, $shopId, $orders, $sender, $template, $waybillAuth,
                                               $batchNoKey, $packageNum = 1, $orderItemOId = [], $contents = [],
                                               $printMode = 1, array $printConfig = [], int $maxPackageSkuNum = 1, string $operatorName = ""): array
    {
        $accessToken = $waybillAuth->access_token;
        \Log::info("未取号订单打印", ["packageNum" => $packageNum, "orderItemOId" => $orderItemOId,"operatorName" => $operatorName]);
        $printData = [];
        $failedOrders = [];
        $orderInfoArr = [];
        $company = $template['company'];
        $ordersCount = collect($orders)->pluck('packageNum');
        $num = array_sum(collect($ordersCount)->toArray());
        $user = User::query()->find($userId);
        $shop = Shop::query()->find($shopId);
        //先创建package
        foreach ($orders as $order) {
            \Log::info("未取号订单打印", [$order]);
            $tids = $order['tid'] ?? null;
            if (!is_null($order->mergeOrders) && count($order->mergeOrders) > 0) {
                //把合单的tid拼接起来
                $tids = $tids . ',' . implode(',', collect($order->mergeOrders)->pluck('tid')->toArray());
            }
            //查询是否有取号中的记录
            $idStr = handleOrderIdStr($order);
            $oidArr = collect(explode('_', $idStr))->sort()->all();
            $index = 1;
            $packageInfo = Package::query()
                ->where(['shop_id' => $order->shop_id, 'tids' => $tids, 'waybill_status' => Package::WAYBILL_STATUS_DOING])
                ->orderBy('id', 'desc')
                ->get();

            $requestId = [];
            $tempPackageNum = $packageNum;
            if ($tempPackageNum < 0) {
                $tempPackageNum = $order->packageNum;
            }
            \Log::info("取号打印的面单数量", [$tempPackageNum]);
            if (!empty($packageInfo) && $tempPackageNum == count($packageInfo)) {
                foreach ($packageInfo as $item) {
                    $requestId[$index] = $item['id'];
                    $index++;
                }
                $order['request_id'] = $requestId;
            } else {
                while ($index <= $tempPackageNum) {
                    \Log::info("未取号订单打印", [$order->waybillPackages]);
                    if (!empty($order->waybillPackages)) {
                        $packageJson = json_encode($order->waybillPackages[$index - 1]);
                    } else {
                        $packageJson = null;
                    }
                    //没有则创建
                    $package = Package::create([
                        'tids' => $tids,
                        'waybill_status' => Package::WAYBILL_STATUS_DOING,
                        'user_id' => $order->user_id,
                        'shop_id' => $order->shop_id,
                        'goods_info' => empty($packageJson) ? null : $packageJson,
                    ]);
                    foreach ($oidArr as $oid) {
                        PackageOrder::create([
                            'order_id' => $oid,
                            'package_id' => $package->id,
                        ]);
                    }
                    $requestId[$index] = $package->id;
                    //子母件只创建一个
                    if (in_array($template['wp_code'], array_merge(DYApi::ZIMUJIANMAP, array_merge(PDDWBApi::ZIMUJIANMAP, NewTBApi::ZIMUJIANMAP)))) {
                        //拼多多只有快运才支持子母件
                        if ($company['auth_source'] == Waybill::AUTH_SOURCE_PDD_WB) {
                            if ($template['wp_code'] != 'SF') {
                                $index = $tempPackageNum;
                            }
                        } else {
                            $index = $tempPackageNum;
                        }
                    }
                    $index++;
                }

                $order['request_id'] = $requestId;
            }
            // 微信加密
            if (Environment::isWxOrWxsp() && !empty($order->orderCipherInfo)) {
//                $order->receiver_phone = appDecrypt($order->orderCipherInfo->receiver_phone_ciphertext);
//                $order->receiver_name = appDecrypt($order->orderCipherInfo->receiver_name_ciphertext);
//                $order->receiver_address = appDecrypt($order->orderCipherInfo->receiver_address_ciphertext);
                unset($order->orderCipherInfo);
            }
        }
        StatisticsCost('完成创建package');
        if (!empty($orders)) {
            //每次取号判断是否是虚拟网点，且正常状态为0，面单余额不为0
            //共享电子面单走这个逻辑
            if ($company['source'] == Company::SOURCE_COMPANY_STATUS_YES) {
                if ($num > $company['quantity'] && $company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY) {
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
//					throw new \Exception('打印订单数大于可用单号余额!');
                }
                if ($company['source_status'] == Company::SOURCE_COMPANY_STATUS_OPEN && $company['quantity'] !== Company::INIT_QUANTITY) {
                    $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
                    //共享电子面单肯定是应用内的授权，授权肯定是从shop里面取的
                    $waybillService->setShop($waybillAuth);
                    $packages = $waybillService->assemWaybillPackages($sender['branch_address'], $orders, $template, $packageNum);
                    //单号余额非无限量，取号后，电子面单余额数量减少，已用面单数量增加
                    if ($company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY) {
                        $res = Company::query()
                            ->where('id', $company['id'])
                            ->where('quantity', '>=', $num)
                            ->update([
                                'quantity' => DB::raw("`quantity` - $num"),
                                'allocated_quantity' => DB::raw("`allocated_quantity` + $num")
                            ]);
                        if (!$res) {
                            throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
                        }
                    } else {
                        //无限量的不用减少电子面单余额数量，已用面单数量增加
                        $query = Company::where('id', $company['id']);
                        $query->increment('allocated_quantity', $num);
                    }
                } else {
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
//                    throw new \Exception('电子面单余额为0，请联系您的单号分享者!');
                }
            } else {
                //加盟快递公司余额减少，实际余额以command更新为准
                $quanData = ['allocated_quantity' => DB::raw("allocated_quantity + " . $num)];
                if (!in_array($company['wp_code'], Company::ZHI_YING_COMPANY_LIST)) {
                    $quanData = array_merge($quanData, ['quantity' => DB::raw("quantity - " . $num)]);
                }
                Company::query()->where('source', Company::SOURCE_COMPANY_STATUS_NO)
                    ->where('auth_source', $company['auth_source'])
                    ->where('owner_id', $company['owner_id'])
                    ->where('branch_code', $company['branch_code'])
                    ->where('wp_code', $company['wp_code'])
                    ->update($quanData);

                //非虚拟网点，正常取号
                $waybillService = WaybillServiceManager::init($template['auth_source'], $accessToken);
                if ($waybillAuth instanceof Shop) {
                    $waybillService->setShop($waybillAuth);
                }
                $packages = $waybillService->assemWaybillPackages($sender['branch_address'], $orders, $template, $packageNum);
            }
            StatisticsCost('取号成功');
//            \Log::info('取号成功', ['packages' => $packages]);

            foreach ($packages as $idStr => $package) {
//                Log::info('package', [$idStr, $package]);

                $oidArr = collect(explode('_', explode('|', $idStr)[0]))->sort()->all();
                $order = collect($orders)->where('id', $oidArr[0])->first();
                $order->index = explode('|', $idStr)[1] ?? 0;
                //$idStr="1379689|1"，把|后面的数字提取处理
                $explode = explode('|', $idStr);
                if (count($explode) > 1) {
                    $packageIndex = $explode[1];
                } else {
                    $packageIndex = 0;
                }
                //是否取号错误
                if (!is_string($package[0]) && is_array($package[0]) && !empty($package[0]) && (isset($package[0]['err_no']) ? $package[0]['err_no'] == 0 : true)
                    && (Waybill::AUTH_SOURCE_DY == $template['auth_source'] ? (!isset($package[0]['data']['ebill_infos'])) : true)
                    && (Waybill::AUTH_SOURCE_KS == $template['auth_source'] ? (!isset($package[0]['result'])) : true)) {
//                    $index  = Order::getBatchNoIndex($shopId, $batchNoKey);
//                    $batchNo = $batchNoKey . '-' . $index;
                    //分合单打印和一单多包打印
                    \Log::info('合单打印保存1', [$oidArr, $tids]);
                    if (count($oidArr) > 1) {
                        //合单打印
                        $tids = $order['tid'] ?? null;
                        if (count($order->mergeOrders) > 0) {
                            $tids = $tids . ',' . implode(',', collect($order->mergeOrders)->pluck('tid')->toArray());
                        }
                        \Log::info('合单打印保存2', [$oidArr, $tids]);
                        //合单多包（用户误操作）
                        foreach ($package as $key => $item) {
                            $tid_oids = [];
                            foreach ($oidArr as $oid) {
                                $tid_oids[] = [
                                    'id' => $oid,
                                    'subIds' => $orderItemOId[$oid] ?? [],
                                ];
                            }
                            if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                                $order->index = $key;
                            }
                            $parentWaybillCode = $item['parent_waybill_code'] ? [$item['parent_waybill_code']] : [];
                            $p = Package::query()
                                ->where(['id' => $item['object_id']])
                                ->update([
                                    'user_id' => $order->user_id,
                                    'shop_id' => $order->shop_id,
                                    'waybill_code' => $item['parent_waybill_code'] ? $item['parent_waybill_code'] : $item['waybill_code'],
                                    //'tids'         => $tids,
                                    'wp_code' => $template['wp_code'],
                                    'template_id' => $template['id'],
                                    'auth_source' => $template['auth_source'],
                                    'batch_no' => $batchNoKey,
                                    'tid_oids' => json_encode($tid_oids),
                                    'waybill_status' => Package::WAYBILL_STATUS_SUCCESS,
                                    'sub_waybill_codes' => $item['parent_waybill_code'] ? implode(',', array_diff(array_column($package, 'waybill_code'), $parentWaybillCode)) : null,
                                ]);
                            $express_no_Arr = isset($order->express_no) ? json_decode($order->express_no, true) : '';
                            $waybill_code_Arr[] = isset($order->express_no) && !is_array($express_no_Arr) ? $order->express_no : '';

                            $extra = !empty($orderItemOId) ? $orderItemOId : [];
                            //生成模板打印的内容
                            //如果模板的是wx视频号的话，就走独立的一个分支
//                            \Log::info("获取打印内容", [$template]);

                            $tempPrintData = PrintDataService::templateData($sender['sender_address'], $order, $template,
                                $item, $orderItemOId, false, $key + 1, $packageNum, $contents, [], $packageIndex, $maxPackageSkuNum);


                            $print_data_items = '';
                            if ($template['auth_source'] != Waybill::AUTH_SOURCE_JD) {
                                if (count($tempPrintData) >= 2) {
                                    $print_data_items = json_encode($tempPrintData[1]['contents'][0]['data']['printNextItemBeans'] ?? '');
                                } else {
                                    $print_data_items = json_encode($tempPrintData['contents'][1]['data']['printNextItemBeans'] ?? '');
                                }
                            }


                            foreach ($oidArr as $oid) {
//                                $po = PackageOrder::create([
//                                    'order_id'   => $oid,
//                                    'package_id' => $p->id,
//                                ]);
//                                if (!$p || !$po) {
//                                    throw new ApiException(ErrorConst::PACKAGE_ORDER_SAVE_FAIL);
////                                    throw new \Exception('包裹记录失败！');
//                                }
                                //保存模板id，回收单号使用
                                Order::where('id', $oid)->update([
                                    'express_no' => '',
                                    'template_id' => $template['id'],
                                    // 修改打印中状态
                                    //'print_status'  => Order::PRINT_STATUS_NO,
                                    'print_num' => \DB::raw('print_num + 1'),
                                ]);
                                //打印次数加1
//                                Order::query()->where(['id'=>$oid])->increment('print_num', 1);


                                $orderInfo = Order::query()->with('orderCipherInfo')->where('id', $oid)->first();
                                $waybillResourceShopId = isset($company) ? $company['shop_id'] : $order->shop_id;
                                $extraData = $orderItemOId[$order->id] ?? [];
                                if (empty($extraData)) {
                                    $extra = '[]';
                                } else {
                                    $extra = json_encode([$order->id => $orderItemOId[$order->id]]);
                                }
                                $toShopId = $printMode == 2 ? $order['factory_id'] : $order->shop_id;
                                $history = WaybillHistory::create([
                                    'user_id' => $orderInfo->user_id,
                                    'shop_id' => $waybillResourceShopId,
                                    'order_id' => $orderInfo->id,
                                    'package_id' => $item['object_id'],
                                    'order_no' => $orderInfo->tid ?? null,
                                    'template_id' => $template['id'],
                                    'auth_source' => $template['auth_source'],
                                    'source' => $company['source'] ?? '',
                                    'source_shopid' => $company['source_shopid'] ?? '',
                                    'parent_waybill_code' => $item['parent_waybill_code'] ?? '',
                                    'company_id' => $company['id'],
                                    'waybill_code' => $item['waybill_code'],
                                    'wp_code' => $template['wp_code'],
                                    'print_data' => in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS]) ? json_encode($item) : $item['print_data'],
                                    //'receiver_province'   => $orderInfo->receiver_state,
                                    //'receiver_city'       => $orderInfo->receiver_city,
                                    //'receiver_district'   => $orderInfo->receiver_district,
                                    //'receiver_name'       => $orderInfo->OrderCipherInfo ? $orderInfo->OrderCipherInfo->receiver_name_mask : $orderInfo->receiver_name,
                                    //'receiver_phone'      => $orderInfo->OrderCipherInfo ? $orderInfo->OrderCipherInfo->receiver_phone_mask : $orderInfo->receiver_phone,
                                    //'receiver_address'    => $orderInfo->OrderCipherInfo ? $orderInfo->OrderCipherInfo->receiver_address_mask : $orderInfo->receiver_address,
                                    //'name_index'          => $orderInfo->receiver_name,
                                    //'phone_index'         => $orderInfo->receiver_phone,
                                    'extra' => json_encode($extra),
                                    'print_data_items' => $print_data_items,
                                    'to_shop_id' => $toShopId,
                                    'batch_no' => $batchNoKey,
                                    'platform_waybill_id' => $item['platform_waybill_id'] ?? '',
                                    'created_by' => $operatorName,
                                    'updated_by' => $operatorName,
//                                    'waybill_index' => $counter,
//                                    'waybill_count' => $total,
                                ]);


                                PrintRecord::create([
                                    'user_id' => $orderInfo->user_id,
                                    'shop_id' => $waybillResourceShopId,
                                    'order_id' => $orderInfo->id,
                                    'history_id' => $history->id,
                                    'package_id' => $item['object_id'],
                                    'order_no' => $orderInfo->tid ?? null,
                                    'waybill_code' => $item['waybill_code'],
                                    'wp_code' => $template['wp_code'],
                                    'receiver_province' => $orderInfo->receiver_state,
                                    'receiver_city' => $orderInfo->receiver_city,
                                    'receiver_district' => $orderInfo->receiver_district,
                                    'receiver_town' => $orderInfo->receiver_town,
                                    'receiver_address' => $orderInfo->OrderCipherInfo ? $orderInfo->OrderCipherInfo->receiver_address_mask : $orderInfo->receiver_address,
                                    'receiver_name' => $orderInfo->OrderCipherInfo ? $orderInfo->OrderCipherInfo->receiver_name_mask : $orderInfo->receiver_name,
                                    'receiver_zip' => $orderInfo->receiver_zip,
                                    'receiver_phone' => $orderInfo->OrderCipherInfo ? $orderInfo->OrderCipherInfo->receiver_phone_mask : $orderInfo->receiver_phone,
                                    'buyer_remark' => $orderInfo->buyer_message,
                                    'print_data' => json_encode($tempPrintData),
                                    'name_index' => $orderInfo->receiver_name,
                                    'phone_index' => $orderInfo->receiver_phone,
                                    'batch_no' => $batchNoKey,
                                    'to_shop_id' => $toShopId,
                                    'template_id' => $template['id'],
                                    'template_name' => $template['name'],
                                    'created_by' => $operatorName,
                                    'updated_by' => $operatorName,
                                ]);

                                $tmpInfoArr = [
                                    'id' => $orderInfo->id,
                                    'tid' => $orderInfo->tid,
                                    'package_id' => $history['package_id'],
                                    'waybill_code' => $history['waybill_code'],
                                    'wp_code' => $history['wp_code'],
                                    'tid_arr' => explode(',', $tids),
                                ];
                                $orderInfoArr[] = $tmpInfoArr;

                                //记录操作表
                                event((new OrderWaybillEvent($user, $shop, time(), $tmpInfoArr))->setClientInfoByRequest(request()));
                            }

                            if (ArrayUtil::is_indexed_array($tempPrintData)) {
                                foreach ($tempPrintData as $printDataItem) {
                                    $printData[] = $printDataItem;
                                }
//                                $printData[] = $tempPrintData[0];
//                                $printData[] = $tempPrintData[1];
                            } else {
                                $printData[] = $tempPrintData;
                            }
//                            \Log::info('打印数据', ["printData" => $printData,"tempPrintData" => $tempPrintData]);

//                            $index++;  //批次顺序
//                            self::setBatchNoIndex($shopId, $batchNoKey, $index);
                        }
                    } else { //一单多包
                        \Log::info('一单多包', [$oidArr, $tids]);
                        //查询一个订单是否存在旧单号
                        $express_no_Arr = isset($order->express_no) ? json_decode($order->express_no, true) : '';
                        $waybill_code_Arr[] = isset($order->express_no) && !is_array($express_no_Arr) ? $order->express_no : '';
                        //保存模板id，回收单号使用
                        Order::where('id', $oidArr[0])->update([
                            'express_no' => '',
                            'template_id' => $template['id'],
                            // 修改打印中状态
                            //'print_status'  => Order::PRINT_STATUS_NO,
                            'print_num' => \DB::raw('print_num + 1'),
                        ]);
                        //打印次数加1
//                        Order::query()->where(['id'=>$oidArr[0]])->increment('print_num', 1, ['id'=>$oidArr[0]]);

                        foreach ($package as $key => $item) {
                            $tid_oids = [];
                            foreach ($oidArr as $oid) {
                                $tid_oids[] = [
                                    'id' => $oid,
                                    'subIds' => $orderItemOId[$oid] ?? [],
                                ];
                            }
                            if ($template['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                                $order->index = $key;
                            }
                            $parentWaybillCode = !empty($item['parent_waybill_code']) ? [$item['parent_waybill_code']] : [];
                            $p = Package::query()
                                ->where(['id' => $item['object_id']])
                                ->update([
                                    'user_id' => $order->user_id,
                                    'shop_id' => $order->shop_id,
                                    'template_id' => $template['id'],
                                    'waybill_code' => $item['parent_waybill_code'] ? $item['parent_waybill_code'] : $item['waybill_code'],
                                    //'tids'         => $order['tid'] ?? null,
                                    'wp_code' => $template['wp_code'],
                                    'auth_source' => $template['auth_source'],
                                    'batch_no' => $batchNoKey,
                                    'tid_oids' => json_encode($tid_oids),
                                    'waybill_status' => Package::WAYBILL_STATUS_SUCCESS,
                                    'sub_waybill_codes' => $item['parent_waybill_code'] ? implode(',', array_diff(array_column($package, 'waybill_code'), $parentWaybillCode)) : null,
                                ]);
//                            $po = PackageOrder::create([
//                                'order_id'   => $oidArr[0],
//                                'package_id' => $p->id,
//                            ]);
//                            if (!$p || !$po) {
//                                throw new ApiException(ErrorConst::PACKAGE_ORDER_SAVE_FAIL);
////                                throw new \Exception('包裹记录失败！');
//                            }

//                            \Log::info("获取打印内容", [$template]);

                            $tempPrintData = PrintDataService::templateData($sender['sender_address'], $order, $template,
                                $item, $orderItemOId, false, $key + 1, $packageNum, $contents, $printConfig, $packageIndex, $maxPackageSkuNum);


                            $print_data_items = '';
                            if ($template['auth_source'] != Waybill::AUTH_SOURCE_JD) {
                                if (ArrayUtil::is_indexed_array($tempPrintData)) {
                                    $print_data_items = json_encode($tempPrintData[1]['contents'][0]['data']['printNextItemBeans'] ?? '');
                                } else {
                                    $print_data_items = json_encode($tempPrintData['contents'][1]['data']['printNextItemBeans'] ?? '');
                                }
                            }


                            $waybillResourceShopId = isset($company) ? $company['shop_id'] : $order->shop_id;
                            $toShopId = $printMode == 2 ? $order['factory_id'] : $order->shop_id;
                            $extraData = $orderItemOId[$order->id] ?? [];
                            if (empty($extraData)) {
                                $extra = '[]';
                            } else {
                                $extra = json_encode([$order->id => $orderItemOId[$order->id]]);
                            }
                            $history = WaybillHistory::create([
                                'user_id' => $order->user_id,
                                'shop_id' => $waybillResourceShopId,
                                'order_id' => $order->id,
                                'package_id' => $item['object_id'],
                                'order_no' => $order['tid'] ?? null,
                                'template_id' => $template['id'],
                                'auth_source' => $template['auth_source'],
                                'source' => $company['source'] ?? '',
                                'source_shopid' => $company['source_shopid'] ?? '',
                                'parent_waybill_code' => array_get($item, 'parent_waybill_code') ?? '',
                                'waybill_code' => array_get($item, 'waybill_code'),
                                'wp_code' => $template['wp_code'],
                                'print_data' => in_array($template['auth_source'], [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS]) ? json_encode($item) : array_get($item, 'print_data'),
                                //'receiver_province'   => $order->receiver_state,
                                //'receiver_city'       => $order->receiver_city,
                                //'receiver_district'   => $order->receiver_district,
                                //'receiver_name'       => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_name_mask : $order->receiver_name,
                                //'receiver_phone'      => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_phone_mask : $order->receiver_phone,
                                //'receiver_address'    => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_address_mask : $order->receiver_address,
                                //'name_index'          => $order->receiver_name,
                                //'phone_index'         => $order->receiver_phone,
                                'extra' => $extra,
                                'print_data_items' => $print_data_items,
                                'to_shop_id' => $toShopId,
                                'batch_no' => $batchNoKey,
                                'platform_waybill_id' => $item['platform_waybill_id'] ?? '',
                                'created_by' => $operatorName,
                                'updated_by' => $operatorName,

                            ]);
                            PrintRecord::create([
                                'user_id' => $order->user_id,
                                'shop_id' => $waybillResourceShopId,
                                'order_id' => $order->id,
                                'history_id' => $history->id,
                                'package_id' => $item['object_id'],
                                'order_no' => $order['tid'] ?? null,
                                'waybill_code' => array_get($item, 'waybill_code'),
                                'wp_code' => $template['wp_code'],
                                'receiver_province' => $order->receiver_state,
                                'receiver_city' => $order->receiver_city,
                                'receiver_district' => $order->receiver_district,
                                'receiver_town' => $order->receiver_town,
                                'receiver_address' => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_address_mask : $order->receiver_address,
                                'receiver_name' => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_name_mask : $order->receiver_name,
                                'receiver_zip' => $order->receiver_zip,
                                'receiver_phone' => $order->OrderCipherInfo ? $order->OrderCipherInfo->receiver_phone_mask : $order->receiver_phone,
                                'buyer_remark' => $order->buyer_message,
                                'print_data' => json_encode($tempPrintData),
                                'name_index' => $order->receiver_name,
                                'phone_index' => $order->receiver_phone,
                                'batch_no' => $batchNoKey,
                                'to_shop_id' => $toShopId,
                                'template_id' => $template['id'],
                                'template_name' => $template['name'],
                                'created_by' => $operatorName,
                                'updated_by' => $operatorName,
                            ]);

                            if (ArrayUtil::is_indexed_array($tempPrintData)) {
                                foreach ($tempPrintData as $printDataItem) {
                                    $printData[] = $printDataItem;
                                }
//                                $printData[] = $tempPrintData[0];
//                                $printData[] = $tempPrintData[1];
                            } else {
//                                \Log::info('打印数据一条', ["tempPrintData" => $tempPrintData]);
                                $printData[] = $tempPrintData;
                            }
//                            \Log::info('打印数据', ["printData" => $printData,"tempPrintData" => $tempPrintData]);
                            $tmpInfoArr = [
                                'id' => $order->id,
                                'tid' => $order->tid,
                                'package_id' => $history['package_id'],
                                'waybill_code' => $history['waybill_code'],
                                'wp_code' => $history['wp_code'],
                                'tid_arr' => explode(',', $tids),
                            ];
                            $orderInfoArr[] = $tmpInfoArr;
                            //记录操作表
                            event((new OrderWaybillEvent($user, $shop, time(), $tmpInfoArr))->setClientInfoByRequest(request()));

//                            $index++;  //批次顺序
//                            self::setBatchNoIndex($shopId, $batchNoKey, $index);
                        }
                    }
                } else {
                    //取号错误信息
                    $obj = new \stdClass();
                    $obj->text = '取号错误';
                    $obj->tid = [$order['tid'] ?? null];
                    if (Waybill::AUTH_SOURCE_DY == $template['auth_source']) {
                        $obj->info = $package[0]['data']['err_infos'][0]['err_msg'] ?? "未知错误";
                        if (!empty($package[0]) && is_string($package[0])) {
                            $obj->info = $package[0];
                        }
                    } else if (Waybill::AUTH_SOURCE_TAOBAO == $template['auth_source']) {
                        $package = json_decode(json_encode($package), true);
                        $obj->info = $package[0]['sub_msg'] ?? "未知错误";
                    } else {
                        $obj->info = $package;
                    }
                    Order::where('id', $oidArr[0])->update([
                        'express_no' => json_encode($obj, 320),
                        'template_id' => $template['id'],
                        'express_code' => $template['wp_code'],
//                        'merge_flag' => '', // 取消合单标记
                    ]);
                    $failedOrders[] = json_encode($obj, 320); //失败数据
                    $tids = $order['tid'] ?? null;
                    if (!empty($order->mergeOrders) && count($order->mergeOrders) > 0) {
                        $tids = $tids . ',' . implode(',', collect($order->mergeOrders)->pluck('tid')->toArray());
                    }
                    foreach ($oidArr as $oid) {
                        $tmpOrder = collect($orders)->where('id', $oid)->first();
                        empty($tmpOrder) && $tmpOrder = Order::query()->find($oid);
                        //记录取号失败
                        event((new OrderWaybillFailEvent($user, $shop, time(), $tmpOrder, explode(',', $tids), $obj->info))->setClientInfoByRequest(request()));
                    }

                }
            }
        } else {
            throw new ApiException(ErrorConst::ORDER_PRINT_DATA_EMPTY);
//			throw new \Exception('无可获取打印数据订单!');
        }

        return ['print_data' => $printData, 'failed_data' => $failedOrders, 'orderInfoArr' => $orderInfoArr];
    }

    /**
     * 按规格件数拆单,按规格拆单，按逻辑拆包裹,
     * 如果指定打印件数，就把订单全部复制到包裹里面
     * 返回的包裹格式为：
     *     [["tid" => $item["tid"],
     * "oid" => $item["oid"],
     * "orderId" => $item['order_id'],
     * 'orderItemId' => $item['id'],
     * 'shippedNum' => 1,  //一件一个包裹，所以这里写死了
     * ]];
     *
     * @param $order
     * @param $packageNum
     * @return array|null
     */
    public static function multiPackages($order, $packageNum): ?array
    {
        $packages = [];
        if ($packageNum < 0) {
            // 按规格件数拆，就是一件商品一件商品拆
            if ($packageNum == Order::PRINT_BY_GOODS_NUM) {
                //先把所有的子订单都找出来
                $orderItems = $order['order_item'];
//                $packageNum = array_sum(array_column($order['order_item'], 'goods_num'));
                if (isset($order['mergeOrders']) && !empty($order['mergeOrders'])) {
                    foreach ($order['mergeOrders'] as $value) {
                        $value = collect($value)->toArray();
                        foreach ($value['order_item'] as $item) {
                            $orderItems[] = $item;
                        }
//                        $packageNum += array_sum(array_column($value['order_item'], 'goods_num'));
                    }
                }


                //然后按子订单里面的商品数量，把一个商品一个包裹,

                foreach ($orderItems as $item) {
                    for ($i = 0; $i < $item['goods_num']; $i++) {
                        $packages[] = [["tid" => $item["tid"],
                            "oid" => $item["oid"],
                            "orderId" => $item['order_id'],
                            'orderItemId' => $item['id'],
                            'shippedNum' => 1,  //一件一个包裹，所以这里写死了
                            'num_iid' => $item['num_iid'],
                            'sku_id' => $item['sku_id'],
                        ]];
                    }
                }
                \Log::info('按规格件数拆包裹', [$packages]);

            }

            //按规格种类
            if ($packageNum == Order::PRINT_BY_GOODS_KIND) {
                $mergeGoods = $order['order_item'];
                //如果有合单，就把合单的商品也加进来
                if (isset($order['mergeOrders']) && !empty($order['mergeOrders'])) {
                    foreach ($order['mergeOrders'] as $value) {
                        $value = collect($value)->toArray();
                        foreach ($value['order_item'] as $item) {
                            $mergeGoods[] = $item;
                        }
                    }
                }
                //规格用sku_id分组
                $newMergeGoods = collect($mergeGoods)->groupBy('sku_id');

                foreach ($newMergeGoods as $skuId => $goods) {
                    foreach ($goods as $item) {
                        $packages[] = [["tid" => $item["tid"],
                            "oid" => $item["oid"],
                            "orderId" => $item['order_id'],
                            'orderItemId' => $item['id'],
                            'shippedNum' => $item['goods_num'],  //一件一个包裹，所以这里写死了
                            'num_iid' => $item['num_iid'],
                            'sku_id' => $item['sku_id'],
                        ]];
                    }

                }
                \Log::info('按规格件包裹', $packages);
            }
        } //不拆直接打印多包的情况，就把所有订单的商品都放包裹里面
        elseif ($packageNum > 1) {
            $mergeGoods = $order['order_item'];
            //如果有合单，就把合单的商品也加进来
            if (isset($order['mergeOrders']) && !empty($order['mergeOrders'])) {
                foreach ($order['mergeOrders'] as $value) {
                    $value = collect($value)->toArray();
                    foreach ($value['order_item'] as $item) {
                        $mergeGoods[] = $item;
                    }
                }
            }
            //按件数打印，就生成件数的包裹
            for ($i = 0; $i < $packageNum; $i++) {
                $singlePackage = [];
                foreach ($mergeGoods as $item) {
                    $singlePackage[] = ["tid" => $item["tid"],
                        "oid" => $item["oid"],
                        "orderId" => $item['order_id'],
                        'orderItemId' => $item['id'],
                        'shippedNum' => $item['goods_num'],  //一件一个包裹，所以这里写死了
                        'num_iid' => $item['num_iid'],
                        'sku_id' => $item['sku_id'],
                    ];
                }
                $packages[] = $singlePackage;
            }

        } else {
            return null;
        }
        \Log::info('一单多包', [$packages]);
        return $packages;
    }

    public static function getRelPackageNum($order, $packageNum, int $maxPackageSkuNum = 1, array $orderItemOId = [])
    {
        if ($packageNum < 0) {
            // 按规格件数
            if ($packageNum == Order::PRINT_BY_GOODS_NUM) {
                $packageNum = array_sum(array_column($order['order_item'], 'goods_num'));
                if (isset($order['mergeOrders']) && !empty($order['mergeOrders'])) {
                    foreach ($order['mergeOrders'] as $value) {
                        $value = collect($value)->toArray();
                        $packageNum += array_sum(array_column($value['order_item'], 'goods_num'));
                    }
                }
            }

            //按规格种类
            if ($packageNum == Order::PRINT_BY_GOODS_KIND) {
                $mergeGoods = $order['order_item'];
                if (isset($order['mergeOrders']) && !empty($order['mergeOrders'])) {
                    foreach ($order['mergeOrders'] as $value) {
                        $value = collect($value)->toArray();
                        foreach ($value['order_item'] as $item) {
                            $mergeGoods[] = $item;
                        }
                    }
                }
                $newMergeGoods = collect($mergeGoods)->groupBy(['num_iid', 'sku_id']);
                $packageNum = 0;
                foreach ($newMergeGoods as $val) {
                    $packageNum += count($val);
                }
            }


        }

        return $packageNum;
    }

    public static function getBranchAndSendAddress(int $userId, int $shopId, $company, $template): array
    {
        //网点地址
        $branchAddress = [
            'province' => $company->province,
            'city' => $company->city,
            'district' => $company->district,
            'address' => $company->detail,
            'street' => $company->street ?? ''
        ];
        //发货地址
        $ship = ShippingAddress::query()
            ->where('shop_id', $shopId)
            ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_YES)
            ->where('is_default', ShippingAddress::IS_DEFAULT_YES)->first();
        if (empty($ship)) {
            throw new ApiException(ErrorConst::NOT_SET_DELIVERY_ADDRESS);
//            throw new \Exception('未设置默认发货地址');
        }
        $senderAddress = [
            'province' => $ship->province,
            'city' => $ship->city,
            'district' => $ship->district,
            'address' => $ship->address,
        ];
        $sender = [
            'sender_name' => $ship->sender_name,
            'mobile' => $ship->mobile,
        ];
        if ($template['shipping_address_id'] && $template['shipping_address_id'] > 0) {
            $ship = ShippingAddress::find($template['shipping_address_id']);
            $senderAddress = [
                'province' => $ship->province,
                'city' => $ship->city,
                'district' => $ship->district,
                'address' => $ship->address,
            ];
            $sender = [
                'sender_name' => $ship->sender_name,
                'mobile' => $ship->mobile,
            ];
        }
        if (!empty($template['sender_name']) && !empty($template['sender_mobile'])) {
            $sender = [
                'sender_name' => isset($ship->sender_name) ? $ship->sender_name : $template['sender_name'],
                'mobile' => isset($ship->mobile) ? $ship->mobile : $template['sender_mobile'],
            ];
        }

        return [
            'branch_address' => array_merge($branchAddress, $sender),
            'sender_address' => array_merge($senderAddress, $sender),
        ];
    }

    /**
     * @param $userId
     * @param $shopId
     * @param string $mode
     * @param int $templateId
     * @param string $batchNo
     * @param array $packs
     * @param string $softRemark
     * @param array $senderInfo
     * @param $version
     * @param $printOrderStatus
     * @param $orderType
     * @param $packageNum
     * @param $isPrintRefund
     * @param $companyId
     * @param string $operatorName
     * @return array
     * @throws ApiException
     * @throws BusinessException
     * @throws ErrorCodeException
     * @throws \Throwable
     */
    public static function getPrintDataAndWaybillWarehouse($userId, $shopId, string $mode, int $templateId, string $batchNo,
                                                           array $packs, string $softRemark, array $senderInfo, $version,
                                                           $printOrderStatus, $orderType, $packageNum, $isPrintRefund,
                                                           $companyId, string $operatorName = ""): array
    {

        $waybillHistoryList = [];
        $failedOrders = [];
        $printDataArr = [];
        $failedData = [];
        $orderIdArr = array_pluck($packs, 'orderId');
        $allOrderIdArr = collect($packs)->pluck('items.*.orderId')->collapse()->merge($orderIdArr)->filter()->unique()->toArray();
        $allOrderItemIds = collect($packs)->pluck('items.*.orderItemId')->collapse()->filter()->unique()->toArray();
        Log::info('获取全部的订单ID', [$allOrderIdArr]);
        Log::info('获取全部的子订单ID', [$allOrderItemIds]);
        if ($orderType == WaybillHistory::ORDER_TYPE_GENERAL){
            // 批量修改 orders 表 soft_remark 字段  。感觉有问题
//            if ($mode == 'new' && !empty($softRemark)) {
//                Order::query()->whereIn('id', $allOrderIdArr)->update(['soft_remark' => $softRemark]);
//            }
            $allOrderList = \App\Models\Fix\Order::query()->with(['orderItem', 'orderCipherInfo'])
                ->where(['locked_at' => null])
                ->whereIn('id', $allOrderIdArr)
                ->get();
            $allOrderItemList = \App\Models\Fix\OrderItem::query()
                ->with(['order', 'order.orderCipherInfo', 'customGoods', 'customGoodsSkus'])
                ->whereIn('id', $allOrderItemIds)
                ->get();
        }else{
            $allOrderList = CustomizeOrder::query()
                ->with(['order.orderCipherInfo'])
                ->whereIn('id', $allOrderIdArr)
                ->get();
            $allOrderList = $allOrderList->map(function ($item){
                $item->orderCipherInfo = $item->order->orderCipherInfo??null;
                return $item;
            });
            $allOrderItemList = new Collection();
        }

        $orders = [];
        $itemIdStrArr = [];
        // 处理合单
        foreach ($packs as $index => $pack) {
            if ($mode == PrintModeConst::OLD_REDO || $mode == PrintModeConst::PRINT_RECORD) {
                continue;
            }
            $orderId = $pack['orderId'];
            $tmpPack = $pack;
            $tmpPack['mergeOrderItems'] = null;
            $tmpPack['itemIdStr'] = null;
            if (!empty($pack['items'])) {
                $thisOrderItemIds = array_pluck($pack['items'], 'orderItemId');
                // 前端用
                $tmpPack['mergeOrderItems'] = $allOrderItemList->whereIn('id', $thisOrderItemIds)
                    ->map(function ($item){
                        return array_only($item->toArray(),['tid', 'order_id']);
                    })->values()->all();
                $tmpPack['itemIdStr'] = implode(',', $allOrderItemList->whereIn('id', $thisOrderItemIds)->pluck('id')->toArray());
                $itemIdStrArr[] = $tmpPack['itemIdStr'];
                Log::info('加入合单', ["orderId" => $orderId, "thisOrderItemIds" => $thisOrderItemIds]);
            }
            $packs[$index] = $tmpPack;
        }

        $successList = $failList = [];
        if ($mode == PrintModeConst::PRINT_RECORD) { // 打印日志打印
            list($successList, $failList) = self::handlePrintRecordPrint($userId, $shopId, $packs,  $batchNo, $version);
        } elseif (in_array($mode, [PrintModeConst::OLD_REDO, PrintModeConst::OLD_REBUILD, PrintModeConst::OLD_API_PRINT_DATA])) {
            list($successList, $failList) = self::handleOldHistoryPrint($userId, $shopId, $packs, $batchNo, $mode,
                $allOrderList, $allOrderItemList, $version, $isPrintRefund, $operatorName, $orderType);
        } elseif (in_array($mode, [PrintModeConst::NEW,PrintModeConst::RESEND_NEW, PrintModeConst::ONLY_TAKE])) { // new 新单号打印 onlyTake 只取号
            list($successList, $failList) = self::handleNewPrint($templateId, $companyId, $userId, $shopId, $packageNum, $packs,
                $orders, $orderType, $itemIdStrArr, $allOrderList, $allOrderItemList, $batchNo, $senderInfo, $version,
                $mode, $printOrderStatus, $softRemark,$isPrintRefund, $operatorName);
        }elseif ($mode == PrintModeConst::MIX_OLD_AND_NEW){
            // 提取包含 waybillCode 的
            $oldPacks = array_filter($packs, function ($pack) {
                return !empty($pack['waybillCode']);
            });
            $newPacks = array_filter($packs, function ($pack) {
                return empty($pack['waybillCode']);
            });
            if (count($packs) != (count($oldPacks) + count($newPacks))) {
                throw new InvalidArgumentException('拆分后 packs 数对不上');
            }
            $oldMode = PrintModeConst::OLD_REBUILD;
            list($successList1, $failList1) = self::handleOldHistoryPrint($userId, $shopId, $oldPacks, $batchNo, $oldMode,
                $allOrderList, $allOrderItemList, $version, $isPrintRefund, $operatorName, $orderType);
            $newMode = PrintModeConst::NEW;
            list($successList2, $failList2) = self::handleNewPrint($templateId, $companyId, $userId, $shopId, $packageNum, $newPacks,
                $orders, $orderType, $itemIdStrArr, $allOrderList, $allOrderItemList, $batchNo, $senderInfo, $version,
                $newMode, $printOrderStatus, $softRemark,$isPrintRefund, $operatorName);

            $successList = collect($successList1)->merge($successList2)->sortBy('packIndex')->values()->all();
            $failList = collect($failList1)->merge($failList2)->sortBy('packIndex')->values()->all();

            $counter = 0;
            $printRecordCountTotal = collect($successList)->filter(function ($item){return !empty($item['printRecordId']);})->count();
            foreach ($successList as $successItem) {
                if (!empty($successItem['printRecordId'])) { // 判断是因为有一单多包
                    $counter++;
                    PrintRecord::query()->where('id', $successItem['printRecordId'])
                        ->update(['print_index' => $counter,'print_count' => $printRecordCountTotal]);
                }
            }
        }else{
            // 不支持的模式
            throw new ApiException(ErrorConst::NOT_SUPPORT_MODE);
        }

        if (empty($successList) && empty($failList)) {
            throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
//            throw new \Exception('订单获取打印数据异常！');
        }
        // 打印数据排序
        $failList = collect($failList)->sortBy('packIndex')->values()->toArray();
        return ['successList' => $successList, 'failList' => $failList];
    }

    /**
     * @param $company
     * @param array $template
     * @return Shop|Waybill|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws ApiException
     */
    public static function getWaybill($company, array $template)
    {
        if ($company->source == Company::SOURCE_COMPANY_STATUS_NO) {
            $waybillAuth = WaybillUtil::findShopWaybillAuth($template['shop_id'], $template['auth_source'], $template['owner_id']);
//            if (!Environment::isWx() && WaybillUtil::isValidateAuthSource($template['auth_source'])) {
//                $waybillAuth = Shop::find($template['shop_id']);
//                $waybillAuth->auth_source = $template['auth_source'];
//            } else {
//                $waybillAuth = Waybill::where([
//                    'owner_id' => $template['owner_id'],
//                    'auth_source' => $template['auth_source']
//                ])->orderBy('id', 'desc')->first();
//            }
        } else {
            if (WaybillUtil::isValidateAuthSource($template['auth_source'])) {
                $waybillAuth = Shop::query()->where('identifier', $company->owner_id)->first();
                $waybillAuth->auth_source = Waybill::AUTH_SOURCE_DY;
            } else {
                $waybillAuth = Waybill::where([
                    'owner_id' => $template['owner_id'],
                    'auth_source' => $template['auth_source']
                ])->orderBy('id', 'desc')->first();
            }
        }
        return $waybillAuth;
    }

    /**
     * 排除退款订单
     * @param $allOrders
     * @param array $mergeOrderUnsetId
     * @param array $orderItemIds
     * @return array
     */
    public static function excludeRefund(&$allOrders, array &$orderItemIds, array &$mergeOrderUnsetId = []): array
    {
        if (is_array($allOrders)) {
            $allOrdersArr = $allOrders;
        } else {
            $allOrdersArr = $allOrders->toArray();
        }
        $orderStatus = array_column($allOrdersArr, null, 'tid');
        //干掉订单状态非已付款和已发生退款的订单
        foreach ($allOrders as $index => $newOrder) {
            if (isset($orderStatus[$newOrder['tid']]) && $orderStatus[$newOrder['tid']]['refund_status'] == Order::REFUND_STATUS_YES) {
                unset($allOrders[$index]);
                $mergeOrderUnsetId[] = $newOrder['id'];
                // 异步保存订单
//                    dispatch(new SyncSaveOrders($newOrder['user_id'], $newOrder['shop_id'], [$orderStatus[$newOrder['tid']]]));
                continue;
            }
            //部分退款 只打印没退款的子订单
            if (isset($orderStatus[$newOrder['tid']]) && $orderStatus[$newOrder['tid']]['refund_status'] == Order::REFUND_STATUS_PART) {
                /*两种情况 一种是全选 一种是选中了退款子订单*/
                //先判断是全选还是部分选中
                $tempArr = collect($orderStatus[$newOrder['tid']]['order_item'])->where('refund_status', 0)->all();
                $tempOidArr = array_column($tempArr, 'oid');
                $oidArr = OrderItem::query()->whereIn('oid', $tempOidArr)->get()->toArray();
                $oidIdArr = array_column($oidArr, 'id');
                if (array_key_exists($newOrder['id'], $orderItemIds)) {
                    //包含就是部分选中  过滤掉退款的子订单
                    foreach ($orderItemIds[$newOrder['id']] as $key => $item) {
                        if (!in_array($item, $oidIdArr)) {
                            unset($orderItemIds[$newOrder['id']][$key]);
                        }
                    }
                } else {
                    $orderItemIds[$newOrder['id']] = $oidIdArr;
                }
//                    dispatch(new SyncSaveOrders($newOrder['user_id'], $newOrder['shop_id'], [$orderStatus[$newOrder['tid']]]));
            }
            //过滤掉已发货订单
//            if (isset($orderStatus[$newOrder['tid']]) && in_array($orderStatus[$newOrder['tid']]['order_status'], [Order::ORDER_STATUS_DELIVERED,
//                    Order::ORDER_STATUS_RECEIVED, Order::ORDER_STATUS_SUCCESS, Order::ORDER_STATUS_PART_DELIVERED])) {
//                unset($allOrders[$index]);
//                $mergeOrderUnsetId[] = $newOrder['id'];
//                // 异步保存订单
////                    dispatch(new SyncSaveOrders($newOrder['user_id'], $newOrder['shop_id'], [$orderStatus[$newOrder['tid']]]));
//                continue;
//            }
        }
        return [];
    }

    /**
     * @param $userId
     * @param $shopId
     * @param array $packs
     * @param string $batchNo
     * @param string $mode
     * @param Collection $allOrderList
     * @param Collection $allOrderItemList
     * @param $version
     * @param $isPrintRefund
     * @param $operatorName
     * @return array
     * @throws ApiException
     * @throws ErrorCodeException
     */
    public static function handleOldHistoryPrint($userId, $shopId, array $packs, string $batchNo, string $mode,
                                                 Collection $allOrderList, Collection $allOrderItemList, $version,
                                                 $isPrintRefund, $operatorName,$orderType): array
    {
        $shop = \App\Models\Fix\Shop::find($shopId);
        $successList = $failList = [];
        if (empty($packs)) {
            return [$successList, $failList];
        }
        $waybillCodeArr = array_pluck($packs, 'waybillCode');
        $waybillCodeArr = array_filter($waybillCodeArr);
        $waybillHistoryList = WaybillHistory::query()
            ->with(['order','lastPrintRecord','package','packageOrders','template.company'])
//            ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
//            ->where('wp_code', $template['wp_code'])
            ->whereIn('waybill_code', $waybillCodeArr)
//            ->where('auth_source', $template['auth_source'])
//            ->where('version', $version)
            ->get();
        // 暂时取消回收
//        $recoveryCount = $waybillHistoryList->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_YES)->count();
//        if ($recoveryCount > 0){
//            throw new ApiException(ErrorConst::WAYBILL_RECOVERY_ERROR);
//        }

        $printDataArr = [];
        $printRecordArr = $waybillHistoryList->pluck('lastPrintRecord', 'waybill_code')->toArray();

        $paramsStrArr = [];
        if (Environment::isDy()) {
            $templateIdArr = $waybillHistoryList->pluck('template_id')->toArray();
            $paramsStrArr = self::getParamsStr($templateIdArr);
            // 更新最新的加密打印信息
//            $historyArrPrintData = self::handleLatestEncryptedPrintData($template, $waybillAuth, $waybillHistoryList);
        }

        $packTotal = count($packs);
        foreach ($packs as $index => $pack) {
            $printData = json_decode($printRecordArr[$pack['waybillCode']]['print_data'], true);
            $waybillHistory = $waybillHistoryList->where('waybill_code', $pack['waybillCode'])->first();
            // 获取历史打印数据
            if ($mode == PrintModeConst::OLD_REDO) {
                if (empty($printData)){ // 仅取号，没有打印记录
                    $printData = json_decode($waybillHistory['print_data'], true);
                    if (empty($printData)){
                        // 找不到打印数据
                        throw new ApiException(ErrorConst::PRINT_DATA_NOT_FOUND);
                    }
                }
                if (Environment::isDy()) {
                    $printData = self::handlePrintDataMiss($printData, $waybillHistory);
                    $printData['contents'][0]['params'] = $paramsStrArr[$waybillHistory['template_id']] ?? '';
                }
                if (!empty($pack['content']) && !empty($pack['customContent'])) {
                    $printData['contents'][1] = $pack['customContent'];
                }
            } elseif ($mode == PrintModeConst::OLD_REBUILD) {
                if (empty($printData)){ // 仅取号，没有打印记录
                    $printData = json_decode($waybillHistory['print_data'], true);
                }
                if (Environment::isDy()) {
                    $printData = self::handlePrintDataMiss($printData, $waybillHistory);
                }
//                $packageOrders = PackageOrder::query()->with(['orderItem'])->where('package_id', $waybillHistory['package_id'])->get();
                PackageOrder::query()->with(['orderItem'])
                    ->where('package_id', $waybillHistory['package_id'])
                    ->where('source_type', Package::SOURCE_TYPE_PRINT)
                    ->delete();
                if ($orderType == WaybillHistory::ORDER_TYPE_GENERAL) {
                    $packItems = [];
                    foreach ($pack['items'] as $item) {
                        $orderItem = $allOrderItemList->where('id', $item['orderItemId'])->first();
                        log::info("orderItem",[$isPrintRefund,"orderItem"=>$orderItem]);
                        // 不打印退款，就移出数据
                        if (!$isPrintRefund && $orderItem['refund_status'] != OrderItem::REFUND_STATUS_NO){
                            continue;
                        }
                        PackageOrder::create([
                            'package_id' => $waybillHistory['package_id'],
                            'order_id' => $item['orderId'],
                            'order_item_id' => $item['orderItemId'],
                            'num' => $item['num'],
                            'version' => $version,
                            'tid' => $orderItem['tid'],
                            'oid' => $orderItem['oid'],
                            'num_iid' => $orderItem['num_iid'],
                            'sku_id' => $orderItem['sku_id'],
                            'status' => Order::ORDER_STATUS_PAYMENT,
                            'source_type' => Package::SOURCE_TYPE_PRINT,
                        ]);
                        $packItems[] = [
                            'orderId' => $item['orderId'],
                            'orderItemId' => $item['orderItemId'],
                            'num' => $item['num'],
                        ];
                    }

                    Log::info("packItems",["packItems"=>$packItems]);
                    $totalNum = collect($packItems)->sum('num');
                    Package::query()->where('id', $waybillHistory['package_id'])->update(['total_num' => $totalNum]);

                    $pack['items'] = $packItems;
//                    $allOrderIdArr = collect($packItems)->pluck('orderId')->unique()->toArray();
//                    $allOrderItemIds = collect($packItems)->pluck('orderItemId')->unique()->toArray();
//                    $allOrderList = Order::query()->with(['orderItem', 'orderCipherInfo'])
//                        ->where(['locked_at' => null])
//                        ->whereIn('id', $allOrderIdArr)
//                        ->get();
//                    $allOrderItemList = OrderItem::query()
//                        ->with(['order', 'order.orderCipherInfo', 'customGoods', 'customGoodsSkus'])
//                        ->whereIn('id', $allOrderItemIds)
//                        ->get();
                    $templateId=$waybillHistory['template_id'];

                    $order = $allOrderList->where('id', $pack['orderId'])->first();
                    $printContentConfig = self::getPrintContentConfig($shopId,$templateId);
                    $goodsList = self::extractGoodsList($pack, $allOrderList, $allOrderItemList, $printContentConfig, $waybillHistory['soft_remark']);
                }else{
                    $templateId = $waybillHistory['template_id'];
                    $order = $allOrderList->where('id', $pack['orderId'])->first();
                    $printContentConfig = self::getPrintContentConfig($shopId,$templateId);
                    $goodsList = OrderPrintService::extractGoodsListByCustom($order, $printContentConfig);
                }
                list($contentArr, $goodsList) = self::genPrintCustomContent($printContentConfig, $goodsList);
                list($itemListMap, $otherCustomList) = self::genItemListMap($contentArr, $goodsList, $order, $batchNo, $index, $packTotal);

                $printData['itemListMap'] = $itemListMap;
                $printData['otherCustomList'] = $otherCustomList;
            }elseif ($mode == PrintModeConst::OLD_API_PRINT_DATA) {
                if (empty($printData)){ // 仅取号，没有打印记录
                    // 找不到打印数据
                    throw new ApiException(ErrorConst::PRINT_DATA_NOT_FOUND);
                }
                $waybillList = [
                    'wp_code' => $printData['express_code'],
                    'waybill_code' => $printData['express_no'],
                ];
                $shop = \App\Models\Fix\Shop::firstById($shopId);
                $waybillService = WaybillServiceManager::init(Waybill::AUTH_SOURCE_DY, $shop->access_token);
                $printDataNew = $waybillService->getPrintData($waybillList);

                if (Environment::isDy()) {
                    $printData['contents'][0]['params'] = $paramsStrArr[$waybillHistory['template_id']] ?? '';
                }
                $printData['contents'][0]['encryptedData'] = $printDataNew['print_data'];
                $printData['contents'][0]['signature'] = $printDataNew['sign'];

            }else {
                // 不支持的模式
                throw new ApiException(ErrorConst::NOT_SUPPORT_MODE);
            }
            $printDataArr[$pack['waybillCode']] = $printData;
        }

//        $printRecords = [];
        foreach ($waybillHistoryList as $index => $history) {
            $order = $history['order'];
            $pack = collect($packs)->where('waybillCode', $history['waybill_code'])->first();
            $printData = $printDataArr[$history['waybill_code']];
            $numOfPackage = ($index + 1). '/' . $packTotal;
            if (Environment::isDy()) {
                $printData['contents'][0]['params'] = $paramsStrArr[$history['template_id']] ?? '';
            }
            $printData['itemListMap']['numOfPackage'] = $numOfPackage;

            // 提取send_content
            $sendContentArr = array_merge([$printData['itemListMap']['contents']], array_column($printData['otherCustomList'], 'contents'));
            $send_content = implode("\n分页\n",$sendContentArr);
            $total = count($waybillHistoryList);
            $thisPrintRecord = $printRecordArr[$pack['waybillCode']];
            if (empty($thisPrintRecord)){ // 没有打印记录
                $template = $history->template;
                $thisPrintRecord = PrintRecord::newModelInstance([
                    'user_id' => 0,
                    'shop_id' => $template['company']['shop_id'],
                    'order_id' => $order['id'],
                    'package_id' => $history->package_id,
                    'history_id' => $history->id,
                    'order_no' => $order['tid'],
                    'waybill_code' => $history['waybill_code'],
                    'wp_code' => $history['wp_code'],
                    'receiver_province' => $order['receiver_province'],
                    'receiver_city' => $order['receiver_city'],
                    'receiver_district' => $order['receiver_district'],
                    'receiver_name' =>  $order['receiver_district'],
                    'receiver_phone' =>  $order['receiver_phone'],
                    'receiver_address' =>  $order['receiver_address'],
                    'receiver_zip' => 0,
                    'buyer_remark' => '',
                    'print_data' => json_encode($printData, JSON_UNESCAPED_UNICODE),
                    'batch_no' => $batchNo,
                    'version' => $version,
                    'to_shop_id' => $order['shop_id'],
                    'template_id' => $template['id'],
                    'template_name' => $template['name'],
                    'print_index' => $index + 1,
                    'print_count' => $total,
                    'order_type' => $history['order_type'],
                    'created_by'=>$operatorName,
                    'updated_by'=>$operatorName,
                    'send_content' => $send_content,
                ]);
                $thisPrintRecord = $thisPrintRecord->toArray();
            }else{
                $thisPrintRecord['print_data'] = json_encode($printData, JSON_UNESCAPED_UNICODE);
                $thisPrintRecord['send_content'] = $send_content;
            }

            if ($history['order_type'] == WaybillHistory::ORDER_TYPE_GENERAL){
//                $printRecords[] = self::buildPrintRecordData($thisPrintRecord, $order, $printData, $index, $total, $batchNo, $version);
                $thisPrintRecord['print_index'] = $index + 1;
                $thisPrintRecord['print_count'] = $total;
                $thisPrintRecord['batch_no'] = $batchNo;
                unset($thisPrintRecord['id']);
                $printRecord = PrintRecord::query()->create($thisPrintRecord);
            }elseif($history['order_type'] == WaybillHistory::ORDER_TYPE_FREE){
                $thisPrintRecord['print_index'] = $index + 1;
                $thisPrintRecord['print_count'] = $total;
                $thisPrintRecord['batch_no'] = $batchNo;
                unset($thisPrintRecord['id']);
                $printRecord = PrintRecord::query()->create($thisPrintRecord);
//                $printRecords[] = $thisPrintRecord;
            }
            $successList[] = [
                'packIndex' => $pack['packIndex'],
                'orderIdArr' => [$pack['orderId']],
                'printData' => $printData,
                'pack' => $pack,
                'printRecordId' => $printRecord->id ?? 0,
            ];
            if (!empty($printData['subPrintDataArr'])){
                foreach ($printData['subPrintDataArr'] as $printDatum) {
                    if (Environment::isDy()) {
                        $printDatum['contents'][0]['params'] = $paramsStrArr[$history['template_id']] ?? '';
                    }
                    $printDatum['itemListMap']['numOfPackage'] = $numOfPackage;
                    $successList[] = [
                        'packIndex' => $pack['packIndex'],
                        'orderIdArr' => [$pack['orderId']],
                        'printData' => $printDatum,
                        'pack' => $pack,
                        'printRecordId' => 0,
                    ];
                }
            }
//            $orderPrintEventArr = [];


            // 修改订单打印状态
            if ($version == 3){

                if($mode == 'oldRebuild'){
                    $thisPackageOrders = PackageOrder::query()->where('package_id', $history['package_id'])->get();
                }else{
                    $thisPackageOrders = $history['packageOrders'];
                }
                $orderItemIdArr = $thisPackageOrders->pluck('order_item_id')->unique()->values()->toArray();
                $orderIdArr = $thisPackageOrders->pluck('order_id')->unique()->values()->toArray();
                $allTidArr = $thisPackageOrders->pluck('tid')->unique()->values()->toArray();
                self::waybillDoneUpdateOrder($orderIdArr, $orderItemIdArr, 1, $history['package_id']);

                $orderPrintEventArr = [];
                foreach ($orderIdArr as $orderId) {
                    $thisOrder = $allOrderList->where('id', $orderId)->first();
                    $orderPrintEventArr[] = [
                        'id' => $thisOrder['id'],
                        'tid' => $thisOrder['tid'],
                        'package_id' => $history['package_id'],
                        'waybill_code' => $history['waybill_code'],
                        'wp_code' => $history['wp_code'],
                        'print_record_id' => $printRecord['id'] ?? 0,
                        'tid_arr' => $allTidArr,
                    ];
                }
                event((new OrderPrintEvent($shop->user, $shop, time(), $orderPrintEventArr))->setClientInfoByRequest(request()));
            }
        }

        return [$successList, $failList];
    }

    /**
     * 获取接口最新加密打印信息
     * @param array $template
     * @param $waybillAuth
     * @param $waybillHistoryList
     * @param array $historyPrintDataArr
     * @return array
     * @throws ApiException
     */
    public static function handleLatestEncryptedPrintData(array $template, $waybillAuth, $waybillHistoryList): array
    {
        $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
        // 抖音会过期，所以要请求接口获取最新的
        $waybillBoList = [];
        foreach ($waybillHistoryList as $index => $waybillHistory) {
            $waybillBo = new WaybillBo();
            $waybillBo->wp_code = $template['wp_code'];
            $waybillBo->waybill_code = $waybillHistory['waybill_code'];
            $waybillBoList[] = $waybillBo;
        }
        $waybillsPrintDataBoList = $waybillService->getPrintDataByWaybillBos($waybillBoList);
        $waybillsPrintDataBoList = array_pluck($waybillsPrintDataBoList, null, 'waybill_code');
        $historyPrintDataArr = [];
        foreach ($waybillsPrintDataBoList as $waybillsPrintDataBo) {
            /** @var WaybillsPrintDataBo $waybillsPrintDataBo */
            if ($waybillsPrintDataBo->hasError()) {
                throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
            }
            $waybillHistory = $waybillHistoryList->where('waybill_code', $waybillsPrintDataBo->waybill_code)->first();
            $printData = json_decode($waybillHistory['print_data'], true);
            $printData['contents'][0]['params'] = $waybillsPrintDataBo->param_str;
            $printData['contents'][0]['signature'] = $waybillsPrintDataBo->sign;
            $printData['contents'][0]['encryptedData'] = $waybillsPrintDataBo->encrypted_data;
            $historyPrintDataArr[$waybillsPrintDataBo->waybill_code] = $printData;
        }
        return $historyPrintDataArr;
    }

    /**
     * @param array $packs
     * @param $userId
     * @param $shopId
     * @param array $itemIdStrArr
     * @param Collection $allOrderList
     * @param Collection $allOrderItemList
     * @param array $addressInfo
     * @param array $template
     * @param $waybillAuth
     * @param string $batchNo
     * @param array $senderInfo
     * @param $version
     * @param $mode
     * @param $printOrderStatus
     * @param int $packageNum
     * @param string $softRemark
     * @param $company
     * @param $isPrintRefund
     * @param string $operatorName
     * @return array
     * @throws ApiException
     * @throws BusinessException
     * @throws ErrorCodeException
     */
    public static function handleNewPtPrint(array  $packs, $userId, $shopId, array $itemIdStrArr, Collection $allOrderList,
                                          Collection $allOrderItemList, array $addressInfo, array $template, $waybillAuth,
                                          string $batchNo, array $senderInfo, $version, $mode,$printOrderStatus,
                                          int $packageNum, string $softRemark,$company,$isPrintRefund, string $operatorName = ""): array
    {
        $waybillCodeArr = array_pluck($packs, 'resendWaybillCode');
        $waybillCodeArr = array_filter($waybillCodeArr);
        $ptLogisticsList = PtLogistics::query()->whereIn('waybill_code', $waybillCodeArr)->get();
        $optionUser = User::query()->find($userId);
        $optionShop = Shop::query()->find($shopId);
        $templateId=$template['id'];
        $templateShopId=$template['shop_id'];
        $printContentConfig=self::getPrintContentConfig($templateShopId, $templateId);
        // 查找有没有取号中的
        $packageList = Package::query()
            ->where(['waybill_status' => Package::WAYBILL_STATUS_DOING, 'version' => $version,'shop_id'=>$template['company']['shop_id']])
            ->whereIn('tids', $itemIdStrArr)
            ->orderBy('id', 'desc')
            ->get()
            ->keyBy('id');

        // 过滤
        foreach ($packs as $index => $pack) {
            foreach ($pack['items'] as $index2 => $item) {
                $orderItem = $allOrderItemList->where('id', $item['orderItemId'])->first();
                // 不打印退款，就移出数据
                if (!$isPrintRefund && $orderItem['refund_status'] != OrderItem::REFUND_STATUS_NO){
                    unset($packs[$index]['items'][$index2]);
                }
                if (empty($packs[$index]['items'])){
                    unset($packs[$index]);
                }
            }
        }
        if (empty($packs)){
            throw new ApiException(ErrorConst::ORDER_PRINT_DATA_EMPTY);
        }
        $printPackBoList = [];
        foreach ($packs as $pack) {
            $package = null;
            $createPackageNum = $packageNum;
            if (!empty($template['parent_part']) && $template['parent_part'] == 1){ // 子母件
                $createPackageNum = 1;
            }
            for ($i = 0; $i < $createPackageNum; $i++) {
                // 获取 $package
                $package = $packageList->where('tids', $pack['itemIdStr'])->first();
                $order = $allOrderList->where('id', $pack['orderId'])->first();

                if (!empty($package)) {
                    // 移除被已经取走的数据
                    $packageList->forget($package->id);
                } else {
                    //没有package则创建
                    //把items里面所有的orderId都找出来,然后找出所有的订单，从所有的订单里面找出最早的承诺发货时间(promise_ship_at)，作为包裹的发货时间
                    $orderIds = collect($pack['items'])->pluck('orderId')->toArray();
                    $packOrders = $allOrderList->whereIn('id', $orderIds);
                    $minPromiseShipAt= $packOrders->min('promise_ship_at')??null;
                    Log::info('获取最早的承诺发货时间:'.$minPromiseShipAt);

                    $package = Package::create([
                        'tids' => $pack['itemIdStr'],
                        'waybill_status' => Package::WAYBILL_STATUS_DOING,
                        'user_id' => 0,
                        'shop_id' => $order['shop_id'],
                        'version' => $version,
                        'total_num' => collect($pack['items'])->sum('num'),
                        'promise_ship_time'=>$minPromiseShipAt,
                        'company_id' => $template['company_id']
                    ]);
                    foreach ($pack['items'] as $item) {
                        $orderItem = $allOrderItemList->where('id', $item['orderItemId'])->first();
                        PackageOrder::create([
                            'package_id' => $package->id,
                            'order_id' => $item['orderId'],
                            'order_item_id' => $item['orderItemId'],
                            'num' => $item['num'],
                            'version' => $version,
                            'tid' => $orderItem['tid'],
                            'oid' => $orderItem['oid'],
                            'num_iid' => $orderItem['num_iid'],
                            'sku_id' => $orderItem['sku_id'],
                            'status' => Order::ORDER_STATUS_PAYMENT,
                        ]);
                    }
                }
                $printPackBo = new PrintPackBo();
                $printPackBo->index = $pack['packIndex'];
                $printPackBo->request_id = $package->id;
                $printPackBo->package_id = $package->id;
                $printPackBo->master_order_info = $order;
                $printPackBo->order_infos[] = $printPackBo->master_order_info;
                foreach ($pack['items'] as $item) {
                    $printOrderItemBo = new PrintOrderItemBo();
                    $printOrderItemBo->order_item_info = $allOrderItemList->where('id', $item['orderItemId'])->first();
                    $printOrderItemBo->num = $item['num'];
                    $printPackBo->print_order_item_bo_list[] = $printOrderItemBo;
                }
                $printPackBoList[] = $printPackBo;
            }
        }
        $senderAddressBo = new SenderAddressBo($addressInfo['branch_address']);
        $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
        //因为JD的取号接口需要用到取号店铺的信息，把取号店铺的给放进去,如果是共享单号，这个waybillAuth就是实际电子面单的店铺。
        $waybillService->setShop($waybillAuth);
//        Log::info("订单内容",[$printPackBoList]);
        // 批量取号和打印数据
        $printDataPackBoList = $waybillService->assemWaybillPackageByPrintPackBo($senderAddressBo, $printPackBoList, $template, $packageNum);
        $successList = [];
        $failList = [];
        DB::transaction(function () use (
            $printDataPackBoList, &$successList, &$failList, $packs, $senderInfo, $template, $softRemark,$company,
            $batchNo, $version, $allOrderList, $allOrderItemList, $mode, $printOrderStatus, $operatorName,$shopId,
            $optionUser, $optionShop, $printContentConfig, $ptLogisticsList
        ) {
            $counter = 1;
            $total = collect($printDataPackBoList)->filter(function ($v, $k) {
                return $v->error_code == 0;
            })->count();
            // 循环组装打印数据，并写入打印记录
            foreach ($printDataPackBoList as $index => $printDataPackBo) {
                $pack = collect($packs)->where('packIndex', $printDataPackBo->index)->first();
                $thisSoftRemark = $pack['softRemark'] ?? $softRemark;
                $isSplit = $pack['isSplit'] ?? false;
                $order = $printDataPackBo->master_order_info;
                Log::info("检查取号内容",[$printDataPackBo->hasError()]);
                if ($printDataPackBo->hasError()) {
                    $errorMessage = $printDataPackBo->getErrorMessage();
                    $failList[] = [
                        'packIndex' => $pack['packIndex'],
                        'tid' => $order['tid'],
                        'errorMsg' => $errorMessage,
                        'errorCode' => $printDataPackBo->getErrorCode(),
                    ];
                    $orderIdArr = collect($pack['items'])->pluck('orderId')->toArray();
                    $thisOrderList = $allOrderList->whereIn('id', $orderIdArr);
                    $tidArr = $thisOrderList->pluck('tid')->toArray();
                    //记录取号失败
                    event((new OrderWaybillFailEvent($optionUser, $optionShop, time(), $thisOrderList->toArray(), $errorMessage))->setClientInfoByRequest(request()));
                    $abnormal_type = AbnormalOrder::TYPE_PRINT_FAIL;
                    $thisOrderList->each(function ($item) use ($abnormal_type) {
                        AbnormalOrder::query()->updateOrCreate(['order_id' => $item['id'], 'type' => $abnormal_type], [
                            'type' => $abnormal_type,
                            'status' => AbnormalOrder::STATUS_OF_UNREAD,
                            'shop_id' => $item['shop_id'],
                            'desc' => '取号失败',
                            'order_id' => $item['id']
                        ]);
                    });
                    Order::query()->whereIn('id', $orderIdArr)->update(['abnormal_type' => $abnormal_type]);
                    continue;
                }
                $waybillsPrintDataBo = $printDataPackBo->getWaybillsPrintData();

                $implodeOrderId = collect($pack['items'])->pluck('orderId')->implode('_');
                $allOrderIdArr = collect($pack['items'])->pluck('orderId')->unique()->toArray();

                $printData = null;
                $subPrintDataArr = []; // 子运单打印数据
                if ($version == 2){
                    $printData = self::buildPrintDataBase($printDataPackBo, $implodeOrderId, $template, $waybillsPrintDataBo, $version, $senderInfo);
                    $printData['contents'][1] = $pack['customContent'];
                }elseif ($version == 3){
                    $goodsList = self::extractGoodsList($pack, $allOrderList, $allOrderItemList, $printContentConfig,$thisSoftRemark);
                    list($printData, $subPrintDataArr) = self::getBuildPrintData($printDataPackBo, $implodeOrderId, $template, $waybillsPrintDataBo,
                        $version, $senderInfo, $printContentConfig, $goodsList, $order, $batchNo, $index, $total);
                }
                if ($isSplit){
                    Order::query()->where('id', $order['id'])->update(['is_split' => 1]);
                }
                $sub_waybill_codes = null;
                if (!empty($printDataPackBo->sub_waybill_code_arr)){
                    $sub_waybill_codes = implode(',', $printDataPackBo->sub_waybill_code_arr);
                }
                $printStatus = Package::PRINT_STATUS_NO;
                if ($mode == PrintModeConst::NEW || $mode == PrintModeConst::RESEND_NEW) {
                    $printStatus = Package::PRINT_STATUS_YES;
                }
                $waybill_wp_index = null;
                if ($mode == PrintModeConst::RESEND_NEW){
                    $ptLogistics = $ptLogisticsList->firstWhere('waybill_code', $pack['resendWaybillCode']);
                    $waybill_wp_index = $ptLogistics->waybill_wp_index ?? '';
                }
                $orderShop = Shop::query()->where('id', $order['shop_id'])->first();
                Package::query()
                    ->where(['id' => $printDataPackBo->package_id])
                    ->update([
                        'user_id' => 0,
                        'shop_id' => $order['shop_id'],
                        'to_shop_id' => $order['shop_id'],
                        'waybill_code' => $printDataPackBo->waybill_code,
                        'wp_code' => $template['wp_code'],
                        'template_id' => $template['id'],
                        'auth_source' => $template['auth_source'],
                        'batch_no' => $batchNo,
//                    'tid_oids' => json_encode($tid_oids),
                        'waybill_status' => Package::WAYBILL_STATUS_SUCCESS,
                        'print_order_status' => $printOrderStatus,
                        'sub_waybill_codes' => $sub_waybill_codes,
                        'print_status' => $printStatus,
                        'is_split' => $isSplit,
                        'take_waybill_at' => date('Y-m-d H:i:s'),
                        'total_num' => collect($pack['items'])->sum('num'),
                        'operation_shop_id' => $shopId,
                        'waybill_wp_index' => $waybill_wp_index, // 原单号
                    ]);

                $thisSoftRemark = $pack['softRemark'] ?? $softRemark;
                $printRecord = null;
                $history = self::createWaybillHistory($order, $waybillsPrintDataBo, $template,$company, $printData, $subPrintDataArr, $version,
                    $batchNo, $counter, $total, WaybillHistory::ORDER_TYPE_GENERAL, $operatorName, $sub_waybill_codes, $thisSoftRemark);
                if ($mode == PrintModeConst::NEW || $mode == PrintModeConst::RESEND_NEW){
                    $printRecord = self::createPrintRecord($order, $waybillsPrintDataBo, $history, $template, $printData, $subPrintDataArr, $batchNo,
                        $version, $counter, $total, WaybillHistory::ORDER_TYPE_GENERAL, $operatorName);
                    // 修改订单打印状态
                    if ($version == 3){
                        $orderItemIdArr = array_column($pack['items'], 'orderItemId');
                        self::waybillDoneUpdateOrder($allOrderIdArr, $orderItemIdArr, $isSplit, $printDataPackBo->package_id);
                    }
                }elseif ($mode == PrintModeConst::ONLY_TAKE) {
                    Order::query()
                        ->where('id', $order['id'])
                        ->update([
                            'take_waybill_at' => date('Y-m-d H:i:s'),
                        ]);
                }

                $counter ++;
                $successList[] = [
                    'packIndex' => $pack['packIndex'],
                    'orderIdArr' => $allOrderIdArr,
                    'pack' => $pack,
                    'printData' => $printData,
                    'printRecordId' => $printRecord->id ?? 0,
                ];

                $orderItemIdArr = array_column($pack['items'], 'orderItemId');
                $orderIdArr = collect($pack['items'])->pluck('orderId')->unique()->values()->toArray();
                $allTidArr = $allOrderItemList->whereIn('id', $orderItemIdArr)->pluck('tid')->unique()->toArray();
                $orderPrintEventArr = [];
                foreach ($orderIdArr as $orderId) {
                    $thisOrder = $allOrderList->where('id', $orderId)->first();
                    $orderPrintEventArr[] = [
                        'id' => $thisOrder['id'],
                        'tid' => $thisOrder['tid'],
                        'package_id' => $history['package_id'],
                        'waybill_code' => $history['waybill_code'],
                        'wp_code' => $history['wp_code'],
                        'print_record_id' => $printRecord['id'] ?? 0,
                        'tid_arr' => $allTidArr,
                        'isNewPrint' => 1, // 是否新取号
                    ];
                }
                event((new OrderPrintEvent($optionUser, $orderShop, time(), $orderPrintEventArr))->setClientInfoByRequest(request()));

                foreach ($subPrintDataArr as $item) {
                    $successList[] = [
                        'packIndex' => $pack['packIndex'],
                        'orderIdArr' => $allOrderIdArr,
                        'pack' => $pack,
                        'printData' => $item,
                        'printRecordId' => 0,
                    ];
                }

            }
        });

        return [$successList, $failList];
    }

    /**
     * 自由打印新取号
     * @param array $packs
     * @param $userId
     * @param $shopId
     * @param Collection $allOrderList
     * @param array $addressInfo
     * @param array $template
     * @param $waybillAuth
     * @param string $batchNo
     * @param array $senderInfo
     * @param $version
     * @param $mode
     * @param $packageNum
     * @param $company
     * @param string $operatorName
     * @return array
     * @throws BusinessException
     * @throws ErrorCodeException
     * @throws RandomException
     */
    public static function handleNewCustomizePrint(array $packs, $userId, $shopId, Collection $allOrderList,
                                                   array $addressInfo, array $template, $waybillAuth, string $batchNo,
                                                   array $senderInfo, $version, $mode, $packageNum,$company, string $operatorName = ""): array
    {
        $printPackBoList = [];
        $templateId=$template['id'];
        $templateShopId=$template['shop_id'];
        $printContentConfig = self::getPrintContentConfig($templateShopId, $templateId);
        foreach ($packs as $pack) {
            $createPackageNum = $packageNum;
            if ($template['parent_part'] == 1){ // 子母件
                $createPackageNum = 1;
            }
            for ($i = 0; $i < $createPackageNum; $i++) {
                $printPackBo = new PrintPackBo();
                $printPackBo->index = $pack['packIndex'];
                $printPackBo->request_id = $pack['orderId'] . '_' . $i. '_' .random_int(10000,99999);
                $printPackBo->package_id = $pack['orderId'] . '_' . $i. '_' .random_int(10000,99999);
                $printPackBo->master_order_info = $allOrderList->where('id', $pack['orderId'])->first();
                $printPackBo->order_infos[] = $printPackBo->master_order_info;
                $printOrderItemBo = new PrintOrderItemBo();
                $printOrderItemBo->order_item_info = ['sku_value'=> '默认商品'];
                $printOrderItemBo->num = 1;
                $printPackBo->print_order_item_bo_list[] = $printOrderItemBo;
                $printPackBoList[] = $printPackBo;
            }
        }
        $senderAddressBo = new SenderAddressBo($addressInfo['branch_address']);
        $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
        $waybillService->setShop($waybillAuth);
        // 批量取号和打印数据
        $printDataPackBoList = $waybillService->assemWaybillPackageByPrintPackBo($senderAddressBo, $printPackBoList, $template, $packageNum);
        $successList = [];
        $failList = [];
        DB::transaction(function () use (
            $printDataPackBoList, &$successList, &$failList, $packs, $senderInfo, $template,$company,
            $batchNo, $version, $allOrderList, $mode, $operatorName,$printContentConfig
        ) {
            $counter = 1;
            $total = collect($printDataPackBoList)->filter(function($v, $k) {
                return $v->error_code == 0;
            })->count();
            // 循环组装打印数据，并写入打印记录
            foreach ($printDataPackBoList as $index => $printDataPackBo) {
                $pack = collect($packs)->where('packIndex', $printDataPackBo->index)->first();
                if ($printDataPackBo->hasError()) {
                    $failList[] = [
                        'packIndex' => $pack['packIndex'],
                        'orderId' => $pack['orderId'],
                        'errorMsg' => $printDataPackBo->getErrorMessage(),
                        'errorCode' => $printDataPackBo->getErrorCode(),
                    ];
                    continue;
                }
                $waybillsPrintDataBo = $printDataPackBo->getWaybillsPrintData();

//                $implodeOrderId = collect($pack['items'])->pluck('orderId')->implode('_');

                $order = $printDataPackBo->master_order_info;
                $order['tid']=$order['order_no']??null;
                $order['receiver_state']=$order['receiver_province']??null;
                $order['outer_order_no']=$order['outer_order_no']?$order['outer_order_no']:($order['tid']??null);
                $implodeOrderId = $order->id;

                $goodsList = OrderPrintService::extractGoodsListByCustom($order, $printContentConfig);

                list($printData, $subPrintDataArr) = self::getBuildPrintData($printDataPackBo, $implodeOrderId, $template, $waybillsPrintDataBo,
                    $version, $senderInfo, $printContentConfig, $goodsList, $order, $batchNo, $index, $total);
//                $company = $template['company'];
                $sub_waybill_codes = null;
                if (!empty($printDataPackBo->sub_waybill_code_arr)){
                    $sub_waybill_codes = implode(',', $printDataPackBo->sub_waybill_code_arr);
                }
                $history = self::createWaybillHistory($order, $waybillsPrintDataBo, $template, $company, $printData, $subPrintDataArr, $version,
                    $batchNo, $counter, $total, WaybillHistory::ORDER_TYPE_FREE, $operatorName, $sub_waybill_codes);
                if ($mode == 'new'){
                    $printRecord = self::createPrintRecord($order, $waybillsPrintDataBo, $history, $template, $printData,$subPrintDataArr, $batchNo,
                        $version, $counter, $total, WaybillHistory::ORDER_TYPE_FREE,$operatorName);
                    // 修改订单打印状态
                    $customizeOrderTmp = CustomizeOrder::query()->where('id', $order['id'])->first();
                    $customizeOrderTmp->update([
                        'template_id' => $template['id'],
                        'wp_code' => $template['wp_code'],
                        'print_status' => CustomizeOrder::PRINT_STATUS_YES,
                        'printed_at' => date('Y-m-d H:i:s'),
                        'parent_waybill_code' => $item['parent_waybill_code'] ?? '',
                        'waybill_code' => empty($customizeOrderTmp->waybill_code) ? $waybillsPrintDataBo->waybill_code : ($customizeOrderTmp->waybill_code . ',' . $waybillsPrintDataBo->waybill_code),
                        'recycled_at' => null,
                    ]);
                }
                $counter ++;
                $successList[] = [
                    'packIndex' => $pack['packIndex'],
                    'orderIdArr' => [$pack['orderId']],
                    'printData' => $printData,
                    'printRecordId' => $printRecord->id ?? 0,
                ];
                foreach ($subPrintDataArr as $item) {
                    $successList[] = [
                        'packIndex' => $pack['packIndex'],
                        'orderIdArr' => [$pack['orderId']],
                        'printData' => $item,
                        'printRecordId' => 0,
                    ];
                }
            }
        });

        return [$successList, $failList];
    }

    public static function handlePrintRecordPrint($userId, $shopId, array $packs,string $batchNo, $version)
    {
        $shop = \App\Models\Fix\Shop::find($shopId);
        $printRecordList = PrintRecord::query()
            ->whereIn('id', array_pluck($packs, 'printRecordId'))
            ->get();

        $allOrderList = Order::query()->with(['orderItem', 'orderCipherInfo'])
            ->where(['locked_at' => null])
            ->whereIn('id', $printRecordList->pluck('order_id')->unique())
            ->get();
        $customizeOrderList = CustomizeOrder::query()
            ->with(['order.orderCipherInfo'])
            ->whereIn('id', $printRecordList->pluck('order_id')->unique())
            ->get();
        $customizeOrderList = $customizeOrderList->map(function ($item){
            $item->orderCipherInfo = $item->order->orderCipherInfo??null;
            return $item;
        });
        $allOrderList = $allOrderList->merge($customizeOrderList);
        if ($printRecordList->isEmpty()){
            throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
        }

        $successList = $failList = [];
//        $printRecords = [];
        $paramsStrArr = [];
        if (Environment::isDy()) {
            $templateIdArr = $printRecordList->pluck('template_id')->toArray();
            $paramsStrArr = self::getParamsStr($templateIdArr);
        }
        foreach ($printRecordList as $index => $printRecord) {
            $total = count($printRecordList);
            $numOfPackage = $index + 1 . '/' . $total;
            $order = $allOrderList->where('id', $printRecord['order_id'])->first();
            $pack = collect($packs)->where('printRecordId', $printRecord['id'])->first();
            $printData = $printRecord['print_data'];
            $printData = json_decode($printData, true);
            if (Environment::isDy()) {
                $printData['contents'][0]['params'] = $paramsStrArr[$printRecord['template_id']] ?? '';
            }
            $printData['itemListMap']['numOfPackage'] = $numOfPackage;
            $successList[] = [
                'packIndex' => $pack['packIndex'],
                'printData' => $printData,
            ];
            if (!empty($printData['subPrintDataArr'])){
                foreach ($printData['subPrintDataArr'] as $printDatum) {
                    if (Environment::isDy()) {
                        $printDatum['contents'][0]['params'] = $paramsStrArr[$printRecord['template_id']] ?? '';
                    }
                    $printDatum['itemListMap']['numOfPackage'] = $numOfPackage;
                    $successList[] = [
                        'packIndex' => $pack['packIndex'],
                        'printData' => $printDatum,
                    ];
                }
            }
            // 提取send_content
            $sendContentArr = array_merge([$printData['itemListMap']['contents']], array_column($printData['otherCustomList'], 'contents'));
            $send_content = implode("\n分页\n",$sendContentArr);
            $thisPrintRecord = $printRecord->toArray();
            $thisPrintRecord['print_index'] = $index + 1;
            $thisPrintRecord['print_count'] = $total;
            $thisPrintRecord['batch_no'] = $batchNo;
            $thisPrintRecord['send_content'] = $send_content;
            unset($thisPrintRecord['id']);
            $newPrintRecord = PrintRecord::create($thisPrintRecord);

            $thisPackageOrders = PackageOrder::query()->where('package_id', $printRecord['package_id'])->get();
            $orderIdArr = $thisPackageOrders->pluck('order_id')->unique()->values()->toArray();
            $allTidArr = $thisPackageOrders->pluck('tid')->unique()->values()->toArray();
            // 记录打印日志
            $orderPrintEventArr = [];
            foreach ($orderIdArr as $orderId) {
                $thisOrder = $allOrderList->where('id', $orderId)->first();
                $orderPrintEventArr[] = [
                    'id' => $thisOrder['id'],
                    'tid' => $thisOrder['tid'],
                    'package_id' => $printRecord['package_id'],
                    'waybill_code' => $printRecord['waybill_code'],
                    'wp_code' => $printRecord['wp_code'],
                    'print_record_id' => $newPrintRecord['id'] ?? 0,
                    'tid_arr' => $allTidArr,
                ];
            }
            event((new OrderPrintEvent($shop->user, $shop, time(), $orderPrintEventArr))->setClientInfoByRequest(request()));
//            $printRecords[] = self::buildPrintRecordData($printRecord, $order, $printData, $index, $total, $batchNo, $version);
        }
        //添加打印记录
//        $records = PrintRecord::generate($printRecords, $userId, $shopId);
//        if (!$records) {
//            Log::error('打印记录添加失败!', ['data' => $printRecords]);
//            throw new ApiException(ErrorConst::PRINTED_RECORD_ADD_FAIL);
//        }
        return [$successList, $failList];
    }

    /**
     * @param array $contentArr
     * @param array $goodsList
     * @param $order
     * @param string $batchNo
     * @param $index
     * @param $packTotal
     * @return array
     */
    public static function genItemListMap(array $contentArr, array $goodsList, $order,string $batchNo, $index, $packTotal): array
    {
        /**
         * [
         * 'order_orderId' => '6926624884069111728',
         * 'order_quantity' => '2',
         * 'contents' => '1. 垃圾袋家用手提式加厚黑色办公室厨房宿舍实惠装中大号背心塑料袋 1只 [1]件
         * 2. 加厚菠萝格卫生间洗手间厨房专用擦手巾不发臭挂式抹布吸水擦手布 1 [1]件',
         * 'remark' => ' ',
         * 'sm' => '01260009 1',
         * 'printSize' => '2'
         * ]
         */
        $totalPrice = '0'; // 总价
        $payAmount = '0'; //总支付金额

        $shop = Shop::query()->where('id', $order['shop_id'])->first();

        $count = array_sum(array_column($goodsList, 'goods_num'));
//        $buyerMemo = collect($goodsList)->pluck('buyer_message')->flatten(1)->unique()->implode('；');
        $buyerMemo = collect($goodsList)->pluck('buyer_message')->flatten(1)->pluck('value','order_id')
            ->values()->flatten(1)->filter()->implode('；');
        $buyerMemo == '[]' && $buyerMemo = '';
//        $sellerRemark = collect($goodsList)->pluck('seller_memo')->flatten(1)->unique()->implode('；');
        $sellerRemark = collect($goodsList)->pluck('seller_memo')->flatten(1)->pluck('value','order_id')
            ->values()->flatten(1)->map(function ($item) {
            $var = json_decode($item, true);
            return $var ?? $item;
        })->flatten()->implode("；");
        $sellerRemark == '[]' && $sellerRemark = '';
        $softRemark = collect($goodsList)->pluck('soft_remark')->unique()->implode('；');
        $mallName = $shop['shop_name'];
        $ownerName = $shop['id'];
        $watermark = $count?:1;
        $orderNum = $order['tid'];
        $confirmTime = (string)$order['created_at'];
        foreach ($goodsList as $item) {
            $totalPrice = bcadd($totalPrice, $item['total_fee']??'0', 2);
            $payAmount = bcadd($payAmount, $item['payment']??'0', 2);
        }
        $numOfPackage = $index + 1 . '/' . $packTotal;
        $otherCustomList = [];
        $itemListMap = [
            'count' => $count, // 总数
            'contents' => $contentArr[0], // 发货内容
            'buyerMemo' => $buyerMemo, // 买家留言
            'remark' => $sellerRemark, // 卖家备注
            'mallName' => $mallName,  // 店铺名
            'ownerName' => $ownerName, // 商家ID
            'orderNum' => $orderNum, // 订单号
            'confirmTime' => $confirmTime, // 下单时间
            'totalPrice' => $totalPrice, // 总价
            'payAmount' => $payAmount, //总支付金额
            'numOfPackage' => $numOfPackage, //包裹数
            'watermark' => $watermark, //水印
            'softRemark' => $softRemark, //软件备注
        ];
        if (count($contentArr) > 1){
            for ($i = 1; $i < count($contentArr); $i++) {
                $otherCustomItem = $itemListMap;
                $otherCustomItem['contents'] = $contentArr[$i];
                $otherCustomList[] = $otherCustomItem;
            }
        }
        return [$itemListMap, $otherCustomList];
    }

    /**
     * @param $printRecord
     * @param $order
     * @param $printData
     * @param $index
     * @param $total
     * @param string $batchNo
     * @param $version
     * @return array
     */
    public static function buildPrintRecordData($printRecord, $order, $printData, $index, $total, string $batchNo, $version): array
    {
        return [
            'wp_code' => $printRecord['wp_code'],
            'waybill_code' => $printRecord['waybill_code'],
            'order' => !empty($order)?$order->toArray():null,
            'shop_id' => $printRecord['shop_id'],
            'user_id' => $printRecord['user_id'],
            'package_id' => $printRecord['package_id'],
            'history_id' => $printRecord['history_id'],
            'print_data' => json_encode($printData, JSON_UNESCAPED_UNICODE),
            'name_index' => $printRecord['name_index'],
            'phone_index' => $printRecord['phone_index'],
            'print_index' => $index + 1,
            'print_count' => $total,
            'batch_no' => $batchNo,
            'to_shop_id' => $printRecord['to_shop_id'],
            'template_id' => $printRecord['template_id'],
            'template_name' => $printRecord['template_name'],
            'version' => $version,
        ];
    }

    /**
     * 根据商品列表生成打印内容
     * @param $printContentConfig
     * @param array $goodsList [{
     *       "tid",
     *       "goods_title",
     *       "num_iid",
     *       "outer_iid",
     *       "sku_id",
     *       "sku_value",
     *       "outer_sku_iid",
     *       "goods_price",
     *       "payment",
     *       "total_fee",
     *       "goods_num",
     *       "buyer_message",
     *       "seller_memo"
     *  ];
     * @return array
     */
    public static function genPrintCustomContent($printContentConfig, array $goodsList): array
    {

        // 组装商品数据
        $separator = '；';
        $lineArr = [];
        $tidArr = [];
        foreach ($goodsList as $i => $item) {
            if (empty($item['goods_title']) && empty($item['sku_value'])){
                continue;
            }
            $tidArr = array_merge($tidArr, $item['tid']);
//            $line = sprintf("%s. ", $i + 1);
            $line = '';
            // {"goodsTitle":"1","outerIid":"0","numIid":"0","skuDesc":"1","skuId":"0","outerSkuIid":"0","payment":"0","goodsNum":"1",
            // "goodsLineFeed":"1","goodsPaging":"1","goodsNumStyle":"[%s]","goodsNumCompany":"件","pageCountNum":"2","goodsMerge":"1",
            // "buyerMemo":"0","remark":"0"}

            // 商品编码
            if ($printContentConfig['outerIid']) {
                $line .= $item['outer_iid']?? ' ';
            }
            // 1 都展示 0 都不展示 2 仅展示商品简称 3 仅展示商品名称 4 有商品简称展示商品简称，无商品简称展示商品名称
            if (isset($printContentConfig['goodsTitle'])) {
                $str = '';
                $goodsTitle = $item['goods_title'] ?? '';
                $goodsCustomTitle = $item['goods_custom_title'] ?? '';
                switch ($printContentConfig['goodsTitle']){
                    case '0': // 都不展示
                    default:
                        break;
                    case '1': // 都展示
                        $str = $goodsTitle .' '. $goodsCustomTitle;
                        break;
                    case '2': // 仅展示商品简称
                        $str = $goodsCustomTitle;
                        break;
                    case '3': // 仅展示商品名称
                        $str = $goodsTitle;
                        break;
                    case '4': // 有商品简称展示商品简称，无商品简称展示商品名称
                        if (!empty($goodsCustomTitle)){
                            $str = $goodsCustomTitle;
                        }else{
                            $str = $goodsTitle;
                        }
                        break;
                }
                $line .= $str . '  ';
            }
            if ($printContentConfig['numIid']) {
                $line .= $item['num_iid']?? ' ';
            }
            // 规格编码
            if ($printContentConfig['outerSkuIid']) {
                $line .= $item['outer_sku_iid']?? ' ';
            }
            // 1都展示 0都不展示 2仅展示规格简称 3仅展示规格名称 4有规格简称展示规格简称，无规格简称展示规格名称
            if ($printContentConfig['skuDesc']) {
                $str = '';
                $skuValue = $item['sku_value'] ?? '';
                $skuCustomValue = $item['sku_custom_value'] ?? '';
                switch ($printContentConfig['skuDesc']) {
                    case '0': // 都不展示
                    default:
                        break;
                    case '1': // 都展示
                        $str = $skuValue . ' ' . $skuCustomValue;
                        break;
                    case '2': // 仅展示规格简称
                        $str = $skuCustomValue;
                        break;
                    case '3': // 仅展示规格名称
                        $str = $skuValue;
                        break;
                    case '4': // 有规格简称展示规格简称，无规格简称展示规格名称
                        if (!empty($skuCustomValue)){
                            $str = $skuCustomValue;
                        }else{
                            $str = $skuValue;
                        }
                        break;
                }
                $line .= $str . '  ';
            }
            if ($printContentConfig['skuId']) {
                $line .= $item['sku_id']?? ' ';
            }
            if ($printContentConfig['payment']) {
                $line .= $item['goods_price']?? ' ';
            }
            if ($printContentConfig['goodsNum']) {
                $goodsNumStyle = $printContentConfig['goodsNumStyle'] ?? '【%s】';
                $goodsNumCompany = $printContentConfig['goodsNumCompany'] ?? '件';
                $line .= sprintf($goodsNumStyle, $item['goods_num']??'') . $goodsNumCompany;
            }
            if ($printContentConfig['goodsLineFeed']) { // 是否换行
                $separator = "\n";
            }

            $lineArr[] = $line;
        }
        // 是否分页
        $isSplitPage = false;
        $lineChunkArr = [$lineArr];
        if ($printContentConfig['goodsPaging']) { // 是否分页
            $pageCountNum = $printContentConfig['pageCountNum'] ?? 5;
            if ($pageCountNum > 0 && count($lineArr) > $pageCountNum) { // 商品规格大于 pageCountNum 种进行分页
                $maxPageCountNum = $printContentConfig['maxPageCountNum'] ?? 20;
                if ($maxPageCountNum > 0){
                    $isSplitPage = true;
                    if (Environment::isKs()){
                        $maxPageCountNum = 999; // ks 只能分一页
                    }
                    $lineChunkArr = array_chunk($lineArr, $maxPageCountNum); // 每页展示 maxPageCountNum 种规格
                }
            }
        }
        $contentArr = [];
        if ($isSplitPage){ // 需要分页
            $contentArr[] = sprintf('详情见下面%s页',count($lineChunkArr));
            foreach ($lineChunkArr as  $item) {
//                $contentArr[] = implode($separator, $item);
                $contentArr[] = collect($item)->filter()->implode($separator);
            }
        }else{
//            $contentArr[] = implode($separator, $lineArr);
            $contentArr[] = collect($lineArr)->filter()->implode($separator);
        }

        if (!empty($printContentConfig['orderNo'])) { // 1顶部 2底部 0不展示
//                $line .= implode(',', $item['tid']) . "\n"; // 比较长 默认换行
            $implode = collect($tidArr)->filter()->unique()->map(function ($item) {
                return str_replace('A', '', $item);// 去掉字符串里的A
            })->implode(',');
            if ($printContentConfig['orderNo'] == 2){ // 展示在底部
                $contentArr[count($contentArr) - 1] .= "\n" . $implode;
            }elseif ($printContentConfig['orderNo'] == 1){ // 展示在顶部
                $contentArr[count($contentArr) - 1] = $implode ."\n" . $contentArr[count($contentArr) - 1];
            }
        }
        if (!empty($printContentConfig['buyerMemo'])) {
            $implode = collect($goodsList)->pluck('buyer_message')->flatten(1)->pluck('value','order_id')
                ->values()->flatten()->filter()->implode("\n");
            if ($printContentConfig['buyerMemo'] == 1){ // 展示在底部
                $contentArr[count($contentArr) - 1] .= "\n" . $implode;
            }elseif ($printContentConfig['buyerMemo'] == 2){ // 展示在顶部
                $contentArr[count($contentArr) - 1] = $implode ."\n" . $contentArr[count($contentArr) - 1];
            }
        }
        if (!empty($printContentConfig['remark'])) {
            $implode = collect($goodsList)->pluck('seller_memo','order_id')->flatten(1)->pluck('value','order_id')
                ->values()->flatten(1)->map(function ($item) {
                $var = json_decode($item, true);
                // 解不出来就用原来值
                return $var ?? $item;
            })->flatten()->filter()->implode("\n");
            if ($printContentConfig['remark'] == 1){ // 展示在底部
                $contentArr[count($contentArr) - 1] .= "\n" . $implode;
            }elseif ($printContentConfig['remark'] == 2){ // 展示在顶部
                $contentArr[count($contentArr) - 1] = $implode ."\n" . $contentArr[count($contentArr) - 1];
            }
        }
        return [$contentArr, $goodsList];
    }

    /**
     * 提取商品数据
     * @param $pack
     * @param Collection $allOrderList
     * @param Collection $allOrderItemList
     * @param $printContentConfig
     * @return array
     */
    public static function extractGoodsList($pack, Collection $allOrderList, Collection $allOrderItemList,
                                            $printContentConfig, $softRemark = ''): array
    {
        $goodsList = [];

        // 提取商品数据，并判断是否数据合并
        foreach ($pack['items'] as $i => $item) {
            $orderTmp = $allOrderList->where('id', $item['orderId'])->first();
            $orderItemTmp = $allOrderItemList->where('id', $item['orderItemId'])->first();
            Log::debug('extractGoodsList', [$orderItemTmp]);
            Log::debug('extractGoodsList:custom_goods', [$orderItemTmp['customGoods']]);
            // tid goods_title num_iid outer_iid sku_id sku_value outer_sku_iid goods_price goods_num buyer_message seller_memo
            $sku_id = $orderItemTmp['sku_id'];
//            $skuIdIdx = $sku_id;
            $sku_value = $orderItemTmp['sku_value'];
            if (empty($sku_value)){ // sku 没值取 goods_title
                $sku_value = $orderItemTmp['goods_title'];
            }
            $skuIdIdx = md5($orderItemTmp['num_iid'] . $sku_id . $sku_value);

//            if (empty($sku_id)){ // sku_id 没值取 $sku_value md5
//                $skuIdIdx = md5($sku_value);
//            }else{
//                $skuIdIdx = $sku_id;
//            }
            $goodsArr = [
                'order_id' => $orderItemTmp['order_id'],
                'tid' => [$orderItemTmp['tid']],
                'goods_title' => $orderItemTmp['goods_title'],
                'goods_custom_title' => $orderItemTmp['customGoods']['custom_title'] ?? '',
                'num_iid' => $orderItemTmp['num_iid'],
                'outer_iid' => $orderItemTmp['outer_iid'],
                'sku_id' => $sku_id,
                'sku_value' => $sku_value,
                'sku_custom_value' => $orderItemTmp['customGoodsSkus']['custom_sku_value'] ?? '',
                'outer_sku_iid' => $orderItemTmp['outer_sku_iid'],
                'goods_price' => $orderItemTmp['goods_price'],
                'payment' => $orderItemTmp['payment'],
                'total_fee' => $orderItemTmp['total_fee']??0,
                'goods_num' => $item['num'],
                'buyer_message' => [['order_id' => $orderItemTmp['order_id'], 'value' => $orderTmp['buyer_message']]],
                'seller_memo' => [['order_id' => $orderItemTmp['order_id'], 'value' => $orderTmp['seller_memo']]],
//                'seller_memo' => [$orderTmp['seller_memo']],
                'soft_remark' => $softRemark,
            ];
            Log::debug('extractGoodsList:$goodsArr', [$goodsArr]);
            if ($printContentConfig['goodsMerge']) { // 合并宝贝
                if (isset($goodsList[$skuIdIdx])){
                    $goodsList[$skuIdIdx]['tid'][] = $orderItemTmp['tid'];
                    $goodsList[$skuIdIdx]['goods_num'] += $item['num'];
                    $goodsList[$skuIdIdx]['payment'] = bcadd($goodsList[$skuIdIdx]['payment'], $orderItemTmp['payment'], 2);
                    $goodsList[$skuIdIdx]['total_fee'] = bcadd($goodsList[$skuIdIdx]['total_fee'], $orderItemTmp['total_fee'], 2);
                    $goodsList[$skuIdIdx]['buyer_message'][] = ['order_id' => $orderItemTmp['order_id'], 'value' => $orderTmp['buyer_message']];
                    $goodsList[$skuIdIdx]['seller_memo'][] =  ['order_id' => $orderItemTmp['order_id'], 'value' => $orderTmp['seller_memo']];
                }else{
                    $goodsList[$skuIdIdx] = $goodsArr;
                }
            } else {
                // 不合并，这里不要加上 $skuIdIdx 作为 key
                $goodsList[] = $goodsArr;
            }
        }
        return $goodsList;
    }

    /**
     * 提取商品列表 自由打印订单
     * @param $order
     * @param $printContentConfig
     * @return array
     */
    public static function extractGoodsListByCustom($order, $printContentConfig)
    {
        /**
         * $goodsList [{
         *        "tid",
         *        "goods_title",
         *        "num_iid",
         *        "outer_iid",
         *        "sku_id",
         *        "sku_value",
         *        "outer_sku_iid",
         *        "goods_price",
         *        "payment",
         *        "total_fee",
         *        "goods_num",
         *        "buyer_message",
         *        "seller_memo"
         *   ];
         */
        $goodsList = [];
        $goodsInfo = $order['goods_info'];
        $goodsInfoArr = json_decode($goodsInfo, true);
        /**
         * $goodsInfoArr = [{"index":0,"value":"","title":"标题","code":"编码","norm":"规格","num":1,"price":1.1,"weight":0.1,"volume":2}]
         */
        if (empty($goodsInfoArr)){
            $goodsList[] = [
                'seller_memo' => [['order_id' => $order['id'], 'value' => $order['seller_memo'] ?? '']],
            ];
            return $goodsList;
        }
        foreach ($goodsInfoArr as $index => $item) {
            $goodsList[] = [
                'order_id' => $order['id'],
                'tid' => [],
                'goods_title' => $item['title'] ?? '',
                'num_iid' => '',
                'outer_iid' => '',
                'sku_id' => '',
                'sku_value' => $item['norm'] ?? '',
                'outer_sku_iid' => $item['code'] ?? '',
                'goods_price' => $item['price'] ?? '',
                'payment' => '',
                'total_fee' => '',
                'goods_num' => $item['num'] ?: 1,
                'buyer_message' => [],
//                'seller_memo' => $order['seller_memo'] ?? '',
                'seller_memo' => [['order_id' => $order['id'], 'value' => $order['seller_memo'] ?? '']],
            ];

        }
        if ($printContentConfig['goodsMerge']) { // 合并宝贝

        }
        return $goodsList;
    }

    public static function printTag($templateId, $batchNo, $packages, $shopId, $ownerIdList)
    {
        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $shopIdArr = $shops->pluck('id')->toArray();

//        $template = TagTemplate::query()->where('template_id', $templateId)->firstOrFail();
        $orderItemIdArr = array_pluck($packages, 'orderItemId');
//        $buyerIdArr = array_pluck($packages, 'buyer_id');
        $orderItemList = OrderItem::query()->with(['order','order.blacklist','order.orderCipherInfo','shop'])
            ->whereIn('shop_id', $shopIdArr)
            ->whereIn('id', $orderItemIdArr)->get();
        if ($orderItemList->count() != count($orderItemIdArr)){
            throw new ApiException(ErrorConst::ORDER_NOT_FOUND);
        }
        $buyerIdArr = Arr::pluck($orderItemList->toArray(),'order.buyer_id');
//        $shopIdArr = array_pluck($orderItemList, 'shop_id');

        $redis = redis('cache');
        $keyPrefix = 'printTag:'.date('Ymd').':';
        // 用户购买次数
//        $userTotalOrderNumArr = Order::query()->where('buyer_id', $buyerIdArr)->groupBy(['buyer_id'])
//            ->selectRaw('buyer_id, count(1) as count')->get()->pluck('count', 'buyer_id')->toArray();
//        $keyUserBuyNum = $keyPrefix . 'userBuyNum:';
        // 用户今日下单数
        $keyUserTodayOrderNum = $keyPrefix . 'userTodayOrderNum:';
        // 今日打印次数
        $keyTodayPrintNum = $keyPrefix . 'todayPrintNum:';
        // 店铺今日下单数
        $keyShopTodayOrderNum = $keyPrefix . 'shopTodayOrderNum:';

        $list = [];
        // 生成打印内容
        foreach ($packages as $index => $package) {
            $orderItem = $orderItemList->where('id', $package['orderItemId'])->first();
            $order = [
                'tid' => $orderItem['tid'],
                'oid' => $orderItem['oid'],
                'buyer_nick' => $orderItem['order']['buyer_nick'],
                'phone' => $orderItem['order']['orderCipherInfo']['receiver_phone_mask'],
                'name' => $orderItem['order']['orderCipherInfo']['receiver_name_mask'],
                'address' => $orderItem['order']['orderCipherInfo']['receiver_address_mask'],
                'receiver_state' => $orderItem['order']['receiver_state'],
                'receiver_city' => $orderItem['order']['receiver_city'],
                'receiver_district' => $orderItem['order']['receiver_district'],
                'shop_name' => $orderItem['shop']['shop_name'],
                'author_name' => $orderItem['author_name'],
                'order_created_at' => $orderItem['order']['order_created_at'],
                'pay_at' => $orderItem['order']['pay_at'],
//                'print_tag_at' => $orderItem['print_tag_at'],
                'goods_price' => $orderItem['goods_price'],
                'total_fee' => $orderItem['total_fee'],
                'goods_title' => $orderItem['goods_title'],
                'sku_value' => $orderItem['sku_value'],
                'goods_num' => $orderItem['goods_num'],
                'outer_iid' => $orderItem['outer_iid'],
                'outer_sku_iid' => $orderItem['outer_sku_iid'],
                'refund_status' => $orderItem['refund_status'],
                'blacklist' => $orderItem['order']['blacklist'],
                'seller_memo' => $orderItem['order']['seller_memo'],
                'buyer_message' => $orderItem['order']['buyer_message'],
            ];
            $print_tag_at = date('Y-m-d H:i:s');
            OrderItem::query()->where('id', $orderItem['id'])
                ->update(['print_tag_at' => $print_tag_at, 'print_tag_num' => DB::raw('print_tag_num+1')]);
            $userTotalOrderNum = $userTotalOrderNumArr[$orderItem['order']['buyer_id']] ?? 0;
            $todayPrintNum = $redis->incr($keyTodayPrintNum . $shopId);
            if (empty($orderItem['print_tag_at'])){ // 原来是空的，那就是新订单，数据递增
                $userTodayOrderNum = $redis->incr($keyUserTodayOrderNum . $orderItem['order']['buyer_id']);
                $shopTodayOrderNum = $redis->incr($keyShopTodayOrderNum . $orderItem['shop_id']);
            }else{
                $userTodayOrderNum = (int)$redis->get($keyUserTodayOrderNum . $orderItem['order']['buyer_id']);
                $shopTodayOrderNum = (int)$redis->get($keyShopTodayOrderNum . $orderItem['shop_id']);
            }
            $statistics = [
                'userTotalOrderNum' => $userTotalOrderNum + 1,
                'userTodayOrderNum' => $userTodayOrderNum,
                'todayPrintNum' => $todayPrintNum,
                'shopTodayOrderNum' => $shopTodayOrderNum,
            ];
            $order['print_tag_at'] = $print_tag_at;
            $list[] = [
                'order' => $order,
                'statistics' => $statistics,
            ];
        }

        return $list;
    }

    /**
     * @param int $templateId
     * @param $companyId
     * @param $userId
     * @param $shopId
     * @param $packageNum
     * @param array $packs
     * @param array $orders
     * @param $orderType
     * @param array $itemIdStrArr
     * @param $allOrderList
     * @param $allOrderItemList
     * @param string $batchNo
     * @param array $senderInfo
     * @param $version
     * @param string $mode
     * @param $printOrderStatus
     * @param string $softRemark
     * @param $isPrintRefund
     * @param string $operatorName
     * @return array
     * @throws ApiException
     * @throws BusinessException
     * @throws ErrorCodeException
     * @throws \Throwable
     */
    public static function handleNewPrint(int    $templateId, $companyId, $userId, $shopId, $packageNum, array $packs, array $orders,
                                                 $orderType, array $itemIdStrArr, $allOrderList, $allOrderItemList,
                                          string $batchNo, array $senderInfo, $version, string $mode, $printOrderStatus,
                                          string $softRemark, $isPrintRefund, string $operatorName): array
    {
        $successList = $failList = [];
        if (empty($packs)) {
            return [$successList, $failList];
        }
        $templateObj = Template::query()->with('company')->findOrFail($templateId);
        if (empty($companyId)){
            $companyId = $templateObj->company_id;
        }
        $company = Company::query()->findOrFail($companyId);
        $template = $templateObj->toArray();
        //网点地址与真实发货地址
        $addressInfo = self::getBranchAndSendAddress($userId, $shopId, $company, $template);
        StatisticsCost('获取真实发货地址');
        //判断是否为虚拟网点
        $waybillAuth = self::getWaybill($company, $template);

        if (!$waybillAuth) {
            throw new ApiException(ErrorConst::WAYBILL_AUTH_LOSE);
        }

        //判断是否是虚拟分享网点，且余额不为0，状态是正常
        if ($company->source == Company::SOURCE_COMPANY_STATUS_YES) {
            if ($company->source_status == Company::SOURCE_COMPANY_STATUS_CLOSED) {
                throw new ApiException(ErrorConst::WAYBILL_SHARE_FREEZE);
            }
            $totalPackageNum = $packageNum * count($packs);
            if ($totalPackageNum > $company['quantity'] && $company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY) {
                throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
            }
        }

        // 平台订单
        if ($orderType == WaybillHistory::ORDER_TYPE_GENERAL) {
            list($successList, $failList) = self::handleNewPtPrint($packs, $userId, $shopId, $itemIdStrArr, $allOrderList,
                $allOrderItemList, $addressInfo, $template, $waybillAuth, $batchNo, $senderInfo, $version,
                $mode, $printOrderStatus, $packageNum, $softRemark, $company,$isPrintRefund,$operatorName);
        } else {
            // 自由打印订单
            list($successList, $failList) = self::handleNewCustomizePrint($packs, $userId, $shopId, $allOrderList,
                $addressInfo, $template, $waybillAuth, $batchNo, $senderInfo, $version, $mode, $packageNum,$company, $operatorName);
        }
        $successPackageNum = count($successList);
        // 扣除成功的面单
        if ($company['source_status'] == Company::SOURCE_COMPANY_STATUS_OPEN && $company['quantity'] !== Company::INIT_QUANTITY) {
            $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
            //共享电子面单肯定是应用内的授权，授权肯定是从shop里面取的
            $waybillService->setShop($waybillAuth);
            //单号余额非无限量，取号后，电子面单余额数量减少，已用面单数量增加
            if ($company['quantity'] !== Company::INIT_UNLIMITE_QUANTITY) {
                $res = Company::query()
                    ->where('id', $company['id'])
                    ->where('quantity', '>=', $successPackageNum)
                    ->update([
                        'quantity' => DB::raw("`quantity` - $successPackageNum"),
                        'allocated_quantity' => DB::raw("`allocated_quantity` + $successPackageNum")
                    ]);
                if (!$res) {
                    Log::error('电子面单余额不足', ["res"=>$res,"successPackageNum"=>$successPackageNum]);
                    throw new ApiException(ErrorConst::WAYBILL_SHARE_INSUFFICIENT_BALANCE);
                }
            } else {
                //无限量的不用减少电子面单余额数量，已用面单数量增加
                $query = Company::where('id', $company['id']);
                $query->increment('allocated_quantity', $successPackageNum);
            }
        }
        return array($successList, $failList);
    }

    /**
     * @param array $template
     * @return mixed
     */
    public static function getTemplateUrl(array $template)
    {
//        Log::info('生成模板URL', $template);
        $templateId = $template['id'];
        //微信视频号的templateURL是个特殊的逻辑，自定义区域的内容是存在模板表里面，然后通过这个模板取出来
        if($template['auth_source']==PlatformConst::WAYBILL_WXSP){
            return '/api/template/contentPrintTemplate?id='.$templateId.'&md5='.md5(json_encode($template));
        }else {
            return $template['template_url'];
        }
    }

    /**
     * @param array $templateIdArr
     * @return array
     */
    public static function getParamsStr(array $templateIdArr): array
    {
        $paramsStrArr = [];
        foreach ($templateIdArr as $templateId) {
            $shopTmp = self::getPrintShopByTemplateId($templateId);
            $waybillService = WaybillServiceManager::init(PlatformConst::WAYBILL_DY, $shopTmp->access_token);
            $paramsStrArr[$templateId] = $waybillService->getPrintParamsStr();
        }
        return $paramsStrArr;
    }

    /**
     * 通过模板id获取店铺
     * @param $templateId
     * @return Shop
     * @throws ApiException
     */
    public static function getPrintShopByTemplateId($templateId): Shop
    {
        $templateTmp = Template::query()->find($templateId);
        if (empty($templateTmp)) {
            $templateTmp = Template::query()->withTrashed()->find($templateId);
            if (empty($templateTmp)) {
                throw new ApiException(ErrorConst::WAYBILL_TEMPLATE_NOT_FOUND);
            }
        }
        $companyTmp = Company::query()->where('id', $templateTmp->company_id)->first();
        if ($companyTmp->source == Company::SOURCE_COMPANY_STATUS_YES) {
            $tempShopId = $companyTmp->source_shopid;
        } else {
            $tempShopId = $companyTmp->shop_id;
        }
        $shopTmp = \App\Models\Fix\Shop::firstById($tempShopId);
        return $shopTmp;
    }

    /**
     * @param $printData
     * @param $waybillHistory
     * @return array
     * @throws ApiException
     * @throws BusinessException
     */
    public static function handlePrintDataMiss($printData, $waybillHistory): array
    {
        // 抖音取号和取密文是2个接口
        // 密文丢了
        $templateId = $waybillHistory['template_id'];
        if (empty($printData['contents'][0]['encryptedData'])) { // 重新取下密文
            $shopTmp = self::getPrintShopByTemplateId($templateId);
            $waybillService = WaybillServiceManager::init(PlatformConst::WAYBILL_DY, $shopTmp->access_token);
            $waybillBo = new WaybillBo();
            $waybillBo->waybill_code = $printData['express_no'];
            $waybillBo->wp_code = $printData['express_code'];
            $printDataByWaybillBos = $waybillService->getPrintDataByWaybillBos([$waybillBo]);
            Log::info('重新取密文', [$waybillHistory['waybill_code'], $printDataByWaybillBos]);
            /** @var WaybillsPrintDataBo $printDataByWaybillBo */
            $printDataByWaybillBo = collect($printDataByWaybillBos)->first();
            $printData['contents'][0]['encryptedData'] = $printDataByWaybillBo->encrypted_data;
            $printData['contents'][0]['signature'] = $printDataByWaybillBo->sign;
            $printData['contents'][0]['version'] = $printDataByWaybillBo->version;


        }
        $templateURL = $printData['contents'][0]['templateURL'];
        Log::info('重新取模板URL', [$templateId, $templateURL]);
        if(empty($templateURL)){
            $templateURL = Template::withTrashed()->find($templateId)->template_url;
            $printData['contents'][0]['templateURL']= $templateURL;
        }

        return $printData;
    }

    /**
     * @throws ApiException
     */
    private static function handleLatestEncryptedPrintDataPrintData(array $template, $waybillAuth, Collection $printRecordList)
    {
        $waybillService = WaybillServiceManager::init($template['auth_source'], $waybillAuth->access_token);
        // 抖音会过期，所以要请求接口获取最新的
        $waybillBoList = [];
        foreach ($printRecordList as $index => $printRecord) {
            $waybillBo = new WaybillBo();
            $waybillBo->wp_code = $template['wp_code'];
            $waybillBo->waybill_code = $printRecord['waybill_code'];
            $waybillBoList[] = $waybillBo;
        }
        $waybillsPrintDataBoList = $waybillService->getPrintDataByWaybillBos($waybillBoList);
        $waybillsPrintDataBoList = array_pluck($waybillsPrintDataBoList, null, 'waybill_code');
        $historyPrintDataArr = [];
        foreach ($waybillsPrintDataBoList as $waybillsPrintDataBo) {
            /** @var WaybillsPrintDataBo $waybillsPrintDataBo */
            if ($waybillsPrintDataBo->hasError()) {
                throw new ApiException(ErrorConst::PRINTED_DATA_ABNORMAL);
            }
            $waybillHistory = $printRecordList->where('waybill_code', $waybillsPrintDataBo->waybill_code)->first();
            $printData = json_decode($waybillHistory['print_data'], true);
            $printData['contents'][0]['params'] = $waybillsPrintDataBo->param_str;
            $printData['contents'][0]['signature'] = $waybillsPrintDataBo->sign;
            $printData['contents'][0]['encryptedData'] = $waybillsPrintDataBo->encrypted_data;
            $historyPrintDataArr[$waybillsPrintDataBo->waybill_code] = $printData;
        }
        return $historyPrintDataArr;
    }

    /**
     * @param $order
     * @param WaybillsPrintDataBo $waybillsPrintDataBo
     * @param $history
     * @param array $template
     * @param array $printData
     * @param array $subPrintDataArr
     * @param string $batchNo
     * @param $version
     * @param int $counter
     * @param int $total
     * @param int $orderType
     * @param string $operatorName
     * @return PrintRecord
     */
    public static function createPrintRecord($order, WaybillsPrintDataBo $waybillsPrintDataBo, $history, array $template,
                                             array $printData,array $subPrintDataArr, string $batchNo, $version,
                                             int $counter, int $total, $orderType = 1, $operatorName = '')
    {
        $printData['subPrintDataArr'] = $subPrintDataArr;
        if ($orderType == WaybillHistory::ORDER_TYPE_GENERAL){
            $receiver_name = $order['orderCipherInfo']['receiver_name_mask'];
            $receiver_phone = $order['orderCipherInfo']['receiver_phone_mask'];
            $receiver_address = $order['orderCipherInfo']['receiver_address_mask'];
            $package_id = $waybillsPrintDataBo->package_id;
        }else{
            $receiver_name = $order['receiver_name'];
            $receiver_phone = $order['receiver_phone'];
            $receiver_address = $order['receiver_address'];
            $package_id = 0;
        }
        // 提取send_content
        $sendContentArr = array_merge([$printData['itemListMap']['contents']], array_column($printData['otherCustomList'], 'contents'));
        $send_content = implode("\n分页\n",$sendContentArr);
        return PrintRecord::create([
            'user_id' => 0,
            'shop_id' => $template['company']['shop_id'],
            'order_id' => $order['id'],
            'package_id' => $package_id,
            'history_id' => $history->id,
            'company_id' => $template['company_id'],
            'order_no' => $order['tid'],
            'waybill_code' => $waybillsPrintDataBo->waybill_code,
            'wp_code' => $template['wp_code'],
            'receiver_province' => $order['receiver_state'],
            'receiver_city' => $order['receiver_city'],
            'receiver_district' => $order['receiver_district'],
            'receiver_name' => $receiver_name,
            'receiver_phone' => $receiver_phone,
            'receiver_address' => $receiver_address,
            'receiver_zip' => 0,
            'buyer_remark' => '',
            'print_data' => json_encode($printData, JSON_UNESCAPED_UNICODE),
            'batch_no' => $batchNo,
            'version' => $version,
            'to_shop_id' => $order['shop_id'],
            'template_id' => $template['id'],
            'template_name' => $template['name'],
            'print_index' => $counter,
            'print_count' => $total,
            'order_type' => $orderType,
            'send_content' => $send_content,
            'created_by'=>$operatorName,
            'updated_by'=>$operatorName,
            'outer_order_no'=>$order['outer_order_no']??'',
        ]);

    }

    /**
     * @param $order
     * @param WaybillsPrintDataBo $waybillsPrintDataBo
     * @param array $template
     * @param array $printData
     * @param array $subPrintDataArr
     * @param $version
     * @param string $batchNo
     * @param int $counter
     * @param int $total
     * @param int $orderType
     * @param string $operatorName
     * @param null $sub_waybill_codes
     * @param string $softRemark
     * @return mixed
     */
    public static function createWaybillHistory($order, WaybillsPrintDataBo $waybillsPrintDataBo, array $template, $company,
                                                array $printData,array $subPrintDataArr, $version, string $batchNo, int $counter, int $total,
                                                $orderType = 1, $operatorName='',$sub_waybill_codes = null,$softRemark = '')
    {
        $printData['subPrintDataArr'] = $subPrintDataArr;
//        Log::debug('createWaybillHistory $company',[$company]);
//        $waybillResourceShopId = isset($company) ? $company['shop_id'] : $order->shop_id;
        if ($orderType == WaybillHistory::ORDER_TYPE_GENERAL){
            $receiver_name = $order['orderCipherInfo']['receiver_name_mask'];
            $receiver_phone = $order['orderCipherInfo']['receiver_phone_mask'];
            $receiver_address = $order['orderCipherInfo']['receiver_address_mask'];
            $package_id = $waybillsPrintDataBo->package_id;
        }else{
            $receiver_name = $order['receiver_name'];
            $receiver_phone = $order['receiver_phone'];
            $receiver_address = $order['receiver_address'];
            $package_id = 0;
        }
        // 提取send_content
        $sendContentArr = array_merge([$printData['itemListMap']['contents']], array_column($printData['otherCustomList'], 'contents'));
        $send_content = implode("\n分页\n",$sendContentArr);

        return WaybillHistory::create([
            'user_id' => 0,
            'shop_id' => $template['company']['shop_id'],
            'order_id' => $order['id'],
            'package_id' => $package_id,
            'order_no' => $order['tid'],
            'outer_order_no' => $order['outer_order_no']??'',
            'template_id' => $template['id'],
            'company_id' => $template['company_id'],
            'auth_source' => $template['auth_source'],
            'source' => $company['source'] ?? '',
//            'source_userid' => $company['source_userid'] ?? '',
            'source_shopid' => $company['source_shopid'] ?? '',
            'parent_waybill_code' => $waybillsPrintDataBo->parent_waybill_code,
            'waybill_code' => $waybillsPrintDataBo->waybill_code,
            'platform_waybill_id'=>$waybillsPrintDataBo->platform_waybill_id,
            'wp_code' => $template['wp_code'],
            'print_data' => json_encode($printData, JSON_UNESCAPED_UNICODE),
            'receiver_province' => $order['receiver_state'],
            'receiver_city' => $order['receiver_city'],
            'receiver_district' => $order['receiver_district'],
            'receiver_name' => $receiver_name,
            'receiver_phone' => $receiver_phone,
            'receiver_address' => $receiver_address,
            'extra' => json_encode([]),
            'print_data_items' => '',
            'version' => $version,
            'batch_no' => $batchNo,
            'to_shop_id' => $order['shop_id'],
            'waybill_index' => $counter,
            'waybill_count' => $total,
            'order_type' => $orderType,
            'sub_waybill_codes' => $sub_waybill_codes,
            'created_by'=>$operatorName,
            'updated_by'=>$operatorName,
            'soft_remark' => $softRemark,
            'send_content' => $send_content,
        ]);
    }

    /**
     * 电子面单打印完成修改订单状态
     * @param $orderItemIdArr
     * @param int $isSplit
     * @param $orderId
     * @return void
     */
    public static function waybillDoneUpdateOrder(array $orderIdArr, array $orderItemIdArr, int $isSplit = 0, int $package_id = 0): void
    {

        OrderItem::query()
            ->whereIn('id', $orderItemIdArr)
            ->update([
                'print_status' => OrderItem::PRINT_STATUS_YES,
                'print_num' => DB::raw('print_num + 1')
            ]);

        foreach ($orderIdArr as $orderId) {
            $printStatus = Order::PRINT_STATUS_YES;
            // 拆分的情况下，判断是否全部打印
            if ($isSplit) {
                $noPrintCount = OrderItem::query()
                    ->where('order_id', $orderId)
                    ->where('print_status', OrderItem::PRINT_STATUS_NO)
                    ->count();
                if ($noPrintCount > 0) {
                    $printStatus = Order::PRINT_STATUS_PART;
                }
            }
            Order::query()
                ->where('id', $orderId)
                ->update([
                    'print_status' => $printStatus,
                    'express_no' => null,
                    'printed_at' => date('Y-m-d H:i:s'),
                    'take_waybill_at' => date('Y-m-d H:i:s'),
                    'abnormal_type' => AbnormalOrder::TYPE_OF_NONE,
                    'print_num' => DB::raw('print_num + 1')
                ]);
        }
        // 去掉订单的异常状态
        $orderList = Order::query()->whereIn('id', $orderIdArr)->get(['address_md5', 'shop_id']);
        $addressMd5Arr = $orderList->pluck('address_md5')->unique()->toArray();
        $shopIdArr = $orderList->pluck('shop_id')->unique()->toArray();
        $updateOrderIdArr = Order::query()
            ->whereIn('shop_id', $shopIdArr)
            ->whereIn('address_md5', $addressMd5Arr)
            ->whereIn('order_status', [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])
            ->where('abnormal_type', '!=', AbnormalOrder::TYPE_OF_NONE)
            ->get()->pluck('id')->toArray();
        if (!empty($updateOrderIdArr)) {
            // 防止死锁
            Order::query()->whereIn('id',$updateOrderIdArr)
                ->update(['abnormal_type' => AbnormalOrder::TYPE_OF_NONE]);
        }


        if ($package_id > 0) {
            Package::query()->where('id', $package_id)->update([
                'print_status' => Package::PRINT_STATUS_YES,
                'print_at' => date('Y-m-d H:i:s'),
            ]);
        }

    }

    /**
     * @param PrintDataPackBo $printDataPackBo
     * @param string $implodeOrderId
     * @param array $template
     * @param WaybillsPrintDataBo $waybillsPrintDataBo
     * @param $version
     * @param array $senderInfo
     * @return array
     */
    public static function buildPrintDataBase(PrintDataPackBo $printDataPackBo, string $implodeOrderId, array  $template,
                                              WaybillsPrintDataBo $waybillsPrintDataBo, $version, array $senderInfo,
                                              string $parentExpressNo = ''): array
    {
        return [
            'documentID' => $printDataPackBo->package_id . '-' . $implodeOrderId,
            'express_code' => $template['wp_code'],
            'express_no' => $waybillsPrintDataBo->waybill_code,
            'parent_express_no' => $parentExpressNo,
            'is_dy' => Environment::isDy()? 1 : 0,
            'version' => $version,
            'contents' => [
                [
                    "templateURL" => self::getTemplateUrl($template),
                    "params" => $waybillsPrintDataBo->param_str,
                    "signature" => $waybillsPrintDataBo->sign,
                    "encryptedData" => $waybillsPrintDataBo->encrypted_data,
                    "version"=>$waybillsPrintDataBo->version,
                    "userId"=>$waybillsPrintDataBo->userId,
                    'addData' => ['senderInfo' => $senderInfo],
                ],
            ]
        ];
    }

    /**
     * 构建打印数据
     * @param PrintDataPackBo $printDataPackBo
     * @param string $implodeOrderId
     * @param array $template
     * @param WaybillsPrintDataBo $waybillsPrintDataBo
     * @param $version
     * @param array $senderInfo
     * @param $printContentConfig
     * @param array $goodsList
     * @param $order
     * @param string $batchNo
     * @param $index
     * @param int $total
     * @return array
     */
    public static function getBuildPrintData(PrintDataPackBo $printDataPackBo, string $implodeOrderId, array $template,
                               WaybillsPrintDataBo $waybillsPrintDataBo, $version, array $senderInfo, $printContentConfig,
                               array $goodsList, $order, string $batchNo, $index, int $total): array
    {
        $printData = self::buildPrintDataBase($printDataPackBo, $implodeOrderId, $template, $waybillsPrintDataBo, $version, $senderInfo);
        list($contentArr, $goodsList) = self::genPrintCustomContent($printContentConfig, $goodsList);
        list($itemListMap, $otherCustomList) = self::genItemListMap($contentArr, $goodsList, $order, $batchNo, $index, $total);
        $printData['itemListMap'] = $itemListMap;
        $printData['otherCustomList'] = $otherCustomList;
        $subPrintDataArr = [];
        foreach ($printDataPackBo->getSubWaybillsPrintDataArr() as $waybillsPrintDataBoTmp) {
            $implodeOrderIdTemp = $implodeOrderId . '_' . $waybillsPrintDataBoTmp->waybill_code;
            $tmpPrintData = self::buildPrintDataBase($printDataPackBo, $implodeOrderIdTemp, $template,
                $waybillsPrintDataBoTmp, $version, $senderInfo, $printDataPackBo->waybill_code);
            $tmpPrintData['itemListMap'] = $itemListMap;
            $tmpPrintData['otherCustomList'] = $otherCustomList;
            $subPrintDataArr[] = $tmpPrintData;
        }
        return [$printData, $subPrintDataArr];
    }

    /**
     * 获取打印内容配置，如果模板里面有打印内容取模板打印内容，如果模板里面print_contents字段是空就取店铺一级的
     * @param int $shopId
     * @param int|null $templateId
     * @return array
     * @throws ErrorCodeException
     */
    public static function getPrintContentConfig(int $shopId, ?int $templateId): array
    {
        $template = null;
        if($templateId) {
            $template = Template::withTrashed()->find($templateId);
        }
        if (!empty($template) && !empty($template->print_contents)) {
            $printContentConfig=json_decode($template->print_contents, true);
            \Log::info('模板打印内容', ['templateId'=>$templateId, 'printContentConfig'=>$printContentConfig]);
        }
        else{
            $printContentConfig = ShopExtra::getPrintContentConfig($shopId);
            \Log::info("店铺打印内容",["shopId"=>$shopId,"printContentConfig"=>$printContentConfig]);
        }
        return $printContentConfig;
    }

}
