<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/6/9
 * Time: 19:41
 */

namespace App\Services\Client;

use App\Exceptions\OrderException;

class TaoBaoClient{

    /**
     * 返回淘宝客户端
     * @return \TopClient\TopClient
     */
    public static function newInstance(){
        return topClient();
    }

    /**
     * 处理响应
     *
     * @param $response
     * @param bool $isPoolCurl
     * @return array
     * @throws OrderException
     * <AUTHOR>
     */
    public static  function handleResp($response)
    {
        $resp = objectToArray($response);
        /**
         * 异常示例
         * "code" => 27
         * "msg" => "Invalid session"
         * "sub_code" => "invalid-sessionkey"
         * "sub_msg" => "SessionKey非法"
         * "request_id" => "3qvdigpsl5on"
         */
        if (!empty($resp['code'])) {
            \Log::error('TaobaoOrder handleResp', [$resp]);
            throw new OrderException("淘宝服务错误:{$resp['msg']};" . json_encode($resp));
        }

        return $resp;
    }
}
