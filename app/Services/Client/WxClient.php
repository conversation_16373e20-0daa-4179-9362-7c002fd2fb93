<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/18
 * Time: 20:59
 */

namespace App\Services\Client;


use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Psr\Http\Message\StreamInterface;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;

class WxClient
{
	protected $appKey;
	protected $secretKey;
	private   $accessToken;
	protected $serviceId;
	protected $specificationId;
	public    $gatewayUrl = "https://api.weixin.qq.com";
	public    $authUrl    = "https://api.weixin.qq.com";

    protected $concurrency = 50;  //并发数
    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

	public function __construct($appKey, $secretKey)
	{
		$this->appKey    = $appKey;
		$this->secretKey = $secretKey;
        $this->serviceId = null;
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient()
    {
        // 创建 Handler
        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));

        return new Client([
//            'http_errors' => false,
            'handler' => $handlerStack //重试策略
        ]);
    }

    public static function newInstance($accessToken,$serviceId,$specificationId, $appKey = null, $secretKey = null): WxClient
    {

        $client = new WxClient($appKey ?? config('socialite.wx.client_id'), $secretKey ?? config('socialite.wx.client_secret'));
        $client->setAccessToken($accessToken);
        $client->setServiceId($serviceId);
        $client->setSpecificationId($specificationId);
        return $client;
    }

    /**
     * @param $method
     * @param $url
     * @param array $params
     * @return array|StreamInterface
     * @throws ApiException
     * @throws ClientException
     * @throws GuzzleException
     * <AUTHOR>
     */
	public function execute($method, $url, array $params = [])
	{
		$httpClient = $this->getHttpClient();

		//post和get方式处理
		$headers = [
			'Content-type' => 'application/json',
			"Accept"       => "application/json"
		];
		switch (strtolower($method)) {
			case 'get':
				$postKey = 'query';
				break;
			case 'post':
				$postKey = 'json';
				break;
			default:
				$postKey = 'form_params';
				$headers = [];
				break;
		}
        if (empty($params)){
            $params = null;
        }
		$validParams = [
			'access_token'     => $this->accessToken,
			'service_id'       => $this->serviceId,
			'specification_id' => $this->specificationId,
		];
		$applyUrl    = $this->gatewayUrl . $url . '?' . http_build_query($validParams);
//        \Log::info("请求url:".$applyUrl);

        if (empty($params)){
            $params = new \stdClass();
        }
        $response = $httpClient->request($method, $applyUrl, [
            $postKey  => $params,
            'headers' => $headers,
        ]);

		return $this->handleResponse($response->getBody());
	}

    /**
     * 构建批量请求的参数
     * @param string $url
     * @param array $apiParams
     * @return array
     */
    public function buildRequestData(string $url,array  $apiParams): array
    {

        return [
            'url' => $this->buildRequestUrl($url),
            'apiParams' => $apiParams
        ];

    }

    /**
     *  构建请求的URL
     * @param string $url
     * @return string
     */
    public function buildRequestUrl(string $url):string{
        $validParams = [
            'access_token'     => $this->accessToken,
            'service_id'       => $this->serviceId,
            'specification_id' => $this->specificationId,
        ];
        return $this->gatewayUrl . $url . '?' . http_build_query($validParams);
    }




    public function executeByCustom($method, $url, $params = [])
    {
        $httpClient = $this->getHttpClient();

        //post和get方式处理
        $headers = [
            'Content-type' => 'application/json',
            "Accept"       => "application/json"
        ];
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'json';
                break;
            default:
                $postKey = 'form_params';
                $headers = [];
                break;
        }

        $validParams = [
            'access_token'     => $this->accessToken,
            'service_id'       => $this->serviceId,
            'specification_id' => $this->specificationId,
        ];
        $applyUrl    = $this->gatewayUrl . $url . '?' . http_build_query($validParams);

        $response = $httpClient->request($method, $applyUrl, [
            $postKey  => $params,
            'headers' => $headers,
        ]);

        return json_decode($response->getBody(), true);
    }

	public function getComponentAccessToken(string $ticket = '')
	{
		$params = [
			'component_appid'         => $this->appKey,
			'component_appsecret'     => $this->secretKey,
			'component_verify_ticket' => $ticket,
		];
		\Log::debug('getComponentAccessToken params', [$params]);
		$httpClient = $this->getHttpClient();
		$response   = $httpClient->post($this->authUrl . '/cgi-bin/component/api_component_token', [
			'json'    => $params,
			'headers' => [
				'Content-type' => 'application/json',
				"Accept"       => "application/json"
			],
		]);

		return $this->handleResponse($response->getBody());
	}

	public function getPlatformAccessToken()
    {
        $httpClient = $this->getHttpClient();

        $params = [
            'grant_type'     => 'client_credential',
            'appid'          => env('SERVICE_APP_ID', 'wxa6dae325683540ed'),
            'secret'         => env('SERVICE_APP_SECRET', 'c46ccce8ff7bb9512d2e22704b99dd38'),
        ];
        $applyUrl    = $this->gatewayUrl . '/cgi-bin/token' . '?' . http_build_query($params);

        $response = $httpClient->request('get', $applyUrl, [
            'query'  => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept"       => "application/json"
            ]
        ]);

        \Log::Info('获取服务市场accesstoken:'.$response->getBody());
        return $this->handleResponse($response->getBody());
    }

    public function getServiceBuyer($platformAccessToken, $appId)
    {
        $httpClient = $this->getHttpClient();
        $applyUrl    = $this->gatewayUrl . '/wxa/servicemarket/service/get_service_buyer' . '?access_token=' . $platformAccessToken;

        $params = [
            "appid"      =>$appId,
            "service_id" => $this->serviceId ?: env('SERVICE_ID', 2575068212342849541),
            "buyer_type" =>1
        ];
        $response = $httpClient->request('post', $applyUrl, [
            'json'    => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept"       => "application/json"
            ]
        ]);

        \Log::Info('获取用户服务列表:'.$response->getBody(),[$params]);
        return $this->handleResponse($response->getBody());
    }

	public function getService(string $code, string $componentAccessToken)
	{
	    if (config('app.platform') == PlatformConst::WX) {
	        $url = '/product/service/check_auth?component_access_token=';
        } else {
	        $url = '/wxa/servicemarket/service/login_auth?access_token=';
        }
		$params = [
			'code' => $code,
		];
		\Log::debug('getService params', [$params]);
		$httpClient = $this->getHttpClient();
		$response   = $httpClient->post($this->authUrl . $url .
		                                $componentAccessToken, [
			'json'    => $params,
			'headers' => [
				'Content-type' => 'application/json',
				"Accept"       => "application/json"
			],
		]);

		return $this->handleResponse($response->getBody());
	}

	public function getAuthInfo(string $componentAccessToken, string $authorizerAppid)
	{
		$params = [
			'component_appid'  => $this->appKey,
			'authorizer_appid' => $authorizerAppid,
		];
		\Log::info('getService params', [$params]);
		$httpClient = $this->getHttpClient();
		$response   = $httpClient->post($this->authUrl .
		                                '/cgi-bin/component/api_get_authorizer_info?component_access_token=' .
		                                $componentAccessToken, [
			'json'    => $params,
			'headers' => [
				'Content-type' => 'application/json',
				"Accept"       => "application/json"
			],
		]);

		return $this->handleResponse($response->getBody());
	}

	public function getAccessToken(string $componentAccessToken, string $authorizerAppid, string $authorizerRefreshToken)
	{
		$params = [
			'component_appid'          => $this->appKey,
			'authorizer_appid'         => $authorizerAppid,
			'authorizer_refresh_token' => $authorizerRefreshToken,
		];
		\Log::info('getAccessToken params', [$params]);
		$httpClient = $this->getHttpClient();
		$response   = $httpClient->post($this->authUrl .
		                                '/cgi-bin/component/api_authorizer_token?component_access_token=' .
		                                $componentAccessToken, [
			'json'    => $params,
			'headers' => [
				'Content-type' => 'application/json',
				"Accept"       => "application/json"
			],
		]);

		return $this->handleResponse($response->getBody());
	}

	/**
	 * @param mixed $accessToken
	 * @return WxClient
	 */
	public function setAccessToken($accessToken)
	: WxClient
	{
		$this->accessToken = $accessToken;
		return $this;
	}

	/**
	 * @param mixed $serviceId
	 * @return WxClient
	 */
	public function setServiceId($serviceId)
	: WxClient
	{
		$this->serviceId = $serviceId;
		return $this;
	}

	/**
	 * @param mixed $specificationId
	 * @return WxClient
	 */
	public function setSpecificationId($specificationId)
	: WxClient
	{
		$this->specificationId = $specificationId;
		return $this;
	}

	/**
	 * @param StreamInterface $body
	 * @return array
	 * @throws ClientException|ApiException
     * <AUTHOR>
	 */
	private function handleResponse($body)
	{
		if (!is_array($body)) {
			$body = json_decode($body, true);
		}
		//		\Log::error('request result:', [$body]);

		if (isset($body['errcode']) && $body['errcode'] != 0) {
			\Log::error(get_class($this) . ' request error:' . $body['errmsg'], $body);
            switch ($body['errcode']) {
                case 109001:
                    throw new ApiException(ErrorConst::ORDER_DELIVERED);
                case 40001:
                case 42001:
                    throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                default:
                    \Log::error('WxClient error result:', [$body]);
                    throw new ClientException(get_class($this) . ':' . $body['errmsg']);
//                    throw new OrderException('微信服务异常：' . json_encode($body, JSON_UNESCAPED_UNICODE));
            }
		}
		return $body;
	}

    public function getPreAuthCode($componentAccessToken)
    {
        $params = [
            'component_appid'         => $this->appKey,
        ];
        \Log::debug('getPreAuthCode params', [$params]);
        $httpClient = $this->getHttpClient();
        $response   = $httpClient->post($this->authUrl . '/cgi-bin/component/api_create_preauthcode?component_access_token=' .$componentAccessToken, [
            'json'    => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept"       => "application/json"
            ],
        ]);

        return $this->handleResponse($response->getBody());
    }

    public function getQueryAuthInfo($authCode, $componentAccessToken)
    {
        $params = [
            'component_appid'         => $this->appKey,
            'authorization_code'      => $authCode
        ];
        \Log::debug('getQueryAuthInfo params', [$params]);
        $httpClient = $this->getHttpClient();
        $response   = $httpClient->post($this->authUrl . '/cgi-bin/component/api_query_auth?component_access_token=' .$componentAccessToken, [
            'json'    => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept"       => "application/json"
            ],
        ]);

        return $this->handleResponse($response->getBody());
    }

    public static  function handleErrorCode($result)
    {
        $errcode = $result['errcode'] ?? 0;
        switch ($errcode) {
            case 0: // 正常
                break;
            case 109001:
                throw new ApiException(ErrorConst::ORDER_DELIVERED);
            case 40001:
            case 42001:
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                throw new ClientException('微信服务异常：' . json_encode($result, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(),$params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['taobao.logistics.offline.send'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // todo 收集wx错误码进行重试
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }
}
