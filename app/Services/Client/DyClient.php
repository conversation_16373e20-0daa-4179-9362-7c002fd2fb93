<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/6/9
 * Time: 19:41
 */

namespace App\Services\Client;


use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;

class DyClient
{
    public $appkey;

    public $secretKey;

    public $gatewayUrl = "https://openapi-fxg.jinritemai.com";
//    public $gatewayUrl = "http://proxy-dy.mayiapps.cn";

    protected $apiVersion = "2";
    /**
     * @var mixed
     */
    private $accessToken = '';
    /**
     * 默认超时时间 秒
     * @var int
     */
    private $clientTimeout = 8;
    private $clientConnectTimeout = 5; // 客户端连接超时
    private $shopId = 0;

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    /**
     * @var Client
     */
    protected $client;

    public static function newInstance($accessToken, $appKey = null, $secretKey = null)
    {

        $client = new DyClient($appKey ?? config('socialite.dy.client_id'), $secretKey ?? config('socialite.dy.client_secret'));
        $client->setAccessToken($accessToken);
        return $client;
    }

    public function __construct($appKey, $secretKey, $timeout = 8)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->clientTimeout = $timeout;
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams, $requestMethod = 'get')
    {
        $request_data = $this->buildRequestData($apiParams, $apiMethod);
        $base_url = $this->getBaseUrlByApimethod($apiMethod);

        $httpClient = $this->getHttpClient();
        if ($apiMethod == 'order/batchSensitive' || $apiMethod == 'logistics/newCreateOrder') {
            $response = $httpClient->post($base_url, [
                'form_params' => $request_data,
            ]);
        } else {
            $requestMethod = strtolower($requestMethod);
            if ($requestMethod == 'get') {
                $dataType = 'query';
            }else {
                $dataType = 'form_params';
            }
            $response = $httpClient->$requestMethod($base_url, [
                $dataType => $request_data,
            ]);
        }

        if ($apiMethod == 'product/detail' && array_get($apiParams, 'product_id', 0) == '3423135763418741917') {
            \Log::debug('bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb', [
                'base_url' => $base_url,
                'params' => $request_data,
                'body' => $this->handleResponse($response->getBody())
            ]);
        }

        return $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名 例如：product/getGoodsCategory
     * @param array $apiParams 接口参数
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    public function executeByCustom(string $apiMethod, array $apiParams, $requestMethod = 'POST')
    {
        $request_data = $this->buildRequestData($apiParams, $apiMethod);
        $apiMethod = str_replace('.', '/', $apiMethod);
        $base_url = $this->getBaseUrlByApimethod($apiMethod);

        $httpClient = $this->getHttpClient();
        $requestMethod = strtoupper($requestMethod);
        if ($requestMethod == 'GET') {
            $dataType = 'query';
        }else {
            $dataType = 'form_params';
        }
        $response = $httpClient->$requestMethod($base_url, [
            $dataType => $request_data,
        ]);
        $contents = $response->getBody()->getContents();
        return json_decode($contents, true);
    }

    /**
     * 执行异步请求
     * @param string $apiMethod
     * @param array $apiParamsArr
     * @return array
     * <AUTHOR>
     */
    public function executeAsync(string $apiMethod, array $apiParamsArr): array
    {
        $result = [];
        $base_url = $this->getBaseUrlByApimethod($apiMethod);
        $requests = function ($apiParamsArr) use ($base_url, $apiMethod) {
            foreach ($apiParamsArr as $index => $apiParams) {
                $request_data = $this->buildRequestData($apiParams, $apiMethod);
                yield new Request('GET', $base_url . '?' . http_build_query($request_data));
            }
        };
        $httpClient = $this->getHttpClient();
        $pool = new Pool($httpClient, $requests($apiParamsArr), [
            'concurrency' => 10, //并发数
            'fulfilled' => function (Response $response, $index) use (&$result, $apiMethod, $apiParamsArr) {
                $apiParams = $apiParamsArr[$index] ?? [];
                // 请求成功
                try {
                    $data = $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
                    $result[$index] = $data['data'] ?? [];
                } catch (\Exception $ex) {
                    Log::info("返回有错误." . $ex->getMessage(), []);
                    $result[$index] = null;
                }
//                echo $response->getBody()->getContents();
            },
            'rejected' => function (RequestException $reason, $index) {
                // 请求失败
                $result[$index] = null;
                Log::info("执行异常." . $reason->getMessage(), []);
//                throw new OrderException(class_basename($this) . ' executeAsync error:' . $reason->getMessage());
            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();
        return $result;
    }

    /**
     * 刷新token
     * @param string $refreshToken
     * @return array
     * @throws ClientException
     */
    public function refreshToken(string $refreshToken)
    {

        $data = [
            'refresh_token' => $refreshToken,
            'grant_type' => 'refresh_token',
        ];
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        $client = new DyClient($appKey, $secretKey);

        $params = $client->buildRequestData($data, 'token.refresh');
        $httpClient = $this->getHttpClient();
        $response = $httpClient->get($this->gatewayUrl . '/token/refresh', [
            'query' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        return $this->handleResponse($response->getBody());
    }

    /**
     * 生成签名
     * @param $method
     * @param $params
     * @param $timestamp
     * @return string
     */
    private function generateSign($method, $params, $timestamp)
    {
        $this->rec_ksort($params);
        //不转义中文(转义后会导致签名错误)
        if (in_array($method, ['order.addOrderRemark', 'logistics.createSFOrder', 'logistics.newCreateOrder', 'logistics.updateOrder', 'order.batchEncrypt', 'order.getSearchIndex'])) {
            $param_json = json_encode($params, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        } else if ($method == 'logistics.getShopKey' || $method == 'rights.info' || empty($params)) {
            $param_json = '{}';
        }else if (empty($params)) {
            $param_json = '{}';
        } else {
            $param_json = json_encode($params, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
        }

        // 计算签名
        $str = "app_key" . $this->appkey . "method" . $method . "param_json" . $param_json . "timestamp" . $timestamp . "v" . $this->apiVersion;
        $md5_str = $this->secretKey . $str . $this->secretKey;
        if (in_array($method, ['logistics.createSFOrder', 'logistics.newCreateOrder', 'logistics.updateOrder'])) {
            $md5 = md5(str_replace('\\/', '/', $md5_str));
        } else if ($method == 'order.batchSensitive' || $method == 'order.batchDecrypt' || $method == 'order.batchEncrypt') {
            $md5 = md5(str_replace('\\/', '/', $md5_str));
        } else {
            $md5 = md5($md5_str);
        }
        return $md5;
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient()
    {
        // 创建 Handler
        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));

        return new Client([
//            'http_errors' => false,
            'timeout' => $this->clientTimeout, //秒
            'connect_timeout' => $this->clientConnectTimeout, //秒
//            'handler' => $handlerStack //重试策略
        ]);
    }

    /**
     * @param StreamInterface $body
     * @param $request
     * @return array
     * <AUTHOR>
     */
    private function handleResponse($body, $request = [])
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        if ($body['code'] != 10000) {
            $shop_id = $this->shopId;
            \Log::error(get_class($this) . ' request error:' . $body['msg'] ?? 'message为空', compact('shop_id', 'request', 'body'));
//            throw new ClientException(get_class($this) . ':' . $body['message']);
        }
        return $body;
    }

    /**
     * @param mixed $accessToken
     * @return DyClient
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param string $apiMethod
     * @return string
     * <AUTHOR>
     */
    public function getBaseUrlByApimethod(string $apiMethod): string
    {
        return $this->gatewayUrl . '/' . $apiMethod;
    }

    /**
     * @param array $apiParams
     * @param string $apiMethod
     * @return array
     * <AUTHOR>
     */
    public function buildRequestData($apiParams, string $apiMethod): array
    {
        $apiParams = array_map(function ($item) {
            return is_array($item) ? $item : (is_bool($item) ? $item : (string)$item);
        }, $apiParams);
        $this->rec_ksort($apiParams);
        $method = str_replace('/', '.', $apiMethod);
        $timestamp = date('Y-m-d H:i:s', time());


        // 构造请求url
        $sign = $this->generateSign($method, $apiParams, $timestamp);
        $request_data = [
            'app_key' => $this->appkey,
            'method' => $method,
            'access_token' => $this->accessToken,
            'param_json' => empty($apiParams) ? '{}' : json_encode($apiParams, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE),
            'timestamp' => $timestamp,
            'v' => $this->apiVersion,
            'sign' => $sign,
        ];
        return $request_data;
    }

    /**
     * @param int $shopId
     */
    public function setShopId(int $shopId)
    {
        $this->shopId = $shopId;
        return $this;
    }

    /**
     * @param $shop
     */
    public function setShopIdByShop($shop)
    {
        $this->shopId = $shop->id ?? 0;
        return $this;
    }


    /**
     * 处理抖音响应的错误
     * @see https://op.jinritemai.com/docs/guide-docs/10/23
     * <AUTHOR>
     * @param $result
     * @throws ApiException
     * @throws OrderException
     */
    public static function handleErrorCode($result)
    {
        if (is_object($result)) {
            $result = json_decode(json_encode($result), true);
        }
        // 错误码改了 去掉 err_no
        // @see https://op.jinritemai.com/docs/notice-docs/5/2151
        if (!isset($result['code'])) {
            throw new OrderException('抖音平台错误');
        }
        if ($result['code'] == 10000) {
            return;
        }
        switch ($result['sub_code']) {
            // 错误码 @see https://op.jinritemai.com/docs/guide-docs/212/1427
//            case 10000: // 正常
//                break;
//            case 7: // 这个错误码非常奇怪，举例了部分情况如下：
//                // {"err_no":7,"log_id":"202107292002390101311300265C03A30C","message":"电子面单号不存在"}
//                // {"err_no":7,"log_id":"202107291952040101501792121001C694","message":"请求内部服务超时，请重试"}
//                // {"err_no":7,"log_id":"2021072915563801015120909525004264","message":"请求内部服务超时，请重试"}
//                // {"err_no":7,"log_id":"2021072901165701015010720732158565","message":"获取商品详情读取数据失败"}
//                $error = ErrorConst::PLATFORM_SERVER_ERROR;
//                $error[1] = '抖音' . $error[1] . '：' . $result['sub_msg'] ?? '';
//                throw new ApiException($error);
            case 'isv.business-failed:60110': // 订单已发货，不允许再次执行发货
                // 已发货就当做发货成功
                return;
            case 'dop.authorization-no-existed':
            case 'dop.authorization-closed':
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_CANCELED);
            case 'isv.access-token-expired':
            case 'isv.access-token-no-existed': // access_token不存在，请使用最新的access_token访问
            case 'isv.access-token-missing': // accessToken不能为空
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            case 'isv.traffic-limited':
                throw new ApiException(ErrorConst::PLATFORM_REQUEST_LIMIT);
            case 'isv.env-suspected':
                throw new ApiException(ErrorConst::PLATFORM_ANTISPAM);
            default:
                \Log::error('抖音平台错误：' ,[$result]);
                $sub_msg = $result['sub_msg'] ?? '???';
                throw new OrderException('抖音平台错误：' . $sub_msg);
        }
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['order.logisticsAdd'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // dy 错误码汇总 https://op.jinritemai.com/docs/guide-docs/161/1427
                        if (isset($body['code']) && in_array($body['code'], [20000, 60000])) {
                            return true;
                        }
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    // 关联数组排序，递归
    protected function rec_ksort(array &$arr)
    {
        $kstring = true;
        foreach ($arr as $k => &$v) {
            if (!is_string($k)) {
                $kstring = false;
            }
            if (is_array($v)) {
                $this->rec_ksort($v);
            }
        }
        if ($kstring) {
            ksort($arr);
        }
    }
}
