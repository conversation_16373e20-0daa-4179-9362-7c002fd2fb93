<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/18
 * Time: 20:59
 */

namespace App\Services\Client;


use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use GuzzleHttp\Client;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;

class KsClient
{
    protected $appKey;
    protected $secretKey;
//    protected $shopId;
    private $accessToken;
    public $gatewayUrl = "https://open.kwaixiaodian.com";
    public $authUrl = "https://s.kwaixiaodian.com";

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;


    public static function newInstance($accessToken, $appKey = null, $secretKey = null)
    {
        $ksClient = new KsClient($appKey ?? config('socialite.ks.client_id'),
            $secretKey ?? config('socialite.ks.client_secret'));
        $ksClient->setAccessToken($accessToken);
        return $ksClient;
    }

    public function __construct($appKey, $secretKey)
    {
        $this->appKey = $appKey;
        $this->secretKey = $secretKey;
//        $this->shopId = $shopId;
    }

    /**
     * @param $method
     * @param $url
     * @param array $params
     * @return array
     * @throws ClientException
     * <AUTHOR>
     */
    public function execute($method, $url, $params = [])
    {
        $httpClient = $this->getHttpClient();

        $requestData = $this->getRequestData($url, $params);

        //post和get方式处理
        $headers = [
            'Content-type' => 'application/json',
            "Accept" => "application/json"
        ];
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
                $postKey = 'form_params';
                $headers = [];
                break;
            default:
                $postKey = 'json';
                break;
        }


        $response = $httpClient->request($method, $this->gatewayUrl . $url, [
            $postKey => $requestData,
            'headers' => $headers,
        ]);

//	    if ($url == '/open/seller/order/pcursor/list') {
//		    \Log::debug('aaaaaaaaaaaaaaaaaaaaaaaaaaa', [
//			    'method'        => $method,
//			    'url'           => $this->gatewayUrl . $url,
//			    'requestData'   => $requestData,
//			    'response'      => $this->handleResponse($response->getBody()),
//		    ]);
//	    }
        $requestArr = [
            'method' => $method,
            'url' => $this->gatewayUrl . $url,
            'requestData' => $requestData,
        ];

        return $this->handleResponse($response->getBody(), $requestArr);
    }

    /**
     * @param $method
     * @param $url
     * @param array $params
     * @return array
     * @throws ClientException
     * <AUTHOR>
     */
    public function executeByCustom($method, $url, $params = [])
    {
        $httpClient = $this->getHttpClient();
        $requestData = $this->getRequestData($url, $params);
        //post和get方式处理
        $headers = [
            'Content-type' => 'application/json',
            "Accept" => "application/json"
        ];
        switch ($method) {
            case 'get':
                $postKey = 'query';
                break;
            case 'post':
            default:
                $postKey = 'form_params';
                $headers = [];
                break;
        }
        $url = str_replace('.', '/', $url);
        $response = $httpClient->request($method, $this->gatewayUrl . $url, [
            $postKey => $requestData,
            'headers' => $headers,
        ]);
        return json_decode($response->getBody(), true);
    }

    /**
     * 刷新token
     * @param string $refreshToken
     * @return array
     * @throws ClientException
     * <AUTHOR>
     */
    public function refreshToken(string $refreshToken)
    {
        $params = [
            'grant_type' => 'refresh_token',
            'app_id' => $this->appKey,
            'refresh_token' => $refreshToken,
            'app_secret' => $this->secretKey
        ];
        $httpClient = $this->getHttpClient();
        $response = $httpClient->get($this->authUrl . '/oauth2/refresh_token', [
            'query' => $params,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        return $this->handleResponse($response->getBody());
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient()
    {
        // 创建 Handler
        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));
        return new Client([
//            'http_errors' => false,
            'handler' => $handlerStack //重试策略
        ]);
    }

    /**
     * 签名
     * @param $params
     * @return string
     */
    private function generateSign($params)
    {
//        $params['appsecret'] = $this->secretKey;
        ksort($params);
        $sign = '';
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k=$v&";
            }
        }
        $sign .= "signSecret=" . env('KS_SIGN_SECRET', 'cf87e89ac34dc9dacfc73d6f7039ba0a');
        unset($k, $v);

//        Log::info('sisssssssssssssssssssss', [$sign]);
        return md5($sign);
    }

    /**
     * @param mixed $accessToken
     * @return KsClient
     */
    public function setAccessToken($accessToken): KsClient
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param StreamInterface $body
     * @param array $context
     * @return array
     * <AUTHOR>
     */
    private function handleResponse($body, $context = [])
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        if (!isset($body['result']) || $body['result'] != 1) {
            $context['response'] = $body;
            $error_msg = $body['error_msg'] ?? '未知';
            \Log::error(get_class($this) . ' request error:' . $error_msg, $context);
//            throw new ClientException(get_class($this) . ':' . $body['error_msg'], $body['result']);
        }
        return $body;
    }

    public function getRequestData($url, array $params)
    {
        if (strpos($url, '/') === 0) {
            $url = substr($url, 1);
        }
        $method = str_replace('/', '.', $url);
        $request = [
            'appkey' => $this->appKey,
            'access_token' => $this->accessToken,
//            'timestamp' => time(),
            'version' => 1,
            'param' => json_encode($params, 320),
            'method' => $method,
            'signMethod' => 'MD5',
        ];
        $request['sign'] = $this->generateSign($request);
//	    $request['param'] = urlencode($request['param']);
        return $request;
    }


    /**
     * 处理错误码
     * @see https://open.kwaixiaodian.com/docs/dev?pageSign=5de448fecaddd4c58104e8aea442695b1614263998209
     * <AUTHOR>
     * @param $result
     * @throws ApiException
     * @throws OrderException
     */
    public static function handleErrorCode($result)
    {
        if (is_object($result)){
            $result = json_decode(json_encode($result), true);
        }
        switch ($result['result']) {
            case 1:
                break;
            case 24:
            case 28:
                throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
            default:
                Log::error('快手服务异常', $result);
                throw new ClientException('快手服务异常：' . $result['error_msg']);
        }
    }
//    /**
//     * @param int $shopId
//     */
//    public function setShopId(int $shopId): void
//    {
//        $this->shopId = $shopId;
//    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(), $params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['order.logisticsAdd'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // ks 错误码汇总 https://open.kwaixiaodian.com/docs/dev?pageSign=5de448fecaddd4c58104e8aea442695b1614263998209#section-1
                        if (isset($body['code']) && in_array($body['code'], [802000, 803000, 805000])) {
                            return true;
                        }
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    /**
     * 执行异步请求
     * @param string $apiMethod
     * @param array $apiParamsArr
     * @return array
     * <AUTHOR>
     */
    public function executeAsync(string $apiMethod, array $apiParamsArr): array
    {
        $result = [];
        $base_url = $this->getBaseUrlByApimethod($apiMethod);
        $requests = function ($apiParamsArr) use ($base_url, $apiMethod) {
            foreach ($apiParamsArr as $index => $apiParams) {
                $request_data = $this->buildRequestData($apiParams, $apiMethod);
                yield new Request('GET', $base_url . '?' . http_build_query($request_data));
            }
        };
        $httpClient = $this->getHttpClient();
        $pool = new Pool($httpClient, $requests($apiParamsArr), [
            'concurrency' => 10, //并发数
            'fulfilled' => function (Response $response, $index) use (&$result, $apiMethod, $apiParamsArr) {
                $apiParams = $apiParamsArr[$index] ?? [];
                // 请求成功
                $data = $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));
                $result[$index] = $data['data'] ?? [];
//                echo $response->getBody()->getContents();
            },
            'rejected' => function (RequestException $reason, $index) {
                // 请求失败
                throw new OrderException(class_basename($this) . ' executeAsync error:' . $reason->getMessage());
            },
        ]);
        // 初始化并创建promise
        $promise = $pool->promise();
        // 等待所有进程完成
        $promise->wait();
        return $result;
    }

    /**
     * @param string $apiMethod
     * @return string
     * <AUTHOR>
     */
    public function getBaseUrlByApimethod(string $apiMethod): string
    {
        return $this->gatewayUrl . '/' . $apiMethod;
    }

    /**
     * @param array $apiParams
     * @param string $apiMethod
     * @return array
     * <AUTHOR>
     */
    public function buildRequestData($apiParams, string $apiMethod): array
    {
        $method = str_replace('/', '.', $apiMethod);

        // 构造请求url
        $request_data = [
            'appkey' => $this->appKey,
            'access_token' => $this->accessToken,
//            'timestamp' => time(),
            'version' => 1,
            'param' => json_encode($apiParams, 320),
            'method' => $method,
            'signMethod' => 'MD5',
        ];
        $request_data['sign'] = $this->generateSign($request_data);
        return $request_data;
    }

}
