<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 22:41
 */

namespace App\Services\Client;


use App\Exceptions\ClientException;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Query;
use Psr\Http\Message\StreamInterface;

/**
 * @see https://open.pinduoduo.com/#/document?url=https%253A%252F%252Fmai.pinduoduo.com%252Fautopage%252F84_static_9%252Findex.html
 * Class PddClient
 * @package App\Services\Client
 */
class PddClient
{
    public $appkey;

    public $secretKey;

    public $gatewayUrl = "https://gw-api.pinduoduo.com/api/router";

    public $dataType = "JSON";

    protected $apiVersion = "V1";

    private $accessToken;

    public function __construct($appKey, $secretKey)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名
     * @param array $apiParams 接口参数
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams)
    {
        $sysParams = [];
        $sysParams['client_id'] = $this->appkey;
        if ($accessToken = $this->getAccessToken()) { // 无需授权的接口，该字段不参与sign签名运算
            $sysParams['access_token'] = $accessToken;
        }
        $sysParams['data_type'] = $this->dataType;
        $sysParams['version'] = $this->apiVersion;
        $sysParams['timestamp'] = time();
        $sysParams['type'] = $apiMethod;
        $sysParams['sign'] = $this->generateSign($this->secretKey, array_merge($sysParams, $apiParams));

        $url = $this->gatewayUrl . '?' . Query::build(array_merge($sysParams, $apiParams));
        $httpClient = new Client([
//            'http_errors' => false,
        ]);
        $response = $httpClient->post($url, [
//            'json' => $apiParams,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        return $this->handleResponse($response->getBody());
    }


    /**
     * 生成签名
     * @param $appSecret
     * @param $params
     * @return string
     */
    private function generateSign($appSecret, $params)
    {
        ksort($params);
        $sign = $appSecret;
        foreach ($params as $k => $v) {
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $appSecret;
        return strtoupper(md5($sign));
    }

    public function getAccessToken()
    {
        return $this->accessToken;
    }

    /**
     * @param mixed $accessToken
     * @return PddClient
     */
    public function setAccessToken($accessToken): PddClient
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param StreamInterface $body
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    private function handleResponse($body)
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        if (!empty($body['error_response'])) {
            \Log::error(get_class($this) . ' request error:', $body);
            throw new ClientException(get_class($this) . ':'.$body['error_response']['error_msg']);
        }
        return $body;
    }


}
