<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 22:41
 */

namespace App\Services\Client;


use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use function GuzzleHttp\Psr7\build_query;

/**
 * @see https://open.pinduoduo.com/#/document?url=https%253A%252F%252Fmai.pinduoduo.com%252Fautopage%252F84_static_9%252Findex.html
 * Class JdClient
 * @package App\Services\Client
 */
class JdClient
{
    public $appkey;

    public $secretKey;

    public $gatewayUrl  = "https://api.jd.com/routerjson";

    public $dataType = "json";

    protected $apiVersion = "2.0";

    private $accessToken;
    private $jsonParamKey = '360buy_param_json';

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;


    public function __construct($appKey, $secretKey)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->gatewayUrl== config('socialite.jd.gateway_url',"https://api.jd.com/routerjson");
    }

    /**
     * 生成JD client
     * @param string $accessToken
     * @param string|null $appKey
     * @param string|null $secretKey
     * @return self
     */
    public static  function newInstance(string $accessToken,?string $appKey=null,?string $secretKey=null):JdClient{
        $jdClient = new self($appKey ?? config('socialite.jd.client_id'), $secretKey ?? config('socialite.jd.client_secret'));
        $jdClient->setAccessToken($accessToken);
        return $jdClient;
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient()
    {
        // 创建 Handler
        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));

        return new Client([
//            'http_errors' => false,
            'handler' => $handlerStack //重试策略
        ]);
    }

    /**
     * 执行请求
     * @param string $apiMethod 接口名
     * @param array $apiParams 接口参数
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    public function execute(string $apiMethod, array $apiParams)
    {
        $sysParams = [];
        $sysParams['app_key'] = $this->appkey;
        if ($accessToken = $this->getAccessToken()) { // 无需授权的接口，该字段不参与sign签名运算
            $sysParams['access_token'] = $accessToken;
        }
        $sysParams['format'] = $this->dataType;
        $sysParams['v'] = $this->apiVersion;
        $sysParams['timestamp'] = date('Y-m-d H:i:s');
        $sysParams['method'] = $apiMethod;
        $allParams = array_merge($sysParams, $apiParams);
        $allParams['sign'] = $this->generateSign($this->secretKey, $allParams);

        $url = $this->gatewayUrl . '?' . http_build_query($allParams);
        $httpClient = new Client([
//            'http_errors' => false,
        ]);
        $response = $httpClient->post($url, [
//            'json' => $apiParams,
            'headers' => [
                'Content-type' => 'application/json',
                "Accept" => "application/json"
            ],
        ]);
        return $this->handleResponse($response->getBody(), $allParams);
    }

    /**
     * 执行请求
     * <AUTHOR>
     */
    public function executeByCustom($requestUrl, array $apiParams)
    {
        $httpClient = new Client([
//            'http_errors' => false,
        ]);
        $response = $httpClient->post($requestUrl, [
            'json' => $apiParams,
        ]);
        return json_decode($response->getBody(), true);
    }


    /**
     * 生成签名
     * @param $appSecret
     * @param $params
     * @return string
     */
    private function generateSign($appSecret, $params)
    {
        ksort($params);
        $sign = $appSecret;
        foreach ($params as $k => $v) {
//            Log::info('k: ' . $k, ['v' => $v]);
            if ("@" != substr($v, 0, 1)) {
                $sign .= "$k$v";
            }
        }
        unset($k, $v);
        $sign .= $appSecret;
        return strtoupper(md5($sign));
    }

    public function getAccessToken()
    {
        return $this->accessToken;
    }

    /**
     * @param mixed $accessToken
     * @return PddClient
     */
    public function setAccessToken($accessToken): JdClient
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    /**
     * @param StreamInterface $body
     * @return mixed|StreamInterface
     * @throws ClientException
     * <AUTHOR>
     */
    private function handleResponse($body,$params=[])
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        if (!empty($body['error_response'])) {
            \Log::error(get_class($this) . ' request error:', [$body, $params]);
            $error_msg = $body['error_response']['error_msg'] ?? ($body['error_response']['zh_desc'] ?? '');
            throw new ClientException(get_class($this) . ':'. $error_msg);
        }
        return $body;
    }

    public static  function handleErrorCode($result)
    {
//        switch ($result['jingdong_pop_order_shipment_responce']['sopjosshipment_result']['errorCode']) {
//            //{"jingdong_pop_order_shipment_responce":{"code":"0","sopjosshipment_result":{"englishErrCode":"orders have been out of library","errorCode":"10400001","success":false,"chineseErrCode":"218487032232订单已出库"}}}
//            case '50010': // 这个错误码非常奇怪，举例了部分情况如下：
//                throw new ApiException(ErrorConst::PLATFORM_SERVER_ERROR);
//            default:
//                throw new OrderException('京东服务异常：' . json_encode($result, JSON_UNESCAPED_UNICODE));
//        }
    }

    /**
     * 只能处理通用的错误，京东的大部分错误在内部
     * @param $response
     * @param null $request
     * @return array
     * @throws ApiException
     * @throws OrderException
     * <AUTHOR>
     */
    public function handleResp($response, $request = null): array
    {
        $resp = json_decode(json_encode($response), true);
        $apiParas = [];
        if (!empty($request)) {
            $apiParas = $request->getApiParas();
            $apiParas = json_decode($apiParas,true);
        }
//        Log::info('JdClient',['request' => $apiParas,'response'=>$resp]);
        /**
         *
         * {"error_response":{"code":"95","zh_desc":"该接口不xxx"}}
         */
        if (!empty($resp['error_response'])) {
            if (!empty($resp['error_response']['code'])) {
                switch ($resp['error_response']['code']) {
                    case '19':
                        throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                }
            }
            $msg = $resp['error_response']['zh_desc'] ?? '';
            throw new OrderException("京东服务错误:{$msg};", ErrorConst::PLATFORM_SERVER_ERROR[0]);
        }
//        Log::info("调用JD接口返回",$resp);
        $respArr = array_first($resp);
        /**
         *
         * {"jingdong_isv_uploadBatchLog_responce":{"code":"0","c":4}}
         *
         * {"jingdong_order_venderRemark_queryByOrderId_responce":{"code":"0","venderRemarkQueryResult":{"api_jos_result":{"result_code":"10400022","result_describe":"没有查询到此订单的备注信息","success":false}}}}
         * {"jingdong_pop_order_shipment_responce":{"code":"0","sopjosshipment_result":{"englishErrCode":"logistic company dose not belong to the business","success":false,"errorCode":"10300002","chineseErrCode":"物流公司不属于当前商家"}}}
         * {"jingdong_pop_order_shipment_responce":{"code":"0","sopjosshipment_result":{"englishErrCode":"waybill number incompatible with logistics company","success":false,"errorCode":"10200005","chineseErrCode":"运单号不符合对应物流公司的校验规则"}}}
         */
        if (!empty($respArr['code'])) {
            $msg = $resp['errorMessage'];
            throw new OrderException("京东服务错误:{$msg};".json_encode($resp), ErrorConst::PLATFORM_SERVER_ERROR[0]);
        }

        return $resp;
    }


    /**
     * 返回的是JD SDK里面的Client
     * @param $accessToken
     * @param $appKey
     * @param $secretKey
     * @return \JdClient
     */
    public static function newSdkClient($accessToken,$appKey=null,$secretKey=null){
        $client = new \JdClient();
        $client->appKey =$appKey??config('socialite.jd.client_id');
        $client->appSecret =$secretKey??config('socialite.jd.client_secret');
        $client->accessToken =$accessToken;

        return $client;
    }
    /**
     * <AUTHOR>
     * @param $request
     * @return array
     */
    public function getApiParamsAndUrl($request): array
    {
        $sysParams["app_key"] = $this->appkey;
        $version = $request->getVersion();
        $sysParams["v"] = empty($version) ? $this->apiVersion : $version;
        $sysParams["method"] = $request->getApiMethodName();
        $sysParams["timestamp"] = date('Y-m-d H:i:s');
        if ($accessToken = $this->getAccessToken()) { // 无需授权的接口，该字段不参与sign签名运算
            $sysParams['access_token'] = $accessToken;
        }

        $apiParams = $request->getApiParas();
        $sysParams[$this->jsonParamKey] = $apiParams;

        $sysParams["sign"] = $this->generateSign($this->secretKey, $sysParams);
        $requestUrl = $this->gatewayUrl . "?";
        foreach ($sysParams as $sysParamKey => $sysParamValue)
        {
            $requestUrl .= "$sysParamKey=" . urlencode($sysParamValue) . "&";
        }
        return [$requestUrl, $apiParams];
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(),$params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['jingdong.pop.order.shipment'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $contents = str_replace("\n", "" ,$response->getBody()->getContents());
                        $body = !is_array($contents) ? json_decode($contents, true) : $contents;
                        // todo 收集jd错误码进行重试
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    /**
     * <AUTHOR>
     * @param string $apiMethod
     * @param array $apiParams
     * @return string
     */
    protected function getUrlAndParams(string $apiMethod, array $apiParams): string
    {
        $sysParams = [];
        $sysParams['app_key'] = $this->appkey;
        if ($accessToken = $this->getAccessToken()) { // 无需授权的接口，该字段不参与sign签名运算
            $sysParams['access_token'] = $accessToken;
        }
        $sysParams['format'] = $this->dataType;
        $sysParams['v'] = $this->apiVersion;
        $sysParams['timestamp'] = date('Y-m-d H:i:s');
        $sysParams['method'] = $apiMethod;
        $sysParams['sign'] = $this->generateSign($this->secretKey, array_merge($sysParams, $apiParams));

        $url = $this->gatewayUrl . '?' . http_build_query(array_merge($sysParams, $apiParams));
        return $url;
    }
}
