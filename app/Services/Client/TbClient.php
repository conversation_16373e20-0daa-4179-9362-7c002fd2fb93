<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/5/16
 * Time: 15:24
 */

namespace App\Services\Client;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;
use TopClient\ResultSet;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Handler\CurlHandler;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;

class TbClient
{
    public $appkey;

    public $secretKey;

    public $gatewayUrl = "http://gw.api.taobao.com/router/rest";
//    public $gatewayUrl = "http://httpbin.org/post";

    public $format = "json";

    public $connectTimeout;

    public $readTimeout;

    /** 是否打开入参check**/
    public $checkRequest = true;

    protected $signMethod = "md5";

    protected $apiVersion = "2.0";

    protected $sdkVersion = "top-sdk-php-20180326";
    /**
     * @var mixed
     */
    private $accessToken;

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;

    public function __construct($appKey, $secretKey, $accessToken = null)
    {
        $this->appkey = $appKey;
        $this->secretKey = $secretKey;
        $this->accessToken = $accessToken;
    }

    /**
     * @param $accessToken
     * @return TbClient
     */
    public static function newInstance($accessToken = null)
    {
        return new TbClient(config('socialite.taobao_top.client_id'), config('socialite.taobao_top.client_secret'), $accessToken);
    }

    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient()
    {
        // 创建 Handler
        $handlerStack = HandlerStack::create(new CurlHandler());
        // 创建重试中间件，指定决策者为 $this->retryDecider(),指定重试延迟为 $this->retryDelay()
        $handlerStack->push(Middleware::retry($this->retryDecider(), $this->retryDelay()));

        return new Client([
//            'http_errors' => false,
            'handler' => $handlerStack //重试策略
        ]);
    }


    /**
     * 生成签名
     * @param $params
     * @return string
     */
    protected function generateSign($params)
    {
        ksort($params);

        $stringToBeSigned = $this->secretKey;
        foreach ($params as $k => $v)
        {
            if(is_string($v) && "@" != substr($v, 0, 1))
            {
                $stringToBeSigned .= "$k$v";
            }
        }
        unset($k, $v);
        $stringToBeSigned .= $this->secretKey;

        return strtoupper(md5($stringToBeSigned));
    }

    public function getAccessToken()
    {
        return $this->accessToken;
    }

    /**
     * @param mixed $accessToken
     * @return $this
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    private function getClusterTag()
    {
        return substr($this->sdkVersion,0,11)."-cluster".substr($this->sdkVersion,11);
    }


    /**
     * <AUTHOR>
     * @param $request
     * @param $session
     * @param $bestUrl
     * @return array
     */
    public function getApiParamsAndUrl($request, $bestUrl = null): array
    {
        //组装系统参数
        $sysParams["app_key"] = $this->appkey;
        $sysParams["v"] = $this->apiVersion;
        $sysParams["format"] = $this->format;
        $sysParams["sign_method"] = $this->signMethod;
        $sysParams["method"] = $request->getApiMethodName();
        $sysParams["timestamp"] = date("Y-m-d H:i:s");
        $sysParams["session"] = $this->getAccessToken();
        $apiParams = array();
        //获取业务参数
        $apiParams = $request->getApiParas();

        //系统参数放入GET请求串
        if ($bestUrl) {
            $requestUrl = $bestUrl . "?";
            $sysParams["partner_id"] = $this->getClusterTag();
        } else {
            $requestUrl = $this->gatewayUrl . "?";
            $sysParams["partner_id"] = $this->sdkVersion;
        }
        //签名
        $sysParams["sign"] = $this->generateSign(array_merge($apiParams, $sysParams));

        foreach ($sysParams as $sysParamKey => $sysParamValue) {
            // if(strcmp($sysParamKey,"timestamp") != 0)
            $requestUrl .= "$sysParamKey=" . urlencode($sysParamValue) . "&";
        }

        $fileFields = array();
        foreach ($apiParams as $key => $value) {
            if (is_array($value) && array_key_exists('type', $value) && array_key_exists('content', $value)) {
                $value['name'] = $key;
                $fileFields[$key] = $value;
                unset($apiParams[$key]);
            }
        }
        // $requestUrl .= "timestamp=" . urlencode($sysParams["timestamp"]) . "&";
        $requestUrl = substr($requestUrl, 0, -1);
        return array($requestUrl, $apiParams);
    }

    /**
     * 执行请求
     * <AUTHOR>
     */
    public function executeByCustom($requestUrl, array $apiParams)
    {
        $httpClient = new Client([
//            'http_errors' => false,
        ]);
        $response = $httpClient->post($requestUrl, [
            'form_params' => $apiParams,
        ]);
        return json_decode($response->getBody(), true);
    }

    /**
     * retryDecider
     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
     * @return \Closure
     */
    protected function retryDecider()
    {
        return function (
            $retries,
            Request $request,
            Response $response = null,
            RequestException $exception = null
        ) {
            // 超过最大重试次数，不再重试
            if ($retries >= self::MAX_RETRIES) {
                return false;
            }

            if ($response) {
                parse_str($request->getUri()->getQuery(),$params);
                if (!empty($params)) {
                    $method = $params['method'] ?? "";
                    //发货失败请求重试
                    if (in_array($method, ['taobao.logistics.offline.send'])) {
                        // 请求失败，继续重试
                        if ($exception instanceof ConnectException) {
                            return true;
                        }
                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
                        // todo 收集tb错误码进行重试
                    }
                }
            }

            return false;
        };
    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }

    /**
     * 处理响应
     *
     * @param $response
     * @return array
     * @throws ApiException
     * @throws OrderException
     * <AUTHOR>
     */
    public function handleResp($response)
    {
        if (empty($response)) {
            throw new ApiException(ErrorConst::PLATFORM_RETURN_EMPTY);
        }
        $resp = objectToArray($response);
        if (isset($resp['error_response'])) {
            $resp = $resp['error_response'];
        }
        /**
         * 异常示例
         * "code" => 27
         * "msg" => "Invalid session"
         * "sub_code" => "invalid-sessionkey"
         * "sub_msg" => "SessionKey非法"
         * "request_id" => "3qvdigpsl5on"
         */
//        if (!isset($resp['code'])) {
//            throw new OrderException("淘宝服务错误");
//        }
        if (!empty($resp['code'])) {
            \Log::error('TaobaoOrder handleResp', [$resp]);
            $msg = isset($resp['sub_msg']) ? $resp['sub_msg'] : $resp['msg'];
            switch ($resp['code']){
                case 27:
                    throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                    break;
                default:
                    throw new OrderException("淘宝服务错误：{$msg}；", ErrorConst::PLATFORM_SERVER_ERROR[0]);
                    break;
            }
        }

        return $resp;
    }

}
