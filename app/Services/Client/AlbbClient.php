<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/5/16
 * Time: 15:24
 */

namespace App\Services\Client;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\OrderException;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\StreamInterface;

/**
 * 阿里巴巴的客户端
 */
class AlbbClient
{
    public $appKey;

    public $secretKey;

    public $gatewayUrl = "http://gw.open.1688.com/openapi/";

    public $format = "json";

    public $connectTimeout;

    public $readTimeout;

    /** 是否打开入参check**/
    public $checkRequest = true;

    protected $signMethod = "md5";

    protected $apiVersion = "2.0";

    protected $sdkVersion = "top-sdk-php-20180326";
    /**
     * @var mixed
     */
    private $accessToken;

    /**
     * 最大重试次数
     */
    const MAX_RETRIES = 3;
    /**
     * 默认超时时间 秒
     * @var int
     */
    private $clientTimeout = 8;
    private $clientConnectTimeout = 5; // 客户端连接超时


    public static function newInstance($accessToken = null)
    {
        return new AlbbClient(config('socialite.albb.client_id'), config('socialite.albb.client_secret'), $accessToken);
    }


    public function __construct($appKey, $secretKey, $accessToken = null)
    {
        $this->appKey = $appKey;
        $this->secretKey = $secretKey;
        $this->accessToken = $accessToken;
    }


    /**
     * 处理响应
     *
     * @param $response
     * @return array
     * @throws ApiException
     * @throws OrderException
     * <AUTHOR>
     */
    public static function handleResp($response)
    {

        $resp = objectToArray($response);
        if (empty($response)||empty($resp)) {
            throw new ApiException(ErrorConst::PLATFORM_RESPONSE_EMPTY);
        }
        /**
         * 异常示例
         * "code" => 27
         * "msg" => "Invalid session"
         * "sub_code" => "invalid-sessionkey"
         * "sub_msg" => "SessionKey非法"
         * "request_id" => "3qvdigpsl5on"
         */
        if (!empty($resp['code'])) {
            switch ($resp['code']) {
                case 27:
                    throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                default:
                    Log::error(class_basename(self::class).' handleResp', [$resp]);
                    $msg = $resp['msg'] ?? '';
                    throw new OrderException("阿里巴巴服务错误:{$msg};" . json_encode($resp));
            }
        }
        if (isset($resp['errorCode'])) {
            $errMsg = ($resp['errorMessage'] ?? '') ."[{$resp['errorCode']}]";
            Log::error(class_basename(self::class).' handleResp', [$resp]);
            throw new OrderException("阿里巴巴服务错误:{$errMsg};");
        }
        if (isset($resp['error_code'])) {
            $errMsg = ($resp['error_message'] ?? '') ."[{$resp['error_code']}]";
            Log::error(class_basename(self::class).' handleResp', [$resp]);
            throw new OrderException("阿里巴巴服务错误{:$errMsg};");
        }

        return $resp;
    }
    /**
     * @param StreamInterface $body
     * @param $request
     * @return array
     * <AUTHOR>
     */
    private function handleResponse($body, $request = [])
    {
        if (!is_array($body)) {
            $body = json_decode($body, true);
        }
        $code = $body['code'] ?? 0;
        if (empty($code)) {
            return $body;
        }
        return $body;
    }


    /**
     * 批量请求的时候获取请求参数
     * 请求参数需要在业务参数上加上签名和access_token
     * @param array $apiParams
     * @param $apiMethod
     * @return array
     */

    public function buildRequestData(array $apiParams, $apiMethod): array
    {
        $requestUrl = $this->getApiRequestUrl($apiMethod);
        $apiParams['access_token'] = $this->getAccessToken();

        // 构造请求url
        $sign = $this->generateSign($requestUrl, $apiParams);
        return array_merge($apiParams, [
            '_aop_signature' => $sign,
        ]);
    }

    /**
     * URL的构成 http://gw.open.1688.com:80/openapi/param2/{version}/{apiMethod}/{appKey}
     * http://gw.open.1688.com:80/openapi 是固定的网关路径
     * version默认为1
     * apiMethod为具体的api方法,输入的格式com.alibaba.trade:alibaba.trade.ec.getOrder 需要转换为com.alibaba.trade/alibaba.trade.ec.getOrder
     * appKey为appKey
     * @param $apiMethod
     * @param $apiParams
     * @param $method
     * @param $version
     * @return mixed
     * @throws ApiException
     * @throws GuzzleException
     */
    public function execute($apiMethod, $apiParams, $method = 'POST_FORM', $version = "1"): array
    {
        $apiParams['access_token'] = $this->getAccessToken();
        $requestUrl = $this->getApiRequestUrl($apiMethod, $version);
        $fullUrl = $this->getApiFullUrl($apiMethod, $version);
        $sign = $this->generateSign($requestUrl, $apiParams);
        $apiParams['_aop_signature'] = $sign;
        $method = strtolower($method);
        try {
            if ($method == 'get') {
                $response = $this->getHttpClient()->get($fullUrl, [
                    'query' => $apiParams,
                ]);
                return json_decode($response->getBody(), true);
            }
            elseif ($method == 'post_form') {
                $response = $this->getHttpClient()->post($fullUrl, [
                    'form_params' => $apiParams,
                ]);
            }
            elseif ($method == 'post') {
                $response = $this->getHttpClient()->post($fullUrl, [
                    'json' => $apiParams,
                ]);
            }else{
//           ApiException::throwException("不支持的请求方式",ErrorConstants::ILLEGAL_PARAMETER[0]);
                throw new ApiException(ErrorConst::PLATFORM_METHOD_NOT_SUPPORT);
            }
        }catch (\Exception $exception){
            Log::error("请求失败", [$fullUrl, $apiParams, $method]);
            throw $exception;
        }
//        Log::info("请求结果", [$response->getBody()]);
        return $this->handleResponse($response->getBody(), compact('apiMethod', 'apiParams'));

    }


    /**
     * @return Client
     * <AUTHOR>
     */
    public function getHttpClient(): Client
    {
        return new Client([
//            'http_errors' => false,
            'timeout' => $this->clientTimeout, //秒
            'connect_timeout' => $this->clientConnectTimeout, //秒
//            'handler' => $handlerStack //重试策略
        ]);
    }


    /**
     * 生成签名
     * @see https://open.1688.com/api/apidoclist.htm?spm=a260s.26059351.0.0.797855edIlNoBl&id=624397
     * @param $requestUrl
     * @param $params
     * @return string
     */
    protected function generateSign($requestUrl, $params): string
    {
        $aliParams = array();
        foreach ($params as $key => $val) {
            $aliParams[] = $key . $val;
        }
        sort($aliParams);
        $sign_str = join('', $aliParams);
        $sign_str = $requestUrl . $sign_str;
        $singResult = strtoupper(hash_hmac("sha1", $sign_str, $this->secretKey, false));
//        Log::info("签名字符串", [$sign_str, $singResult]);
//        dd(urldecode(http_build_query($params)),$sign_str,$aliParams,$singResult);

        return $singResult;
    }

    public function getAccessToken()
    {
        return $this->accessToken;
    }

    /**
     * @param mixed $accessToken
     * @return $this
     */
    public function setAccessToken($accessToken)
    {
        $this->accessToken = $accessToken;
        return $this;
    }

    private function getClusterTag()
    {
        return substr($this->sdkVersion, 0, 11) . "-cluster" . substr($this->sdkVersion, 11);
    }


    /**
     * @param $request
     * @return array
     * <AUTHOR>
     */
    public function getApiParamsAndUrl($request): array
    {
        $bestUrl = null;
        //组装系统参数
        $sysParams["app_key"] = $this->appKey;
        $sysParams["v"] = $this->apiVersion;
        $sysParams["format"] = $this->format;
        $sysParams["sign_method"] = $this->signMethod;
        $sysParams["method"] = $request->getApiMethodName();
        $sysParams["timestamp"] = date("Y-m-d H:i:s");
        $sysParams["session"] = $this->getAccessToken();
        $apiParams = array();
        //获取业务参数
        $apiParams = $request->getApiParas();

        //系统参数放入GET请求串
        if ($bestUrl) {
            $requestUrl = $bestUrl . "?";
            $sysParams["partner_id"] = $this->getClusterTag();
        } else {
            $requestUrl = $this->gatewayUrl . "?";
            $sysParams["partner_id"] = $this->sdkVersion;
        }
        //签名
        $sysParams["sign"] = $this->generateSign(array_merge($apiParams, $sysParams));

        foreach ($sysParams as $sysParamKey => $sysParamValue) {
            // if(strcmp($sysParamKey,"timestamp") != 0)
            $requestUrl .= "$sysParamKey=" . urlencode($sysParamValue) . "&";
        }

        $fileFields = array();
        foreach ($apiParams as $key => $value) {
            if (is_array($value) && array_key_exists('type', $value) && array_key_exists('content', $value)) {
                $value['name'] = $key;
                $fileFields[$key] = $value;
                unset($apiParams[$key]);
            }
        }
        // $requestUrl .= "timestamp=" . urlencode($sysParams["timestamp"]) . "&";
        $requestUrl = substr($requestUrl, 0, -1);
        return array($requestUrl, $apiParams);
    }

//    /**
//     * retryDecider
//     * 返回一个匿名函数, 匿名函数若返回false 表示不重试，反之则表示继续重试
//     * @return \Closure
//     */
//    protected function retryDecider()
//    {
//        return function (
//            $retries,
//            Request $request,
//            Response $response = null,
//            RequestException $exception = null
//        ) {
//            // 超过最大重试次数，不再重试
//            if ($retries >= self::MAX_RETRIES) {
//                return false;
//            }
//
//            if ($response) {
//                parse_str($request->getUri()->getQuery(), $params);
//                if (!empty($params)) {
//                    $method = $params['method'] ?? "";
//                    //发货失败请求重试
//                    if (in_array($method, ['taobao.logistics.offline.send'])) {
//                        // 请求失败，继续重试
//                        if ($exception instanceof ConnectException) {
//                            return true;
//                        }
//                        $body = !is_array($response->getBody()) ? json_decode($response->getBody(), true) : $response->getBody();
//                        // todo 收集tb错误码进行重试
//                    }
//                }
//            }
//
//            return false;
//        };
//    }

    /**
     * 返回一个匿名函数，该匿名函数返回下次重试的时间（毫秒）
     * @return \Closure
     */
    protected function retryDelay()
    {
        return function ($numberOfRetries) {
            return 1000 * $numberOfRetries;
        };
    }




    public function executeRequest($request, $accessToken)
    {
        // TODO: Implement executeRequest() method.
    }

    /**
     * 把请求的完整URL拼出来
     * @param $apiMethod
     * @param $version
     * @param array $apiParams
     * @return string
     */
    public function getApiFullUrl($apiMethod = '', $version = "1", array $apiParams = []): string
    {
        $apiMethodUrl = str_replace(':', '/', $apiMethod);

        $requestUrl = $this->getApiRequestUrl($apiMethodUrl, $version);

        return $this->gatewayUrl . $requestUrl;

    }

    /**
     * 获取请求路径URL
     * fullUrl=gatewayUrl + requestUrl
     * @param $apiMethod
     * @param $version
     * @return string
     */
    public function getApiRequestUrl($apiMethod = '', $version = "1"): string
    {
        $apiMethodUrl = str_replace(':', '/', $apiMethod);
        return "param2/" . $version . "/" . $apiMethodUrl . "/" . $this->appKey;
    }
}
