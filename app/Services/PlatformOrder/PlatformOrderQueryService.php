<?php

namespace App\Services\PlatformOrder;

use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Services\Common\QueryBuilder;
use Illuminate\Support\Facades\Log;

class PlatformOrderQueryService
{

    /**
     * @var QueryBuilder $shopQueryBuilder
     */
    private $shopQueryBuilder;

    /**
     * @var QueryBuilder $platformOrderQueryBuilder
     */
    private $platformOrderQueryBuilder;

    /**
     * @var QueryBuilder $apiShopBindQueryBuilder
     */
    private $apiShopBindQueryBuilder;

    /**
     */
    public function __construct()
    {
        $this->shopQueryBuilder =QueryBuilder::newInstance(Shop::class);
        $this->platformOrderQueryBuilder=QueryBuilder::newInstance(PlatformOrder::class);
        $this->apiShopBindQueryBuilder = QueryBuilder::newInstance(ApiShopBind::class);
    }

    public function pageQuery($shopSearch, $platformSearch, $apiShopBindSearch, $needTotal): array
    {
        Log::info("平台订单搜索",["shopSearch"=>$shopSearch,"platformSearch"=>$platformSearch,"apiShopBindSearch"=>$apiShopBindSearch]);
        $shopQuery=null;
        if(!empty($shopSearch)){
            $shopQuery=$this->shopQueryBuilder->build($shopSearch);
            Log::info($shopQuery->toSql());
        }
        $platformOrderQuery=$this->platformOrderQueryBuilder->build($platformSearch);
        if(isset($shopQuery)) {
            $platformOrderQuery = $platformOrderQuery->whereIn("shop_id", $shopQuery->select(['id']));
        }
        if(sizeof($apiShopBindSearch)>0){
            $platformOrderQuery=$platformOrderQuery->whereIn("shop_id", $this->apiShopBindQueryBuilder->build($apiShopBindSearch)->select(['shop_id']));
        }
        Log::info("平台订单查询:".getSqlByQuery($platformOrderQuery));
        $total=null;
        if($needTotal){
            $total=$platformOrderQuery->count();
        }
        $page = $platformSearch['page'];
        $offset= $page['offset'];
        $limit=$page['limit'];
        $pagination = [
            'rows_found' => $total,
            'offset' => $offset,
            'limit' => $limit,
        ];
        $this->platformOrderQueryBuilder->sort($platformOrderQuery,$platformSearch['sort']);
        $arr = $platformOrderQuery->with(['shop:shop_name,id','bindApiAuth:name,api_auth.app_id'])->select()->offset($offset)->limit($limit)->get();
        return ['pagination' => $pagination, $arr];


    }

}
