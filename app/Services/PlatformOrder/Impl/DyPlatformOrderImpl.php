<?php

namespace App\Services\PlatformOrder\Impl;

use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\User;
use App\Models\UserExtra;
use App\Services\PlatformOrder\AbstractPlatformOrderService;

class DyPlatformOrderImpl extends AbstractPlatformOrderService
{
	protected $platformType = Shop::PLATFORM_TYPE_DY;

	protected $orderStatusMap
		= [
			0 => PlatformOrder::STATUS_UNKNOWN,
			1 => PlatformOrder::STATUS_PADDING,
			4 => PlatformOrder::STATUS_CLOSE,
			5 => PlatformOrder::STATUS_SUCCESS,
		];

	protected $versionMap
		= [
			UserExtra::VERSION_FREE         => '试用版',
			UserExtra::VERSION_STANDARD     => '标准版',
			UserExtra::VERSION_PROFESSIONAL => '专业版',
			UserExtra::VERSION_SENIOR       => '高级版',
		];

	public function formatToOrder(array $order)
	: array
	{
		$info       = (array)$order['order_info'];
		$identifier = $info['shop_id'];
		$skuInfo    = (array)$info['push_sku_info'];
		$shop       = Shop::firstByIdentifier($identifier);

        if(empty($shop)){
			$authSaveData = [
                'auth_status' => Shop::AUTH_STATUS_SUCCESS,
				'sync_switch' => Shop::SYNC_SWITCH_OPEN,
				'type'        => Shop::PLATFORM_TYPE_DY,
			];
			// 如果不存在授权, 新建
			$user = User::query()->create([
				'phone' => '',
				'nickname' => '',
				'password' => '',
				'invite_code' => getRandStr(16),
			]);

			$authSaveData['user_id'] = $user->id;
			$authSaveData['original_user_id'] = 0;
			$authSaveData['auth_user_id'] = 0;
			$authSaveData['identifier'] = $identifier;
			$shop = Shop::query()->create($authSaveData);
		}

		$status = PlatformOrder::STATUS_UNKNOWN;
		if (isset($this->orderStatusMap[$info['status']])) {
			$status = $this->orderStatusMap[$info['status']];
		}

		$format = [
			'user_id'          => $shop->user_id,
			'shop_id'          => $shop->id,
			'platform_type'    => $this->platformType,
			'identifier'       => $identifier,
			'order_id'         => $info['order_id'],
			'order_no'         => $info['order_id'],
			'status'           => (int)$status,
			//			'service_id'       => '',
			//			'service_name'     => '',
			//			'fee'              => '',
			//			'prom_fee'         => '',
			//			'refund_fee'       => '',
			'pay_fee'          => $info['pay_amount'],
			'pay_at'           => date('Y-m-d H:i:s', $info['pay_time']),
			'pay_type'         => $info['pay_type'],
			'order_created_at' => date('Y-m-d H:i:s', $info['order_create_time']),
			//			'order_cycle'      => '',
			'cycle_start_at'   => date('Y-m-d H:i:s', $info['service_start_time']),
			'cycle_end_at'     => date('Y-m-d H:i:s', $info['service_end_time']),
			'source'           => '',
			'sku_id'           => '',
			'sku_title'        => $skuInfo['title'] ?? '',
			'sku_spec'         => $skuInfo['spec_value'] ?? '',
			'duration'         => $skuInfo['duration'] ?? '',
			'duration_unit'    => $skuInfo['duration_unit'] ?? '',
		];
        // 双11 活动，标准版升级高级版
        if (strtotime($format['pay_at']) > strtotime('2022-11-01')
            && strtotime($format['pay_at']) < strtotime('2022-12-01')
            && $format['sku_spec'] == UserExtra::VERSION_STANDARD_NAME) {
            $format['sku_spec'] = UserExtra::VERSION_SENIOR_NAME;
        }

		return $format;
	}

	public function getVersionDesc()
	: array
	{
		$shop  = $this->getShop();
		$order = PlatformOrder::calcuMaxLevel($shop);

		return array_merge(collect($order)->toArray(), [
			'version' => array_search($order['sku_spec'], $this->versionMap)
		]);
	}
}
