<?php

namespace App\Services\PlatformOrder\Impl;

use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\PlatformOrder\AbstractPlatformOrderService;

class TaobaoPlatformOrderImpl extends AbstractPlatformOrderService
{
    protected $platformType = Shop::PLATFORM_TYPE_TAOBAO;

	protected $versionMap
		= [
			'FW_GOODS-1000043442-1' => UserExtra::VERSION_STANDARD,
			'FW_GOODS-1838184-1'    => UserExtra::VERSION_STANDARD,
			'FW_GOODS-1838184-v2'   => UserExtra::VERSION_PROFESSIONAL,
			'FW_GOODS-1838184-v3'   => UserExtra::VERSION_PROFESSIONAL,
			'FW_GOODS-1838184-v4'   => UserExtra::VERSION_SENIOR,
			'FW_GOODS-1838184-v5'   => UserExtra::VERSION_SENIOR,
            // 淘订单
			'service-0-22985-1'   => UserExtra::VERSION_STANDARD,
            // 旋风打印
            'ts-26066-2'          => UserExtra::VERSION_STANDARD,
            'ts-26066-4'          => UserExtra::VERSION_STANDARD,
		];

	public function formatToOrder(array $order): array
	{
		$nick = $order['nick'];
		$shop       = Shop::query()->where('name', $nick)->first();
        if (empty($shop)) {
            return [];
        }
		$status = PlatformOrder::STATUS_SUCCESS;

		$format = [
			'user_id'          => $shop->user_id,
			'shop_id'          => $shop->id,
			'platform_type'    => $this->platformType,
			'identifier'       => $shop->identifier,
			'order_id'         => $order['order_id'],
			'order_no'         => $order['order_id'],
			'status'           => (int)$status,
			'service_id'       => $order['article_code'] ?? null,
			'service_name'     => $order['article_name'] ?? null,
			'fee'              => $order['fee'] ?? 0,
			'prom_fee'         => $order['prom_fee'] ?? 0,
			'refund_fee'       => $order['refund_fee'] ?? 0,
			'pay_fee'          => $order['total_pay_fee'],
			'pay_at'           => $order['order_cycle_start'],
			'pay_type'         => $order['pay_type'] ?? 0,
			'order_created_at' => $order['create'],
			//			'order_cycle'      => '',
			'cycle_start_at'   => $order['order_cycle_start'],
			'cycle_end_at'     => $order['order_cycle_end'],
			'source'           => '',
			'sku_id'           => $order['item_code'] ?? null,
			'sku_title'        => $order['article_name'] ?? '',
			'sku_spec'         => $order['article_item_name'] ?? '',
			'duration'         => $order['duration'] ?? '',
			'duration_unit'    => $order['duration_unit'] ?? '',
			'auth_user_id'     => $order['nick'] ?? '',
		];

		return $format;
	}

	public function getVersionDesc(): array
	{
		$shop  = $this->getShop();
		$order = PlatformOrder::calcuMaxLevelByCycleEndAt($shop);

		return array_merge(collect($order)->toArray(), [
			'version' => array_get($this->versionMap, $order['sku_id'],UserExtra::VERSION_STANDARD)
		]);
	}
}
