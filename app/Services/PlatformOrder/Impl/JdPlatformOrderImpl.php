<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/8/22
 * Time: 16:54
 */

namespace App\Services\PlatformOrder\Impl;


use App\Models\Shop;
use App\Services\PlatformOrder\AbstractPlatformOrderService;

class JdPlatformOrderImpl extends AbstractPlatformOrderService
{

    /**
     * 格式化成订单表结构
     * @param array $order
     * @return array
     * <AUTHOR>
     */
    public function formatToOrder(array $order): array
    {
        $shop = $this->getShop();
        $data = [
            'order_id' => $order['orderId'],
            'order_no' => $order['orderId'],
            'status' => $order['orderStatus'],
            'service_id' => $order['itemCode'],
            'service_name' => $order['serviceName'],
            'fee' => $order['totalRealpayPrice'],
            'pay_fee' => $order['totalRealpayPrice'],
            'pay_at' =>  date('Y-m-d H:i:s',($order['orderDate'] / 1000)),
            'order_created_at' => date('Y-m-d H:i:s',($order['orderDate'] / 1000)),
            'cycle_start_at' => date('Y-m-d H:i:s',($order['startDate'] / 1000)),
            'cycle_end_at' => date('Y-m-d H:i:s',($order['endDate'] / 1000)),
            'sku_title' => $order['itemName'] . $order['orderCycle'] . '天',
            'sku_spec' => $order['itemName'],
            'sku_id' => $order['skuId'],
            'duration' => $order['orderCycle'],
            'platform_type' => Shop::PLATFORM_TYPE_JD,
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'auth_user_id' => $order['childBuyer'],
        ];

        return $data;
    }

    /**
     * 计算版本
     * @param array $order
     * @return array
     * <AUTHOR>
     */
    public function getVersionDesc(): array
    {
        // TODO: Implement getVersionDesc() method.
    }
}