<?php

namespace App\Services\PlatformOrder;

use App\Constants\ErrorConst;
use App\Constants\SubscriptionConst;
use App\Events\Subscription\SubscriptionOrderSucceeded;
use App\Exceptions\ErrorCodeException;
use App\Http\Request\PageParam;
use App\Models\PlatformOrder;
use App\Models\Subscription\SubscriptionProduct;
use App\Models\SystemConfig;
use App\Models\UserExtra;
use App\Services\Payment\PaymentService;
use App\Utils\DateTimeUtil;
use App\Utils\Environment;
use App\Utils\RedisUtil;
use App\Utils\RequestUtil;
use App\Utils\StrUtil;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

/**
 *
 */
class SelfPlatformOrderService
{
    /**
     * @return void 创建独立的平台订单，就是自己应用
     */
    public function create(){

    }

    //时长单位, 0:天，1:月，2:年
     const DURATION_UNIT_MAP=[
        'day'=>0,
        'month'=>1,
        'year'=>2,

    ];

    public function platformMainProduct():array{

        $systemConfig = SystemConfig::firstByKey('subscription_product');
        return  json_decode($systemConfig->value??'{}',true);
    }

    /**
     * 查询店铺的订单
     * @param array $shopIds
     * @param PageParam $pageParam
     * @return LengthAwarePaginator
     */
    public function pageQueryOrders(array $shopIds,PageParam  $pageParam): LengthAwarePaginator
    {
        $query=PlatformOrder::query()->whereIn('shop_id', $shopIds);
        $sorts=$pageParam->getSorts([['id',"desc"]]);
        foreach ($sorts as $sort){
            $query->orderBy($sort[0], $sort[1]);
        }

        return $query->paginate($pageParam->pageSize,$pageParam->column,$pageParam->pageName,$pageParam->page);
    }

    /**
     * @param string $versionCode
     * @param string $unit
     * @param int  $buyValue
     * @return array{"price":10,"unit":"month","value":1}
     * @throws ErrorCodeException
     */
    public function pricing(string $versionCode,string $unit, int $buyValue): array
    {
        $version = $this->getVersion($versionCode);
        if(!$version){
           throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'版本不存在');
        }
        $priceItem=array_first($version['price'],function($priceItem)use($unit,$buyValue){
            return strtoupper($priceItem['unit'])==strtoupper($unit)&&$priceItem['value']==$buyValue;
        });
        if(!$priceItem){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'价格不存在');
        }
        $priceItem['sku_spec']=$version['name'];
        $lowerUnit = strtolower($unit);
        $unitName=SubscriptionConst::UNIT_NAME[$lowerUnit];
        $unitTitle =SubscriptionConst::UNIT_TITLE[$lowerUnit];
        $priceItem['sku_title']=$version['name'].$buyValue. $unitTitle .$unitName;
        if(PaymentService::isDev()){
            $price=$priceItem['price'];
            if(bccomp($price,"0")>0){
                $priceItem['price']="0.01";
                Log::info('价格调试模式，原价'.$price.'，改成0.01',[$versionCode,$unit,$buyValue]);
            }
        }else{
            $feeRate=config('pay.alipay.fee_rate',"0.00");
            $fee=round_bcmul($priceItem['price'],$feeRate,2);
            $priceItem['price']=round_bcadd($priceItem['price'],$fee,2);

        }

        return $priceItem;

    }


    /**
     * 检查有没有未支付的平台订单，如果有，则不允许创建新的订单
     * @param CreatePlatformOrderRequest $request
     * @return void
     * @throws ErrorCodeException
     */
    public function validateNewOrRenewOrder(CreatePlatformOrderRequest $request): void{
        $platformOrder=PlatformOrder::query()->where('shop_id', $request->shopId)
            ->whereIn('status',[PlatformOrder::STATUS_PADDING])
            ->first();
        if($platformOrder){
            Log::info('存在未支付的平台订单，不允许创建新的订单,请取消或者付款',[$platformOrder]);
            $data=[
                'orderId'=>$platformOrder->id,
                'status'=>PlatformOrder::STATUS_PADDING
            ];
           throw_error_code_exception(ErrorConst::PARAM_ERROR,$data,'存在未支付的平台订单，不允许创建新的订单');
        }
    }

    /**
     * 检查有没有未支付的平台订单,sku相同的订单(duration,duration_unit,)
     * @param CreatePlatformOrderRequest $request
     * @return PlatformOrder|null
     */
    public function findUnpaidSameSkuOrder(CreatePlatformOrderRequest $request):?PlatformOrder{
        $durationUnit = self::DURATION_UNIT_MAP[strtolower($request->unit)];
        $skuSpec = UserExtra::VERSION_MAP_ARR[$request->versionCode] ?? $request->versionCode;
        $platformOrder = PlatformOrder::query()->where('shop_id', $request->shopId)
            ->whereIn('status', [PlatformOrder::STATUS_PADDING])
            ->where('duration', $request->value)
            ->where('duration_unit', $durationUnit)
            ->where('sku_spec', $skuSpec)
            ->first();
        Log::info('检查有没有未支付的平台订单,sku相同的订单',["duration"=>$request->value,
            "duration_unit"=> $durationUnit,
            "sku_spec"=>UserExtra::VERSION_MAP_ARR[$request->versionCode]]);
        return $platformOrder;
    }




    /**
     * @throws ErrorCodeException
     */
    public function createOrder(CreatePlatformOrderRequest $request): PlatformOrder
    {
        Log::info('创建平台订单',[$request]);
        $priceItem=$this->pricing($request->versionCode,$request->unit,$request->value);
        $platformOrder=new PlatformOrder();
        $platformOrder->shop_id=$request->shopId;
        $platformOrder->user_id=$request->userId;
        $platformOrder->identifier=$request->identifier;
        $orderSn = RedisUtil::getOrderSn(strtoupper(Environment::platform()));
        $platformOrder->order_id= $orderSn;
        $platformOrder->order_no=$orderSn;
        $payFee = bcmul($priceItem['price'], "100", 2);
        if($payFee==0){
            //如果支付金额是0，直接订购成功
            $platformOrder->status=PlatformOrder::STATUS_SUCCESS;

        }else {
            //如果不是0，则创建订单，等待支付
            $platformOrder->status = PlatformOrder::STATUS_PADDING;
        }

        $platformOrder->platform_type=Environment::platformType();

        $platformOrder->fee=bcmul($priceItem['originalPrice']??$priceItem['price'],"100",2);
        $platformOrder->prom_fee=bcmul($priceItem['discount']??"0","100",2);
        $platformOrder->pay_fee = $payFee;
        $platformOrder->sku_spec=$priceItem['sku_spec'];
        $platformOrder->sku_title=$priceItem['sku_title'];
        $platformOrder->duration=$request->value;
        $platformOrder->order_created_at=DateTimeUtil::strNow();
        $platformOrder->duration_unit=self::DURATION_UNIT_MAP[strtolower($request->unit)];
        $platformOrder->cycle_start_at=$request->cycleStartAt;
        $platformOrder->cycle_end_at=$request->cycleEndAt;
        $platformOrder->order_created_at=DateTimeUtil::strNow();
        $platformOrder->version=$request->versionCode;


        $platformOrder->save();
        if($platformOrder->status==PlatformOrder::STATUS_SUCCESS){
            //直接订购成功，触发事件
            event(new SubscriptionOrderSucceeded($platformOrder));
        }
        return $platformOrder;

    }

    /**
     *
     * @param int $id
     * @param int|null $shopId
     * @return PlatformOrder|null
     */
    public function getById(int $id,?int $shopId=null):?PlatformOrder{
        $builder = PlatformOrder::query()->where('id', $id);
        if($shopId){
            $builder->where('shop_id', $shopId);
        }
        return $builder->first();
    }

    /**
     * 根据订单ID查询订单信息
     * @param string $orderId
     * @param int|null $shopId
     * @return PlatformOrder|null
     */
    public function getByOrderId(string $orderId,?int $shopId=null):?PlatformOrder{

        $builder = PlatformOrder::query()->where('order_id', $orderId);
        if($shopId){
            $builder->where('shop_id', $shopId);
        }
        return $builder->first();
    }


    /**
     * // 1、商户需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号；
     *  // 请自行对 trade_status 进行判断及其它逻辑进行判断，在支付宝的业务通知中，只有交易通知状态为 TRADE_SUCCESS 或 TRADE_FINISHED 时，支付宝才会认定为买家付款成功。
     *  // 1、商户需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号；
     *  // 2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额）；
     *  // 3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）；
     *  // 4、验证app_id是否为该商户本身。
     *  // 5、其它业务逻辑情况
     * @param string $orderId
     * @param string $totalAmount
     * @param string $payAt
     * @return bool
     * @throws ErrorCodeException
     */
    public function paySuccess(string $orderId,string $totalAmount,string $payAt): bool
    {
        $platformOrder=$this->getByOrderId($orderId);
        if(!$platformOrder){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'订单不存在');
        }
        if(bccomp(bcmul($totalAmount,"100"),$platformOrder->pay_fee)){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'金额不匹配');
        }
        $platformOrder->pay_at=$payAt;
        $platformOrder->status=PlatformOrder::STATUS_SUCCESS;
        $saved=$platformOrder->save();
        event(new SubscriptionOrderSucceeded($platformOrder));
        Log::info('支付成功',[$platformOrder]);
        return $saved;

    }

    /**
     * 取消订单(关闭待支付的订单)
     * @param int $id
     * @param int|null $shopId
     * @return bool
     * @throws ErrorCodeException
     */
    public function cancelOrder(int $id,?int $shopId=null): bool
    {
        $platformOrder=$this->getById($id,$shopId);
        if(!$platformOrder){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'订单不存在');
        }
        if($platformOrder->status!=PlatformOrder::STATUS_PADDING){
            throw_error_code_exception(ErrorConst::PARAM_ERROR,null,'订单状态不允许取消');
        }
        $platformOrder->status=PlatformOrder::STATUS_CLOSE;
        return $platformOrder->save();

    }

    /**
     * @param string $versionCode
     * @return mixed
     */
    public function getVersion(string $versionCode)
    {
        $versions = array_get($this->platformMainProduct(), 'versions');
        Log::info('版本信息', $versions);
        return array_first($versions, function ($version) use ($versionCode) {
            Log::info('版本信息', [$version]);
            return strtoupper($version['code']) == strtoupper($versionCode);
        });
    }

    /**
     * 把未服务的已支付的订单找处理
     * @param int $shopId
     * @return Builder[]|Collection
     */
    public function findPaidUnServiceOrders(int $shopId)
    {
        return PlatformOrder::query()->where('shop_id', $shopId)
            ->whereIn('status', [PlatformOrder::STATUS_SUCCESS])
            ->where('cycle_start_at','>',DateTimeUtil::strNow())
            ->get();
    }

    /**
     * 查找已支付的在服务期限内的订单
     * @param int $shopId
     * @return Builder[]|Collection
     */
    public function findPaidInServiceOrders(int $shopId){
        return PlatformOrder::query()->where('shop_id', $shopId)
            ->whereIn('status', [PlatformOrder::STATUS_SUCCESS])
            ->where('cycle_start_at','<=',DateTimeUtil::strNow())
            ->where('cycle_end_at','>',DateTimeUtil::strNow())
            ->get();
    }

    /**
     * @param array $where
     * @return Builder[]|Collection
     */
    public function queryOrders(array  $where){
        $query=PlatformOrder::query();
        if(!empty($where)){
            $query->where($where);
        }
        return $query->get();
    }

}
