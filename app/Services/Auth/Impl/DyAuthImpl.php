<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:30
 */

namespace App\Services\Auth\Impl;

use App\Exceptions\ClientException;
use App\Http\StatusCode\StatusCode;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\DyClient;
use App\Services\Client\PddClient;
use Overtrue\Socialite\User as SocialiteUser;

class DyAuthImpl extends AbstractAuthService
{

    protected $platformType = Shop::PLATFORM_TYPE_DY;

    /**
     * @inheritDoc
     * @throws ClientException
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', time() + $original['expires_in']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $socialiteUser->getName(),
            // 'name' => $socialiteUser->getName(),
            'name' => $socialiteUser->getName(),
            'auth_user_id' => 0,
        ];
    }

    /**
     * @inheritDoc
     */
    function refreshToken($shop, string $componentToken = "")
    {
        $client = $this->getClient();
        $data = [
            'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE
        ];
        try {
            $response = $client->refreshToken($shop['refresh_token']);
            if (isset($response['data'])) {
                $data = [
                    'access_token' => $response['data']['access_token'],
                    'refresh_token' => $response['data']['refresh_token'],
                    'expire_at' => date('Y-m-d H:i:s', $response['data']['expires_in'] + time()),
                ];
            } else {
                \Log::error('refreshToken failed!',["response"=>$response]);
                throw_error_code_exception(StatusCode::REFRESH_TOKEN_FAIL);
            }
        } catch (ClientException $e) {
            // 刷新token失败，可能用户取消授权
            // {"result":100200102,"error":"access_denied","error_msg":"refreshToken.revokedAuthorization"}
            \Log::error('refreshToken failed!', [$e->getMessage()]);
        }

        return $data;
    }

    /**
     * @param $token
     * @return array
     * @throws ClientException
     */
    public function sendShopInfo($token)
    {
        $client = $this->getClient();
        $client->setAccessToken($token);
        $params = [
        ];
        $res = $client->execute('pdd.mall.info.get', $params);

        /*
         * {
          "mall_info_get_response": {
            "logo": "str",
            "mall_desc": "str",
            "mall_name": "str",
            "merchant_type": 0
          }
        }
         */
        return $res['mall_info_get_response'];
    }

    /**
     * 获取店铺名
     * @param $token
     * @return mixed
     * @throws ClientException
     * <AUTHOR>
     */
    public function getShopName($token)
    {
        return $this->sendShopInfo($token)['mall_name'];
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        return new DyClient($appKey, $secretKey);
    }

    function getComponentAccessToken()
    {
        // TODO: Implement getComponentAccessToken() method.
    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }

    function getQueryAuthInfo(string $authCode, string $componentAccessToken)
    {
        // TODO: Implement getQueryAuthInfo() method.
    }
}
