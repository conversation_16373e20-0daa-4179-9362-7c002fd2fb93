<?php
/**
 * Created by <PERSON><PERSON><PERSON>torm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:30
 */

namespace App\Services\Auth\Impl;

use App\Exceptions\ClientException;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\JdClient;
use Overtrue\Socialite\User as SocialiteUser;

class JdAuthImpl extends AbstractAuthService
{
    protected $platformType = Shop::PLATFORM_TYPE_JD;

    /**
     * @inheritDoc
     * @throws ClientException
     */
    public function formatToShop($socialiteUser): array
    {
//        \Log::info("formatToShop",[$socialiteUser]);
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        // $shopName = $this->getShopName($token->getToken());
        $shopInfo = $this->sendShopInfo($token->getToken());
//        \Log::info('formatToShop', compact('token','original', 'shopInfo'));
//        \Log::info('formatToShop',[$shopInfo]);
//        \Log::info('formatToShop',[$token,$original]);
        return [
            'type' => $this->platformType,
            'identifier' => $shopInfo['shop_id'],
            'shop_identifier' => $shopInfo['shop_id'],
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', time() + $original['expires_in']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $shopInfo['shop_name'],
            // 'shop_logo' => '',
            // 'name' => $socialiteUser->getName(),
            'name' => $shopInfo['shop_name'],
            'service_id' => $shopInfo['vender_id'],
            'auth_user_id' => $original['open_id'],
            'vender_id'=>$shopInfo['vender_id']??null,
            'col_type'=>$shopInfo['col_type']??null,
            'cate_main'=>$shopInfo['cate_main']??null
        ];
    }

    /**
     * @inheritDoc
     */
    function refreshToken($shop, string $componentToken)
    {
        $client = $this->getClient();
        $params = [];
        $res = $client->execute('jingdong.seller.vender.info.get',$params);
    }

    /**
     * @param $token
     * @return array
     * @throws ClientException
     */
    public function sendShopInfo($token)
    {
        $client = $this->getClient();
        $client->setAccessToken($token);
        $params = [
        ];
        \Log::info('sendShopInfo',[$params,$token]);
        $res = $client->execute('jingdong.seller.vender.info.get',$params);
        \Log::info(var_export($res, true));
        /*
         * {
          "mall_info_get_response": {
            "logo": "str",
            "mall_desc": "str",
            "mall_name": "str",
            "merchant_type": 0
          }
        }
         */
        return $res['jingdong_seller_vender_info_get_responce']['vender_info_result'];
    }

    /**
     * 获取店铺名
     * <AUTHOR>
     * @param $token
     * @return mixed
     * @throws ClientException
     */
    public function getShopName($token)
    {
        return $this->sendShopInfo($token)['mall_name'];
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        return new JdClient(config('socialite.jd.client_id'), config('socialite.jd.client_secret'));
    }

	public function getComponentAccessToken()
	{

	}

    public function getQueryAuthInfo(string $authCode, string $componentAccessToken){

    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }



}
