<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:30
 */

namespace App\Services\Auth\Impl;

use App\Exceptions\ClientException;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\PddClient;
use Overtrue\Socialite\User as SocialiteUser;

class PddAuthImpl extends AbstractAuthService
{

    protected $platformType = Shop::PLATFORM_TYPE_PDD;

    /**
     * @inheritDoc
     * @throws ClientException
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        // $shopName = $this->getShopName($token->getToken());
        $shopInfo = $this->sendShopInfo($token->getToken());
        \Log::info(var_export($shopInfo['logo'], true));
        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', $original['expires_at']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $shopInfo['mall_name'],
            'shop_logo' => $shopInfo['logo'],
            // 'name' => $socialiteUser->getName(),
            'name' => $original['owner_name'],
            'auth_user_id' => $original['owner_id'],
        ];
    }

    /**
     * @inheritDoc
     */
    public function refreshToken($shop, string $componentToken)
    {
        // TODO: Implement refreshToken() method.
    }

    /**
     * @param $token
     * @return array
     * @throws ClientException
     */
    public function sendShopInfo($token)
    {
        $client = $this->getClient();
        $client->setAccessToken($token);
        $params = [
        ];
        $res = $client->execute('pdd.mall.info.get',$params);

        /*
         * {
          "mall_info_get_response": {
            "logo": "str",
            "mall_desc": "str",
            "mall_name": "str",
            "merchant_type": 0
          }
        }
         */
        return $res['mall_info_get_response'];
    }

    /**
     * 获取店铺名
     * <AUTHOR>
     * @param $token
     * @return mixed
     * @throws ClientException
     */
    public function getShopName($token)
    {
        return $this->sendShopInfo($token)['mall_name'];
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        return new PddClient(config('socialite.pdd.client_id'), config('socialite.pdd.client_secret'));
    }

	public function getComponentAccessToken()
	{

	}

    public function getQueryAuthInfo(string $authCode, string $componentAccessToken){

    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }
}
