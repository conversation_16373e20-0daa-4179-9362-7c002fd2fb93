<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:30
 */

namespace App\Services\Auth\Impl;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\OrderException;
use App\Http\StatusCode\StatusCode;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\DyClient;
use App\Services\Client\PddClient;
use App\Services\Client\XhsClient;
use Overtrue\Socialite\User as SocialiteUser;

class XhsAuthImpl extends AbstractAuthService
{

    protected $platformType = Shop::PLATFORM_TYPE_XHS;

    /**
     * @inheritDoc
     * @throws ClientException
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        \Log::debug('formatToShop',[$token,$original]);
        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refreshToken'],
            'expire_at' => date('Y-m-d H:i:s', intval($original['accessTokenExpiresAt']/1000)),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $socialiteUser->getName(),
            // 'name' => $socialiteUser->getName(),
            'name' => $socialiteUser->getName(),
            'auth_user_id' => 0,
        ];
    }

    /**
     * @inheritDoc
     */
    function refreshToken($shop, string $componentToken = "")
    {
        $client = $this->getClient();
        $res = [];
        try {
            $response = $client->refreshToken($shop['refresh_token']);
            if (isset($response['data'])) {
                $res = [
                    'access_token' => $response['data']['accessToken'],
                    'refresh_token' => $response['data']['refreshToken'],
                    'expire_at' => date('Y-m-d H:i:s', $response['data']['accessTokenExpiresAt'] / 1000),
                ];
            } else {
                \Log::error('refreshToken failed!',["response"=>$response]);
                throw_error_code_exception(StatusCode::REFRESH_TOKEN_FAIL);
            }
        } catch (ClientException $e) {
            // 刷新token失败，可能用户取消授权
            \Log::error('refreshToken failed!', [$e->getMessage()]);
        } catch (ApiException $exception){
            if ($exception->getCode() == ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED[0]) {
                $res = [
                    'auth_status' => Shop::AUTH_STATUS_EXPIRE
                ];
            }
        }

        return $res;
    }

    /**
     * @param $token
     * @return array
     * @throws ClientException
     */
    public function sendShopInfo($token)
    {

    }

    /**
     * 获取店铺名
     * @param $token
     * @return mixed
     * @throws ClientException
     * <AUTHOR>
     */
    public function getShopName($token)
    {
        return $this->sendShopInfo($token)['mall_name'];
    }

    /**
     * @inheritDoc
     */
    protected function getClient()
    {
        $appKey = config('socialite.dy.client_id');
        $secretKey = config('socialite.dy.client_secret');
        return new XhsClient($appKey, $secretKey);
    }

    function getComponentAccessToken()
    {
        // TODO: Implement getComponentAccessToken() method.
    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }

    function getQueryAuthInfo(string $authCode, string $componentAccessToken)
    {
        // TODO: Implement getQueryAuthInfo() method.
    }
}
