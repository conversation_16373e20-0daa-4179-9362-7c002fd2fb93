<?php

namespace App\Services\Auth\Impl;

use App\Exceptions\ClientException;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\WxClient;
use Illuminate\Support\Facades\Log;

class WxspAuthImpl extends AbstractAuthService
{
    protected $platformType = Shop::PLATFORM_TYPE_WXSP;

    /**
     * @inheritDoc
     */
    public function formatToShop($user): array
    {
	    $service = $user['service'];
        $token = $user['token'];
        $user = $user['auth_info'];

        Log::info('formatToShop', [$user]);
        return [
            'type' => $this->platformType,
            'identifier' => $user['authorization_info']['authorizer_appid'],
            'shop_identifier' => $user['authorization_info']['authorizer_appid'],
            'access_token' => $token['authorizer_access_token'],
            'refresh_token' => $token['authorizer_refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', time() + $token['expires_in']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $user['authorizer_info']['nick_name'],
            'shop_logo' => $user['authorizer_info']['head_img'],
            'name' =>$user['authorizer_info']['nick_name'],
            'auth_user_id' => $user['authorizer_info']['user_name'],
            'service_id' => $service['service_id'],
            'specification_id' => $service['specification_id'] ?? '',
        ];
    }

    /**
     * @inheritDoc
     */
    function refreshToken($shop, string $componentToken)
    {
        $client = $this->getClient();
        $data = [
            'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE
        ];
        try {
            $response = $client->getAccessToken($componentToken, $shop->identifier, $shop->refresh_token);
            if ($response) {
                $data = [
                    'access_token' => $response['authorizer_access_token'],
                    'refresh_token' => $response['authorizer_refresh_token'],
                    'expire_at' => date('Y-m-d H:i:s', $response['expires_in'] + time()),
                ];
            }
        } catch (ClientException $e) {
            // 刷新token失败，可能用户取消授权
            Log::error('refreshToken failed!', [$e->getMessage()]);
        }

        return $data;
    }

    protected function getClient()
    {
        $appKey = config('socialite.wx.client_id');
        $secretKey = config('socialite.wx.client_secret');
        return new WxClient($appKey, $secretKey);
    }

	public function getComponentAccessToken()
	{
        $client = $this->getClient();
		$redis = redis('cache');
		$redisKey = 'component_verify_ticket';
		if (!$redis->exists($redisKey)) {
			Log::error('component_verify_ticket get failed!', [$redisKey]);
		}
		$ticket = $redis->get($redisKey);
		$redis->exists($redisKey);
		$response = $client->getComponentAccessToken($ticket);

		return $response;
	}

	public function getService(string $code, string $componentAccessToken)
	{
		$client = $this->getClient();
		$response = $client->getService($code, $componentAccessToken);

		return $response;
	}

	public function getOauthUser(string $componentAccessToken, string $authorizerAppid)
	{
		$client = $this->getClient();
		$authInfo = $client->getAuthInfo($componentAccessToken, $authorizerAppid);

		$token = $client->getAccessToken($componentAccessToken, $authorizerAppid, $authInfo['authorization_info']['authorizer_refresh_token']);

		return ['auth_info' => $authInfo, 'token' => $token];
	}

    /**
     * @inheritDoc
     */
    public function sendShopInfo()
    {
        // TODO: Implement sendShopInfo() method.
    }

    public function getPreAuthCode()
    {
        $client = $this->getClient();
        $componentAccessToken = $this->getComponentAccessToken();
        $preAuthCode = $client->getPreAuthCode($componentAccessToken['component_access_token']);

        return $preAuthCode['pre_auth_code'];
    }

    public function getQueryAuthInfo(string $authCode, string $componentAccessToken){
        $client = $this->getClient();
        $authInfo = $client->getQueryAuthInfo($authCode, $componentAccessToken);

        return $authInfo['authorization_info'] ?? [];
    }
}
