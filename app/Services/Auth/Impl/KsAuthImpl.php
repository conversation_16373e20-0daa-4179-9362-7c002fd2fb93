<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/3/9
 * Time: 18:30
 */

namespace App\Services\Auth\Impl;


use App\Exceptions\ClientException;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\KsClient;
use Illuminate\Support\Facades\Log;
use Overtrue\Socialite\User as SocialiteUser;

class KsAuthImpl extends AbstractAuthService
{

    protected $platformType = Shop::PLATFORM_TYPE_KS;

    /**
     * @inheritDoc
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();
        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', time() + $original['expires_in']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $socialiteUser->getUsername(),
            'name' => $socialiteUser->getUsername(),
            'auth_user_id' => $original['openId'],
            'shop_logo' => $original['head'],
        ];
    }

    /**
     * @inheritDoc
     */
    function refreshToken($shop, string $refreshToken)
    {
        $client = $this->getClient();
        $data = [
            'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE
        ];
        try {
            $response = $client->refreshToken($shop->refresh_token);
            if (isset($response['result']) && $response['result'] == 1) {
                $data = [
                    'access_token' => $response['access_token'],
                    'refresh_token' => $response['refresh_token'],
                    'expire_at' => date('Y-m-d H:i:s', $response['expires_in'] + time()),
                ];
            }
        } catch (ClientException $e) {
            // 刷新token失败，可能用户取消授权
            // {"result":100200102,"error":"access_denied","error_msg":"refreshToken.revokedAuthorization"}
            Log::error('refreshToken failed!', [$e->getMessage()]);
        }

        return $data;
    }


    /**
     * @return KsClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        $appKey = config('socialite.ks.client_id');
        $secretKey = config('socialite.ks.client_secret');
        return new KsClient($appKey, $secretKey);
    }

    /**
     * @inheritDoc
     */
    public function sendShopInfo()
    {
        // TODO: Implement sendShopInfo() method.
    }

	public function getComponentAccessToken()
	{

	}

    public function getQueryAuthInfo(string $authCode, string $componentAccessToken){

    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }
}
