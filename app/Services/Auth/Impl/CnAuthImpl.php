<?php

namespace App\Services\Auth\Impl;

use App\Exceptions\ClientException;
use App\Http\StatusCode\StatusCode;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\CnClient;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class CnAuthImpl  extends AbstractAuthService
{

    protected $platformType = Shop::PLATFORM_TYPE_CN;

    /**
     * @inheritDoc
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        \Log::debug('formatToShop', [$original]);
        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', time() + $original['expires_in']),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $socialiteUser->getName(),
            // 'name' => $socialiteUser->getName(),
            'name' => $original['taobao_user_nick']??$socialiteUser->getName(),
            'auth_user_id' => 0,
        ];
    }

    /**
     * @inheritDoc
     */
    function refreshToken($shop, string $componentToken)
    {
        $albbClient = $this->getClient();
        $client = new Client([
            'timeout' => 20,
            'connect_timeout' => 5,
        ]);
        $appKey = $albbClient->appKey;
        $appSecret = $albbClient->secretKey;
        $refreshTokenUrl = "https://gw.open.1688.com/openapi/http/1/system.oauth2/getToken/". $appKey .'?grant_type=refresh_token&client_id='.$appKey.'&client_secret='. $appSecret .'&refresh_token='.$refreshToken;
        $response = $client->request('POST', $refreshTokenUrl);
        if($response->getStatusCode() != 200){
            Log::error("刷新token失败", [$refreshTokenUrl, $response->getBody()]);
            throw_error_code_exception(StatusCode::REFRESH_TOKEN_FAIL);
        }
        $responseBody = $response->getBody();
        Log::info("刷新token", [$refreshTokenUrl, $responseBody]);
        $tokenResult = json_decode($responseBody, true);
        return [
            'access_token' => $tokenResult['access_token'] ,
            'refresh_token' => $refreshToken, // 阿里巴巴没有返回refresh_token
            'expire_at' => date('Y-m-d H:i:s', $tokenResult['expires_in'] + time()),
            'auth_status' => Shop::AUTH_STATUS_SUCCESS,
        ];

    }

    function getComponentAccessToken()
    {
        // TODO: Implement getComponentAccessToken() method.
    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }

    function getQueryAuthInfo(string $authCode, string $componentAccessToken)
    {
        // TODO: Implement getQueryAuthInfo() method.
    }

    protected function getClient()
    {
        return CnClient::newInstance();
    }
}
