<?php

namespace App\Services\Auth\Impl;

use App\Exceptions\OrderException;
use App\Models\Shop;
use App\Services\Auth\AbstractAuthService;
use App\Services\Client\YchClient;
use TopAuthTokenRefreshRequest;

class TaobaoAuthImpl extends AbstractAuthService
{
    protected $platformType = Shop::PLATFORM_TYPE_TAOBAO;
    /**
     * @var string
     */
    private $gatewayHttps = 'https://eco.taobao.com/router/rest';

    /**
     * @inheritDoc
     */
    public function formatToShop($socialiteUser): array
    {
        $token = $socialiteUser->getToken();
        $original = $socialiteUser->getOriginal();

        return [
            'type' => $this->platformType,
            'identifier' => $socialiteUser->getId(),
            'shop_identifier' => $socialiteUser->getId(),
            'access_token' => $token->getToken(),
            'refresh_token' => $original['refresh_token'],
            'expire_at' => date('Y-m-d H:i:s', $original['expire_time'] / 1000),
            'auth_at' => date('Y-m-d H:i:s'),
            'name' => $socialiteUser->getName(),
            'shop_name' => $original['shop_name'],
        ];
    }

	/**
	 * refreshToken
	 * @param string $refreshToken
	 * @return mixed
	 * @throws OrderException
	 */
    public function refreshToken($shop, string $componentToken)
    {
	    $data = [
		    'auth_status' => Shop::AUTH_STATUS_ABNORMAL_EXPIRE
	    ];
	    try {
		    $topClient = $this->getClient();
		    $req = new TopAuthTokenRefreshRequest();
		    $req->setRefreshToken($shop['refresh_token']);
            $resp = $topClient->execute($req, null, $this->gatewayHttps);
		    $resp = $this->handleResponse($resp);
            $token_result = json_decode($resp['token_result'], true);
		    $data = [
			    'access_token' => $token_result['access_token'],
			    'refresh_token' => $token_result['refresh_token'],
                'expire_at' => date('Y-m-d H:i:s', $token_result['expire_time']/1000),
		    ];
	    } catch (\Exception $e) {
		    // 刷新token失败，可能用户取消授权
		    // {"result":100200102,"error":"access_denied","error_msg":"refreshToken.revokedAuthorization"}
		    \Log::error('refreshToken failed!', [$e->getMessage(),$shop]);
	    }

	    return $data;
    }

	/**
	 * token_result
	 * @param $resp
	 * @return array
	 * @throws OrderException
	 */
	private function handleResponse($resp)
	{
		$resp = objectToArray($resp);
		/**
		 * 异常示例
		 * "code" => 27
		 * "msg" => "Invalid session"
		 * "sub_code" => "invalid-sessionkey"
		 * "sub_msg" => "SessionKey非法"
		 * "request_id" => "3qvdigpsl5on"
		 */
		if (!empty($resp['code'])) {
			throw new OrderException("TaobaoAuth error:{$resp['msg']};" . json_encode($resp));
		}

		return $resp;
	}

    /**
     * @return \TopClient\TopClient
     * <AUTHOR>
     */
    protected function getClient()
    {
        return topClient();
    }

    public function saveAuthData($authData, $userId = 0)
    {
        list($user, $shop) = parent::saveAuthData($authData, $userId);

        return [$user, $shop];
    }
	public function getComponentAccessToken()
	{

	}

    public function getQueryAuthInfo(string $authCode, string $componentAccessToken){

    }

    function getService(string $code, string $componentAccessToken)
    {
        // TODO: Implement getService() method.
    }

    function getOauthUser(string $componentAccessToken, string $authorizerAppid)
    {
        // TODO: Implement getOauthUser() method.
    }
}
