<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 20:32
 */

namespace App\Services\Auth;


use App\Constants\PlatformConst;
use App\Models\Shop;
use App\Services\Auth\Impl\AlbbAuthImpl;
use App\Services\Auth\Impl\Alc2mAuthImpl;
use App\Services\Auth\Impl\CnAuthImpl;
use App\Services\Auth\Impl\DyAuthImpl;
use App\Services\Auth\Impl\KsAuthImpl;
use App\Services\Auth\Impl\PddAuthImpl;
use App\Services\Auth\Impl\TaobaoAuthImpl;
use App\Services\Auth\Impl\JdAuthImpl;
use App\Services\Auth\Impl\WxAuthImpl;
use App\Services\Auth\Impl\WxspAuthImpl;
use App\Services\Auth\Impl\XhsAuthImpl;
use InvalidArgumentException;

class AuthServiceManager
{
    protected static $initMap = [
        PlatformConst::TAOBAO => TaobaoAuthImpl::class,
        PlatformConst::PDD => PddAuthImpl::class,
        PlatformConst::KS => KsAuthImpl::class,
        PlatformConst::JD => JdAuthImpl::class,
        PlatformConst::WX => WxAuthImpl::class,
        PlatformConst::DY => DyAuthImpl::class,
        PlatformConst::WXSP => WxspAuthImpl::class,
        PlatformConst::XHS => XhsAuthImpl::class,
        PlatformConst::ALBB => AlbbAuthImpl::class,
        PlatformConst::ALC2M => Alc2mAuthImpl::class,
        PlatformConst::CN => CnAuthImpl::class,
    ];

    /**
     * 创建一个订单server
     * @param $name
     * @return AbstractAuthService
     * <AUTHOR>
     */
    public static function create($name)
    {
        if (isset(self::$initMap[$name])) {
            return new self::$initMap[$name]();
        }
        throw new InvalidArgumentException('不存在的 AuthServiceManager:' . $name);
    }



    public static function checkPlatformValid($platformCode)
    {
        if (isset(self::$initMap[$platformCode])) {
            return true;
        }
        return false;
    }

}
