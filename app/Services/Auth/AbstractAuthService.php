<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/21
 * Time: 19:43
 */

namespace App\Services\Auth;


use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\User;
use App\Models\Shop;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Overtrue\Socialite\User as SocialiteUser;
use Throwable;

abstract class AbstractAuthService
{
    /**
     * @var bool
     */
    protected $newShop = false;
    /**
     * @var bool
     */
    protected $newUser = false;

    protected $platformType = 0;

    /**
     * 格式化成用户授权表结构
     * @param array $socialiteUser
     * @return array
     * <AUTHOR>
     */
    abstract public function formatToShop($user): array;

    /**
     * 保存授权关系
     * @param SocialiteUser $socialiteUser
     * @param int $userId 用户id，如果没设置将会新建用户
     * @return mixed
     * @throws Throwable
     * <AUTHOR>
     */
    public function saveAuth($user, $userId = 0)
    {
        $authData = $this->formatToShop($user);
        return $this->saveAuthData($authData, $userId);
    }

    public function saveAuthData($authData, $userId = 0)
    {
        return DB::transaction(function () use ($authData, $userId) {
            $shop = Shop::query()->where([
                'identifier' => $authData['identifier'],
                'type' => $authData['type'],
            ])->first();
            $authSaveData = [
                'access_token' => $authData['access_token'],
//                'shop_identifier' => $authData['shop_identifier'],
                'refresh_token' => $authData['refresh_token'],
                'name' => $authData['name'],
                'auth_at' => $authData['auth_at'],
                'expire_at' => $authData['expire_at'],
                'auth_user_id' => $authData['auth_user_id'] ?? '',
                'auth_status' => Shop::AUTH_STATUS_SUCCESS,
                'login_count' => DB::raw("login_count + " . 1),
                'service_id' => $authData['service_id'] ?? "",
                'specification_id' => $authData['specification_id'] ?? "",
                'sync_switch' => Shop::SYNC_SWITCH_OPEN,
            ];
            if (!empty($authData['shop_name']))
                $authSaveData['shop_name'] = $authData['shop_name'];

            if (!empty($authData['shop_logo']))
                $authSaveData['shop_logo'] = $authData['shop_logo'];

            if ($shop) {
                //shop_identifier 给个默认值
                if (empty($shop->shop_identifier)) {
                    $authSaveData['shop_identifier'] = $authData['identifier'];
                }
                if (!$shop->shop_code) {
                    $authSaveData['shop_code'] = createShopCode();
                }
                // 如果已存在授权 更新
                $shop->update($authSaveData);
                $user = User::query()->where('id', $shop['user_id'])->firstOrFail();
                if($user && empty($user->nickname)){
                  $user->nickname = $authData['name'];
                  $user->save();
                }
            } elseif ($userId > 0) {
                // 如果不存在授权，且指定UserId
                $user = User::query()->where('id', $userId)->firstOrFail();
                if ($user)
                $authSaveData['user_id'] = $userId;
                $authSaveData['identifier'] = $authData['identifier'];
                $authSaveData['shop_identifier'] = $authData['identifier'];
                $authSaveData['type'] = $authData['type'];
                $authSaveData['shop_code'] = createShopCode();
                $shop = Shop::query()->create($authSaveData);
                $this->newShop = true;
            } else {
                // 如果不存在授权, 新建
                $user = User::query()->create([
                    'phone' => '',
                    'nickname' => $authData['name'],
                    'password' => '',
                    'invite_code' => getRandStr(16),
                ]);
                $this->newUser = true;
                $authSaveData['user_id'] = $user->id;
                $authSaveData['original_user_id'] = 0;
                $authSaveData['identifier'] = $authData['identifier'];
                $authSaveData['shop_identifier'] = $authData['identifier'];
                $authSaveData['type'] = $authData['type'];
                $authSaveData['shop_code'] = createShopCode();
                $shop = Shop::query()->create($authSaveData);
                $this->newShop = true;
            }
            if (empty($user) || empty($shop)) {
                Log::error('User info create Failed !', ['authData' => $authData]);
                throw new ApiException(ErrorConst::USER_CREATE_FAIL);
//                throw new \Exception('用户信息创建失败!');
            }
            return [$user, $shop];
        }, 2);
    }

    /**
     * @return int
     */
    public function getPlatformType()
    {
        return $this->platformType;
    }

    /**
     * 刷新Token通过授权
     * @param $shop
     * @param $componentToken
     * @return int
     * <AUTHOR>
     */
    public function refreshTokenByAuth($shop, $componentToken)
    {
        $data = $this->refreshToken($shop, $componentToken);
        if (empty($data)) {
            return 0;
        }
        if (isset($data['auth_status']) && $data['auth_status'] == Shop::AUTH_STATUS_ABNORMAL_EXPIRE) {
            Log::info('用户授权失效:refreshTokenByAuth', ['shop_id' => $shop->id,'data'=>$data]);
        }
        return Shop::query()->where('id', $shop->id)->update($data);
    }

    /**
     * 获取店铺的查询对象，
     * @param $syncSwitch=null,就忽略同步状态
     * @param $shoIdArr 不为空
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Query\Builder
     */
    public function selectAuthAvailableShops($shoIdArr = [], $syncSwitch=true){
        $query = Shop::query()
            ->where('auth_status', Shop::AUTH_STATUS_SUCCESS)
            ->whereNotNull('access_token')
            ->where('access_token','<>','')
            ->where('expire_at', '>', Carbon::now()->toDateTimeString())
            ->where('type', $this->getPlatformType());
        if($syncSwitch){
            $query->where("sync_switch",$syncSwitch);
        }
        if($shoIdArr){
            $query->whereIn('id', $shoIdArr);
        }
        return $query;
    }

    /**
     * 刷新token
     * @param string $refreshToken
     * @return mixed
     * <AUTHOR>
     */
    abstract function refreshToken($shop, string $componentToken);

    abstract function getComponentAccessToken();

    abstract function getService(string $code, string $componentAccessToken);

	abstract function getOauthUser(string $componentAccessToken, string $authorizerAppid);

	abstract function getQueryAuthInfo(string $authCode, string $componentAccessToken);

}
