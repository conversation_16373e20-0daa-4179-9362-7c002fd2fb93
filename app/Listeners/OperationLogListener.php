<?php

namespace App\Listeners;

use App\Constants\OperationLogTypeConst;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderDeliveryEvent;
use App\Events\Orders\OrderDeliveryFailEvent;
use App\Events\Orders\OrderFactoryAssignCancelEvent;
use App\Events\Orders\OrderFactoryAssignEvent;
use App\Events\Orders\OrderFlagEvent;
use App\Events\Orders\OrderLockEvent;
use App\Events\Orders\OrderPreshipmentCancelEvent;
use App\Events\Orders\OrderPreshipmentFailEvent;
use App\Events\Orders\OrderPreshipmentJoinEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderRedeliveryEvent;
use App\Events\Orders\OrderShippingOrder;
use App\Events\Orders\OrderUnLockEvent;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\Orders\OrderWaybillEvent;
use App\Events\Orders\OrderWaybillFailEvent;
use App\Events\Shops\ShopSettingUpdateEvent;
use App\Events\Users\UserLoginEvent;
use App\Events\Waybills\WaybillRecycledEvent;
use App\Models\OperationLog;
use App\Models\Order;
use App\Models\Package;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class OperationLogListener extends QueueEvent
{
    protected $orderFlagMap = [
        Order::FLAG_GRAY => '[灰色]',
        Order::FLAG_PURPLE => '[紫色]',
        Order::FLAG_CYAN => '[青色]',
        Order::FLAG_GREEN => '[绿色]',
        Order::FLAG_ORANGE => '[橙色]',
        Order::FLAG_RED => '[红色]',
        Order::FLAG_YELLOW => '[黄色]',
        Order::FLAG_BLUE => '[蓝色]'
    ];

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {

    }

    /**
     * Handle the event.
     *
     * @param $event
     * @return void
     */
    public function handle($event)
    {
        $data = [];
        if ($event instanceof OrderDeliveryEvent) {
            foreach ($event->orderArr as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'tid' => $order['tid'],
                    'waybill_code' => $order['waybill_code'],
                    'wp_code' => $order['wp_code'],
                    'content' => "订单发货",
                    'remark' => "订单号：{$order['tid']}；快递单号：{$order['waybill_code']}，物流公司：{$order['wp_code']}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_DELIVERY,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode(['packs' => $order['packs'] ?? []], JSON_UNESCAPED_UNICODE), // 'requests' => $event->clientInput
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof OrderDeliveryFailEvent) {
            $errorMsg = $event->errorMsg;
            foreach ($event->orderArr as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'tid' => $order['tid'],
                    'waybill_code' => $order['waybill_code'],
                    'wp_code' => $order['wp_code'],
                    'content' => "订单发货失败",
                    'remark' => "订单号：{$order['tid']}；快递单号：{$order['waybill_code']}，物流公司：{$order['wp_code']}，失败原因：{$errorMsg}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_DELIVERY_FAIL,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode(['packs' => $order['packs'] ?? []], JSON_UNESCAPED_UNICODE), // 'requests' => $event->clientInput
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof OrderPrintEvent) {
            foreach ($event->orderArr as $order) {
                $orderIdStr = $this->handleOrderIdStr($order['tid_arr']);
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'package_id' => $order['package_id'],
                    'print_record_id' => $order['print_record_id'] ?? 0,
                    'tid' => $order['tid'],
                    'waybill_code' => $order['waybill_code'],
                    'wp_code' => $order['wp_code'],
                    'content' => "订单打印",
                    'remark' => "订单号：{$orderIdStr}；快递单号：{$order['waybill_code']}，物流公司：{$order['wp_code']}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_PRINT,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode(['order' => $order], JSON_UNESCAPED_UNICODE),
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof UserLoginEvent) {
            $loginStatusMsg = $event->loginStatus == $event::LOGIN_STATUS_SUCCESS ? '成功' : '失败：' . $event->loginMessage;
            $data[] = [
                'user_id' => $event->user->id,
                'shop_id' => $event->shop->id,
                'order_id' => 0,
                'package_id' => 0,
                'tid' => '0',
                'waybill_code' => '',
                'wp_code' => '',
                'content' => "用户登录",
                'remark' => $loginStatusMsg,
                'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                'type' => OperationLogTypeConst::USER_LOGIN,
                'operator_ip' => $event->clientIp,
                'operator_name' => $event->user->nickname,
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];
        } elseif ($event instanceof OrderDecryptEvent) {
            foreach ($event->orderArr as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'tid' => $order['tid'],
                    'package_id' => 0,
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => "查看订单明文信息",
                    'remark' => "订单号：{$order['tid']}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_PRIVACY_INFO_VIEW,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof OrderUpdateEvent) {
            $type = OperationLogTypeConst::ORDER_MODIFY;
            $content = '地址修改';
            if ($event->type == 'merge') {
                $type = OperationLogTypeConst::ORDER_MERGE;
                $content = '手动合单';
            } else if ($event->type == 'split') {
                $type = OperationLogTypeConst::ORDER_SPLIT;
                $content = '手动拆单';
            } else if ($event->type == 'flag') {
                $type = OperationLogTypeConst::ORDER_FLAG_REMARK;
                $content = '更改留言备注';
            }
            $afterOrders = array_pluck($event->afterOrders, null, 'id');
            foreach ($event->beforeOrders as $order) {
                //$beforeOrderJson = json_encode($order, JSON_UNESCAPED_UNICODE);
                //$afterOrderJson = json_encode($afterOrders[$order['id']], JSON_UNESCAPED_UNICODE);
                if ($event->type == 'merge') {
                    $remark = "订单号：" . $this->handleOrderIdStr(array_column($event->beforeOrders, 'tid'));
                } else if ($event->type == 'split') {
                    $remark = "订单号：" . $this->handleOrderIdStr(array_column($event->beforeOrders, 'tid'));
                } else if ($event->type == 'flag') {
                    $beforeFlag = $this->orderFlagMap[$order['seller_flag']] ?? '';
                    $afterFlag = $this->orderFlagMap[$afterOrders[$order['id']]['seller_flag']] ?? '';
                    $remark = "修改前旗标：" . $beforeFlag . " 修改后旗标：" . $afterFlag .
                        "\n" . "修改前留言：" . $order['seller_memo'] . " 修改后旗标：" . $afterOrders[$order['id']]['seller_memo'];
                } else {
                    $remark = "订单号：{$order['tid']}；修改前：" . $order['receiver_state'] . $order['receiver_city'] . $order['receiver_district'] . $order['receiver_town'] . $order['receiver_name'] . $order['receiver_phone'] .
                        "，修改后：" . $afterOrders[$order['id']]['receiver_state'] . $afterOrders[$order['id']]['receiver_city'] . $afterOrders[$order['id']]['receiver_district'] . $afterOrders[$order['id']]['receiver_town'] . $afterOrders[$order['id']]['receiver_name'] . $afterOrders[$order['id']]['receiver_phone'];
                }
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'tid' => $order['tid'],
                    'package_id' => 0,
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => $content,
                    'remark' => $remark,
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => $type,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof OrderFlagEvent) {
            foreach ($event->orderArr as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'package_id' => 0,
                    'tid' => $order['tid'],
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => "订单打标",
                    'remark' => "订单号：{$order['tid']}；由：{$order['old_flag']}，更改为：{$order['new_flag']}。修改备注为：{$order['new_seller_memo']}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_FLAG,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => $order['seller_nick'],
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof OrderWaybillEvent) {
            $orderIdStr = $this->handleOrderIdStr($event->orderInfo['tid_arr']);
            $data[] = [
                'user_id' => $event->user->id,
                'shop_id' => $event->shop->id,
                'order_id' => $event->orderInfo['id'],
                'package_id' => $event->orderInfo['package_id'],
                'tid' => $event->orderInfo['tid'],
                'waybill_code' => $event->orderInfo['waybill_code'],
                'wp_code' => $event->orderInfo['wp_code'],
                'content' => "订单取号",
                'remark' => "订单号：{$orderIdStr}；快递单号：{$event->orderInfo['waybill_code']}，物流公司：{$event->orderInfo['wp_code']}",
                'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                'type' => OperationLogTypeConst::ORDER_WAYBILL,
                'operator_ip' => $event->clientIp,
                'operator_name' => $event->user->nickname,
                'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];
        } elseif ($event instanceof OrderShippingOrder) {
            foreach ($event->orderList as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'package_id' => '',
                    'tid' => $order['tid'],
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => "打印发货单",
                    'remark' => "",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_SHIPPING,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        } elseif ($event instanceof WaybillRecycledEvent) {
            $data[] = [
                'user_id' => $event->user->id,
                'shop_id' => $event->shop->id,
                'order_id' => $event->waybillHistory['order_id'],
                'package_id' => $event->waybillHistory['package_id'],
                'tid' => $event->waybillHistory['order_no'] ?? '',
                'waybill_code' => $event->waybillHistory['waybill_code'],
                'wp_code' => $event->waybillHistory['wp_code'],
                'content' => "运单号回收",
                'remark' => "快递单号：{$event->waybillHistory['waybill_code']}，物流公司：{$event->waybillHistory['wp_code']}",
                'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                'type' => OperationLogTypeConst::WAYBILL_RECYCLED,
                'operator_ip' => $event->clientIp,
                'operator_name' => $event->user->nickname,
                'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];
        } elseif ($event instanceof OrderFactoryAssignEvent) {
            $data[] = [
                'user_id' => $event->user->id,
                'shop_id' => $event->shop->id,
                'order_id' => $event->orderInfo->id,
                'target_shop_id' => $event->targetShopId,
                'package_id' => 0,
                'tid' => $event->orderInfo->tid,
                'waybill_code' => '',
                'wp_code' => '',
                'content' => "厂家订单分配",
                'remark' => "",
                'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                'type' => OperationLogTypeConst::FACTORY_ORDER_ASSIGN,
                'operator_ip' => $event->clientIp,
                'operator_name' => $event->user->nickname,
                'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];
        } elseif ($event instanceof OrderFactoryAssignCancelEvent) {
            $data[] = [
                'user_id' => $event->user->id,
                'shop_id' => $event->shop->id,
                'order_id' => $event->orderInfo->id,
                'target_shop_id' => $event->targetShopId,
                'package_id' => 0,
                'tid' => $event->orderInfo->tid,
                'waybill_code' => '',
                'wp_code' => '',
                'content' => "厂家订单分配取消",
                'remark' => "",
                'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                'type' => OperationLogTypeConst::FACTORY_ORDER_ASSIGN_CANCEL,
                'operator_ip' => $event->clientIp,
                'operator_name' => $event->user->nickname,
                'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];
        } elseif ($event instanceof OrderWaybillFailEvent) {
            $orderIdStr = $this->handleOrderIdStr($event->tidArr);
            foreach ($event->orderList as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'target_shop_id' => 0,
                    'package_id' => 0,
                    'tid' => $order['tid'],
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => "取号失败",
                    'remark' => "订单号：{$orderIdStr}；失败原因：" . $event->failReason,
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_WAYBILL_FAIL,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }

        } elseif ($event instanceof OrderLockEvent || $event instanceof OrderUnLockEvent) {
            if ($event instanceof OrderLockEvent) {
                $type = OperationLogTypeConst::ORDER_LOCK;
                $content = '锁定订单';
            } else {
                $type = OperationLogTypeConst::ORDER_UNLOCK;
                $content = '解锁订单';
            }
            $allTidStr = $this->handleOrderIdStr(array_column($event->orderList, 'tid'));
            foreach ($event->orderList as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'package_id' => 0,
                    'tid' => $order['tid'],
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => $content,
                    'remark' => "订单号：{$allTidStr}；",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => $type,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => '[]',
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        }elseif ($event instanceof OrderRedeliveryEvent) {
            foreach ($event->orderList as $order) {
                $deliveryType = $order['deliveryType']??-1;
                $typeName = Package::DELIVERY_TYPE_NAME_MAPPING[$deliveryType] ?? '未知：' . $deliveryType;
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'package_id' => 0,
                    'tid' => $order['tid'],
                    'waybill_code' => '',
                    'wp_code' => '',
                    'content' => "订单重新发货",
                    'remark' => "类型：{$typeName}，订单号：{$order['tid']}；操作单号：{$order['oldWaybillCode']}，新单号：{$order['newWaybillCode']}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_FLAG,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => '[]',
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
        }elseif ($event instanceof ShopSettingUpdateEvent) {
            $data[] = [
                'user_id' => $event->user->id,
                'shop_id' => $event->shop->id,
                'order_id' => 0,
                'target_shop_id' => 0,
                'package_id' => 0,
                'tid' => '',
                'waybill_code' => '',
                'wp_code' => '',
                'content' => "店铺设置更新",
                'remark' => "更新内容：" . json_encode($event->data, JSON_UNESCAPED_UNICODE),
                'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                'type' => OperationLogTypeConst::SHOP_SETTING_UPDATE,
                'operator_ip' => $event->clientIp,
                'operator_name' => $event->user->nickname,
                'append_info' => json_encode([], JSON_UNESCAPED_UNICODE),
                'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                'created_at' => Carbon::now()->toDateTimeString(),
                'updated_at' => Carbon::now()->toDateTimeString(),
            ];
        }elseif ($event instanceof OrderPreshipmentJoinEvent || $event instanceof OrderPreshipmentCancelEvent || $event instanceof OrderPreshipmentFailEvent) {
            if ($event instanceof OrderPreshipmentFailEvent) {
                $content = '预发货失败';
            }elseif ($event instanceof OrderPreshipmentCancelEvent) {
                $content = '取消预发货';
            }else{
                $content = '加入预发货';
            }
            //            $tidStr = collect($event->orderList)->pluck('tid')->implode(',');
            foreach ($event->orderList as $order) {
                $data[] = [
                    'user_id' => $event->user->id,
                    'shop_id' => $event->shop->id,
                    'order_id' => $order['id'],
                    'tid' => $order['tid'],
                    'waybill_code' => $order['waybill_code'],
                    'wp_code' => $order['wp_code'],
                    'content' => $content,
                    'remark' => "订单号：{$order['tid']}；快递单号：{$order['waybill_code']}，物流公司：{$order['wp_code']}",
                    'time' => Carbon::createFromTimestamp($event->time)->toDateTimeString(),
                    'type' => OperationLogTypeConst::ORDER_DELIVERY,
                    'operator_ip' => $event->clientIp,
                    'operator_name' => $event->user->nickname,
                    'append_info' => json_encode(['packs' => $order['packs'] ?? []], JSON_UNESCAPED_UNICODE), // 'requests' => $event->clientInput
                    'request_data' => json_encode($event->clientInput, JSON_UNESCAPED_UNICODE),
                    'created_at' => Carbon::now()->toDateTimeString(),
                    'updated_at' => Carbon::now()->toDateTimeString(),
                ];
            }
    }
        $int = OperationLog::query()->insert($data);
//        Log::debug('insert', [$int, $data]);

    }

    private function handleOrderIdStr(array $orderIds)
    {
//        $orderIds = array_unique($orderIds);
//        $orderIds = array_filter($orderIds);
//        $orderIds = array_values($orderIds);
        return implode(',', $orderIds);
    }
}
