<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/6/21
 * Time: 11:44
 */

namespace App\Logging;


use Monolog\Formatter\LineFormatter;

class CustomizeLineFormatter extends LineFormatter
{

    public function format(array $record)
    {
        if ('cli' === PHP_SAPI) {
            $argv = $_SERVER['argv'] ?? [];
            $path = implode(' ', $argv);
        } else {
            $path = request()->path();
        }
        $shopId = 0;
        defined('SHOP_ID') && $shopId = SHOP_ID;
        $cost=-1;
        if (defined('LARAVEL_START')) {
            $cost = round((microtime(true) - LARAVEL_START) * 1000, 2);
        }
        $record['extra'] = [
            'req_id' => defined('REQ_ID')?REQ_ID:'',
            'shop_id' => $shopId,
            'method' => request()->getMethod(),
            'path' => $path,
            'cost(ms)'=>$cost
        ];

        return parent::format($record);
    }

}
