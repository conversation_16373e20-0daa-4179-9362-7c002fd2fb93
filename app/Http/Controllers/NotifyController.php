<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2021/9/28
 * Time: 11:00
 */

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;

/**
 * 通知回调控制器
 * Class NotifyController
 * @package App\Http\Controllers
 */
class NotifyController extends Controller
{
    /**
     * 抖音开放平台 SPI 接入
     * @see https://bytedance.feishu.cn/docs/doccnsslxlco3x7UsQAoFmdeQfb#
     * <AUTHOR>
     * @param $method string
     * @param Request $request
     */
    public function dySPI(string $method, Request $request)
    {
        $resp = [
            'code' => 0,
            'message' => 'success',
            'data' => null,
        ];
        $query = $request->query();
        if ($request->isMethod('GET')) {
            $param_json = json_decode($request->get('param_json'), true);
        } else {
            $param_json = $request->json()->all();
        }
        \Log::info('dySPI',[(array)$request->headers,$query,$param_json,$request->input()]);

        if (empty($param_json)) {
            $sign_param_json = '{}';
        }else{
            $sign_param_json = $this->recursionKsort($param_json);
            // https://bytedance.feishu.cn/docs/doccnsslxlco3x7UsQAoFmdeQfb
            // 特殊字符需要转义："&"转成"\u0026"；"<"转成"\u003c"；">"转成"\u003e" ; / 不要转义
            $sign_param_json = json_encode($sign_param_json, JSON_HEX_TAG + JSON_HEX_AMP + JSON_UNESCAPED_SLASHES + JSON_UNESCAPED_UNICODE);
        }
        $signArr = [
            'app_key' => $query['app_key'],
            'param_json' => $sign_param_json,
            'timestamp' => $query['timestamp'],
        ];

        $signStr = '';
        foreach ($signArr as $index => $item) {
            $signStr .= $index . $item;
        }
        $signStr = 'app_key' . $query['app_key'] . 'param_json' . $sign_param_json . 'timestamp' . $query['timestamp'];
        $secretKey = config('socialite.dy.client_secret');
        $signStr = $secretKey . $signStr . $secretKey;
        if ($query['sign'] != md5($signStr)) {
            $resp = [
                'code' => 100001,
                'message' => '验签失败',
                'data' => null,
            ];
            return response(json_encode($resp));
        }

        switch ($method) {
            case 'order-shopAddress-getReviewResult':
                $order_id = $param_json['order_id'];
                $orderModel = Order::query()->where('tid', $order_id . 'A')->first();
                if (!empty($orderModel)) {
                    if ($orderModel->print_status > Order::PRINT_STATUS_NO) {
                        $resp = [
                            'code' => 200014,
                            'message' => '仓库打印快递面单中不支持改地址',
                            'data' => null,
                        ];
                    }elseif ($orderModel->order_status >= Order::ORDER_STATUS_DELIVERED) {
                        $resp = [
                            'code' => 200015,
                            'message' => '订单已经发货无法修改地址',
                            'data' => null,
                        ];
                    }
                }
                break;
            case 'order-shopOperate-getSkuReviewResult':
                $order_id = $param_json['shop_order_id'];
                $orderModel = Order::query()->where('tid', $order_id . 'A')->first();
                if (empty($orderModel)) {
                    $resp = [
                        'code' => 200022,
                        'message' => '订单不存在，请稍后重试',
                        'data' => null,
                    ];
                } else {
                    if ($orderModel->print_status > Order::PRINT_STATUS_NO) {
                        $resp = [
                            'code' => 200008,
                            'message' => '订单对应的物流发货订单已生成不支持改sku',
                            'data' => null,
                        ];
                    }elseif ($orderModel->order_status >= Order::ORDER_STATUS_DELIVERED) {
                        $resp = [
                            'code' => 200015,
                            'message' => '订单已经发货无法修改sku',
                            'data' => null,
                        ];
                    }
                }
                break;
            default:
                $resp = [
                    'code' => 100003,
                    'message' => '未定义的 method ',
                    'data' => null,
                ];
                break;
        }
        return response(json_encode($resp));
    }

    /**
     * 递归 ksort
     * <AUTHOR>
     * @param $params
     * @return array|mixed
     */
    private function recursionKsort($params)
    {
        if (is_array($params)) {
            ksort($params);
            foreach ($params as $k => $v) {
                $params[$k] = $this->recursionKsort($v);
            }
        }
        return $params;
    }
}
