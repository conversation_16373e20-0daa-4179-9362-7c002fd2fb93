<?php

namespace App\Http\Controllers;

use App\Constants\AuthStateTypeConst;
use App\Constants\ErrorConst;
use App\Constants\PlatformConst;
use App\Events\SqlLogEvent;
use App\Events\Users\UserLoginEvent;
use App\Exceptions\ApiException;
use App\Jobs\Shop\ShopTokenPushJob;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\CustomizeOrder;
use App\Models\Order;
use App\Models\ShopBind;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\Shop;
use App\Models\MobileVerifyCode;
use App\Models\UserExtra;
use App\Models\UserShopBind;
use App\Services\Auth\AuthServiceManager;
use App\Services\BusinessException;
use App\Services\Client\DyClient;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Order\Impl\TaobaoOrderImpl;
use App\Services\Order\OrderServiceManager;
use App\Services\Order\OrderSyncService;
use App\Services\ShengYiWang\ShengYiWangClient;
use App\Services\User\UserService;
use App\Services\Waybill\CompanyService;
use App\Utils\Environment;
use App\Utils\OauthUtil;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Overtrue\Socialite\AccessToken;
use Overtrue\Socialite\Providers\AbstractProvider;
use Throwable;
use Illuminate\Support\Facades\Hash;

use InvalidArgumentException;

class UserController extends Controller
{

    /**
     * @var CompanyService $companyService
     */
    private $companyService;
    /**
     * @var UserService $userService
     */
    private $userService;

    public function __construct(CompanyService  $companyService)
    {

        $this->companyService = $companyService;
    }

    /**
     * 用户Oauth授权
     * @param Request $request
     * @param null $platform
     * @return JsonResponse
     * @throws BusinessException
     * @throws Throwable
     */
    public function oauth(Request $request, $platform = null)
    {
        $type = $request->input('platform', $platform);
        Log::info('oauth input data = ', $request->input());
        if (!empty(config('app.sso_url'))){
            $query = $request->input();
            $query['is_oss'] = 1;
            $url = config('app.sso_url') . '/user/oauth/' . $type . '?' . http_build_query($query);
            return $this->success($url, '302', 302);
        }
        $code = $request->input('code');
        $state = $request->input('state', 0);
        $authCode = $request->input('authCode', '');
        $authData = $request->input('authData', ''); // 授权后的数据 json 字符串
        // 是否是老的淘宝 top 授权方式
        $isTaobaoTopAuth = PlatformConst::TAOBAO == config('app.platform') && $request->has('top_parameters');

        if (!$isTaobaoTopAuth && !$code && !$authCode && empty($authData)) {
            Log::warning("platform Code = " . '应用尚未购买，请购买后再授权!');
            throw new BusinessException('应用尚未购买，请购买后再授权!');
        }

        Log::info("platform Code = " . $type);
        Log::info("state = " . $state);
        if (AuthServiceManager::checkPlatformValid($type) == false) {
            throw new InvalidArgumentException('无效的来源，请重试!');
        }

        if (config('app.platform') == PlatformConst::WX || config('app.platform') == PlatformConst::WXSP) {
            $authService = AuthServiceManager::create($type);
            $component = $authService->getComponentAccessToken();
            Log::info("component_access_token = " , [$component['component_access_token']]);
            //微信小商店后台点击使用
            if ($code) {
                $service = $authService->getService($code, $component['component_access_token']);
                Log::info("service = " , [$service]);

                $oauthUser = $authService->getOauthUser($component['component_access_token'], $service['data']['appid'] ?? $service['appid']);
                Log::info("oauthUser = " , [$oauthUser]);
                $oauthUser['service'] = $service['data'] ?? $service;

                list($user, $shop) = $authService->saveAuth($oauthUser);
            }

            //扫码授权判断一下是否有登陆授权以及订购
            if ($authCode) {
                //获取授权账号信息user、shop 拿到appid
                $authInfo = $authService->getQueryAuthInfo($authCode, $component['component_access_token']);
                Log::info("authInfo = " , [$authInfo]);
                if (empty($authInfo)) {
                    return $this->fail('请到微信小商店后台购买服务！');
                }
                //判断账号是否在微信小商店后台登陆过
//                $shop = Shop::query()->where('identifier', $authInfo['authorizer_appid'])->first();
                $shop = Shop::firstByIdentifier($authInfo['authorizer_appid']);
                if (empty($shop)) {
                    return $this->fail('首次登陆请到微信小商店后台购买使用！');
                }
                //订购时效
                $userExtra = UserExtra::query()->where('user_id', $shop->user_id)->first();
                if (strtotime($userExtra->expire_at) < time()) {
                    return $this->fail('服务已到期请重新到微信小商店后台购买！');
                }

                $user = User::query()->where('id', $shop->user_id)->first();
                //更新token
                $updateData = [
                    'access_token'  => $authInfo['authorizer_access_token'],
                    'refresh_token' => $authInfo['authorizer_refresh_token'],
                    'expire_at'     => date('Y-m-d H:i:s', time() + $authInfo['expires_in']),
                    'auth_at'       => date('Y-m-d H:i:s'),
                ];
                //选中店铺列表 给个默认的
                if (!$shop->shop_identifier) {
                    Shop::query()->where('identifier', (string)$authInfo['authorizer_appid'])->update([
                        'shop_identifier'  => $authInfo['authorizer_appid'],
                    ]);
                }
                Shop::query()->where('identifier', (string)$authInfo['authorizer_appid'])->update($updateData);

                $shop->access_token = $authInfo['authorizer_access_token'];
                $shop->refresh_token = $authInfo['authorizer_refresh_token'];
            }
        } else {
            if ($isTaobaoTopAuth){
                $oauthUser = $this->handleTaobaoTopAuth($request->input());
            }elseif (!empty($authData)){
                $authDataArr = json_decode($authData,true);
                $oauthUser =  socialite()->driver($type)->userByAuthData($authDataArr);
            }else{
                $oauthUser = socialite()->driver($type)->user();
            }
            $authService = AuthServiceManager::create($type);

            $whitelist = explode(',', config('app.new_site_whitelist', ''));
            Log::info('new_site_auth_url',[config('app.new_site_auth_url') . '?' . http_build_query([
                    'type' => $type,
                    'oauthUser' => serialize($oauthUser),
                ])]);
            if (Environment::isDy() && !empty(config('app.new_site_auth_url')) && in_array($oauthUser->getId(), $whitelist)){
                $url = config('app.new_site_auth_url') . '?' . http_build_query([
                        'type' => $type,
                        'oauthUser' => serialize($oauthUser),
                    ]);
                return $this->success($url, '302', 302);
            }

            list($user, $shop) = $authService->saveAuth($oauthUser);
        }

        // 存在绑定用户id
        $inviter = null;
        $openApiBind = 0;
        $isShowMessage= 0;
        $message= '';
        $openMsgSubscribe = true;
        if (!empty($state)) {
            // 换绑定授权
            $inviter = $this->getInviterUserId($state);
            if ($inviter) {
                Shop::changeBind($shop, $inviter);
            }else{
                $stateResult = $this->handleState($state, $shop, $openApiBind);
                $isShowMessage= $stateResult['isShowMessage']??0;
                $message= $stateResult['message']??'';
            }
            if ($this->isTurnOffSync($openApiBind)) {
                // openapi 淘宝默认不同步
                Shop::updateSyncSwitch($shop->id, Shop::SYNC_SWITCH_CLOSE);
            }
            if ($openApiBind){
                // 关闭消息订阅
                $openMsgSubscribe = false;
            }
        }

	    $orderService  = OrderServiceManager::create($type);
        Log::info("authShopInfo",["shop"=>$shop,"openApiBind"=>$openApiBind]);
        // 设置授权信息
        $orderService->setShop($shop);

        // 获取厂家代打角色
        if (PlatformConst::DY == $type && isFactory()){
            $shopRoleType = $orderService->sendFactoryShopRoleType();
            $shop->role_type = $shopRoleType;
            $shop->save();
        }

        //查询历史遗留未发货订单数量（10天前~30天内）
//        $beginTime = date('Y-m-d H:i:s', strtotime('-30 days'));
//        $endTime = date('Y-m-d H:i:s', strtotime('-10 days'));
//        $count = Order::query()
//            ->where([
//                ['shop_id', $shop->id],
////                ['user_id', $shop->user_id],
//                ['order_status',Order::ORDER_STATUS_PAYMENT],
//                ['refund_status', Order::REFUND_STATUS_NO],
//                ['pay_at', '>', $beginTime],
//                ['pay_at', '<', $endTime]
//            ])
//            ->count();
//        //是否要弹窗提醒
//        $isPop = false;
//        if ($count > Order::MAX_ORDER_NUM) {
//            $isPop = true;
//        } else {
//            //自动同步历史残留订单
//            $orderService->syncHistoryOrder($beginTime, $endTime);
//        }
        // 保存订购关系表
        $version = $orderService->saveUserEdition();
        // 保存绑定店铺的订购关系表
        $orderService->saveBindUserEdition();
        if ($openMsgSubscribe){
            // 开启消息订阅
            $orderService->openSubscribeMsg();
        }
        // 开启同步订单
        if(!$this->isTurnOffSync($openApiBind)) {
//            $orderService->syncOrderByAuth();
        }

        // 开启同步商品
//        $orderService->syncGoodsByAuth();
        // 推送订购记录
//        $orderService->pushPlatformOrder();
        //获取厂家绑定的商家列表
        $orderService->getSellerList();
//        $this->companyService->initCompanyInfo($shop->id);
        // 推送 token
        $shop_token_push_url = config('app.shop_push_url');
        if (!empty($shop_token_push_url)){
            dispatch(new ShopTokenPushJob($shop));
        }

	    if (env('TB_CLIENT_ID') == '21339677' && isset($version['pay_at']) && strtotime($version['pay_at']) < strtotime('2020-12-20 00:00:00'))
	    {
	    	$url = 'https://oauth.taobao.com/authorize?response_type=code&client_id=21339677&redirect_uri=http://hgb.kuaidixia.net/hgb-v8/oauth';
		    return $this->success($url, '302', 302);
	    }
        /*
        */

        //$orderService->syncGoodsByAuth();

        $payload = [
            'iss' => 'lumen-jwt',
            'sub' => $user->id,
            'iat' => time(),
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'plaftorm_type' => $shop->type,
            'shop_name' => $shop->name,
            'exp' => time() + env('JWT_EXPIRATION_TIME')
        ];
        $token = JWT::encode($payload, env('JWT_SECRET'));
        redis('cache')->setex('jwt_token:' . $payload['sub'], intval($payload['exp'] - $payload['iat']), $token);
        return $this->success([
            'token'      => $token,
            'token_type' => 'bearer',
//            'tobe_delivered_order' => [
//                'is_pop'     => $isPop,
//                'order_num'  => $count,
//                'end_time'   => $endTime,
//                'begin_time' => $beginTime,
//            ],
            'is_show_message'=>$isShowMessage,
            "message"=>$message,
            'is_show_shop_code' => 0, //不跳到店铺码页面
            'jd_pin' => $shop->identifier,
        ]);
    }

    public function oauthByOauthData(Request $request)
    {
        $platform = $request->input('type');
        $appDomain = config('app.app_domain');
        try {
            $url = $appDomain.'/callback?';
            $jsonResponse = $this->oauth($request, $platform);
            $arr = [
                'token' => $jsonResponse->getData()->data->token,
            ];
            return RedirectResponse::create($url . http_build_query($arr));
        }catch (\Exception $e){
            $url = $appDomain.'/msg?';
            $message = $e->getMessage();
            Log::error($message,['trace'=>$e->getTraceAsString()]);
            $arr = [
                'type' => 'error',
                'msg' => "授权失败，失败原因：{$message}",
            ];
            return RedirectResponse::create($url . http_build_query($arr));
        }
    }
    /**
     * 抖音站点迁移用，通过已取的用户信息登录
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws Throwable
     */
    public function oauthByOauthUser(Request $request)
    {
        $type = $request->input('type');
        $oauthUser = $request->input('oauthUser');
        $authService = AuthServiceManager::create($type);
        $oauthUser = unserialize($oauthUser);
        \Log::info('oauthByOauthUser',['type'=>$type,'oauthUser'=>$oauthUser]);
        list($user, $shop) = $authService->saveAuth($oauthUser);

        // 存在绑定用户id
        $openApiBind = 0;
        $isShowMessage= 0;
        $message= '';
        if (!empty($state)) {
            // 换绑定授权
            $inviter = $this->getInviterUserId($state);
            if ($inviter) {
                Shop::changeBind($shop, $inviter);
            }else{
                $stateResult = $this->handleState($state, $shop, $openApiBind);
                $isShowMessage= $stateResult['isShowMessage']??0;
                $message= $stateResult['message']??'';
            }
            if ($this->isTurnOffSync($openApiBind)) {
                // openapi 淘宝默认不同步
                Shop::updateSyncSwitch($shop->id, Shop::SYNC_SWITCH_CLOSE);
            }
        }

        $orderService  = OrderServiceManager::create($type);
        \Log::info("authShopInfo",["shop"=>$shop,"openApiBind"=>$openApiBind]);
        // 设置授权信息
        $orderService->setShop($shop);

        // 获取厂家代打角色
        if (PlatformConst::DY == $type && isFactory()){
            $shopRoleType = $orderService->sendFactoryShopRoleType();
            $shop->role_type = $shopRoleType;
            $shop->save();
        }
        // 保存订购关系表
        $version = $orderService->saveUserEdition();
        // 保存绑定店铺的订购关系表
        $orderService->saveBindUserEdition();
        // 开启消息订阅
        $orderService->openSubscribeMsg();
        // 开启同步订单
        if(!$this->isTurnOffSync($openApiBind)) {
//            $orderService->syncOrderByAuth();
        }

        // 开启同步商品
//        $orderService->syncGoodsByAuth();
        // 推送订购记录
//        $orderService->pushPlatformOrder();
        //获取厂家绑定的商家列表
        $orderService->getSellerList();
//        $this->companyService->initCompanyInfo($shop->id);
        // 推送 token
        $shop_token_push_url = config('app.shop_push_url');
        if (!empty($shop_token_push_url)){
            dispatch(new ShopTokenPushJob($shop));
        }

        if (env('TB_CLIENT_ID') == '21339677' && isset($version['pay_at']) && strtotime($version['pay_at']) < strtotime('2020-12-20 00:00:00'))
        {
            $url = 'https://oauth.taobao.com/authorize?response_type=code&client_id=21339677&redirect_uri=http://hgb.kuaidixia.net/hgb-v8/oauth';
            return $this->success($url, '302', 302);
        }
        /*
        */

        //$orderService->syncGoodsByAuth();

        $payload = [
            'iss' => 'lumen-jwt',
            'sub' => $user->id,
            'iat' => time(),
            'shop_id' => $shop->id,
            'identifier' => $shop->identifier,
            'plaftorm_type' => $shop->type,
            'shop_name' => $shop->name,
            'exp' => time() + env('JWT_EXPIRATION_TIME')
        ];
        $token = JWT::encode($payload, env('JWT_SECRET'));
        redis('cache')->setex('jwt_token:' . $payload['sub'], intval($payload['exp'] - $payload['iat']), $token);
        return $this->success([
            'token'      => $token,
            'token_type' => 'bearer',
            'is_show_message'=>$isShowMessage,
            "message"=>$message,
            'is_show_shop_code' => 0, //不跳到店铺码页面
            'jd_pin' => $shop->identifier,
        ]);
    }

    public function getOauth(Request $request, $platform = null)
    {
        $appDomain = config('app.app_domain');
        $url = $appDomain.'/msg?';
        try {
            $jsonResponse = $this->oauth($request, $platform);
            $message= $jsonResponse->getData()->data->message;
            $arr = [
                'type' => 'success',
                'msg' => $message,
            ];
            return RedirectResponse::create($url . http_build_query($arr));
        }catch (ApiException $ex){
            $redirectUrl = '';
            if (ErrorConst::SHOP_FREE_USER_NOT_SUPPORT[0] == $ex->getCode()){
                if (Environment::isDy()){
                    $redirectUrl = 'https://fuwu.jinritemai.com/detail?service_id='.config('app.service_id');
                }
            }
            $arr = [
                'type' => 'error',
                'msg' => "授权失败，失败原因：{$ex->getMessage()}",
            ];
            if (!empty($redirectUrl)){
                $arr['redirect'] = $redirectUrl;
                $arr['sleep'] = 8;
            }
            return RedirectResponse::create($url . http_build_query($arr));
        } catch (\Exception $ex){
            $arr = [
                'type' => 'error',
                'msg' => "授权失败，失败原因：{$ex->getMessage()}",
            ];
            return RedirectResponse::create($url . http_build_query($arr));
        }

    }

    public function me(Request $request)
    {
//        \Log::info('user me', [$request->auth]);
        $user = User::find($request->auth->user_id);

        $user->isShopSdk = false;
        $cloudWarehouseShop = (string)SystemConfig::getValue(SystemConfig::KEY_CLOUD_WAREHOUSE_SHOP);
        $cloudWarehouseShopArr = explode(',', $cloudWarehouseShop);
        if (!empty($request->auth->shop_id)) {
            $shop = Shop::find($request->auth->shop_id);
            $user->currentShop = $shop->identifier;
            $user->isCloudWarehouseShop = in_array($shop->identifier, $cloudWarehouseShopArr);
            $apiAuth = ApiAuth::query()->where('name', $shop->shop_name)->first();
            if (!empty($apiAuth)) {
                $user->isShopSdk = true;
            }

            $allRelationShops = ShopBind::getAllRelationShop($request->auth->shop_id);
            $user->shopList = $allRelationShops;
//            \Log::info("shopList", [$user->shopList]);
            $redis    = redis('cache');
            $redisKey = 'syncOrderByDataPush:lastTime';
            $lastTime = $redis->get($redisKey);
            $orderService = OrderServiceManager::create();
            $user->shopList = collect(array_map(function ($item) use ($lastTime, $shop, $allRelationShops, $orderService) {
                if ($item['id'] == $shop->id){ // 当前店铺
                    // 清理无效的 shop_identifier
                    $relationShopIdentifierArr = array_column($allRelationShops, 'identifier');
                    $relationShopIdentifierArr[] = $item['identifier'];
                    // 求合集
                    $array_intersect = array_intersect($relationShopIdentifierArr, explode(',', $item['shop_identifier']));
                    $implode_shop_identifier = implode(',', array_unique($array_intersect));

                    $item['shop_identifier'] = $implode_shop_identifier ?: $item['shop_identifier'];
                }
                // 判断是否要同步历史订单 0:不同步 1:未发货订单 2:全部订单
                $sync_history_order = 0;
                if ($item['auth_status'] == Shop::AUTH_STATUS_SUCCESS && strtotime($item['last_sync_at']) < strtotime('-30 minutes')) {
                    if (empty($item['last_sync_at'])) {
                        // 第一次只同步未发货订单
                        $sync_history_order = 1;
                    } else {
                        // 否则同步全部
                        $sync_history_order = 2;
                    }

                }
                $isLimit = OrderSyncService::isLimitSyncOrder4Shop($item['id']);
                $count = Order::countByUnshipped($item['id']);
                if ($isLimit && $count > Order::SYNC_ORDER_LIMIT) {
                    $sync_history_order = 0;
                }
                $item['sync_history_order'] = $sync_history_order;
                if (!empty($item['last_sync_at']) && !empty($lastTime)) {
                    // 最后更新时间取订单推送的时间
                    $item['last_sync_at'] = $lastTime;
                }
                $orderService->setShop($shop);
                try {
                    $checkAuthStatus = $orderService->checkAuthStatus();
                } catch (\Exception $e) {
                    $checkAuthStatus = false;
                }
                $item['checkAuthStatus'] = $checkAuthStatus;

                return $item;
            }, $user->shopList))->sortBy('sort')->values()->all();
            if ($shop->sync_switch > 0) {
                //关联店铺开启同步
                Shop::query()->whereIn('id', array_column($user->shopList,'id'))->update(['sync_switch'=>Shop::SYNC_SWITCH_OPEN]);
            }
        } else {
            $user->currentShop = null;
            $user->shopList = $user->shopBinds();
        }

        return $this->success($user);
    }

    public function checkAuth(Request $request)
    {
        $shopId = $request->auth->shop_id;
        $shopList = ShopBind::getAllRelationShop($shopId);

        $ret = [];
        foreach ($shopList as $item) {
            $shop = Shop::firstById($item['id']);
            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);
            try {
                $checkAuthStatus = $orderService->checkAuthStatus();
            } catch (\Exception $e) {
                $checkAuthStatus = false;
            }
            $userExtra = UserExtra::query()->where('user_id', $shop->user_id)->orderBy('expire_at', 'desc')->first();
            $ret[] = [
                'shop_id' => $shop->id,
                'auth_status' => $checkAuthStatus,
                'expired_at'  => $userExtra->expire_at ?? '',
                'shop_identifier' => $shop->identifier
            ];
        }

        return $this->success($ret);
    }

    public function getShops(Request $request)
    {
		$shopList = ShopBind::getAllRelationShop($request->auth->shop_id);

	    return $this->success($shopList);
    }

    public function getShopCode(Request $request)
    {
        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        $shop = Shop::query()->findOrFail($shopId);

        return $this->success(['shop_code' => $shop->shop_code]);
    }

    public function getAllBindShops(Request $request)
    {
        //我绑定的店铺
        $bindShops = ShopBind::query()
            ->where(['f_shop_id'=>$request->auth->shop_id])
            ->orWhere(['o_shop_id'=>$request->auth->shop_id])
            ->whereIn('type',ShopBind::SCENE_SHOP)
            ->get()
            ->toArray();

        $meBindIds = [];
        $bindMeIds = [];
        foreach ($bindShops as $value) {
            //我绑定的店铺
            if ($value['f_shop_id'] == $request->auth->shop_id) {
                $meBindIds[] = $value['o_shop_id'];
            }
            //绑定我的店铺
            if ($value['o_shop_id'] == $request->auth->shop_id) {
                $bindMeIds[] = $value['f_shop_id'];
            }
        }

        $fTypeArr = array_column($bindShops, 'type', 'f_shop_id');
        $oTypeArr = array_column($bindShops, 'type', 'o_shop_id');
        $meBindShops = Shop::query()->with(['shopGroup'])->whereIn('id', $meBindIds)->whereIn('type',ShopBind::SCENE_SHOP)->get()->toArray();
        if (!empty($meBindShops)) {
            foreach ($meBindShops as &$value){
                $type = $oTypeArr[$value['id']];
                if ($type == ShopBind::BIND_TYPE_BROTHER) {
                    $value['bind_type'] = ShopBind::TYPE_ME_BIND;
                } else {
                    $value['bind_type'] = ShopBind::TYPE_BIND_ME;
                }
            }
        }
        $bindMeShops = Shop::query()->with(['shopGroup'])->whereIn('id', $bindMeIds)->whereIn('type',ShopBind::SCENE_SHOP)->get()->toArray();
        if (!empty($bindMeShops)) {
            foreach ($bindMeShops as &$value) {
                $type = $fTypeArr[$value['id']];
                if ($type == ShopBind::BIND_TYPE_BROTHER) {
                    $value['bind_type'] = ShopBind::TYPE_ME_BIND;
                } else {
                    $value['bind_type'] = ShopBind::TYPE_BIND_ME;
                }
            }
        }

        // 合并去重
        $shopList = array_merge($meBindShops, $bindMeShops);
        if (!empty($shopList)) {
            $shopList = collect($shopList)->sortByDesc('bind_type')->unique('id')->values()->all();
        }
        //当前店铺
        $myShop = Shop::query()->with(['shopGroup'])->where('id', $request->auth->shop_id)->get()->toArray();
        $myShop[0]['bind_type'] = ShopBind::TYPE_MY_SHOP;

        return $this->success(array_merge($myShop, $shopList));
    }

    /**
     * 获取Oauth的Code的URL
     * @param Request $request
     * @param $platformCode
     * @return JsonResponse
     * @throws Throwable
     */
    public function getCodeUrl(Request $request, $platformCode)
    {
        \Log::info('platformCode', [$platformCode]);
        if (!AuthServiceManager::checkPlatformValid($platformCode)) {
            throw new InvalidArgumentException('无效的来源，请重试!');
        }
        $state=$request->input('state',"");
        $provider = socialite()->driver($platformCode);
        //如果请求参数里面state里面指定了redirectUrl就用这个
        if(($provider instanceof AbstractProvider)&&isset($state)) {
            $stateStd = OauthUtil::decodeState($state);
            $callbackUrl = $stateStd->callbackUrl??null;
            if(!empty($callbackUrl)) {
                \Log::info('callbackUrl', [$callbackUrl]);
                $codeUrl = $provider->redirect($stateStd->callbackUrl)->getTargetUrl();
            }else{
                $codeUrl = $provider->redirect()->getTargetUrl();
            }
        }else {
            $codeUrl = $provider->redirect()->getTargetUrl();
        }
        return $this->success([
            'codeUrl' => urldecode($codeUrl)
        ]);
    }


    public function changeShop(Request $request, $shopId)
    {
        \Log::info('changeShop', [$request->input()]);

//        $shop = Shop::query()->where('identifier', $shopId)->firstOrFail();
        $shop = Shop::firstByIdentifier($shopId);

        if ($shop) {
            $user = User::find($shop->user_id);
            $payload = [
                'iss' => 'lumen-jwt',
                'sub' => $user->id,
                'iat' => time(),
                'shop_id' => $shop->id,
                'identifier' => $shop->identifier,
                'plaftorm_type' => $shop->type,
                'shop_name' => $shop->name,
                'exp' => time() + env('JWT_EXPIRATION_TIME')
            ];
            $token = JWT::encode($payload, env('JWT_SECRET'));
            // redis('cache')->setex('jwt_token:' . $payload['sub'], $payload['exp'] - $payload['iat'], $token);
            return $this->success([
                'token' => $token,
                'token_type' => 'bearer',
                'shopId' => $shopId
            ], '店铺切换成功!', 200, ['Authorization' => 'Bearer ' . $token]); // 重置Authorization
        }
        return $this->success();
    }

    public function changePwd(Request $request)
    {
        \Log::info('changePwd', [$request->input()]);

        $user = User::find($request->auth->user_id);
        if ($user) {
            $code_phone = MobileVerifyCode::query()->where('mobile', $user->phone)->first();
            if ($code_phone->code_expire >= time() && $code_phone->code == $request->input('code')) {
                $user->password = Hash::make($request->input('password'));
                $user->save();

                return $this->success($user->password);
            }
        }
        return $this->fail();
    }

    public function changePhone(Request $request)
    {
        \Log::info('changePhone', [$request->input()]);

        $user = User::find($request->auth->user_id);
        if ($user) {
            MobileVerifyCode::where('mobile', $user->phone)->delete();

            $user->phone = $request->input('newPhone');
            $user->save();

            MobileVerifyCode::where('mobile', $request->input('newPhone'))->delete();

            if (!empty($request->auth->shop_id)) {
                $shop = Shop::find($request->auth->shop_id);
                $user->currentShop = $shop->identifier;
                $user->shopList = $user->shops;
            } else {
                $user->currentShop = null;
                $user->shopList = [];
            }

            return $this->success($user);
        }
        return $this->fail(false);
    }

    public function auth(Request $request)
    {
//        \Log::info('auth', [$request->input()]);

        $type = env('CURRENT_PLATFORM', 'ks');
        $phone = $request->input('username');
        $password = $request->input('password');

        $userQuery = User::query()->where('phone', $phone);
        $user = $userQuery->first();
        if (empty($user)) {
            return $this->fail('用户不存在！');
        }
        $shop = Shop::query()->where('user_id', $user->id)->first();
        $sql  = getSqlByQuery($userQuery);
        event(new SqlLogEvent($user, $shop, time(), $sql));

        if ($user && !Hash::check($password, $user->password)) {
            event((new UserLoginEvent($user, $shop, time(), UserLoginEvent::LOGIN_STATUS_FAIL, '密码有误！'))->setClientInfoByRequest($request));
            return $this->fail('账户名或密码错误！');
        }

        if ($user && Hash::check($password, $user->password)) {
            // 成功的事件记录在前端回调成功接口上 @see loginSuccess
//            event((new UserLoginEvent($user, $shop, time(),UserLoginEvent::LOGIN_STATUS_SUCCESS))->setClientInfoByRequest($request));
//            if (!empty($shop) && PlatformConst::DY == config('app.platform')  && $shop->access_token && config('app.env') == 'production') {
//                $dyOrderImpl = new DyOrderImpl();
//                $dyOrderImpl->setShop($shop);
//                $canLogin = $dyOrderImpl->checkAntispamUserLogin($user->id);
//                if (!$canLogin) {
//                    throw new ApiException(ErrorConst::PLATFORM_ANTISPAM);
//                }
//            }
            return $this->userAuth($user->id, $shop, $type, $user);
        }

    }

    /**
     * 登录成功
     * <AUTHOR>
     * @param Request $request
     */
    public function loginSuccess(Request $request)
    {
        $user = User::find($request->auth->user_id);
        $shop = Shop::find($request->auth->shop_id);
        event((new UserLoginEvent($user, $shop, time(), UserLoginEvent::LOGIN_STATUS_SUCCESS))->setClientInfoByRequest($request));
        return $this->success();
    }

    public function ksLogin(Request $request)
    {
        \Log::info('ksLogin', [$request->input()]);

        $type = config('app.platform', 'ks');
        $shopId = $request->input('shopId');

//        $shop = Shop::query()->where('identifier', $shopId)->first();
        $shop = Shop::firstByIdentifier($shopId);
        if ($shop)
            return $this->userAuth($shop->user_id, $shop, $type);
        else
            return $this->fail('无效的来源！');
    }

    private function getInviterUserId($invite_code)
    {
        $first = User::query()->where('invite_code', $invite_code)->first();
        return $first->id ?? 0;
    }

    private function userAuth($user_id, $shop, $type,$user = null)
    {
        $orderService = OrderServiceManager::create($type);
        if (!empty($shop)) {

            // 设置授权信息
            $orderService->setShop($shop);
            // 保存订购关系表
            $orderService->saveUserEdition();
            // 保存绑定店铺的订购关系表
            $orderService->saveBindUserEdition();
            // 开启消息订阅
            $orderService->openSubscribeMsg();
            // 开启同步订单
            //$orderService->syncOrderByAuth();
            // 开启同步商品
            //$orderService->syncGoodsByAuth();
            //获取厂家绑定的商家列表
            $orderService->getSellerList();
        }

        $payload = [
            'iss' => 'lumen-jwt',
            'sub' => $user_id,
            'iat' => time(),
            'shop_id' => $shop ? $shop->id : null,
            'identifier' => $shop ? $shop->identifier : null,
            'plaftorm_type' => $type,
            'shop_name' => $shop->name,
            'exp' => time() + env('JWT_EXPIRATION_TIME')
        ];
        $token = JWT::encode($payload, env('JWT_SECRET'));
        redis('cache')->setex('jwt_token:' . $payload['sub'], intval($payload['exp'] - $payload['iat']), $token);
        $arr = [
            'token' => $token,
            'token_type' => 'bearer',
        ];
        // 切换新站点
        $whitelist = explode(',', config('app.new_site_whitelist', ''));
        \Log::info('new_site_auth_url',[config('app.new_site_auth_url') . '?' . http_build_query(['token' => $token,])]);
        if (Environment::isDy() && !empty(config('app.new_site_auth_url')) && in_array($shop->identifier, $whitelist)){
            $url = config('app.new_site_auth_url') . '?' . http_build_query(['token' => $token]);
//            return redirect($url);
            return $this->success($url, '302', 302);
        }
        if ($user) {
            $arr['jd_pin'] = md5($user->phone);
        }
        return $this->success($arr);
    }

//    /**
//     * 绑定授权
//     * @param Request $request
//     * @return JsonResponse
//     * <AUTHOR>
//     */
//    public function changeAuth(Request $request)
//    {
//        $data = $this->validate($request, [
//            'invite_code' => 'required|string',
//            'type'        => 'required|int'
//        ]);
//
//        $shop = Shop::find($request->auth->shop_id);
//        //不能传入当前店铺的shopCode
//        if ($shop->shop_code == trim($data['invite_code'])) {
//            return $this->fail('不可绑定当前店铺', 400);
//        }
//        $requestShop = Shop::query()->where('shop_code', trim($data['invite_code']))->first()->toArray();
//        if (empty($requestShop)) {
//            return $this->fail('邀请码有误', 400);
//        }
//        $res = ShopBind::bindShop($request->auth->shop_id, $requestShop['id'], $data['type']);
//
//        if (!$res) {
//            return $this->fail('绑定失败', 400);
//        }
//
//        return $this->success();
//    }

    public function reGenInviteCode(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        \Log::info('reGenInviteCode', [$request->input()]);

        $shop = Shop::query()->findOrFail($shopId);
        $shop->shop_code = createShopCode();
        $res = $shop->save();

        if (!$res) {
            return $this->fail('保存失败');
        }

        return $this->success();
    }

    public function findInvitor(Request $request)
    {
        \Log::info('findInvitor', [$request->input()]);

        $data = $this->validate($request,[
            'invite_code' => 'required|string'
        ]);
        // 换绑定授权
        $inviter = $this->getInviterUserId(trim($data['invite_code']));
        if (empty($inviter)){
            return $this->fail('邀请码有误');
        }

        // $user = User::find($request->auth->user_id);
        $shop = Shop::find($request->auth->shop_id);
        if ($shop && $shop->user_id != $inviter)
        {
            $old_user = User::find($shop->user_id);
            $new_user = User::find($inviter);
            if ($old_user->phone) {
                return $this->success(false, 'Hi, '.$old_user->phone.',是否接受【'.$new_user->phone . '】的邀请?');
            }
        }
        return $this->success(true);
    }

	public function unbindShop(Request $request, $shopId)
	{
		if ($request->auth->shop_id == $shopId) {
			return $this->fail('不能删除当前店铺', 400);
		}

		ShopBind::unbindShop($request->auth->shop_id, $shopId);

		return $this->success();
    }

    /**
     * 修改绑定店铺信息
     * @param Request $request
     * @return JsonResponse
     */
    public function editBindShop(Request $request)
    {
        $data = $this->validate($request,[
            'id' => 'required|int',
            'group_id'  => 'int',
        ]);
        $id = $data['id'];

        $bindShopIdArr = ShopBind::getAllRelationShopIds($request->auth->shop_id);

        if (!in_array($id, $bindShopIdArr)) {
            return $this->fail('店铺不存在!', 400);
        }
        $model = Shop::query()->find($id);
        if (empty($model)){
            return $this->fail('店铺不存在', 400);
        }
        $model->update($data);

        return $this->success();
    }

    /**
     *   仅仅为快手搬家应用回传access token raw data 所用
     *
     */
    public function getAccessToken(Request $request)
    {
	    \Log::info('getAccessToken', [$request->input()]);

        $data = $this->validate($request, [
        	'shopId' => 'required|string',
        	'token'  => 'required|string',
        ]);

        if (!isJson($data['token'])) {
        	return $this->fail('token格式错误', '400');
        }

	    $token   = json_decode($data['token']);
	    $type    = config('app.platform', 'ks');
	    $shop_id = $data['shopId'];

        $authData = [
            'type' => PlatformConst::PLATFORM_TYPE_KS,
            'identifier' => $shop_id,
            'access_token' => $token->access_token,
            'refresh_token' => $token->refresh_token,
            'expire_at' => date('Y-m-d H:i:s', time() + $token->expires_in),
            'auth_at' => date('Y-m-d H:i:s'),
            'shop_name' => $shop_id,
            'name' => $shop_id,
            'auth_user_id' => $token->open_id,
        ];

        if (AuthServiceManager::checkPlatformValid($type) == false) {
            throw new InvalidArgumentException('无效的来源，请重试!');
        }

        $authService = AuthServiceManager::create($type);
        list($user, $shop) = $authService->saveAuthData($authData);

        $orderService = OrderServiceManager::create($type);
        // 设置授权信息
        $orderService->setShop($shop);
        // 保存订购关系表
        $orderService->saveUserEdition();
        // 开启消息订阅
        $orderService->openSubscribeMsg();
        // 开启同步订单
//        $orderService->syncOrderByAuth();
        // 开启同步商品
        //$orderService->syncGoodsByAuth();
        return $this->success();
    }


    public function getBranchShopList(Request $request)
    {
        return $this->success();
        /*$result = $shopIdList =  [];
        //查询自由订单表上级id为本店铺的并去重
        $data = CustomizeOrder::query()
            ->where('p_shop_id', $request->auth->shop_id)
            ->select('shop_id')
            ->distinct()
            ->get()
            ->toArray();
        if (!empty($data)) {
            foreach ($data as $shop) {
                $shopIdList[] = $shop['shop_id'];
            }
        }

        if (!empty($shopIdList)) {
            $shopList = Shop::query()->whereIn('id', $shopIdList)->get();
            foreach ($shopList as $shop){
                $result[$shop['id']] = [
                    'shop_id' => $shop['id'],
                    'shop_name' => $shop['shop_name']
                ];
            }
        }

        return $this->success($result);*/
    }

    /**
     * 保存订购信息
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function saveUserEdition(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'shop_identifier' => 'required',
        ]);
        $shopId = $data['shop_identifier'];
//        $shop = Shop::query()->where('identifier', $shopId)->firstOrFail();
        $shop = Shop::firstByIdentifier($shopId);
        $orderService = OrderServiceManager::create(config('app.platform'));
        // 设置授权信息
        $orderService->setShop($shop);
        // 保存订购关系表
        $orderService->saveUserEdition();

        return $this->success();
    }

    /**
     * 处理兼容老版本的淘宝 top 授权
     * @param array $input
     * @return \Overtrue\Socialite\User
     * @throws ApiException
     * <AUTHOR>
     */
    private function handleTaobaoTopAuth(array $input)
    {
        $top_appkey = $input['top_appkey'];
        $top_session = $input['top_session'];
        $top_parameters = $input['top_parameters'];
//        $top_parameters = base64_decode($top_parameters);
        $top_sign = $input['top_sign'];
        $secret = config('taobaotop.connections.app.app_secret');

//        dd($top_appkey , $top_parameters , $top_session , $secret);
        // 取16位 md5
        $sign = base64_encode(md5($top_appkey . $top_parameters . $top_session . $secret, true));
        if ($top_sign != $sign) {
            throw new ApiException(ErrorConst::SIGN_ERROR);
        }
        $top_parameters = base64_decode($top_parameters);
        parse_str($top_parameters,$parameters);

        $socialiteUser = new \Overtrue\Socialite\User([
            'id' => $parameters['visitor_id'],
            'nickname' => $parameters['visitor_nick'],
            'username' => $parameters['visitor_nick'],
            'name' => $parameters['visitor_nick'],
            'avatar' => '',
        ]);
        $token = [
            'access_token' => $top_session,
            'refresh_token' => $parameters['refresh_token'],
        ];
        $socialiteUser->setToken(new AccessToken($token));
        $parameters['expire_time'] = intval($parameters['ts'] / 1000) + $parameters['expires_in'];
        $socialiteUser->merge($parameters);
        return $socialiteUser;
    }

    /**
     * 处理state
     * 1.根据不同的type，进行不同的处理
     * 2.礼品网店铺绑定的
     *
     * @param $state
     * @param $shop
     * @param $openBind
     * @return array{"isShowMessage":bool,"message":string}
     * @throws ApiException
     * <AUTHOR>
     */
    protected function handleState($state, $shop, &$openBind): array
    {
        // 判断免费用户
        $version = UserExtra::getVersionByShopId($shop->id);
        if (Environment::isDy() && in_array($version, [UserExtra::VERSION_FREE])) {
            throw new ApiException(ErrorConst::SHOP_FREE_USER_NOT_SUPPORT);
        }
        /**
         * 是否显示消息
         */
        $isShowMessage=0;
        $message='授权成功，请关闭当前页面，返回上级页面继续操作!';
        $stateArr = json_decode(base64_decode($state), true);

        if (isset($stateArr['type'])) {
            switch ($stateArr['type']) {
                case AuthStateTypeConst::OPEN_BIND_SHOP:
                    $isShowMessage=1;
                    $redisKey = 'auth_code:' . $stateArr['code'];
                    $res = redis('cache')->get($redisKey);
                    if (empty($res)) {
                        $message=ErrorConst::STATE_EXPIRED[1];
                        break;
                    }
                    $resArr = json_decode($res,true);
                    $appId = $resArr['app_id']??null;
                    $model = ApiShopBind::updateOrCreateByAppIdShopId($appId, $shop->id);
                    $redisKey = 'open_auth_shop_id_by_code:' . $stateArr['code'];
                    redis('cache')->setex($redisKey, 600, $shop->id);
                    if (!empty($model)) {
                        $openBind = 1;
                    }

                    break;
                case AuthStateTypeConst::OPEN_BIND_WAYBILL_SHOP:
                    $isShowMessage=1;
                    $redisKey = 'auth_code:' . $stateArr['code'];
                    $res = redis('cache')->get($redisKey);
                    if (empty($res)) {
                        $message=ErrorConst::STATE_EXPIRED[1];
                        break;
                    }
                    $resArr = json_decode($res,true);
                    $appId = $resArr['app_id']??null;
                    $count = ApiShopBind::countByAppId($appId, ApiShopBind::IS_WAYBILL_SHOP_YES,$shop->id);
                    $apiAuth = ApiAuth::firstByAppId($appId);
                    $model = null;
                    Log::info('绑定电子面单网店数量限制',[$appId,$count,$apiAuth['bind_waybill_shop_limit']]);
                    if ($count < $apiAuth['bind_waybill_shop_limit']) {
                        $model = ApiShopBind::updateOrCreateByAppIdShopId($appId, $shop->id,1);
                        $redisKey = 'open_auth_shop_id_by_code:' . $stateArr['code'];
                        redis('cache')->setex($redisKey, 600, $shop->id);
                    }else{
                        $message='绑定失败，绑定的电子面单网店数量已达上限!';
                    }
                    if (!empty($model)) {
                        $openBind = 1;
                    }

                    break;
            }
        }
        return ["isShowMessage"=>$isShowMessage,"message"=>"<h1>".$message."</h1>"];
    }

    /**
     * @param int $openApiBind
     * @return bool
     */
    public function isTurnOffSync(int $openApiBind): bool
    {
        return $openApiBind == 1 and Environment::isDefaultTurnOffSync4Api();
    }

    /**
     * 从新站点同步 token 过来
     * @param Request $request
     */
    public function syncTokenFromNew(Request $request)
    {
        if (config('app.name') != '{dy-shandian}'){
            return $this->fail('非法请求');
        }
        $token = $request->header('Authorization');
        $token = str_replace('Bearer ', '', $token);
        $client = new Client();
        $response = $client->get('https://sddy-new.mayiapps.cn/api/user/me', [
            'headers' => [
                'Authorization' => 'Bearer ' . $token
            ]
        ]);
        $contents = $response->getBody()->getContents();
        \Log::info('syncTokenFromNew', [$contents]);
        $contents = json_decode($contents, true);
        foreach ($contents['data']['shopList'] as $index => $datum) {
            if(empty($datum['access_token'])){
                continue;
            }
            Shop::query()->where('id', $datum['id'])->update([
                'access_token' => $datum['access_token'],
                'expire_at' => $datum['expire_at'],
            ]);
            \Log::info('syncTokenFromNew:update', ['shop_id' => $datum['id'], 'access_token' => $datum['access_token'], 'expire_at' => $datum['expire_at']]);
        }
        return $this->success();
    }

    /**
     * 注销
     * 1.删除token
     * 2.删除session
     * @param Request $request
     * @return JsonResponse|RedirectResponse|\Laravel\Lumen\Http\Redirector
     */
    function logout(Request $request)
    {

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/login');



    }

    /**
     * 通过 open_user_id 登录
     * @param Request $request
     * @return JsonResponse
     */
    public function loginByOpenUser(Request $request)
    {
        $requestData = $this->validate($request, [
            'open_user_id' => 'required|string',
            'open_token' => 'required|string',
        ]);

        $arr = $this->userService->loginByOpenUser($requestData);
        return $this->success($arr);
    }


}
