<?php

namespace App\Http\Controllers\Waybill;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Jobs\Task\ExportTaskJob;
use App\Models\Company;
use App\Models\ExportTask;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\WaybillHistory;
use App\Models\WaybillHistoryStatistic;
use App\Models\WaybillShareAction;
use App\Services\BusinessException;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WaybillShareActionController extends Controller
{

    public function addShareCount(Request $request,$id)
    {
		$data = $this->validate($request, [
			'count'   => 'required|string',
		]);

        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        $company=Company::where([
                'companies.id'=>$id,
            ])->join('shops', 'companies.shop_id', '=', 'shops.id')->select('companies.*','shops.shop_name','shops.name','shops.identifier')->first();
        $count=intval($data['count']);
        $result = Company::where('id',$id)->increment('quantity',$count);
       if($result){
            //分享单号记录追加
            $ret=WaybillShareAction::create([
                'user_id'    => $request->auth->user_id,
                'shop_id'    => $shopId,
                'shop_name'  => $company->shop_name,
                'name'       => $company->name,
                'company_id' => $company->id,
                'identifier' => $company->identifier,
                'branch_name'=> $company->branch_name,
                'wp_code'    => $company->wp_code,
                'wp_name'    => $company->wp_name,
                'balanceLimit' => $count,
                'province'   => $company->province,
                'city'       => $company->city,
                'district'   => $company->district,
                'detail'     => $company->detail,
                'action'     => WaybillShareAction::ACTION_STATUS_ADD,
            ]);
        }
        return $this->success($result);
    }

    /**
     * @throws ApiException
     */
    public function removeShareCount(Request $request, $id)
   {
        $data = $this->validate($request, [
            'count'   => 'required|string',
        ]);

        $company=Company::where([
            'companies.id'=>$id,
        ])->join('shops', 'companies.shop_id', '=', 'shops.id')->select('companies.*','shops.shop_name','shops.name','shops.identifier')->first();

        $count=intval($data['count']);
        $quantity=intval($company->quantity);
        if ( $count > $quantity) {
            throw new ApiException(ErrorConst::NUMBER_CANT_NEGATIVE_NUMBER);
//            throw new \Exception('单号数量不能相减成负数');
        }
        $result = Company::where('id',$id)->decrement('quantity',$count);
        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        if($result){
            //分享单号记录追加
            $ret=WaybillShareAction::create([
                'user_id'    => $request->auth->user_id,
                'shop_id'    => $shopId,
                'shop_name'  => $company->shop_name,
                'name'       => $company->name,
                'company_id' => $company->id,
                'identifier' => $company->identifier,
                'branch_name'=> $company->branch_name,
                'wp_code'    => $company->wp_code,
                'wp_name'    => $company->wp_name,
                'balanceLimit' => $count,
                'province'   => $company->province,
                'city'       => $company->city,
                'district'   => $company->district,
                'detail'     => $company->detail,
                'action'     => WaybillShareAction::ACTION_STATUS_REMOVE,
            ]);
        }
        return $this->success($result);
   }

    public function updateShare(Request $request,$id)
    {
        $data = $this->validate($request, [
            'action'   => 'required|int', //2 删除,1 停用,0 恢复
        ]);
        $shopId = intval($request->input('shopId', $request->auth->shop_id));

        $company=Company::where([
            'companies.id'=>$id,
        ])->join('shops', 'companies.shop_id', '=', 'shops.id')->select('companies.*','shops.shop_name','shops.name','shops.identifier')->first();

        $action =intval($data['action']);
        if ($action==WaybillShareAction::ACTION_STATUS_DEL){
          //删除网点表,模板表
          $result=Company::query()->findOrFail($id);
          $result->templates()->delete();
          $result->delete();
          if (!$result) {
            return $this->fail('操作失败！');
          }
        }else{
            if ($action == 0){ // 店铺绑定的情况下，阻止恢复
                $bindShopIds = ShopBind::getAllRelationShopIds($shopId);
                if (in_array($company->shop_id, $bindShopIds)) {
                    return $this->fail('不能共享给已绑定的店铺');
                }
            }
           $result = Company::where(['id'  => $id])->update(['source_status'=> $action]);
        }
        if($result){
            //分享单号记录追加
            $ret=WaybillShareAction::create([
                'user_id'    => $request->auth->user_id,
                'shop_id'    => $shopId,
                'shop_name'  => $company->shop_name,
                'name'       => $company->name,
                'company_id' => $company->id,
                'identifier' => $company->identifier,
                'branch_name'=> $company->branch_name,
                'wp_code'    => $company->wp_code,
                'wp_name'    => $company->wp_name,
                'province'   => $company->province,
                'city'       => $company->city,
                'district'   => $company->district,
                'detail'     => $company->detail,
                'action'     => $action,
            ]);
        }
        return $this->success($result);
    }


  //查询分享单号列表
    public function queryWaybillAccountPage(Request $request): \Illuminate\Http\JsonResponse
    {
        $this->validate($request, [
            'status' => 'string',
            "sort" => 'string',
            "wpCode" => 'string',
            'shareMallName' => 'string',
            'queryType' => 'string|in:toMe,toOthers',
        ]);
//        $shopName = trim($request->input('shopName', ''));
//        $sourceShopName = trim($request->input('sourceShopName', ''));
        $condition = [];
        $condition[] = ['source', Company::SOURCE_COMPANY_STATUS_YES];
        $status = $request->input('status', '0');
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 20);
        $orderBy = $request->input('sort', "id desc");
        $keyword = trim($request->input('shareMallName', ''));
        $wpCode = $request->input('wpCode');
        $queryType = $request->input('queryType','toOthers');
        $shopIdArr = [intval($request->input('shopId', $request->auth->shop_id))];
        if ($status >= 0) {
            $condition[] = ['source_status', $status];
        }

        list($rowsFound, $ret) = Company::search($condition, $keyword, $offset, $limit, $orderBy, $wpCode, $shopIdArr, $queryType);

        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $offset,
            'limit' => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

  //单号分享设置记录
   public function queryWaybillShareLog(Request $request)
   {
       $this->validate($request, [
            'status'       => 'string',
            "sort"         => 'string',
            "wpCode"       => 'string',
        ]);
        $condition   = [];
        $condition[] = ['shop_id', intval($request->input('shopId', $request->auth->shop_id))];
        $status  = $request->input('status', '0');
        $wpCode  = $request->input('wpCode', '');
        $page  = (int)$request->input('pageNum', 1);
        $pageSize   = (int)$request->input('pageSize', 20);
        $orderBy = $request->input('sort', "created_at,desc");
//        if ($request->input('wpCode')) {
//            $condition[] = ['wp_code', $request->input('wpCode')];
//        }
        if ($status >= 0) {
            $condition[] = ['action', $status];
        }
        $offset = ($page - 1) * $pageSize;
        $keyword = trim($request->input('shareMallName', ''));
        Log::info('查询分享单号记录',["offset"=>$offset,"limit"=>$pageSize]);
        $ret=WaybillShareAction::search($condition, $keyword, $wpCode, $offset, $pageSize, $orderBy);
        if ($keyword) {
            $rowsFound = WaybillShareAction::query()->where($condition)->where(function ($query) use ($keyword) {
            $query->where('shop_name', $keyword)
                  ->orWhere('name', $keyword)
                  ->orWhere('identifier', $keyword);
            })->count();
         } else {
             $rowsFound = WaybillShareAction::where($condition)->count();
         }
        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $pageSize
        ];
       return $this->success(['pagination' => $pagination,$ret]);
   }

   //分享单号明细
   public function queryShareWaybillDetail(Request $request)
   {
        $this->validate($request, [
            "beginTime"       =>'required|date',
            "endTime"         =>'required|date',
            'sourceStatus'    => 'string',
            'waybillStatus'   => 'string',
            "quickSearch"     => 'string',
            "wpCode"          => 'string',
            "shareMallName"   => 'string',
            "waybillCodeArr"  => 'array',
        ]);

        $wpCode  = $request->input('wpCode', '');
        $status  = $request->input('status', '-1');
        $sourceStatus  = $request->input('sourceStatus', '-1');
        $offset  = (int)$request->input('offset', 0);
        $limit   = (int)$request->input('limit', 20);
        $orderBy = $request->input('sort', "created_at,desc");
        $waybillCodeArr = $request->input('waybillCodeArr', []);
        $shareMallName = $request->input('shareMallName', '');
        //身份切换，分享者创建为 1和 被分享者 0
        $condition   = [];
        $shop_id = intval($request->input('shopId', $request->auth->shop_id));
        if ($sourceStatus == 0) {
            $condition[] = ['waybill_histories.shop_id', $shop_id];
            $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];
        }else if($sourceStatus > 0){
            $condition[] = ['source_shopid', $shop_id];
            $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];
        }else{
            $condition[] = ['source_shopid', $shop_id];
            $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];
        }

//        if ($request->input('wpCode')) {
//            $condition[] = ['wp_code', $request->input('wpCode')];
//        }

        if ($status >=0) {
            $condition[] = ['waybill_status', $status];
        }

        if ($request->input('beginTime')) {
            $condition[] = ['waybill_histories.created_at', '>=', $request->input('beginTime')];
        }
        if ($request->input('endTime')) {
            $condition[] = ['waybill_histories.created_at', '<=', $request->input('endTime')];
        }

        $keyword = trim($request->input('quickSearch', ''));
        list($ret, $rowsFound) = WaybillHistory::shareWaybillsearch($condition, $keyword, $wpCode, $offset, $limit,
            $orderBy, $waybillCodeArr, $shareMallName);

//        if ($keyword) {
//            $rowsFound = WaybillHistory::query()->where($condition)->where(function ($query) use ($keyword) {
//            $query->where('waybill_code', $keyword)
//                  ->orWhere('name', $keyword)
//                  ->orWhere('shop_name', $keyword)
//                  ->orWhere('identifier', $keyword);
//            })->join('shops', 'waybill_histories.shop_id', '=', 'shops.id')->select('waybill_histories.*','shops.shop_name','shops.name','shops.identifier')->count();
//        } else {
//            $rowsFound = WaybillHistory::where($condition)->count();
//        }

        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $limit
        ];
       return $this->success(['pagination' => $pagination,$ret]);
   }

   //单号分享统计
   public function statisticWaybillOld(Request $request)
   {
        $this->validate($request, [
            "beginTime"       =>'required|date',
            "endTime"         =>'required|date',
            "quickSearch"     =>'string',
        ]);

        $condition   = [];
        $condition[] = ['source_shopid', $request->auth->shop_id];
        $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];

        if ($request->input('beginTime')) {
            $condition[] = ['waybill_histories.created_at', '>=', $request->input('beginTime')];
        }

        if ($request->input('endTime')) {
            $condition[] = ['waybill_histories.created_at', '<=', $request->input('endTime')];
        }
        $keyword = trim($request->input('quickSearch', ''));
        $offset  = (int)$request->input('offset', 0);
        $limit   = (int)$request->input('limit', 20);
        $ret=WaybillHistory::shareWaybillStatistic($condition, $keyword, $offset, $limit);
        if ($keyword) {
            $res = WaybillHistory::query()->where($condition)->where(function ($query) use ($keyword) {
            $query->where('name', $keyword)
                  ->orWhere('shop_name', $keyword)
                  ->orWhere('identifier', $keyword);
            })->join('shops', 'waybill_histories.shop_id', '=', 'shops.id')
              ->select('shops.shop_name','shops.name','shops.identifier')
              ->selectRaw('DATE_FORMAT(waybill_histories.created_at,"%Y-%m-%d") as day')
              ->selectRaw('COUNT(*) as count')
              ->groupBy('day','shop_id')
              ->get();
        } else {
        $res=WaybillHistory::where($condition)->join('shops', 'waybill_histories.shop_id', '=', 'shops.id')
              ->select('shops.shop_name','shops.name','shops.identifier')
              ->selectRaw('DATE_FORMAT(waybill_histories.created_at,"%Y-%m-%d") as day')
              ->selectRaw('COUNT(*) as count')
              ->groupBy('day','shop_id')
              ->get();
        }
        $rowsFound = collect($res)->count();
        $pagination = [
                'rows_found' => $rowsFound,
                'offset'     => $offset,
                'limit'      => $limit
        ];
        return $this->success(['pagination' => $pagination,$ret]);
   }

    //单号分享统计
    public function statisticWaybill(Request $request) {
        $this->validate($request, [
            "offset"   => 'int',
            "limit"    => 'int',
        ]);
        $offset     = (int)$request->input('offset', 0);
        $limit      = (int)$request->input('limit', 20);
        $sourceShopId = intval($request->input('shopId', $request->auth->shop_id));
        $dateAt       = $request->input('dateAt');
        $keyword      = trim($request->input('quickSearch', ''));

        $ret = WaybillHistoryStatistic::search($sourceShopId, $dateAt, $keyword, $offset, $limit);

        $pagination = [
            'rows_found' => collect($ret)->count(),
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    /**
     * @throws BusinessException
     */
    public function showWaybillcodeCount(Request $request)
  {
        $this->validate($request, [
            "id"       =>'required|int',
        ]);
        $id= $request->input('id','');
        $company = Company::query()->find($id);
        if ($company) {
            $data = [];
            $thisMonthBeginDate = Carbon::now()->startOfMonth();
            $thisMonthEndDate = Carbon::now()->endOfMonth();
            // 本月数据
            $thisMonthQuery = WaybillHistory::query()->where([
                'waybill_histories.shop_id' => $company->shop_id,
                'waybill_histories.source' => $company->source
            ])->where('created_at', ">=",$thisMonthBeginDate)->where('created_at',"<=",$thisMonthEndDate)->groupBy(['waybill_code']);
            Log::info('thisMonthQuery',[ $thisMonthQuery->toSql(),$thisMonthQuery->getBindings()]);
            $data['current_month'] = DB::query()->fromSub($thisMonthQuery, 'q1')->count();

            $lastMonthBeginDate = Carbon::now()->subMonth()->startOfMonth();
            // 上月数据
            $lastMonthQuery = WaybillHistory::query()->where([
                'waybill_histories.shop_id' => $company->shop_id,
                'waybill_histories.source' => $company->source
            ])->where('created_at', "<", $thisMonthBeginDate)->where('created_at', ">=", $lastMonthBeginDate)->groupBy(['waybill_code']);
            $data['last_month'] = DB::query()->fromSub($lastMonthQuery, 'q1')->count();
            Log::info('lastMonthQuery',[ $lastMonthQuery->toSql(),$lastMonthQuery->getBindings()]);
            return $this->success([$data]);
        }else{
            throw new BusinessException('读取网点失败');
        }
  }

    public function export(Request $request)
    {
        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        $redis    = redis('cache');
        $lockIdentity = uniqid();
        $redisKeyPrefix = 'export_lock:';
        $redisKey = $redisKeyPrefix . $shopId;
        $bool = $redis->set($redisKey, $lockIdentity, 'nx', 'ex' , 60);
        if (!$bool) {
            return $this->success([], '', 201);
        }
        //组装条件
        $this->validate($request, [
            "beginTime"       =>'required|date',
            "endTime"         =>'required|date',
            'sourceStatus'    => 'string',
            'waybillStatus'   => 'string',
            "quickSearch"     => 'string',
            "wpCode"          => 'string',
            "shareMallName"   => 'string',
        ]);

        $wpCode  = $request->input('wpCode', '');
        $status  = $request->input('status', '-1');
        $shareMallName = $request->input('shareMallName', '');
        $sourceStatus  = $request->input('sourceStatus', '-1');
        $orderBy = $request->input('sort', "created_at,desc");
        //身份切换，分享者创建为 1和 被分享者 0
        $condition   = [];
        if ($sourceStatus == 0) {
            $condition[] = ['waybill_histories.shop_id', $shopId];
            $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];
        }else if($sourceStatus > 0){
            $condition[] = ['source_shopid', $shopId];
            $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];
        }else{
            $condition[] = ['source_shopid', $shopId];
            $condition[] = ['source', WaybillHistory::WAYBILL_SOURCE_YES];
        }

        if ($status >=0) {
            $condition[] = ['waybill_status', $status];
        }

        if ($request->input('beginTime')) {
            $condition[] = ['waybill_histories.created_at', '>=', $request->input('beginTime')];
        }
        if ($request->input('endTime')) {
            $condition[] = ['waybill_histories.created_at', '<=', $request->input('endTime')];
        }
        if ($shareMallName){
            $shop = Shop::query()->where('shop_name', $shareMallName)->first();
            if (empty($shop)){
                // 店铺不存在
                throw new ApiException(ErrorConst::SHOP_NOT_EXIST);
            }
            $condition[] = ['waybill_histories.shop_id', $shop->id];
        }

        $keyword = trim($request->input('quickSearch', ''));
        $params = [
            'condition' => $condition,
            'keyword'   => $keyword,
            'order_by'  => $orderBy,
            'wp_code'   => $wpCode,
        ];

        $userId = $request->auth->user_id;
        $res = ExportTask::create([
            'user_id' => $userId,
            'shop_id' => $shopId,
            'type'    => ExportTask::SHARE_WAYBILL_LIST,
            'condition' => json_encode($params),
            'status'    => ExportTask::STATUS_WAITING,
            'url'       => env('APP_DOMAIN') . '/api/waybill_share/export_file?id=',
        ]);

        $this->dispatch(new ExportTaskJob($shopId, $userId, $res));
        return $this->success();
    }

    public function exportList(Request $request)
    {
        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        $userId = $request->auth->user_id;

        $result = ExportTask::query()
            ->where(['user_id'=>$userId, 'shop_id'=>$shopId, 'type'=>ExportTask::SHARE_WAYBILL_LIST])
            ->orderBy('id', 'desc')
            ->offset(0)
            ->limit(10)
            ->get();

        return $this->success($result);
    }

    public function exportFile(Request $request)
    {
        $id = $request->input('id');
        $taskInfo = ExportTask::query()->where('id', $id)->first();
        $filePath = $taskInfo->file_path;
        $fileName = $name = basename($filePath);
        return response()->download($filePath, $fileName, ['Content-Type'=>'application/vnd.ms-excel']);
    }
}
