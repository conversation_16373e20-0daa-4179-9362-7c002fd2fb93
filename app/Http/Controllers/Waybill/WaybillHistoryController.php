<?php

namespace App\Http\Controllers\Waybill;

use App\Constants\ExportConst;
use App\Exceptions\ApiException;
use App\Exceptions\OrderException;
use App\Http\Controllers\Controller;
use App\Jobs\Task\ExportTaskJob;
use App\Models\ExportTask;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Services\Order\OrderWaybillService;
use App\Services\PrintRecord\PrintRecordSearchService;
use App\Services\WaybillHistory\Request\WaybillHistorySearchRequest;
use App\Services\WaybillHistory\WaybillHistoryPackageQueryService;
use App\Services\WaybillHistory\WaybillHistoryQueryService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 取号日志Controller
 */
class WaybillHistoryController extends Controller
{
    /**
     * @var WaybillHistoryQueryService $waybillHistoryQueryService
     */
    private $waybillHistoryQueryService;

    /**
     *
     * @var WaybillHistoryPackageQueryService $waybillHistoryPackageQueryService
     */
    private $waybillHistoryPackageQueryService;
    private $printRecordSearchService;

    public function __construct(WaybillHistoryQueryService $waybillHistoryQueryService,
                                WaybillHistoryPackageQueryService $waybillHistoryPackageQueryService,
                                PrintRecordSearchService $printRecordSearchService)
    {
        $this->waybillHistoryQueryService = $waybillHistoryQueryService;
        $this->waybillHistoryPackageQueryService = $waybillHistoryPackageQueryService;
        $this->printRecordSearchService = $printRecordSearchService;
    }


    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     * 2022-1-25查询条件去掉了user_id
     * 取号记录列表
     */
    public function index(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "ownerIdList" => 'array',
            "keyword" => 'string',
            "wp_code" => 'string',
            "waybill_status" => 'string',
            "begin_at" => 'date',
            "end_at" => 'string',
            "batch_no" => 'string',
            "order_type" => 'int',
            "soft_remark" => 'string',
            "mode"=>'int'
        ]);
        /**
         * mode==2 云仓模式
         */
        $mode = $request->input('mode', "1");
        Log::info('查询参数',["data"=>$data,"mode"=>$mode]);
        $waybillHistorySearchRequest = $this->getWaybillHistorySearchRequest($request);

        //把全部的店铺都查出来，电子面单账号通过模板去过滤
        $ownerIdList = $request->input('ownerIdList', []);
        if(!empty($ownerIdList)) {
            $ownerIdList = Shop::shopIdsByidentifier($ownerIdList);
        }
        $waybillHistorySearchRequest->setOwnerIdList($ownerIdList);
        if (in_array($mode, ["2", "3"])) {
            Log::info("包裹模式查询");
            list($ret, $rowsFound) = $this->waybillHistoryPackageQueryService->search($waybillHistorySearchRequest);
            $ret = $ret->toArray();
            foreach ($ret as $index => $item) {
                $arr = collect($item['print_records'])->sortByDesc('id')->toArray();
                $ret[$index]['print_records'] = $this->printRecordSearchService->handleMerge($arr);
                $ret[$index]['print_data'] = '';
            }
        }
        else {
            list($ret, $rowsFound) = $this->waybillHistoryQueryService->search($waybillHistorySearchRequest);
        }
        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $waybillHistorySearchRequest->getOffset(),
            'limit' => $waybillHistorySearchRequest->getLimit(),
        ];

        //$ret = $this->handleMerge($ret);
//        $ret = WaybillHistoryQueryService::handleMerge($ret);
        return $this->success(['pagination' => $pagination, 'hidePrivacy' => true, $ret]);
    }

    private function handleMerge($ret)
    {
        if (empty($ret)) {
            return $ret;
        }
        $groupArr = collect($ret)->groupBy('waybill_code');

        $list = [];
        //单号相同的merge到一起
        foreach ($groupArr as $key => $groupItem) {
            //第一个作为主订单
            $tidArr = $idArr = $orderItems = $sellerMemo = $buyerMessage = $goodsTitle = [];
            $tidStr = '';
            foreach ($groupItem as $k => $item) {
                $idArr[] = $item['id'];
                $tidArr[] = str_replace('A', '', $item['order_no']);

                if (empty($tidStr)) {
                    $tidStr = str_replace('A', '', $item['order_no']);
                } else {
                    $tidStr .= ',' . str_replace('A', '', $item['order_no']);
                }
                foreach ($item->orderItem as $orderItem) {
                    $orderItems[] = $orderItem;
                }

                //卖家备注
                if ($item->order && $item->order['seller_memo'] != '[]') {
                    $tmpSellerMemo = json_decode($item->order['seller_memo'], true);
                    $sellerMemo[] = $tmpSellerMemo[0];
                }
                //买家留言
                if ($item->order && $item->order['buyer_message']) {
                    $buyerMessage[] = $item->order['buyer_message'];
                }
            }
            $list[$key] = $groupItem[0];
            $list[$key]['merge_order_item'] = $orderItems;
            $list[$key]['seller_memo_arr'] = $sellerMemo;
            $list[$key]['buyer_message_arr'] = $buyerMessage;
            $list[$key]['orderNoArr'] = $tidArr;
            $list[$key]['orderNoStr'] = $tidStr;
            $list[$key]['idArr'] = $idArr;
        }

        return array_values($list);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws ApiException
     */
    public function recovery(Request $request): JsonResponse
    {
        $this->validate($request, [
//            "ids" => "required|array",
            "waybillCodes"=>"required|array",
        ]);
        $bindShopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        $shops = Shop::query()->findMany($bindShopIds);
        $shopIds = collect($shops)->pluck('id')->toArray();
        if (empty($shopIds)) {
            $shopIds = [$request->auth->shop_id];
        }
        $waybillCodes = $request->input('waybillCodes', []);
        $ret = OrderWaybillService::batchRecoveryByWaybillCode($waybillCodes,$shopIds, $request->auth->user_id, $request->auth->shop_id);


        return $this->success($ret);
    }







    /**
     * 统计面单
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function statistics(Request $request)
    {
        $data = $this->validate($request, [
            "wp_code" => 'string',
            "begin_at" => 'date',
            "end_at" => 'string',
            "batch_no" => 'string',
        ]);
        $shopId = $request->auth->shop_id;
        $beginAt = $request->input('begin_at');
        $endAt = $request->input('end_at');
        $wpCode = $request->input('wp_code');
        $orderBy = $request->input('order_by', "waybill_histories.id desc");
        $ownerIdList = $request->input('ownerIdList', []);
        $toShopIdList = $request->input('shopIdList', []);
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $mode = $request->input('mode', 1);


        $waybillHistorySearchRequest = $this->getWaybillHistorySearchRequest($request);
        $ownerIdList = $request->input('ownerIdList', []);
        if(empty($ownerIdList)){
//            $ownerIdList = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        }else{
            $ownerIdList= Shop::shopIdsByidentifier($ownerIdList);
        }
        $waybillHistorySearchRequest->setOwnerIdList($ownerIdList);
        if($mode==1) {
            $ret = $this->waybillHistoryQueryService->statistics($waybillHistorySearchRequest);
        }else{
            $ret = $this->waybillHistoryPackageQueryService->statistics($waybillHistorySearchRequest);
        }


        return $this->success($ret);
    }

    /**
     * 异步文件导出
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws OrderException
     */
    public function asyncExport(Request $request){
        $exportTaskJob = $this->export($request, ExportConst::ASYNC_EXPORT);

        $this->dispatch($exportTaskJob);
        return   $this->success();
    }

    /**
     * 浏览器直接导出
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws OrderException
     */
    public function browserDownload(Request $request){
        $exportTaskJob=$this->export($request,ExportConst::BROWSER_DOWNLOAD);
        return $exportTaskJob->handle();
    }

    /**
     * @throws OrderException
     * @throws ValidationException
     */
    public function export(Request $request,int $exportType): ExportTaskJob
    {
        $redisKeyPrefix = 'export_lock:';
        /**
         * mode==2 云仓模式
         */
        $mode = $request->input('mode', 1);
        $orderShopName=$request->input('orderShopName','');
        Log::info("导出任务数量 请求参数" ,["orderShopName"=>$orderShopName,"mode"=>$mode]);

        $data = $this->validate($request, [
            "ownerIdList" => 'array',
            "keyword" => 'string',
            "wp_code" => 'string',
            "waybill_status" => 'string',
            "begin_at" => 'date',
            "end_at" => 'string',
            "batch_no" => 'string',
        ]);
        list($shopIdList, $condition, $waybillHistorySearchRequest) = $this->buildExportParams($request, $data);
        $shopId = $request->auth->shop_id;
        $userId = $request->auth->user_id;
        if($mode==1) {
            list($ret, $count) = $this->waybillHistoryQueryService->search($waybillHistorySearchRequest);
            $type = ExportTask::WAYBILL_HISTORY;
        }else{
            list($ret, $count) = $this->waybillHistoryPackageQueryService->search($waybillHistorySearchRequest);
            $type = ExportTask::WAYBILL_HISTORY_PACKAGE;
        }
        $name = ExportTask::buildExportTaskName($shopIdList,"");
        if(ExportConst::isBrowserDownload($exportType)){
            $name=urlencode($name);
        }
        Log::info("导出任务数量 创建任务" ,["name"=>$name,"type"=>$type,"waybillHistorySearchRequest"=>$waybillHistorySearchRequest]);



        if(ExportConst::isBrowserDownload($exportType)){
            $res =[
                'user_id' => $userId,
                'shop_id' => $shopId,
                'type' => $type,
                'condition' => json_encode($condition),
                'name' => $name
            ];
        }else {
            $res = ExportTask::create([
                'user_id' => $userId,
                'shop_id' => $shopId,
                'type' => $type,
                'condition' => json_encode($condition),
                'status' => ExportTask::STATUS_WAITING,
                'url' => env('APP_DOMAIN') . '/api/waybill_history/export_file?id=',
                'name' => $name
            ]);
        }
        return new ExportTaskJob($shopId, $userId, $res,$name,$exportType);
    }

    public function exportRetry(Request $request){
        $taskId=$request->input('taskId');
        $task=ExportTask::find($taskId);
        if(!$task){
            return $this->fail('任务不存在');
        }
        if($task->status==ExportTask::STATUS_SUCCESS){
            return $this->fail('任务已完成');
        }
        $shopId = $request->auth->shop_id;
        $userId = $request->auth->user_id;
        $name = $task->name;
        $exportTaskJbo=new ExportTaskJob($shopId, $userId, $task,$name,ExportConst::ASYNC_EXPORT);
        $this->dispatch($exportTaskJbo);
        return   $this->success();

    }



    private function getWaybillHistorySearchRequest(Request $request): WaybillHistorySearchRequest
    {
        $waybillHistorySearchRequest = new WaybillHistorySearchRequest();
        $condition = [];
        $keyword = $request->input('keyword');
        if (empty($keyword) && $request->input('begin_at') && $request->input('end_at')) {
            $condition[] = ['waybill_histories.created_at', '>=', $request->input('begin_at')];
            $condition[] = ['waybill_histories.created_at', '<=', $request->input('end_at')];
        }
        $shopIdList = $this->getShopIdList($request);

        $waybillHistorySearchRequest->setWaybillStatus($request->input('waybill_status', -1));
        $waybillHistorySearchRequest->setWpCode($request->input('wp_code'));
        $waybillHistorySearchRequest->setBatchNo($request->input('batch_no'));
//        $waybillHistorySearchRequest->setOwnerIdList($request->input('ownerIdList', []));
        $waybillHistorySearchRequest->setPrintMode($request->input('printMode', 1));
        $waybillHistorySearchRequest->setKeyword(trim($request->input('keyword', '')));
        $waybillHistorySearchRequest->setOffset((int)$request->input('offset', 0));
        $waybillHistorySearchRequest->setLimit((int)$request->input('limit', 20));
        $waybillHistorySearchRequest->setTemplateIds($request->input('template_ids', []));
        $waybillHistorySearchRequest->setOrderBy($request->input('order_by', "waybill_histories.id desc"));
        $waybillHistorySearchRequest->setShopIdList($shopIdList);
        $waybillHistorySearchRequest->setWaybillCodeList($request->input('waybillCodeList', []));
        $waybillHistorySearchRequest->setTidList($request->input('tidList', []));
        $waybillHistorySearchRequest->setOrderType($request->input('order_type'));
        $waybillHistorySearchRequest->setMode($request->input('mode'));
        $waybillHistorySearchRequest->setSoftRemark($request->input('soft_remark'));
        $waybillHistorySearchRequest->setCondition($condition);
        return $waybillHistorySearchRequest;
    }


    /**
     * @param Request $request
     * @param array $data
     * @return array
     */
    public function buildExportParams(Request $request, array $data): array
    {
        $orderBy = $request->input('order_by', "waybill_histories.id desc");
        $keyword = trim($request->input('keyword', ''));
        $printMode = $request->input('printMode', 1);
        $batchNo = $request->input('batch_no', '');
        $ownerIdList = $request->input('ownerIdList', []);
        if(empty($ownerIdList)) {
//            $ownerIdList = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        }else{
            $ownerIdList= Shop::shopIdsByidentifier($ownerIdList);
        }

        $wpCode = $request->input('wp_code', '');
        $waybillStatus = $request->input('waybill_status', -1);
        $shopIdList = $this->getShopIdList($request);
        $begin_at = $data['begin_at'] ?? '';
        $end_at = $data['end_at'] ?? '';
        $templateIds = $request->input('template_ids', []);
        $orderType = $request->input('order_type', null);
        $condition = [
            'waybill_status' => $waybillStatus,
            'wp_code' => $wpCode,
            'keyword' => $keyword,
            'order_by' => $orderBy,
            'ownerIdList' => $ownerIdList,
            'printMode' => $printMode,
            'batch_no' => $batchNo,
            'template_ids' => $templateIds,
            'begin_at' => $begin_at,
            'order_type' => $orderType,
            'end_at' => $end_at,
            'shopIdList' => $shopIdList,
        ];
        $waybillHistorySearchRequest = new WaybillHistorySearchRequest();
        if(empty($keyword)&!empty($begin_at) && !empty($end_at)){
            $waybillHistorySearchRequest->setCondition([['waybill_histories.created_at', '>=', $begin_at], ['waybill_histories.created_at', '<=', $end_at]]);
        }
        $waybillHistorySearchRequest->setOrderBy($orderBy);
        $waybillHistorySearchRequest->setLimit(500);
        $waybillHistorySearchRequest->setTemplateIds($templateIds);
        $waybillHistorySearchRequest->setKeyword($keyword);
        $waybillHistorySearchRequest->setPrintMode($printMode);
        $waybillHistorySearchRequest->setOwnerIdList($ownerIdList);
        $waybillHistorySearchRequest->setBatchNo($batchNo);
        $waybillHistorySearchRequest->setWpCode($wpCode);
        $waybillHistorySearchRequest->setOrderType($orderType);
        $waybillHistorySearchRequest->setWaybillStatus($waybillStatus);
        $waybillHistorySearchRequest->setShopIdList($shopIdList);
//        $waybillHistorySearchRequest->setCondition($condition);
        return array($shopIdList, $condition, $waybillHistorySearchRequest);
    }
}
