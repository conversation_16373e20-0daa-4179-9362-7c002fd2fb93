<?php

namespace App\Http\Controllers\Alipay;

use App\Constants\PaymentConst;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Services\Payment\PaymentCallbackRequest;
use App\Services\Payment\PaymentService;
use App\Services\PlatformOrder\SelfPlatformOrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Yansongda\Pay\Exceptions\InvalidConfigException;
use Yansongda\Pay\Exceptions\InvalidSignException;
use Yansongda\Pay\Pay;
use Yansongda\Supports\Collection;

class CallbackController extends Controller
{
    /**
     * @var PaymentService $paymentService
     */
    protected $paymentService;

    /**
     * @param PaymentService $paymentService
     */
    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * 支付宝支付接口的异步回
     * @param Request $request
     * @return Response
     * @throws InvalidConfigException
     * @throws InvalidSignException
     * @see https://opendocs.alipay.com/open/270/105902?pathHash=d5cd617e
     * // 1、商户需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号；
     * // 请自行对 trade_status 进行判断及其它逻辑进行判断，在支付宝的业务通知中，只有交易通知状态为 TRADE_SUCCESS 或 TRADE_FINISHED 时，支付宝才会认定为买家付款成功。
     * // 1、商户需要验证该通知数据中的out_trade_no是否为商户系统中创建的订单号；
     * // 2、判断total_amount是否确实为该订单的实际金额（即商户订单创建时的金额）；
     * // 3、校验通知中的seller_id（或者seller_email) 是否为out_trade_no这笔单据的对应的操作方（有的时候，一个商户可能有多个seller_id/seller_email）；
     * // 4、验证app_id是否为该商户本身。
     * // 5、其它业务逻辑情况
     * v
     */

    public function notify(Request $request): Response
    {
        $alipay = Pay::alipay(PaymentService::alipayConfig());
        Log::info("支付宝回调", [$request->all()]);
        $data =PaymentService::isDev()?$request->all(): $alipay->verify();
        if ($data['trade_status'] == 'TRADE_SUCCESS') {
            // 订单支付成功
            $outTradeNo = $data['out_trade_no'];
            // 查询支付宝订单详情获取一些回调接口里面没有的参数

            $totalAmount = $data['total_amount'];
            $orderAmount = $totalAmount;
            $tradeNo = $data['trade_no'];
            $appId = $data['auth_app_id'];
            $sellerId = $data['seller_id'];
            $buyerId = $data['buyer_open_id'];
            $patAt=$data['gmt_payment'];
            $paymentCallbackRequest = new PaymentCallbackRequest();
            $paymentCallbackRequest->outTradeNo = $outTradeNo;
            $paymentCallbackRequest->actualAmount = $totalAmount;
            $paymentCallbackRequest->orderAmount = $orderAmount;
            $paymentCallbackRequest->fee = bcsub($totalAmount, $orderAmount, 2);
            $paymentCallbackRequest->payType = PaymentConst::PAYMENT_TYPE_ALIPAY;
            $paymentCallbackRequest->tradeNo = $tradeNo;
            $paymentCallbackRequest->appId = $appId;
            $paymentCallbackRequest->mchid = $sellerId;
            $paymentCallbackRequest->buyerId = $buyerId;
            $paymentCallbackRequest->payAt=$patAt;
            $paymentCallbackRequest->subject = $data['subject'];
//            $paymentCallbackRequest->

            $saved = $this->paymentService->payPlatformOrderSuccess($paymentCallbackRequest);
            Log::info("支付宝回调", ["success" => $saved]);

            return $alipay->success()->send();

        } else {
            Log::info("支付宝回调", ["fail" => $data]);
            return $alipay->success()->send();
        }
    }

    /**
     * 支付宝支付成功以后的回跳页面，用于前端展示支付结果，不是异步支付
     * @param Request $request
     * @return Response
     */
    public function result(Request $request): Response
    {
        $alipay = Pay::alipay(PaymentService::alipayConfig());
        $outTradeNo = $request->input('out_trade_no');
        // 查询支付宝订单详情获取一些回调接口里面没有的参数

        $totalAmount = $request->input('total_amount');
        $tradeNo = $request->input('trade_no');
        $appId = $request->input('app_id');
        $sellerId = $request->input('seller_id');
        $buyerId = $request->input('buyer_id');
        $data = [
            "outTradeNo" => $outTradeNo,
            "totalAmount" => $totalAmount,
            "tradeNo" => $tradeNo,
            "appId" => $appId,
            "sellerId" => $sellerId,
            "buyerId" => $buyerId,
        ];
        return $this->success($data);

    }
}
