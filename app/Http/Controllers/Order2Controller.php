<?php

namespace App\Http\Controllers;

use App\Models\Fix\Order;
use App\Models\Package;
use App\Services\Order\Order2Service;
use App\Services\Order\OrderQueryBuilder;
use App\Services\Order\Request\OrderSearchRequest;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class Order2Controller extends Controller
{
    private $order2Service;

    /**
     * @param $order2Service Order2Service 订单服务
     */
    public function __construct(Order2Service $order2Service)
    {
        $this->order2Service = $order2Service;
    }

    /**
     * 手动拆单
     * @param Request $request
     * @return JsonResponse
     */
    public function packageManager(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'packages' => 'required|array',
        ]);
        $res = $this->order2Service->packageManager($data['packages'], $request->auth->shop_id);
        $this->order2Service->sendEvent(array_merge($data['packages']), $request, 'merge');
        return $this->success($res);
    }

    /**
     * 统计可合并
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function countMergable(Request $request)
    {
        $data = $this->validate($request, [
            'receiver_phone_idx_arr' => 'required|array',
            'order_status' => 'array',
            'print_status' => 'array',
            'refund_status' => 'array',
        ]);
        $res = $this->order2Service->countMergable($data, $request->auth->shop_id);
        return $this->success($res);
    }

    /**
     * 查询可合并订单
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function getMergableList(Request $request)
    {
        $data = $this->validate($request, [
            'receiver_phone_idx' => 'required|string',
            'order_status' => 'array',
            'print_status' => 'array',
            'refund_status' => 'array',
            'order_shop_id' => 'required|string',
        ]);
        $res = $this->order2Service->getMergableInfo($data, $request->auth->shop_id);
        return $this->success($res);
    }

    /**
     * 已发货列表
     * @param Request $request
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\ErrorCodeException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function shippedList(Request $request)
    {
        $res = $this->order2Service->shippedList($request);
        return $this->success($res);
    }
    /**
     * 已发货列表V2
     * @param Request $request
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\ErrorCodeException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function shippedListV2(Request $request)
    {
        $res = $this->order2Service->shippedListV2($request);
        return $this->success($res);
    }
    /**
     * 已发货列表V2 count
     * @param Request $request
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\ErrorCodeException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function shippedListV2Count(Request $request)
    {
        $res = $this->order2Service->shippedListV2Count($request);
        return $this->success($res);
    }

    /**
     * 重新发货列表
     * @return JsonResponse
     */
    public function resendList(Request $request)
    {
        $validate = $this->validate($request, [
            'ownerIdList' => 'required|array',
            'limit' => 'required|integer',
            'offset' => 'required|integer',
        ]);
        list($pagination, $res) = $this->order2Service->resendList($request,$validate);
        return $this->success(['pagination' => $pagination, $res]);
    }
    /**
     * 重新发货列表
     * @return JsonResponse
     */
    public function resendListCount(Request $request)
    {
        $validate = $this->validate($request, [
            'ownerIdList' => 'required|array',
            'limit' => 'required|integer',
            'offset' => 'required|integer',
        ]);
        $res = $this->order2Service->resendListCount($request, $validate);
        return $this->success($res);
    }

    /**
     * 已发货列表的count
     * @param Request $request
     * @return array
     * @throws \App\Exceptions\ApiException
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function shippedListCount(Request $request)
    {
        $orderSearchRequest = new OrderSearchRequest();
        $orderSearchRequest->validate($request);
        $orderSearchRequest->authShopId = $request->auth->shop_id;
        $orderQueryBuilder = new OrderQueryBuilder();
        $orderQueryBuilder->buildQuery($orderSearchRequest);
        $total = $orderQueryBuilder->getCount();

        // 统计各个发货类型
        $delivery_type_count = [];
        $arr = array_merge(Package::DELIVERY_TYPE_ARRAY, [Package::DELIVERY_TYPE_EXTERNAL]); // 88 外部发货
        foreach ($arr as $value) {
            $orderSearchRequest->deliveryType = $value;
            $orderQueryBuilder->buildQuery($orderSearchRequest);
            $count = $orderQueryBuilder->getCount();
            $delivery_type_count[] = [
                'delivery_type' => $value,
                'count' => $count,
            ];
        }

        return ['total' => $total, 'delivery_type_count' => $delivery_type_count];
    }
    /**
     * 预发货列表
     */
    public function preShipmentList(Request $request)
    {
        $res = $this->order2Service->preShipmentList($request);
        return $this->success($res);
    }
    /**
     * 异常订单列表
     * @param Request $request
     * @return JsonResponse
     */
    public function abnormalOrderListV2(Request $request)
    {
        $data = $this->order2Service->abnormalOrderListV2($request);

        return $this->success($data);
    }

    /**
     * 拆分包裹列表
     * @param Request $request
     * @return JsonResponse
     */
    public function splitPackageList(Request $request)
    {
        $data = $this->validate($request, [
            'orderIdArr' => 'required|array',
        ]);
        $orderIdArr = array_get($data, 'orderIdArr');

        $resp = $this->order2Service->splitPackageList($request->auth->shop_id,$orderIdArr);
        return $this->success($resp);
    }

    /**
     * 达人列表
     * @param Request $request
     * @return JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function authorList(Request $request)
    {
        $data = $this->validate($request, [
            'ownerIdList' => 'required|array',
        ]);

        $resp = $this->order2Service->authorList($data);
        return $this->success($resp);
    }

    /**
     * 直播打印订单列表
     */
    public function livePrintOrderList(Request $request)
    {
        $data = $this->validate($request, [
            'timeField' => 'required|string|in:pay_at,print_tag_at,created_at',
            'startTime' => 'required|date',
            'endTime' => 'required|date',
            'ownerIdList' => 'required|array',
            'isPrint' => 'integer|nullable',
            'isAuto' => 'integer|nullable',
            'page' => 'integer',
            'pageSize' => 'integer',
            'tidList' => 'array',
            'status' => 'integer',
            'goodsTitle' => 'string|nullable',
            'buyerNick' => 'string|nullable',
            'authorId' => 'integer|nullable',
            'authorName' => 'string|nullable',
            'refundStatus' => 'integer|nullable',
            'blacklistStatus' => 'integer|nullable',
        ]);

        $shop_id = $request->auth->shop_id;

        $resp = $this->order2Service->livePrintOrderList($data, $shop_id);
        return $this->success($resp);
    }

    /**
     * 直播打印完成
     */
    public function livePrintOrderComplete(Request $request)
    {
        $data = $this->validate($request, [
            'orderItemIdArr' => 'required|array',
            'ownerIdList' => 'required|array',
        ]);
        $orderItemIdArr = array_get($data, 'orderItemIdArr');
        $ownerIdList = array_get($data, 'ownerIdList');
        $shops = \App\Models\Shop::query()->whereIn('identifier', $ownerIdList)->get();
        $shopIds = $shops->pluck('id')->toArray();
        $query = \App\Models\Fix\OrderItem::query();
        $query->whereIn('shop_id', $shopIds);
        $query->whereIn('id', $orderItemIdArr);
        $resp = $query->update(['print_tag_at' => date('Y-m-d H:i:s')]);
        if (!$resp) {
            return $this->fail('更新失败');
        }
        return $this->success();
    }

    /**
     * 批量设置订单昵称
     * @param Request $request
     * @return JsonResponse|void
     * @throws ValidationException
     * <AUTHOR>
     */
    public function batchSetOrderNickname(Request $request)
    {
        $data = $this->validate($request, [
            'token' => 'required|string',
            'list' => 'required|array',
            'list.*.order_id' => 'required|string',
            'list.*.buyer_nick' => 'required|string',
        ]);
        $apiToken = env('API_TOKEN', '');
        if (empty($apiToken) || $apiToken != $data['token']) {
            return $this->fail('Token验证失败');
        }
        foreach ($data['list'] as $index => $datum) {
            $orderId = tidAddA($datum['order_id']);
            $bool = Order::query()->where('tid', $orderId)->update(['buyer_nick' => $datum['buyer_nick']]);
            if (!$bool) {
                return $this->fail('更新失败:'.$datum['order_id']);
            }
        }
        return $this->success();

    }
}
