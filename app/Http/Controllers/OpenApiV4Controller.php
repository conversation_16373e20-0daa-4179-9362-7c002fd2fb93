<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/10/12
 * Time: 18:50
 */

namespace App\Http\Controllers;

use App\Constants\AuthStateTypeConst;
use App\Constants\PlatformConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\StatusCode\StatusCode;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Models\Waybill;
use App\Services\OpenApiV3Service;
use App\Services\OpenApiV4Service;
use App\Services\Order\OrderServiceManager;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class OpenApiV4Controller extends Controller
{
    /**
     * @var OpenApiV4Service
     */
    protected $service;


    public function __construct(OpenApiV4Service $v4Service)
    {
        $this->service = $v4Service;
    }

    /**
     * 获取授权链接
     * <AUTHOR>
     */
    public function genOauthUrl(Request $request)
    {
        $this->validate($request, [
            'appId' => 'required|integer',
            'isBindWaybillShop' => 'integer',
        ]);
        $isBindWaybillShop = (int)$request->input('isBindWaybillShop', 0);
        list($url, $state) = $this->service->genOauthUrl($isBindWaybillShop);
        return $this->successForOpenApi(['url' => $url, 'state' => $state]);
    }

    /**
     * 检查签名
     * @param Request $request
     * @return JsonResponse|\Illuminate\Http\Response|\Laravel\Lumen\Http\ResponseFactory
     * @throws ValidationException
     * <AUTHOR>
     */
    public function checkSign(Request $request)
    {
        $this->validate($request, [
            "appId" => "required|string",
//            "sign" => "required|string",
            "appKey" => "required|string",
            "bizJson" => "required|string",
        ]);
        $appId = $request->get('appId');
        $appKey = $request->get('appKey', '');
        $timestamp = $request->get('timestamp', '');
        $biz_json = $request->input('bizJson', '');
        $step = [];
        $step[] = '1. 获取bizJson参数：' . $biz_json;
        $str = "$appId&$timestamp&$appKey@$biz_json";
        $step[] = '----------------------------------------------------------';
        $step[] = '2. 参数拼接后：' . $str;
        $tmpSign = encryptApiSignV4($appId, $appKey, $timestamp, $biz_json);
        $step[] = '----------------------------------------------------------';
        $step[] = '3. md5 后：sign=' . $tmpSign;
//        $bool = decryptApiSignV3($originParams, $app_key);
//        $step[] = "8. 请求的 sign={$originParams['sign']},后端的 sign=" . $sign2;

        $result = [
//            'isPassed' => $bool,
            'step' => $step,
        ];
        return response(implode(PHP_EOL, $step));
    }

    /**
     * 获取店铺信息
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     * <AUTHOR>
     */
    public function getShopInfo(Request $request)
    {
        $this->validate($request, [
            'shopCode' => 'string',
            'shopName'=>'string',
            'state' => 'string',
        ]);

        $shopCode = $request->input('shopCode', '');
        $state = $request->input('state', '');
        $shopName = $request->input('shopName', '');
        $data = $this->service->getShopInfo($shopCode, $state,$shopName);
        return $this->successForOpenApi($data);
    }

    /**
     * 通用请求
     * @param  Request  $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function doApi(Request $request)
    {
        $validate = $this->validate($request, [
            'appId' => 'string',
            'shopCode' => 'string',
            'wpShopCode' => 'string',
//            'platform' => ['required', 'string', Rule::in(PlatformConst::ALL_PLATFORM)],
            'requestMethod' => ['string', Rule::in(['GET', 'POST'])],
            'apiMethod' => 'required|string',
            'apiParams' => 'array',
            'orderSn' => 'string',
        ]);
        $appId = array_get($validate, 'appId', '');
        $shopCode = array_get($validate, 'shopCode', '');
        $wpShopCode = array_get($validate, 'wpShopCode', '');
//        $platform = array_get($validate, 'platform');
        $platform = config('app.platform');
        $apiMethod = array_get($validate, 'apiMethod');
        $requestMethod = array_get($validate, 'requestMethod', 'POST');
        $requestMethod = strtoupper($requestMethod);
        $apiParams = array_get($validate, 'apiParams', []);
        $orderSn = array_get($validate, 'orderSn', '');
        if (empty($shopCode) && empty($wpShopCode)) {
            $arr = StatusCode::PARAMS_ERROR;
            $arr[1] .= ':shopCode和wpShopCode不能都为空';
            throw new ApiException($arr);
        }
        $this->service->checkApiMethodWhiteList($apiMethod, $appId);
        $data = $this->service->getSendByCustom($platform, $shopCode, $wpShopCode, $requestMethod, $apiMethod, $apiParams, $orderSn);
        return $this->successForOpenApi($data);
    }

    /**
     * 获取面单号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|void
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function getWayBillCode(Request $request)
    {
        // 验证参数
        $validate = $this->validate($request, [
            'senderInfo' => 'required|array',
            'senderInfo.senderName' => 'required|string',
            'senderInfo.senderMobile' => 'required|phone',
            'senderInfo.senderProvince' => 'required|string',
            'senderInfo.senderCity' => 'required|string',
            'senderInfo.senderTown' => 'string',
            'senderInfo.senderStreet' => 'string',
            'senderInfo.senderDetail' => 'required|string',

            'wpCode' => 'required|string',
            'shopCode' => 'required|string',
            'wpShopCode' => 'required|string',
            'waybillType' => 'required|int',

            'orderPrintParams' => 'required|array|max:50',
            'orderPrintParams.*.orderSn' => 'required|string',
            'orderPrintParams.*.packageId' => 'string',
        ]);
        $printDataList = $this->service->getPrintDataList($validate);
        return $this->successForOpenApi($printDataList);
    }

    /**
     * 绑定店铺码
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function bindShopCode(Request $request)
    {
        $data = $this->validate($request, [
            'shopCode' => 'required|string',
            'isBindWaybillShop' => 'integer',
        ]);
        $shopCode = array_get($data, 'shopCode');
        $isBindWaybillShop = (int)array_get($data, 'isBindWaybillShop',0);
        $appId = $this->getAppId();

        $shop = Shop::query()->where('shop_code', $shopCode)->first();
        if (empty($shop)) {
            return throw_error_code_exception(StatusCode::SHOP_NOT_FOUND);
        }
        $apiAuthInfo = ApiAuth::firstByAppId($appId);
        if (!empty($apiAuthInfo->auth_to_app_id)) {
            // 绑定上级
            ApiShopBind::updateOrCreateByAppIdShopId($apiAuthInfo->auth_to_app_id, $shop->id);
        }

        $model = ApiShopBind::updateOrCreateByAppIdShopId($appId, $shop->id, $isBindWaybillShop);
        if (empty($model)) {
            return throw_error_code_exception(StatusCode::SHOP_BIND_FAILED);
        }
        return $this->successForOpenApi();
    }


    /**
     * 获取京东面单收件人掩码信息
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     */
    public function getJdMaskReceiverInfo(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'shopCode' => 'required|string',
            'list' => 'required|array',
            'list.*.tid' => 'required|string',
//            'list.*.receiver_phone' => 'string',
            'list.*.receiver_name' => 'string',
            'list.*.receiver_address' => 'string',
        ]);
        $res = $this->service->getJdMaskReceiverInfo($data);

        return $this->successForOpenApi($res);
    }


    /**
     * 获取有效的appid列表
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getValidShopCodeList(Request $request)
    {
        $data = $this->validate($request, [
            'appId' => 'required|string',
            'page' => 'int',
            'pageSize' => 'int|max:100',
        ]);
        $appId = array_get($data,'appId');
        $page = array_get($data,'page',1);
        $pageSize = array_get($data,'pageSize',100);
        if (!in_array($appId,['16863066765337','16884393554734'])){
            throw new NotFoundHttpException();
        }
        $list = \App\Models\Fix\Shop::query()
            ->where('auth_status', Shop::AUTH_STATUS_SUCCESS)
            ->whereNotNull('access_token')
            ->where('access_token','<>','')
            ->where('expire_at', '>', Carbon::now()->toDateTimeString())
            ->paginate($pageSize,['shop_code'],'page',$page);
        return $this->successForOpenApi($list);
    }

    /**
     * @throws ErrorCodeException
     */
    public function getAppAuthInfo(Request $request){
        $data = $this->validate($request, [
            'appId' => 'required|string',
        ]);
        $appId = array_get($data,'appId');
        $info = ApiAuth::firstByAppId($appId);
        if (empty($info)){
            return throw_error_code_exception(StatusCode::APP_ID_ERROR);
        }
        $result=[
            "appId"=>$appId,
            "relatedAppIds"=>$info->related_app_ids,
            "bindWaybillShopLimit"=>$info->bind_waybill_shop_limit,
        ];
        return $this->successForOpenApi($result);

    }

    /**
     * @throws ErrorCodeException
     */
    public function updateAppRelatedAppIds(Request $request){
        $data = $this->validate($request, [
            'appId' => 'required|string',
            'relatedAppIds' => 'nullable|string',
        ]);
        $appId = array_get($data,'appId');
        $relatedAppIds = array_get($data,'relatedAppIds');
        $info = ApiAuth::firstByAppId($appId);
        if(empty($info)){
            return throw_error_code_exception(StatusCode::APP_ID_ERROR);
        }
        $info->related_app_ids = $relatedAppIds;
        $info->save();
        return $this->successForOpenApi();


    }
}
