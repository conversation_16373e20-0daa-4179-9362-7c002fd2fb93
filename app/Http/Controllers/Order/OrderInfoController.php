<?php

namespace App\Http\Controllers\Order;

use App\Constants\PlatformConst;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Package;
use App\Models\ShopBind;
use App\Models\WaybillHistory;
use App\Utils\Environment;
use Illuminate\Http\Request;

class OrderInfoController extends Controller
{

    public function getOrder(Request $request){
        $search = $request->input('search', '');

        if (empty($search)) {
            return $this->success();
        }

        $shopIdArr = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        $orderNoList = explode(',', $search);
        $tidList = [];
        foreach ($orderNoList as $item) {
            if (Environment::isDy() && !str_ends_with($item,'A')) {
                $tidList[] = $item.'A';
            } else {
                $tidList[] = $item;
            }
        }
        $orderList = Order::query()->with(['shop','orderCipherInfo'])->whereIn('shop_id',$shopIdArr)->whereIn('tid', $tidList)->get()->toArray();
        $inDbOrderNoList = array_column($orderList,'tid');
        $notInDbOrderNoList = [];
        forEach($orderNoList as $item) {
            if (!in_array($item, $inDbOrderNoList)) {
                array_push($notInDbOrderNoList,$item);
            }
        }
        $order2WaybillCodeMap = [];
        if (!empty($notInDbOrderNoList)) {
            //查询运单号
//            $waybillHistory = WaybillHistory::query()
//                -> whereIn('waybill_code',$notInDbOrderNoList)
//                -> where( 'waybill_status',WaybillHistory::WAYBILL_RECOVERY_NO)
//                ->get()->toArray();
            $list = Package::query()->with('packageOrders')->whereIn('shop_id', $shopIdArr)->whereIn('waybill_code', $notInDbOrderNoList)->get()->toArray();
            if (!empty($list)) {
                $orderIdArr = [];
                foreach ($list as $index => $item) {
                    $packageOrderIdArr = collect($item['package_orders'])->pluck('order_id')->toArray();
                    $orderIdArr = array_merge($orderIdArr, $packageOrderIdArr);
                    foreach ($packageOrderIdArr as $packageOrderTid) {
                        $order2WaybillCodeMap[$packageOrderTid] = $item['waybill_code'];
                    }
                }
                //获取order
//                $orders = array_column($waybillHistory, 'order_no');
                $orders = Order::query()->with(['shop','orderCipherInfo'])->whereIn('id', $orderIdArr)->get()->toArray();
                if (!empty($orders)) {
                    $orderList = array_merge($orderList,$orders);
                }
//                $order2WaybillCodeMap = array_column($waybillHistory,'waybill_code','order_no');
            }
        }
        // 同步订单
        $orderInfos = Order::batchGetOrderInfo($orderList);
        $orderGroupArr = collect($orderInfos)->groupBy('shop_id')->toArray();
        foreach ($orderGroupArr as $item) {
            Order::batchSave($item, $item[0]['user_id'], $item[0]['shop_id']);
        }
        $result = [];
        foreach ($orderList as $order) {

            //查询运单号
            $waybillCode = '';
            if (array_key_exists($order['id'], $order2WaybillCodeMap)) {
                $waybillCode = $order2WaybillCodeMap[$order['id']];
            } else {
                $waybillHistory = WaybillHistory::query()->whereIn('shop_id', $shopIdArr)->where(['order_no'=>$order['tid'], 'waybill_status'=>WaybillHistory::WAYBILL_RECOVERY_NO])->get()->toArray();
                if (!empty($waybillHistory)) {
                    $waybillCode = implode(",", array_column($waybillHistory, 'waybill_code'));
                }
            }


            $result[] = [
                'orderNo'         => $order['tid'],
                'province'        => $order['receiver_state'],
                'city'            => $order['receiver_city'],
                'town'            => $order['receiver_district'],
                'receiverName'    => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                    ? (!empty($order['OrderCipherInfo']) ? $order['OrderCipherInfo']['receiver_name_mask'] : $order['receiver_name']) : $order['receiver_name'],
                'receiverPhone'   => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                    ? (!empty($order['OrderCipherInfo']) ? $order['OrderCipherInfo']['receiver_phone_mask'] : $order['receiver_phone']) : $order['receiver_phone'],
                'receiverAddress' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                    ? (!empty($order['OrderCipherInfo']) ? $order['OrderCipherInfo']['receiver_address_mask'] : $order['receiver_address']) : $order['receiver_address'],
                'shopId'          => $order['shop_id'],
                'mallName'        => $order['shop']['name'],
                'identifier'      =>$order['shop']['identifier'],
                'shopName'        => $order['shop']['shop_name'],
                'waybillCode'     => $waybillCode,
                'userId'          => $order['user_id'],
                'orderStatus'     => $order['order_status'],
                'refundStatus'    => $order['refund_status'],
                'printStatus'     => $order['print_status'],
                'send_at'         => $order['send_at'],
                'pay_at'          => $order['pay_at'],
                'order_created_at' => $order['order_created_at'],
                'created_at'      => $order['created_at'],
                'pre_shipment_status'      => $order['pre_shipment_status'],
                'pre_shipment_at'      => $order['pre_shipment_at'],
            ];
        }

        return $this->success($result);
    }
}
