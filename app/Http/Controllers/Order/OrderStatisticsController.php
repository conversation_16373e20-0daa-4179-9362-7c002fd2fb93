<?php

namespace App\Http\Controllers\Order;

use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\WaybillHistory;
use App\Services\AntiSpam\AntiSpamService;
use App\Services\Goods\GoodsQueryService;
use App\Services\Goods\PickingGoodsRequest;
use App\Services\Order\OrderQueryBuilder;
use App\Services\Order\OrderStatisticsService;
use App\Services\Order\Request\OrderDeliverReportRequest;
use App\Services\Order\Request\OrderSearchRequest;
use App\Services\Order\Request\GoodsMergeParam;
use App\Utils\ObjectUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 订单统计相关，主要给备货单、发货对账用
 */
class OrderStatisticsController extends Controller
{

    /**
     * @var GoodsQueryService $goodsQueryService
     */
    private $goodsQueryService;

    /**
     * @var OrderStatisticsService $orderStatisticsService
     */
    protected $orderStatisticsService;

    /**
     * @param GoodsQueryService $goodsQueryService
     * @param OrderStatisticsService $orderStatisticsService
     */
    public function __construct(GoodsQueryService $goodsQueryService, OrderStatisticsService $orderStatisticsService)
    {
        $this->goodsQueryService = $goodsQueryService;
        $this->orderStatisticsService = $orderStatisticsService;
    }


    /**
     * 发货对账
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getDeliveryData(Request $request): JsonResponse
    {
        $this->validate($request, [
            "ownerIdList" => 'array',
            "begin_at" => 'date',
            "end_at" => 'date',
            "offset" => 'int',
            "limit" => 'int',
            "wp_code" => 'string',
        ]);

        $ownerIdList = $this->getOwnerIdList($request);
        $conditions = [];
        $timeField = $request->input('timeField', 'send_at');
        $beginAt = $request->input('begin_at', '');
        $endAt = $request->input('end_at', '');
        $keyword = $request->input('keyword', '');
        $wp_code = $request->input('wp_code', '');
        $refund_status = $request->input('refund_status', -1);   //打印状态
        $skuIdList = $request->input('skuIdList', '');
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);
        $currentShopId = $request->auth->shop_id;

        $includeDeliveryRecords = $request->input('includeDeliveryRecords', 0);  //实付关联发货记录表
        $allRelationShopIds = ShopBind::getAllRelationShopIds($currentShopId);
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $shopIds = collect($shops)->pluck('id')->toArray();
        if ($timeField && $beginAt && $endAt) {
            $conditions[] = ['order_items.' . $timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $conditions[] = ['order_items.' . $timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }

        $query = Order::query()
            ->whereIn('orders.shop_id', $shopIds)
            ->whereIn('order_items.shop_id', $shopIds)
            ->where($conditions);
//            ->whereIn('order_status', [
//                Order::ORDER_STATUS_PART_DELIVERED,
//                Order::ORDER_STATUS_DELIVERED,
//                Order::ORDER_STATUS_RECEIVED,
//                Order::ORDER_STATUS_SUCCESS,
//                 Order::ORDER_STATUS_CLOSE
//            ]);
        if ($includeDeliveryRecords == 1) {
            //关联发货记录表，把不是我们这边发的单子过滤掉
            $query->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('delivery_records')
                    ->whereRaw('orders.id = delivery_records.order_id');
            });
        }
        //退款状态
        if ($refund_status == Order::REFUND_STATUS_YES) {
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
            $query->where('order_items.refund_status', Order::REFUND_STATUS_YES);
        } else if ($refund_status == Order::REFUND_STATUS_NO) {
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_PART, Order::REFUND_STATUS_NO]);
            $query->where('order_items.refund_status', Order::REFUND_STATUS_NO);
        } else {
            //全部
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
            $query->whereIn('order_items.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_YES]);
        }

        if ($wp_code) {
            $wp_code = explode(',', $wp_code);
            $query->whereIn('orders.express_code', $wp_code);
        }

        if ($keyword) {
            $query = $query->where(function ($query) use ($keyword) {
                $query->where('order_items.goods_title', 'like', "%{$keyword}%")
                    ->orWhere('order_items.outer_iid', $keyword)
                    ->orWhere('order_items.sku_value', 'like', "%{$keyword}%")
                    ->orWhere('order_items.sku_id', $keyword)
                    ->orWhere('order_items.num_iid', $keyword)
                    ->orWhere('order_items.outer_sku_iid', $keyword);
            });
        }
        if ($skuIdList) {
            $query = $query->whereIn('order_items.sku_id', $skuIdList);
        }
        //子订单也有部分发货的情况，所以这里要用子订单的发货状态
        $query->where('order_items.status', '>=', Order::ORDER_STATUS_PART_DELIVERED);

//        $maxLimit = 1000;
        $query1 = clone $query;
        $query1->rightJoin('order_items', 'orders.id', '=', 'order_items.order_id')
//            ->limit($maxLimit)
            ->groupBy(['orders.id'])
            ->select(['orders.id']);
        Log::info('查询SQL', [$query1->toSql()]);
        $orderCount = DB::query()->fromSub($query1, 'q1')->count();

        $list = $query->rightJoin('order_items', 'orders.id', '=', 'order_items.order_id')
            ->groupBy(['sku_id'])
            ->orderBy('order_created_at_min')
            ->with(['orderItem.customGoodsSkus', 'orderItem.customGoods', 'shop', 'trace'])
            ->get([
                'orders.id',
                'order_items.sku_id',
                'order_items.sku_value',
                'order_items.num_iid',
                'order_items.goods_title',
                'order_items.goods_pic',
                'order_items.goods_price',
                'order_items.outer_iid',
                'order_items.outer_sku_iid',
                DB::raw('min(orders.order_created_at) order_created_at_min'),
                //发货数量，如果order_items的记录有send_num 则用send_num，否则用goods_nums
//                DB::raw('sum(goods_num) goods_nums')
                DB::raw('sum(if(order_items.send_num>0,order_items.send_num,order_items.goods_num)) goods_nums')
            ])->groupBy('num_iid')->toArray();
        $arr = [];
        $arrs = collect($list)->slice($offset, $limit)->values()->all();
        foreach ($arrs as $index => $item) {
            $arr[] = [
                'goods_pic' => $item[0]['goods_pic'],
                'goods_title' => $item[0]['goods_title'],
                'num_iid' => $item[0]['num_iid'],
                'total' => array_sum(array_column($item, 'goods_nums')),
                'items' => $item,
            ];
        }

        // 商品总数&商品种类
        $goodsCount = $amountCount = 0;
        $numIid = $skuId = $orderIds = [];
        foreach ($list as $skuArr) {
            foreach ($skuArr as $item) {
                $goodsCount += $item['goods_nums'];
                $numIid[] = $item['num_iid'];
                $skuId[] = $item['sku_id'];
                $amountCount += round($item['goods_price'] * $item['goods_nums'], 2);
                $orderIds[] = $item['id'];
            }
        }
        $goodsKindCount = count(array_unique($numIid));

        // 包裹总数
        $packageCount = 0;
        if (!empty($orderIds)) {
            $waybillHistory = WaybillHistory::query()
                ->where([
                    ['created_at', '>=', date('Y-m-d H:i:s', strtotime($beginAt))],
                    ['created_at', '<=', date('Y-m-d H:i:s', strtotime($endAt))],
                ])
                ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
                ->whereIn("shop_id", $allRelationShopIds)
                ->whereIn('to_shop_id', $shopIds)
                ->get([DB::raw('count(DISTINCT(waybill_code)) as count')]);

            $packageCount = $waybillHistory[0]->count;
        }

        $pagination = [
            'rows_found' => collect($list)->count(),
            'offset' => $offset,
            'limit' => $limit,
            'orderCount' => $orderCount,
            'goodsKindCount' => $goodsKindCount,
            'goodsCount' => $goodsCount,
            'amountCount' => round($amountCount, 2),
            'packageCount' => $packageCount,
        ];
        return $this->success(['pagination' => $pagination, $arr]);
    }


    /**
     * 发货对账（老的）有运费和结算价
     * @throws ValidationException
     * @throws ErrorCodeException
     */
    public function deliveryReport(Request $request): JsonResponse
    {
        //延长执行时间
        set_time_limit(5*60);
        $date = $this->validate($request, [
            "orderShopIds" => 'array',
            "beginAt" => 'required|string',
            "endAt" => 'required|string',
            "wpCodes" => 'string',
            "refundStatus"=>'int',
            "refundStatusArr"=>'array',
            "keyword"=>'string',
            "skuIdList"=>'array',
            "deliveryTypes"=>'array'
        ]);
        /**
         * @var  OrderDeliverReportRequest $orderDeliverRequest
         */
        $orderDeliverRequest = ObjectUtil::mapToObject($date, OrderDeliverReportRequest::class);
        $currentShopId = $request->auth->shop_id;
        $operationShopIds = ShopBind::getOShopIdsByType([$currentShopId], ShopBind::TYPE_ME_BIND);
        $operationShopIds[] = $currentShopId;
        $orderDeliverRequest->operationShopIds = $operationShopIds;
        $orderDeliverRequest->currentShopId = $currentShopId;
        if (empty($orderDeliverRequest->orderShopIds)) {
            $orderDeliverRequest->orderShopIds = ShopBind::getAllRelationShopIds($currentShopId);
        }
        $skuMergeType=$request->input('skuMergeType',1);
        $goodsMergeType=$request->input('goodsMergeType',1);
        $outputFormat=$request->input('outputFormat',1);
        $goodsMergeParam=new GoodsMergeParam($goodsMergeType,$skuMergeType,$outputFormat);


        return $this->success($this->orderStatisticsService->deliveryReport($orderDeliverRequest,$goodsMergeParam));
    }


    /**
     * 已发货统计
     * @param Request $request
     * @return JsonResponse
     */

    public function deliveryStatistics(Request $request): JsonResponse{
        $currentShopId = $request->auth->shop_id;
        $packageSearchRequest = new PackageSearchRequest();
        $packageSearchRequest->validate($request);
        $packageSearchRequest->authShopId = $currentShopId;
        $skuMergeType=$request->input('skuMergeType',1);
        $goodsMergeType=$request->input('goodsMergeType',1);
        $outputFormat=$request->input('outputFormat',1);
        $goodsMergeParam=new GoodsMergeParam($goodsMergeType,$skuMergeType,$outputFormat);
        $operationShopIds = ShopBind::getOShopIdsByType([$currentShopId], ShopBind::TYPE_ME_BIND);
//        $operationShopIds[] = $currentShopId;
////        $packageSearchRequest->currentShopId = $currentShopId;
//        if (empty($orderDeliverRequest->orderShopIds)) {
//            $packageSearchRequest->orderShopIds = ShopBind::getAllRelationShopIds($currentShopId);
//        }


        return $this->success($this->orderStatisticsService->deliveryStatisticsReport($packageSearchRequest,$goodsMergeParam));
    }

    /**
     * 新版的备货
     * @param Request $request
     * @return JsonResponse
     */

    public function orderStocking(Request $request): JsonResponse
    {
        $orderSearchRequest = new OrderSearchRequest();
        $orderSearchRequest->validate($request);
//        $orderSearchRequest->tabFlag='4';
        $skuMergeType=$request->input('skuMergeType',1);
        $goodsMergeType=$request->input('goodsMergeType',1);
        $outputFormat=$request->input('outputFormat',1);
        $goodsMergeParam=new GoodsMergeParam($goodsMergeType,$skuMergeType,$outputFormat);

        $orderSearchRequest->authShopId = $request->auth->shop_id;
        $orderStockingSummaryReport = $this->orderStatisticsService->orderStockingReport($orderSearchRequest, $goodsMergeParam);
        Log::info('备货单统计', [$orderStockingSummaryReport]);

        return $this->success($orderStockingSummaryReport);
    }

    /**
     * 订单统计 备货单
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function orderStatistics(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "keyword" => "string",
            "locked_status" => "int",
            "order_status" => "int",
            "print_status" => "int",
            "refund_status" => "int",
            "skuValueList" => "array",
            "timeField" => "string",
            "begin_at" => 'date',
            "end_at" => 'date',
            "ownerIdList" => 'array',
            "goodsId" => 'array',
            "preSaled" => "int",
        ]);
        $timeField = $request->input('timeField', '');
        $beginAt = $request->input('begin_at', '');
        $endAt = $request->input('end_at', '');
        $ownerIdList = $request->input('ownerIdList', []);
        $keyword = trim($request->input('keyword', ''));
        $skuKeyword = trim($request->input('sku_keyword', ''));
        $lockedStatus = $request->input('locked_status', 0);
        $orderStatus = $request->input('order_status', 30);
        $printStatus = $request->input('print_status', 0);
        $refundStatus = $request->input('refund_status', 0);
        $skuValueList = $request->input('skuValueList', '');
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 300);
        $sort = $request->input("sort", "pay_at_min asc");
        $preSaled = $request->input('preSale', null);
        $goodsId = $request->input('goodsId', []);
        $goodsSort = $request->input('goodsSort', []);
        $currentShopId = $request->auth->shop_id;

        $pickingGoodsRequest = new PickingGoodsRequest($keyword, $lockedStatus, $orderStatus, $printStatus, $refundStatus,
            $skuValueList, $timeField, $beginAt, $endAt, $ownerIdList, $currentShopId, $skuKeyword, $limit, $offset, $sort, $goodsId, $goodsSort, $preSaled);
        return $this->success($this->goodsQueryService->queryPickingGoods($pickingGoodsRequest));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function orderStatisticsSummary(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "keyword" => "string",
            'sku_keyword' => "string",
            "locked_status" => "int",
            "order_status" => "int",
            "print_status" => "int",
            "refund_status" => "int",
            "skuValueList" => "array",
            "timeField" => "string",
            "begin_at" => 'date',
            "end_at" => 'date',
            "ownerIdList" => 'array',
            "goodsId" => 'array',
        ]);
        $timeField = $request->input('timeField', '');
        $beginAt = $request->input('begin_at', '');
        $endAt = $request->input('end_at', '');
        $ownerIdList = $request->input('ownerIdList', []);
        $keyword = trim($request->input('keyword', ''));
        $skuKeyword = trim($request->input('sku_keyword', ''));
        $lockedStatus = $request->input('locked_status', 0);
        $orderStatus = $request->input('order_status', 30);
        $printStatus = $request->input('print_status', 0);
        $refundStatus = $request->input('refund_status', 0);
        $skuValueList = $request->input('skuValueList', '');
        $goodsId = $request->input('goodsId', []);
        $preSaled = $request->input('preSale', null);
        $currentShopId = $request->auth->shop_id;
        $pickingGoodsRequest = new PickingGoodsRequest($keyword, $lockedStatus, $orderStatus, $printStatus, $refundStatus, $skuValueList, $timeField, $beginAt, $endAt, $ownerIdList, $currentShopId, $skuKeyword, $goodsId, $preSaled);
        return $this->success($this->goodsQueryService->queryPickingGoodsSummary($pickingGoodsRequest));
    }

}
