<?php

namespace App\Http\Controllers\Order;

use App\Events\Orders\OrderPrintEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\PrintException;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Template;
use App\Models\User;
use App\Services\AntiSpam\AntiSpamService;
use App\Services\AntiSpam\OperationType;
use App\Services\AntiSpam\OrderAntiSpamRequest;
use App\Services\BusinessException;
use App\Services\PrintCheckService;
use App\Services\Printing\OrderPrintService;
use App\Utils\Environment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 平台订单打印
 */
class OrderPrintController extends Controller
{
    private $antiSpamService;

    /**
     *
     * @param AntiSpamService $antiSpamService
     */
    public function __construct(AntiSpamService $antiSpamService)
    {
        $this->antiSpamService = $antiSpamService;
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function printCheckWaybill(Request $request): JsonResponse
    {
        $this->validate($request, [
            'template_id' => 'required|int',
            'package_num' => 'required|int', //订单打印包裹数
            'total_count' => 'required|int', //取号总数量
        ]);

        $templateId = $request->input('template_id');
        $isNewPrint = $request->input('new_print');
        $totalCount = $request->input('total_count');
        try {
            $printCheckService = new PrintCheckService($templateId, $isNewPrint, $totalCount, []);
            //校验电子面单是否过期以及余额
            $printCheckService->checkAuthStatusAndWaybillBalance();
        } catch (PrintException $e) {
            Log::info("电子面单账号失效", [$e->getData()]);
            return $this->success($e->getData(), $e->getMsg(), $e->getStatusCode());
        }

        return $this->success();
    }

    /**
     * 打印前对订单状态进行校验
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ClientException
     * @throws ValidationException
     */
    public function printCheckOrders(Request $request): JsonResponse
    {
        $this->validate($request, [
            'order_ids' => 'required|array',
            'order_item_ids' => 'array',
            'template_id' => 'required|int',
        ]);

        $orderIds = $request->input('order_ids');
        $orderItemIds = $request->input('order_item_ids',[]);
        $templateId = $request->input('template_id');
        $shopIds = $request->input('shop_ids', []);
        $scene = $request->input('scene', 'unprinted');

        // 抖音风控
        if (Environment::isDy()) {
            $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
            $orderAntispamRequest = new OrderAntispamRequest($shop['user_id'], 1, $request->auth->shop_id, null, OperationType::print_order_list, null, null, null, $request);
            $this->antiSpamService->checkAntispamOrderSend($orderAntispamRequest);
        }

        try {
            $printCheckService = new PrintCheckService($templateId, false, 0, $orderIds, array_unique($shopIds),$orderItemIds);
            //校验订单状态
            $printCheckService->checkPrintStatusAndOrderStatus($scene);
        } catch (PrintException $e) {
            \Log::info("检查订单状态异常", [$e->getData()]);
            return $this->success($e->getData(), $e->getMsg(), $e->getStatusCode());
        }

        return $this->success();
    }


    /**
     * 获取打印数据
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function print(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'order_ids' => 'required|array',
            'template_id' => 'required|int',
            'package_num' => 'required|int', //订单打印包裹数
            'batch_no' => 'required|int', //批次号
            //'request_index' => 'required|int', //请求批次序号
        ]);
        $data['rdm'] = $request->input('rdm', '');
        //短时间频繁操作
        $redis = redis('cache');
        $redisKey = 'print:' . json_encode($data);
        $bool = $redis->setnx($redisKey, time());
        if (!$bool) {
            return $this->fail('操作频繁,请稍后再试', 400);
        }
        $redis->expire($redisKey, 10);
        //  dd(json_encode($data));

        $singleMax = 100; //普通打印限制
        $packageMax = 50;  //一单多包打印限制
        $contents = $request->input('contents');
        $orderIdsArr = $request->input('order_ids');
        $isNewPrint = $request->input('new_print');
        $templateId = $request->input('template_id');
        $waybillCode = $request->input('order_to_waybill', []);
        $packageNum = $request->input('package_num', 1);
        $requestIndex = $request->input('request_index', 0);
        $printMode = $request->input('printMode', 1);
        $batchNo = $request->input('batch_no', time() . rand(00, 99));
        $printConfig = $request->input('print_config', []);
        //是否强制打印退款订单
        $forcePrint = $request->input('force_print', false);
        $mergeOrderNoList = $request->input('merge_order_no_list', []);

        if (count($orderIdsArr) > $singleMax) {
            return $this->fail('批量打印数量最大为' . $singleMax, 400);
        }
        if ($packageNum > 1 && count($orderIdsArr) > $packageMax) {
            return $this->fail('一单多包批量打印数量最大为' . $packageMax, 400);
        }

        $lockArr = [];
        $lockIdentity = uniqid();
        $redisKeyPrefix = 'order_print_lock:';
        $requestRedisKey = $redisKeyPrefix . $batchNo . $requestIndex;

        try {
            // 请求加锁
//            $bool = $redis->setnx($requestRedisKey, $lockIdentity);
//            if (!$bool) {
//                return $this->fail('订单正在操作中，请稍后再试');
//            }
            $isDoing = $redis->get($requestRedisKey);
            //isDoing为null 没有请求过；isDoing为0 正在处理中；isDoing为1 处理完成(同一个请求取原单号)
            if (is_null($isDoing)) {
                $redis->setex($requestRedisKey, 60 * 60 * 24, 0);
            } else if ($isDoing == 0) {
                return $this->fail('订单正在操作中，请稍后再试', 400);
            } else {
                $isNewPrint = false;
            }

            // 订单加锁 60秒超时
            foreach ($orderIdsArr as $orderIdArr) {
                $orderId = $orderIdArr[0];
                $orderId = strpos($orderId, ':') ? substr($orderId, 0, strpos($orderId, ':')) : $orderId;
                $redisKey = $redisKeyPrefix . $orderId;
                $bool = $redis->set($redisKey, $lockIdentity, 'nx', 'ex', 180);
                if (!$bool) {
                    // 释放前面的锁
                    foreach ($lockArr as $orderId2) {
                        $redisKey = $redisKeyPrefix . $orderId2;
                        redisDelByEqual($redisKey, $lockIdentity, 'cache');
                    }
                    return $this->fail('订单正在操作中，请稍后再试', 400);
                }
                $lockArr[] = $orderId;
            }

            $printData = OrderPrintService::getPrintDataAndWaybill(
                $request->auth->user_id,
                $request->auth->shop_id,
                $isNewPrint,
                $waybillCode,
                $templateId,
                $orderIdsArr,
                $batchNo,
                $packageNum,
                $contents,
                $forcePrint,
                $printMode,
                $printConfig,
   $request->auth->operatorName ?? ''

            );
            $shopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);
            $successTidArr = [];
            collect($printData['orderInfoArr'])->each(function ($item) use (&$successTidArr) {
                $successTidArr = array_merge($successTidArr, $item['tid_arr']);
            });
//            $successTidArr = array_column($printData['orderInfoArr'], 'tid');
            // 合单处理
            foreach ($mergeOrderNoList as $arr) {
                $merge_flag = 'print_' . microtime(true) . rand(100, 999);
                $successTidArrIntersect = array_values(array_intersect($successTidArr, $arr));
                Order::query()
//                    ->whereIn('shop_id', $shopIds)
                    ->whereIn('tid', $successTidArrIntersect)
//                    ->whereIn('tid', $arr)
//                    ->where('merge_flag','')
                    ->update(['merge_flag' => $merge_flag]);
            }
        } finally {
            // 解锁
            foreach ($lockArr as $orderId) {
                $redisKey = $redisKeyPrefix . $orderId;
                redisDelByEqual($redisKey, $lockIdentity, 'cache');
            }
//            $redis->del($requestRedisKey);
            // 设置为已完成
            $redis->setex($requestRedisKey, 60 * 60 * 24, 1);
        }

        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();

        event((new OrderPrintEvent($user, $shop, time(), $printData['orderInfoArr']))->setClientInfoByRequest($request));
        unset($printData['orderInfoArr']);

        return $this->success($printData);
    }

    /**
     * 打印-云仓版
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     * @throws BusinessException
     * @throws \Throwable
     */
    public function printWarehouse(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'mode' => 'required|string', // new or old
            'templateId' => 'int',
            'companyId' => 'int',
            'batchNo' => 'required|string', //批次号
            'requestIndex' => 'required|int', //请求批次序号
            'remark' => 'string', //备注
            'rdm' => 'string', // 不知道什么东西
            'isSplit' => 'int', // 是否拆单
            'printOrderStatus' => 'int', // 打印状态，30 未发货 40 已发货
            'orderType' => 'int', // 订单类型  1普通订单 2自由打印
            'senderInfo' => 'array',
            'version' => 'int|in:2,3',
            'packageNum' => 'int',
            'isPrintRefund' => 'int', // 是否打印退款
            'packages' => 'array',
            'packages.*.packIndex' => 'int', // 包裹序号
            'packages.*.orderId' => 'int',
//            'packages.*.waybillCode' => 'string', // 老单号打印用
            'packages.*.resendWaybillCode' => 'string', // 重新发货取号用
            'packages.*.items' => 'array',
        ]);
        // 短时间频繁操作
        $redis = redis('cache');
        $json_data = json_encode($data);
        $redisKey = 'print:' . md5($json_data).'_'.strlen($json_data);
        $bool = $redis->setnx($redisKey, time());
        if (!$bool) {
            return $this->fail('操作频繁,请稍后再试', 400);
        }
        $redis->expire($redisKey, 10);

        $singleMax = 1000; //普通打印限制
        $packageMax = 50;  //一单多包打印限制
        $templateId = $data['templateId']??0;
        $companyId = $data['companyId'] ?? 0;
        $packageNum = $request->input('packageNum',1); // 一单多包
        $requestIndex = $data['requestIndex'];// $request->input('request_index', 0);
        $batchNo = array_get($data, 'batchNo', date('md-H:i:s'));// $request->input('batch_no', date('md-H:i:s'));
        //是否强制打印退款订单
//        $forcePrint = $request->input('force_print', false);
        $mergeOrderNoList = $request->input('merge_order_no_list', []);
        $packages = $request->input('packages');
        $senderInfo = $request->input('senderInfo', []);
        $remark = $request->input('remark', '');
        $mode = $request->input('mode');
        $isSplit = $request->input('isSplit');
        $version = $request->input('version',2);
        $printOrderStatus = $request->input('printOrderStatus',0);
        $orderType = $request->input('orderType',1); // 订单类型  1普通订单 2自由打印
        $isPrintRefund = $request->input('isPrintRefund',0); // 是否打印退款订单
//        $maxPackageSkuNum=$request->input('maxPackageSkuNum',1);

        if (count($packages) > $singleMax) {
            return $this->fail('批量打印数量最大为' . $singleMax, 400);
        }
        if ($packageNum > 1 && count($packages) > $packageMax) {
            return $this->fail('一单多包批量打印数量最大为' . $packageMax, 400);
        }


        $lockArr = [];
        $lockIdentity = uniqid();
        $redisKeyPrefix = 'order_print_lock:';
        $requestRedisKey = $redisKeyPrefix. $request->auth->shop_id .'-'. $batchNo .'-'. $requestIndex;

        $printData = [];
        try {
            if (in_array($mode,['new','onlyTake'])){

                // 请求加锁
                $isDoing = $redis->get($requestRedisKey);
                //isDoing为null 没有请求过；isDoing为0 正在处理中；isDoing为1 处理完成(同一个请求取原单号)
                if (is_null($isDoing)) {
                    $redis->setex($requestRedisKey, 60 * 60 * 24, 0);
                } else if ($isDoing == 0) {
                    return $this->fail('订单正在操作中，请稍后再试', 400);
                } else {
//                $isNewPrint = false;
                }

//                $orderIdArr = collect($packages)->pluck('orderId')->unique()->toArray();
//                // 订单加锁 60秒超时
//                foreach ($orderIdArr as $orderId) {
//                    $redisKey = $redisKeyPrefix . $orderId;
//                    $bool = $redis->set($redisKey, $lockIdentity, 'nx', 'ex', 180);
//                    if (!$bool) {
//                        // 释放前面的锁
//                        foreach ($lockArr as $orderId2) {
//                            $redisKey = $redisKeyPrefix . $orderId2;
//                            redisDelByEqual($redisKey, $lockIdentity, 'cache');
//                        }
//                        return $this->fail('订单正在操作中，请稍后再试', 400);
//                    }
//                    $lockArr[] = $orderId;
//                }
            }

            $printData = OrderPrintService::getPrintDataAndWaybillWarehouse(
                $request->auth->user_id,
                $request->auth->shop_id,
                $mode,
                $templateId,
                $batchNo,
                $packages,
                $remark,
                $senderInfo,
                $version,
                $printOrderStatus,
                $orderType,
                $packageNum,
                $isPrintRefund,
                $companyId,
                $request->auth->operatorName ?? ''
            );
//            if (in_array($mode,['new','onlyTake'])){
//                $successOrderIdArrList = collect($printData['successList'])->pluck('orderIdArr')->toArray();
//                // 打印成功的合单处理
//                foreach ($successOrderIdArrList as $orderIdArr) {
//                    $merge_flag = 'print_' . microtime(true) .'_'. rand(100, 999);
//                    Order::query()
////                    ->whereIn('shop_id', $shopIds)
//                        ->whereIn('id', $orderIdArr)
////                    ->whereIn('tid', $arr)
////                    ->where('merge_flag','')
//                        ->update(['merge_flag' => $merge_flag]);
//                }
//            }

        } finally {
            // 解锁
            foreach ($lockArr as $orderId) {
                $redisKey = $redisKeyPrefix . $orderId;
                redisDelByEqual($redisKey, $lockIdentity, 'cache');
            }
//            $redis->del($requestRedisKey);
            // 设置为已完成
            $redis->setex($requestRedisKey, 60 * 60 * 24, 1);
        }

        return $this->success($printData);
    }

    /**
     * 打印-标签版
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     */
    public function printTag(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
//            'templateId' => 'required|int',
            'batchNo' => 'required|string', //批次号
            'ownerIdList' => 'required|array', //批次号
            'packages' => 'required|array',
            'packages.*.packIndex' => 'required|int', // 包裹序号
            'packages.*.orderId' => 'required|int',
            'packages.*.orderItemId' => 'required|int',
        ]);
        $templateId = array_get($data, 'templateId');
        $batchNo = array_get($data, 'batchNo');
        $packages = array_get($data, 'packages');
        $ownerIdList = array_get($data, 'ownerIdList');
        $shopId = $request->auth->shop_id;
        $res = OrderPrintService::printTag($templateId, $batchNo, $packages, $shopId,$ownerIdList);
        return $this->success($res);
    }
}
