<?php

namespace App\Http\Controllers\Order;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Jobs\Orders\SyncOrderByScopeJob;
use App\Models\Shop;
use App\Services\BusinessException;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

/**
 * 订单同步相关的Controller
 */
class OrderSyncController  extends Controller
{
    /**
     * 同步订单通过时间范围
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function syncOrderByScope(Request $request): JsonResponse
    {
        $this->validate($request, [
//            "begin_time" => 'date',
//            "end_time" => 'date',
//            "ownerIdList" => 'array',
            "shop_list" => 'required|array',
            "shop_list.*.owner_id" => 'required|string',
            "shop_list.*.begin_time" => 'date|after:end_time',
            "shop_list.*.end_time" => 'date',
            "shop_list.*.order_type" => 'integer|in:1,2', // 1:未发货订单 2:全部订单
        ]);
        $order_type_unshipped = 1;
        $order_type_all = 2;
        $shop_list = $request->input('shop_list');
//        $shops = Shop::query()->whereIn('identifier', array_column($shop_list, 'owner_id'))->get()
//            ->pluck(null, 'identifier')->toArray();
        $shops = Shop::getListByIdentifiers(array_column($shop_list, 'owner_id'))
            ->pluck(null, 'identifier')->toArray();

        $redis = \redis('cache');

        $responseList = [];
        foreach ($shop_list as $index => $item) {
            try {
                $beginTime = array_get($item, 'begin_time');
                $endTime = array_get($item, 'end_time');
                $orderType = array_get($item, 'order_type', $order_type_unshipped);
                if (strtotime($beginTime) > strtotime($endTime)) {
                    throw new ApiException([ErrorConst::TIME_EXCEEDS_RANGE[0], '开始时间不能大于结束时间']);
                }
                if ((strtotime($endTime) - strtotime($beginTime)) / 86400 > 91) {
                    $beginTime = date('Y-m-d H:i:s', strtotime($endTime . " -91 day"));
//                    throw new ApiException([ErrorConst::TIME_EXCEEDS_RANGE[0], '时间范围不能大于31天']);
                }
                $shop = $shops[$item['owner_id']];
                $shopId = $shop['id'];
                if ($shop['auth_status'] != Shop::AUTH_STATUS_SUCCESS) {
                    throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                }
                $orderService = OrderServiceManager::create();
                $orderService->setShop((object)$shop);
                $checkAuthStatus = $orderService->checkAuthStatus();
                if (!$checkAuthStatus) {
                    // 检查授权状态
                    throw new ApiException(ErrorConst::PLATFORM_SHOP_AUTH_EXPIRED);
                }

                $isSyncLegacyOrder = false;
                $isSaveLastSync = false;
                $isFirstPull = false;
                if ($orderType == $order_type_unshipped) {
                    $isFirstPull = true;
                }
                if (empty($beginTime) && empty($endTime)) {
                    if (time() > strtotime('2023-02-17') && time() < strtotime('2023-03-01')){
                        $beforeMaxDay = strtotime("-20 day");
                    }else{
                        $beforeMaxDay = strtotime("-7 day");
                    }
                    $beginTime = $shop['last_sync_at'] ?: date("Y-m-d H:i:s", $beforeMaxDay);
                    if (strtotime($beginTime) < $beforeMaxDay) {
                        $isSyncLegacyOrder = true;
                        $beginTime = date("Y-m-d H:i:s", $beforeMaxDay);
                    }
                    $endTime = date("Y-m-d H:i:s");
                }

                // 同步时间不可超过当前时间
                if (strtotime($endTime) > time()) {
                    $endTime = date("Y-m-d H:i:s");
                }
                // 店铺最后同步时间大于1天
                if (!empty($shop['last_sync_at']) && (time() - strtotime($shop['last_sync_at']))  > 86400) {
                    $isSyncLegacyOrder = true;
                }
                // 第一次同步 或者 最后同步时间大于1天
                if ($isFirstPull || empty($shop['last_sync_at']) || (time() - strtotime($shop['last_sync_at'])  > 86400)){
                    $isSaveLastSync = true;
                }

                $key = 'syncOrderByScopeLock:' . $shopId;
                $lock = $redis->set($key, 1, 'nx', 'ex', 60);
                if (!$lock) {
                    throw new ApiException(ErrorConst::ORDER_REPEAT_SYNC);
                }
                $action = 'syncOrderByScope';
                // 当前同步数量
                $key = "$action:sync_current:$shopId";
                $redis->setex($key, 86400, 0);
                // 需要同步总数量
                $key = "$action:sync_total:$shopId";
                $redis->setex($key, 86400, 0);
                $key = "$action:sync_finish:$shopId";
                $redis->setex($key, 86400, 0);
                Log::info('new syncOrderByScope', compact('shopId', 'beginTime', 'endTime', 'isSyncLegacyOrder','isSaveLastSync'));
                $syncOrderByScopeJob = new SyncOrderByScopeJob($shopId, $beginTime, $endTime, $action);
                $syncOrderByScopeJob->setIsFirstPull($isFirstPull);
                $syncOrderByScopeJob->setIsSaveLastSync($isSaveLastSync);
                $syncOrderByScopeJob->setIsSyncLegacyOrder($isSyncLegacyOrder);
                //临时改成同步执行
//                $syncOrderByScopeJob->handle();
                $this->dispatch($syncOrderByScopeJob);
                $responseList[] = ['owner_id' => $item['owner_id'], 'code' => 1, 'msg' => '同步成功'];
            } catch (ApiException $e) {
                $responseList[] = ['owner_id' => $item['owner_id'], 'code' => $e->getErrorCode(), 'msg' => $e->getMessage()];
            } catch (\Exception $e) {
                Log::error('syncOrderByScope:' . $e->getMessage(), [
                    'code' => $e->getCode(),
                    'line' => $e->getLine(),
                    'file' => $e->getFile(),
                    'trace' => explode(PHP_EOL, $e->getTraceAsString()),
                ]);
                $responseList[] = ['owner_id' => $item['owner_id'], 'code' => $e->getCode(), 'msg' => $e->getMessage()];
            }
        }

        return $this->success(compact('responseList'));
    }
    /**
     * 获取同步订单的进度
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function getSyncOrderSchedule(Request $request): JsonResponse
    {
        $this->validate($request, [
//            "begin_time" => 'date',
//            "end_time" => 'date',
            "ownerIdList" => 'array',
        ]);
        $ownerIdList = $request->input('ownerIdList', []);
        $shops = Shop::getListByIdentifiers($ownerIdList)->pluck('id')->toArray();
        if (empty($shops)) {
            $shops = [$request->auth->shop_id];
        }

//        $user_id = $request->auth->user_id;
        $redis = \redis('cache');
        $action = 'syncOrderByScope';
        $order_sync_current = 0;
        $order_sync_total = 0;
        $order_sync_finish_count = 0;
        $order_sync_finish = 0;
        foreach ($shops as $index => $shopId) {
            $key = "$action:sync_current:$shopId";
            $order_sync_current += $redis->get($key);
            $key = "$action:sync_total:$shopId";
            $order_sync_total += $redis->get($key);
            $key = "$action:sync_finish:$shopId";
            $order_sync_finish_count += $redis->get($key);
        }
        if ($order_sync_finish_count == count($shops)) {
            $order_sync_finish = 1;
        }


//        $orderCount = Order::query()
//            ->where($where)
//            ->whereBetween('created_at', [$beginTime, $endTime])
//            ->count();


        return $this->success([
//            'order_count' => $orderCount,
            'order_sync_current' => $order_sync_current,
            'order_sync_total' => $order_sync_total,
            'order_sync_finish' => $order_sync_finish,
        ]);


    }
}
