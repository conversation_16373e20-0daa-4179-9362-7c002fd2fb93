<?php

namespace App\Http\Controllers\Order;

use App\Events\Orders\OrderDeliveryEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Http\StatusCode\StatusCode;
use App\Models\Order;
use App\Models\Package;
use App\Models\Shop;
use App\Models\User;
use App\Models\WaybillHistory;
use App\Services\BusinessException;
use App\Services\Order\OrderDeliveryService;
use App\Utils\ArrayUtil;
use App\Utils\Environment;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use App\Services\Order\OrderPreshipmentService;

/**
 * 订单发货相关的
 */
class OrderDeliveryController extends Controller
{

    /**
     * @var OrderDeliveryService $orderDeliveryService
     */
    private $orderDeliveryService;

    /**
     * @var OrderPreshipmentService $orderPreshipmentService
     */
    private $orderPreshipmentService;

    /**
     * @param OrderDeliveryService $orderDeliveryService
     * @param OrderPreshipmentService $orderPreshipmentService
     */
    public function __construct(OrderDeliveryService $orderDeliveryService, OrderPreshipmentService $orderPreshipmentService)
    {
        $this->orderDeliveryService = $orderDeliveryService;
        $this->orderPreshipmentService = $orderPreshipmentService;
    }

    /**
     * 导入模板发货
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws ApiException
     */
    public function uploadDelivery(Request $request): JsonResponse
    {
        $this->validate($request, [
            'file' => 'required|file|mimes:xls,xlsx',
        ]);
        $file = $request->file('file');
        if ($file->isValid()) {
            $currentUserId = $request->auth->user_id;
            $currentShopId = $request->auth->shop_id;
            $ret = $this->orderDeliveryService->uploadDelivery(
                Shop::getShopIdsByShopIdentifier($currentShopId),
                $file->getClientOriginalName(),
                $file->getRealPath(),
                $currentShopId,
                $currentUserId,
                $request
            );
            //            if (!$ret || sizeof($errors) > 0) {
            //                return $this->fail(StatusCode::OPERATION_FAILED[1], StatusCode::OPERATION_FAILED[0], [], $errors);
            //                \Log::info("批量发货失败", $errors);
            //            }
            return $this->success($ret);
        } else {
            return $this->fail('文件格式错误', StatusCode::OPERATION_FAILED[0]);
        }
    }

    /**
     * 发货
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ErrorCodeException
     * @throws ValidationException
     */
    public function delivery(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "delivers" => "required|array",  //[{"waybillCodeStr":"9865717219352","idStr":"1877","express_code":"YZXB","orderOid":true}]
        ]);
        Log::info('发货接口 请求参数：', [$data]);
        /**
         * 1. 整单发货，2. 拆单发货 3 分包发货
         */
        $mode = $request->input('mode', 1);
        $preshipment = $request->input('preshipment', false);
        $isScan = $request->input('isScan', false);
//        $isIgnoreRefund = $request->input('isIgnoreRefund',0); // 是否忽略退款
        $currentUserId = $request->auth->user_id;
        $currentShopId = $request->auth->shop_id;
        if ($mode == 1) {
            //因为有mode=2的入参，走到了mode=1的分支上， mode=2的入参是拆单发货，单号和订单号是被拉平成数组的，而mode=1 面单号是采用了字符串连接的方式。
            //所以兼容的逻辑是如果一个订单有多个waybillCodeStr，就值保留第一个
            $delivers = $data['delivers'];
            $uniqueIdStr = [];
            $deliversWithoutDuplicate = [];
            foreach ($delivers as $deliver) {
                $idStr = $deliver['idStr'];
                if (!in_array($idStr, $uniqueIdStr)) {
                    $uniqueIdStr[] = $idStr;
                    $deliversWithoutDuplicate[] = $deliver;
                } else {
                    Log::info("重复的订单号", ['idStr' => $idStr]);
                }
            }
            $ret = $this->orderDeliveryService->batchDelivery($deliversWithoutDuplicate, $currentUserId, $currentShopId, $request);
        } elseif ($mode == 2 || $mode == 3) {
            //一单多包的发货
            $ret = $this->orderDeliveryService->orderMultiPackagesDeliver($data['delivers'], $currentUserId, $mode);
        } elseif ($mode == 4) {
            //一单多包的发货
            $ret = $this->orderDeliveryService->batchDeliveryV4($data['delivers'], $currentShopId, $preshipment);
        }
        return $this->success($ret);
    }

    /**
     * 扫描发货
     * @param Request $request
     * @return void
     * @throws ErrorCodeException|ValidationException
     */

    public function scanDeliver(Request $request): JsonResponse
    {
        $this->validate($request, [
            'waybillCode' => 'required|string',
        ]);
        $shopId = $request->auth->shop_id;
        $waybillCode = trim($request->input('waybillCode', ''));
        $shopIdList = $this->getShopIdList($request);
        $scanResult=$this->orderPreshipmentService->scanDeliver($shopId, $shopIdList, $waybillCode);

//         $orderResults = $this->orderDeliveryService->scanDeliver($shopIdList, $waybillCode, $request->auth->user_id);
        return $this->success($scanResult);
    }
    /**
     * 扫描发货
     * @param Request $request
     * @return void
     * @throws ValidationException
     */

    public function scanDeliverV2(Request $request): JsonResponse
    {
        $this->validate($request, [
            "waybillCode" => "required|string",
            "shopIdList" => "array",
        ]);
        $shopId = $request->auth->shop_id;
        $waybillCode = trim($request->input('waybillCode', ''));
        $shopIdList = $this->getShopIdList($request);

        $res = $this->orderDeliveryService->scanDeliverV2($shopId, $shopIdList, $waybillCode);

        return $this->success($res);
    }

    /**
     * 重新发货
     * @throws ValidationException
     */
    public function redelivery(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'type' => 'required|int', // 1 补发 2换货 3变更单号 9其他
            'list' => 'required|array', // [{"tid":"","waybillCode":"123456","wpCode":"123456"}]
            //            'list.*.tid' => 'required|string',
//            'list.*.waybillCode' => 'required|string',
            //            'list.*.beforeWaybillCode' => 'string', // 原单号
            'list.*.beforePtLogisticsIdArr' => 'required|array', // 原单号
            'list.*.packArr' => 'required|array', // 原单号
//            'list.*.wpCode' => 'required|string',
        ]);
        $res = $this->orderDeliveryService->redelivery($request->auth->shop_id, $data);
        return $this->success($res);
    }



    /**
     * 自定义发货（拆单发货）
     * @param Request $request
     * @return JsonResponse
     * @throws ErrorCodeException
     */
    public function customDelivery(Request $request): JsonResponse
    {
        //        {"list":[{"waybillCode":"运单号","wpCode":"快递公司 code","items":[{"orderId":"订单id","orderItemId":"子订单id","num":"数量"}]}]}
        $data = $this->validate($request, [
            'list' => 'required|array',
            'list.*.orderId' => 'required|integer',
            'list.*.waybillCode' => 'required|string',
            'list.*.wpCode' => 'required|string',
            'list.*.items' => 'array',
            'list.*.items.*.orderId' => 'required|integer',
            'list.*.items.*.orderItemId' => 'required|integer',
            'list.*.items.*.num' => 'required|integer',
        ]);
        $preshipment = $request->input('preshipment', false);
//        $consignStatus = [["orderItemId"=>"52266096","isPartConsign"=>false]];
        $consignStatus = $request->input('consignStatus', []);
        /**
         * 通过 orderId 分组，看有没有多订单多包裹，然后循环
         *    通过 items.orderId 分组，看有没有一个包裹多个订单，然后循环
         *        请求发货接口
         *        处理成功请求保存发货包裹
         */
        $list = $this->orderDeliveryService->customDelivery($request->auth->shop_id, $data['list'], $consignStatus, $preshipment,$request->auth->user_id);
        return $this->success($list);
    }
}
