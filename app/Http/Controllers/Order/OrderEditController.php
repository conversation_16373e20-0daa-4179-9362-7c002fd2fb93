<?php

namespace App\Http\Controllers\Order;

use App\Constants\PlatformConst;
use App\Events\Orders\OrderUpdateEvent;
use App\Exceptions\OrderException;
use App\Http\StatusCode\StatusCode;
use App\Jobs\Orders\SyncSaveOrders;
use App\Models\Order;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\User;
use App\Services\Order\OrderRemarkService;
use App\Services\Order\OrderServiceManager;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\StrUtil;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Maatwebsite\Excel\Facades\Excel;
use App\Utils\FileUploadUtil;
use Swoole\Coroutine\WaitGroup;
use function Swoole\Coroutine\run;

/**
 * 订单修改
 */
class OrderEditController extends Controller
{

    private $orderRemarkService;

    /**
     * @param $orderRemarkService
     */
    public function __construct(OrderRemarkService  $orderRemarkService)
    {
        $this->orderRemarkService = $orderRemarkService;
    }

    /**
     * 上传文件修改订单
     * @param Request $request
     * @return void
     */
    public function uploadOrder(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|file|mimes:xls,xlsx',
        ]);
        $file = $request->file('file');
        if ($file->isValid()) {
            $ret = $this->uploadSetOrderRemark($request->auth->user_id, $request->auth->shop_id,
                $file->getClientOriginalName(),
                $file->getRealPath()
            );
            if (!$ret) {
                return $this->fail('操作失败');
            }
            return $this->success('success');
        }
    }


    /**
     * 批量修改代打
     * @param Request $request
     * @return false
     */

    public function batchSetRemark(Request $request)
    {
        $this->validate($request, [
            'orders' => 'required|string',
            'flag' => 'string',
            'remark' => 'string',
            'locked' => 'boolean',
        ]);

        $orders = $request->input('orders');
        $tids = StrUtil::splitString($orders);
        $remark = $request->input('remark');
        $locked = $request->input('locked', false);
        $flag = $request->input('flag', 'GRAY');
        $currentShopId = $request->auth->shop_id;
        $failList=$this->orderRemarkService->batchEditRemark($request->auth->user_id,$currentShopId,$tids,$flag,$remark,
            $locked,$request);
        $data = '操作成功';
        if(!empty($failList)){
            $data=$failList;
        }
        return $this->success($data);
    }



    /**
     * 解析导入的文件，批量修改订单
     * @param string $userId
     * @param int $shopId
     * @param string $fileName 导入文件的名称
     * @param string $path 导入文件的路径
     * @return bool
     */

    public function uploadSetOrderRemark(string $userId, int $shopId, string $fileName, string $path)
    {
        $data = FileUploadUtil::uploadFileReadExcel($fileName, $path);
        if (empty($data) || !is_array($data)) {
            Log::warn('数据解析错误', [$shopId, $path, $data]);
            return false;
        }
        $shop = Shop::find($shopId);
        $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
        $orderService->setUserId($userId);
        $orderService->setShop($shop);
        $shopIds = ShopBind::getAllRelationShopIds($shopId);

        $tids = [];
        foreach ($data as $datum) {
            $tids [] = trim($datum[0]);
        }
        $tids = OrderUtil::batchAppendOrderSuffix($tids);// $this->appendOrderSuffix($tids);
        $tidOrderMap = Order::query()->whereIn('shop_id', $shopIds)->whereIn('tid', $tids)->get()->keyBy("tid");
        \Log::info("批量导入订单匹配到" . $tidOrderMap->count() . "条");
        foreach ($data as $datum) {
            $tid = trim($datum[0]);
            $order = $tidOrderMap->get($tid);
            $tid =OrderUtil::appendOrderSuffix($tid);
            $order = $tidOrderMap->get($tid);
            $remark = trim($datum[1]);
            $locked = trim($datum[2]) > 0 ? true : false;

            $orderData = [];
            $orderData['locked_at'] = null;
            if ($locked) {
                $orderData['locked_at'] = date('Y-m-d H:i:s');
            }
            if ($remark) {
                $orderData['seller_memo'] = json_encode([$remark]);
            }
            if ($order) {
                $shop = $order->shop;
                $userId = $order->user_id;
                $flag = $order->seller_flag;
                $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                $orderService->setUserId($userId);
                $orderService->setShop($shop);

                //请求平台修改留言备注
                $tid = $order->tid;
                $res = $orderService->sendEditSellerRemark($tid, $flag, $remark);
                Log::info("批量更新订单 tid=" . $tid . ",result" . $res);
                if (!$res) {
                    return false;
                }
                Order::query()
                    ->where('shop_id', $order->shop_id)
                    ->where('tid', $tid)
                    ->orWhere('tid', $tid . 'A')
                    ->update($orderData);
            }

        }
        return true;
    }

    /**
     * 修改卖家备注和旗帜
     * @param Request $request
     * @param $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function sellerNoteList(Request $request, $id)
    {
        $sellerFlag = $request->input('seller_flag');
        $sellerMemo = $request->input('seller_memo');

        $columns = ['id', 'tid', 'seller_flag', 'seller_memo'];
        $beforeOrders = Order::query()->where('id', $id)->get($columns)->toArray();
        //修改留言备注
        $ret = $this->orderRemarkService->editRemark($id, $sellerFlag, $sellerMemo);

        if (!$ret) {
            return $this->fail('编辑失败', 400);
        }
        $afterOrder = Order::query()->where('id', $id)->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $afterOrder, 'flag'))->setClientInfoByRequest($request));

        return $this->success('编辑成功');
    }

//
//    /**
//     * 修改平台备注
//     * @param Request $request
//     * @param $id
//     * @return \Illuminate\Http\JsonResponse
//     */
//    public function editPlatformMemo(Request $request, $id)
//    {
//        $exist = Order::findOrFail($id);
//        $exist->platform_memo = $request->input('platform_memo');
//        if (!$exist->save()) {
//            return $this->fail('编辑失败', 400);
//        }
//
//        return $this->success('编辑成功');
//    }

}
