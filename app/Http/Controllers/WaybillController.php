<?php

namespace App\Http\Controllers;

use App\Constants\PlatformConst;
use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\Waybill;
use App\Models\Company;
use App\Models\Template;
use App\Models\WaybillShareAction;
use App\Services\BusinessException;
use App\Services\Waybill\DY\DYApi;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\WaybillUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

/**
 * 第三方电子面单Controller
 */
class WaybillController extends Controller
{
    /**
     * @var WaybillUtil $waybillUtil
     */
    protected  $waybillUtil;

    /**
     * @param WaybillUtil $waybillUtil
     */
    public function __construct(WaybillUtil $waybillUtil)
    {
        $this->waybillUtil = $waybillUtil;
    }

    /**
     * 查询面单授权
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $currentShop=intval($request->input('shopId', $request->auth->shop_id));;
        $scope=$request->input('scope',[1]);
        $withShopId=$request->input('shop_id',null);
        $includeThird=$request->input('includeThird',false);
        return $this->success($this->waybillUtil->getWaybillAuth($currentShop,$scope,$withShopId,$includeThird));
    }

    /**
     * 电子面单授权Url
     * @param Request $request
     * @return JsonResponse
     * @throws \Exception
     */
    public function authUrl(Request $request): JsonResponse
    {
        $this->validate($request, [
            "shop_id" => "required",
            "auth_source" => "required|int",
        ]);
        $bool = WaybillServiceManager::checkAuthSource($request->input('auth_source'));
        if (!$bool) {
            throw new ApiException(ErrorConst::PLATFORM_TYPE_ERROR);
//            throw new \Exception('平台类型错误');
        }
        $waybillService = WaybillServiceManager::init($request->input('auth_source'));
        $url = $waybillService->getLoginUrl($request->input('shop_id'));

        return $this->success($url);
    }

    /**
     * 电子面单授权
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws BusinessException
     * @throws ValidationException
     */
    public function auth(Request $request): JsonResponse
    {
        $authInfo = $this->validate($request, [
            "code" => "required",
            "shop_id" => "required",
            "auth_source" => "required|int",
//            "access_token" => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
//            "owner_id"     => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
//            "owner_name"   => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
//            "expires_in"   => "string|required_if:auth_source," . Waybill::AUTH_SOURCE_TWC,
        ]);
        $result = Waybill::auth($authInfo, $request->auth->user_id, $request->auth->shop_id);
        Log::info("电子面单授权",$result);
        return $this->success($result);
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     * 22-1-27 去掉userId
     * 电子面单服务查询
     */
    public function waybillSearch(Request $request): JsonResponse
    {
        $this->validate($request, [
            "auth_source" => "int",
            "owner_id" => "string",
            "wp_code" => "string",
        ]);
        $where = ['shop_id' => $request->auth->shop_id];
        if ( $request->input('shop_id')) {
            $where = ['shop_id' => $request->input('shop_id')];
        }
        $auth = Waybill::where($where);
        $source = $request->input('auth_source', 0);
        $ownerId = $request->input('owner_id', 0);
        //拼多多站内 && 抖音
        if (in_array($source, [Waybill::AUTH_SOURCE_DY, Waybill::AUTH_SOURCE_KS, Waybill::AUTH_SOURCE_TAOBAO, Waybill::AUTH_SOURCE_JD])) {
            $waybills = Shop::where('user_id', $request->auth->user_id)->get();
            if ($waybills->isEmpty()) {
                return $this->success();
            }
            $result = [];
            foreach ($waybills as $key => $value) {
                $waybillService             = WaybillServiceManager::init($source, $value->access_token);
                $waybill                    = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''), $value->service_id);
                $result[$key]['owner_id']   = $value->identifier;
                $result[$key]['owner_name'] = $value->shop_name;
                $result[$key]['list']       = $waybill;
            }

            return $this->success($result);
        }
        if ($source) {
            $auth->where('auth_source', $source);
        }
        if ($ownerId) {
            $auth->where('owner_id', $ownerId);
        }
        $waybills = $auth->get();

        $old = collect($waybills)->where('auth_source', Waybill::AUTH_SOURCE_TWC)
            ->where('created_at', '<', '2020-05-07 17:00:00')
            ->count();
        if ($old > 0) {
            return $this->fail('淘宝电子面单授权失效，请重新授权');
        }

        if ($waybills->isEmpty()) {
            return $this->success();
        }

        $result = [];
        foreach ($waybills as $key => $value) {
            $waybillService = WaybillServiceManager::init($value->auth_source, $value->access_token);
            $waybill = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''));
            $result[$key]['owner_id'] = $value->owner_id;
            $result[$key]['owner_name'] = $value->owner_name;
            $result[$key]['list'] = $waybill;
        }

        return $this->success($result);
    }

    /**
     * @param Request $request
     * @param         $ownerId
     * @return JsonResponse
     *<AUTHOR>
     * 22-1-27 去掉userId
     * 面单查询 by ownerId
     */
    public function getWaybillByOwnerId(Request $request, $ownerId)
    {
        $auth = Waybill::where('shop_id', $request->auth->shop_id)
            ->where('owner_id', $ownerId)->firstOrFail();
        $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
        $waybills = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''));
        $result = [];
        foreach ($waybills as $waybill) {
            $result[] = collect(config('express_company'))->where('wpCode', $waybill->wp_code)->first();
        }

        return $this->success($result);
    }

    /**
     * @param Request $request
     * @param         $ownerId
     * @return JsonResponse
     *<AUTHOR> 22-1-27 去掉userID
     * 面单解绑授权
     */
    public function unbind(Request $request, $ownerId)
    {
        $ret = Waybill::where('shop_id', $request->auth->shop_id)
            ->where('owner_id', $ownerId)
            ->delete();
        if (!$ret) {
            return $this->fail('解绑失败');
        }

        return $this->success();
    }


    /**
     * 获取电子面单的订阅信息
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws ApiException
     * <AUTHOR>
     * 22-1-27 去掉userId
     * 面单查询 by ownerId
     */
    public function getWaybillByOwnerIdAndBranch(Request $request): JsonResponse
    {
        $this->validate($request, [
            'owner_id'    => "required",
            'auth_source' => "required"
        ]);
        $ownerId = $request->input('owner_id');
        $authSource = $request->input('auth_source');
//        if (!in_array($authSource, [Waybill::AUTH_SOURCE_DY,Waybill::AUTH_SOURCE_TAOBAO,Waybill::AUTH_SOURCE_KS,Waybill::AUTH_SOURCE_WXSP])) {
//            $auth = Waybill::where('shop_id', $request->auth->shop_id)
//                ->where('owner_id', $ownerId)->firstOrFail();
//        } else {
////            $auth = Shop::query()->where('identifier', $ownerId)->firstOrFail();
//            $auth = Shop::firstByIdentifier($ownerId);
//            $auth->auth_source = $authSource;
//        }
        $shopId = intval($request->input('shopId', $request->auth->shop_id));
        $waybillAuth = WaybillUtil::findShopWaybillAuth($shopId, $authSource, $ownerId, false);
        $waybillService = WaybillServiceManager::init($waybillAuth->auth_source, $waybillAuth->access_token);
        if ($waybillAuth instanceof Shop){
            $waybillService->setShop($waybillAuth);
        }

        $waybills = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''));
        $expressCompanyArr = [];
        foreach ($waybills as $waybill) {
            $temp = collect(config('express_company'))->where('wpCode', $waybill['wp_code'])->first();
            if (!empty($temp)) {
                $expressCompanyArr[] = $temp;
            }
        }
//        return $this->success($result);
        return $this->success(compact('expressCompanyArr','waybills'));
    }

    /**
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     * @throws ValidationException
     * @throws ApiException
     * <AUTHOR>
     * 22-1-27 去掉userID
     */

    public function addWaybillShare(Request $request)
    {
        $data = $this->validate($request, [
            'balanceLimit' => "required|int",
            'shopId' => "required|int",
            'authSource' => "required|int",
            'branchName' => "string",
            'province' => "required|string",
            'city' => "required|string",
            'district' => "required|string",
            'detail' => "required|string",
            'wpCode' => "required|string",
            'wpName' => "required|string",
            'sourceOwnerId' => "required|string",
            'shopCode' => "string",
            'shopName' => "string",
            'ownerName' => "required|string",
            'street_name' => "sometimes",
        ]);

        $shopCode = trim($request->input('shopCode', ''));
        $shopName = trim($request->input('shopName', ''));
        $wpName = $request->input('wpName', '');
        $shopId = $request->input('shopId', '');
        $wpCode = $request->input('wpCode', '');
        $sourceOwnerId = $request->input('sourceOwnerId', '');
        $balanceLimit = $request->input('balanceLimit', 0);
        $branchName = $request->input('branchName', '');
        $branchCode = $request->input('branchCode', '');
        $authSource = $request->input('authSource', 0);
        $ownerName = $request->input('ownerName', '');

        if ($shopCode || $shopName) {
            if ($shopCode){
                $shop = Shop::where('shop_code', $shopCode)->first();
            }else{
                $shop = Shop::where('shop_name', $shopName)->first();
            }
//            $shop = Shop::where('shop_code', $shopCode)->first();
            if (!empty($shop)) {
                if ($shop->id == $shopId) {
                    return $this->fail('自己店铺不能分享给自己');
                }
            } else {
                return $this->fail('对方店铺不存在');
            }
            $bindShopIds = ShopBind::getAllRelationShopIds($shopId);
            if (in_array($shop->id, $bindShopIds)) {
                return $this->fail('不能共享给已绑定的店铺');
            }
        } else {
            return $this->fail('请输入对方的店铺名称或是店铺ID');
        }

        //查询接受分享者的网点公司是否存在
        $company = Company::where([
//            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'auth_source' => $authSource,
            'wp_name' => $wpName,
            'wp_code' => $wpCode,
            'branch_name' => $branchName,
            'owner_id' => $sourceOwnerId,
            'province' => $data['province'],
            'city' => $data['city'],
            'district' => $data['district'],
            'detail' => $data['detail'],
            'street' => $data['street_name'] ?? ''
        ])->first();

        //网点不存在创建
        if ($company) {
            return $this->fail('电子面单网点信息已经分享给对方店铺!');
        } else {
            $addrArr = [
                'province' => $data['province'],
                'city' => $data['city'],
                'district' => $data['district'],
                'detail' => $data['detail'],
                'street' => $data['street_name'] ?? ''
            ];

            $auth=WaybillUtil::findShopWaybillAuth($shopId, $authSource, $sourceOwnerId);

            $waybillService = WaybillServiceManager::init($auth->auth_source, $auth->access_token);
            if ($auth instanceof Shop) {
                $waybillService->setShop($auth);
            }
            $waybill = $waybillService->waybillSubscriptionQuery($wpCode, $auth->service_id);
            Log::info('添加共享网点', $waybill);
            $waybillTemp = $waybillService->getCloudPrintStdTemplates($wpCode);

            $waybillCompany = $waybill[0];
            $express = Company::create(array_merge([
                'user_id' => $shop->user_id,
                'shop_id' => $shop->id,
                'auth_source' => $authSource,
                'wp_code' => $wpCode,
                'owner_id' => $sourceOwnerId,
                'owner_name' => $ownerName,
                'branch_name' => $branchName,
                'branch_code' => $branchCode,
                'wp_name' => $wpName,
                'status' => Company::EXPRESS_COMPANY_STATUS_OPEN,
                'source' => Company::SOURCE_COMPANY_STATUS_YES,
                'source_shopid' => $shopId,
                'source_status' => Company::SOURCE_COMPANY_STATUS_OPEN,
                'quantity' => $balanceLimit,
                'cancel_quantity' => Company::INIT_QUANTITY,
                'recycled_quantity' => Company::INIT_QUANTITY,
                'allocated_quantity' => Company::INIT_QUANTITY,
                'settlement_code'=> ($waybillCompany['branch_account_cols'])[0]['settlement_code'] ?? "",
                'templates' => json_encode($waybillTemp),
                'platform_account_id' =>($waybillCompany['branch_account_cols'])[0]['platform_account_id'] ?? "",// $company->platform_account_id,
                'platform_shop_id' => ($waybillCompany['branch_account_cols'])[0]['platform_shop_id'] ?? ""   // $company->platform_shop_id
            ], $addrArr));

            if ($express) {
                //单号分享记录
                $ret = WaybillShareAction::create(array_merge([
                    //'user_id' => $request->auth->user_id,
                    'shop_id' => $shopId,
                    'shop_name' => $shop->shop_name,
                    'name' => $shop->name,
                    'company_id' => $express->id,
                    'identifier' => $shop->identifier,
                    'branch_name' => $branchName,
                    'wp_code' => $wpCode,
                    'wp_name' => $wpName,
                    'balanceLimit' => $balanceLimit,
                    'action' => WaybillShareAction::ACTION_STATUS_CREATE,
                ], $addrArr));
            } else {
                throw new BusinessException('网点创建失败！');
            }
            return $this->success();
        }
    }

    /**
     * 只是获取当前登录店铺的电子面单服务列表（含三方和站外）
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     * 22-1-26去掉了userId
     * 电子面单服务查询
     */
    public function waybillQuery(Request $request): JsonResponse
    {
        $this->validate($request, [
            "auth_source" => "int",
            "wp_code" => "string",
        ]);
        $currentShop=intval($request->input('shopId', $request->auth->shop_id));
        $scope=$request->input('scope',[1]);

//        //拼多多站内
//        if (in_array(config('app.platform'), [PlatformConst::TAOBAO, Waybill::AUTH_SOURCE_JD])) {
//            $waybills = Shop::where('user_id', $request->auth->user_id)->get();
//            if ($waybills->isEmpty()) {
//                return $this->success();
//            }
//            $result = [];
//            foreach ($waybills as $key => $value) {
//                switch (config('app.platform')) {
//                    case PlatformConst::TAOBAO:
//                        $source = Waybill::AUTH_SOURCE_TAOBAO;
//                        break;
//                    case Waybill::AUTH_SOURCE_JD:
//                        $source = Waybill::AUTH_SOURCE_JD;
//                        break;
//                    default:
//                        $source = 0;
//                }
//                $waybillService             = WaybillServiceManager::init($source, $value->access_token);
//                $waybill                    = $waybillService->waybillSubscriptionQuery($request->input('wp_code', ''), $value->service_id);
//                $result[$key]['owner_id']   = $value->identifier;
//                $result[$key]['owner_name'] = $value->shop_name ?? $value->name;
//                $result[$key]['auth_source']= $source;
//                $result[$key]['list']       = $waybill;
//            }
//
//            return $this->success($result);
//        }



        return $this->success($this->waybillUtil->getAllWaybillSubscription($currentShop,"",$scope));
    }


    /**
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     * 22-1-26去掉了userId
     */
    public function waybillLast(Request $request)
    {
        $this->validate($request, [
            "auth_source" => "int",
        ]);

        $source = $request->input('auth_source', 0);

        $auth = Waybill::where([
            'shop_id' => $request->auth->shop_id,
        ]);

        if ($source) {
            $auth->where('auth_source', $source);
        }

        $waybills = $auth->orderBy('created_at', 'DESC')->first();
        return $this->success($waybills);
    }

    /**
     * 获取共享的
     * @param Request $request
     * @return JsonResponse
     */
    public function getShareWaybill(Request $request)
    {
        $wpCode = $request->input('wp_code', '');
        $authSource = $request->input('auth_source', '');

        $where = [
            'shop_id' => $request->auth->shop_id,
            'source' => Company::SOURCE_COMPANY_STATUS_YES,
            'source_status' => Company::SOURCE_COMPANY_STATUS_OPEN
        ];
        if ($wpCode && $authSource) {
            if (in_array($authSource, [Waybill::AUTH_SOURCE_TWC, Waybill::AUTH_SOURCE_LINK])) {
                $authSource = [Waybill::AUTH_SOURCE_TWC, Waybill::AUTH_SOURCE_LINK];
            }
            $where['wp_code'] = $wpCode;
            $where['auth_source'] = $authSource;
        }
        $res = Company::query()->where($where)->get();
        //填充增值服务
        $serviceInfoColsMap = collect(config('dy_service_attributes'))->all();
        foreach ($res as $key => $val) {
            if ($val['auth_source'] == Waybill::AUTH_SOURCE_DY) {
                if (array_key_exists($val['wp_code'], $serviceInfoColsMap)) {
                    $res[$key]['service_info_cols'] = $serviceInfoColsMap[$val['wp_code']];
                }
            } else {
                $waybillAuth = Waybill::query()
                    ->where(['owner_id' => $val['owner_id'], 'owner_name' => $val['owner_name']])
                    ->orderBy('id', 'desc')
                    ->first();
                if (empty($waybillAuth)){
                    $waybillAuth = Shop::query()->where('identifier', $val['owner_id'])->first();
                    $source = $val['auth_source'];
                }else{
                    $source = $waybillAuth->auth_source;
                }
                try {
                    $waybillService = WaybillServiceManager::init($source, $waybillAuth->access_token);
                    $waybill = $waybillService->waybillSubscriptionQuery($val['wp_code']);
                    Log::info('查询电子面单', [$waybill]);
                    if (!empty($waybill)) {
                        $res[$key]['service_info_cols'] = $waybill[0]['branch_account_cols'][0]['service_info_cols'];
                    }
                } catch (\Exception $e) {
                    Log::error('查询电子面单错误：' . $e->getMessage());
                    continue;
                }
            }

        }
        return $this->success($res);
    }

    public function addShareTemplate(Request $request)
    {
        $this->validate($request, [
            'company_id' => "required|int",
            'template_name' => "required|string",
        ]);

        $companyId = $request->input('company_id');
        $templateName = $request->input('template_name');
        $templateArr = $request->input('customTemplate');

        $company = Company::query()->where('id', $companyId)->first();
        $obj = json_decode($company->templates);
        if ($company->auth_source == Waybill::AUTH_SOURCE_TWC || $company->auth_source == Waybill::AUTH_SOURCE_LINK) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 6;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 1;
                    break;
                default:
                    $standardWaybillType = $templateArr['style'];
            }
            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
            //快运为4
            if (empty($sample) && $templateArr['size'] == Template::TEMPLATE_SIZE_100_180) {
                $sample = collect($obj)->where('standard_waybill_type', 4)->first();
            }
        } else if ($company->auth_source == Waybill::AUTH_SOURCE_DY) {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 1;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 2;
                    break;
                default:
                    $standardWaybillType = 1;
            }
            if ($templateArr['wp_code'] == 'shunfeng' && $templateArr['size'] == Template::TEMPLATE_SIZE_100_150) {
                $standardWaybillType = 2;
            }

            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
        } else {
            switch ($templateArr['size']) {
                case Template::TEMPLATE_SIZE_76_130:
                    $standardWaybillType = 3;
                    break;
                case Template::TEMPLATE_SIZE_100_180:
                    $standardWaybillType = 1;
                    break;
                default:
                    $standardWaybillType = $templateArr['style'];
            }
            $sample = collect($obj)->where('standard_waybill_type', $standardWaybillType)->first();
        }

        if ($company->auth_source == Waybill::AUTH_SOURCE_DY && $templateArr['wp_code'] == 'youshuwuliu') {
            $templateUrl = 'https://sf3-ttcdn-tos.pstatp.com/obj/logistics-davinci/template/template_100.xml';
        } else {
            $templateUrl = $sample->standard_template_url;
        }

        //自定义区域默认
        $customConfig = Template::NORMAL_CONFIG_100_180;
        if ($templateArr['size'] == Template::TEMPLATE_SIZE_76_130) {
            $customConfig = Template::NORMAL_CONFIG_76_133;
        }
        $data = [
            'user_id' => $request->auth->user_id,
            'shop_id' => $request->auth->shop_id,
            'company_id' => $company->id,
            'auth_source' => $company->auth_source,
            'style' => $company->auth_source,
            'type' => $templateArr['type'],
            'template_url' => $templateUrl,
            'name' => $templateName,
            'description' => $templateArr['description'] ?? '',
            'default_print' => $templateArr['default_print'] ?? '',
            'custom_config' => $customConfig,
            'picture' => $templateArr['picture'],
            'picture_height' => $templateArr['picture_height'],
            'picture_width' => $templateArr['picture_width'],
            'waybill_type' => $templateArr['waybill_type'],
            'width' => $templateArr['width'],
            'height' => $templateArr['height'],
            'time_delivery' => $templateArr['time_delivery'] ?? 0,
            'insure' => $templateArr['insure'] ?? 0,
            'show_logo' => $templateArr['show_logo'] ?? 0,
            'horizontal' => $templateArr['horizontal'] ?? 0,
            'vertical' => $templateArr['vertical'] ?? 0,
            'wp_code' => $templateArr['wp_code'],
            'wp_name' => $templateArr['wp_name'],
            'parent_template_id' => $templateArr['parent_template_id'],
            'merge_template_url' => $templateArr['merge_template_url'],
            'owner_id' => $company->owner_id,
            'owner_name' => $company->owner_name,
            'shipping_address_id' => isset($templateArr['shopping_address_id']) ? $templateArr['shopping_address_id'] : 0,
        ];

        $template = Template::create($data);
        if (!$template) {
            throw new BusinessException('面单模板创建失败！');
        }

        return $this->success();
    }
}
