<?php

namespace App\Http\Controllers;

use App\Models\PlatformAddress;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use App\Services\PlatformAddress\PlatformAddressService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PlatformAddressController extends Controller
{
    /**
     * @var PlatformAddressService
     */
    private $platformAddressService;

    function __construct(PlatformAddressService $platformAddressService)
    {
        $this->platformAddressService = $platformAddressService;
    }

    /**
     * 获取平台地址列表
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $params = $this->validate($request, [
            'shop_id' => 'required|integer',
            'search' => 'nullable|string',
            'offset' => 'nullable|integer',
            'limit' => 'nullable|integer',
            'order_by' => 'nullable|string',
        ]);

        $shopId = $params['shop_id'];
        $search = $params['search'] ?? '';
        $offset = $params['offset'] ?? 0;
        $limit = $params['limit'] ?? 20;
        $orderBy = $params['order_by'] ?? 'id desc';

        $condition = [
            'shop_id' => $shopId,
        ];

        $addresses = PlatformAddress::search($condition, $search, $offset, $limit, $orderBy);
        $total = PlatformAddress::where($condition)->count();

        return $this->success([
            $addresses,
            'pagination' => [
                'total' => $total,
                'count' => count($addresses),
                'offset' => $offset,
                'limit' => $limit,
            ],
        ]);
    }

    /**
     * 创建平台地址
     * @param Request $request
     * @return JsonResponse
     */
    public function create(Request $request): JsonResponse
    {
        $validate = $this->validate($request, [
            'shop_id' => 'required|integer',
            'name' => 'required|string|max:32',
//            'contact' => 'required|string|max:50',
            'phone' => 'nullable|string|max:11',
            'common_phone' => 'nullable|string|max:50',
            'company_phone' => 'nullable|string|max:50',
//            'postal_code' => 'required|string|max:32',
            'province' => 'required|string|max:32',
            'province_id' => 'required|int',
            'city' => 'required|string|max:32',
            'city_id' => 'required|int',
            'district' => 'required|string|max:32',
            'district_id' => 'required|int',
            'town' => 'string|max:32',
            'town_id' => 'int',
            'address' => 'required|string|max:64',
            'is_default' => 'nullable|integer|in:0,1',
            'is_send_default' => 'nullable|integer|in:0,1',
            'link_type' => 'nullable|integer|in:0,1,2', // 0-手机，1-普通座机，2-企业座机
            'remark' => 'nullable|string|max:255',
        ]);
        $shop = Shop::query()->findOrFail($validate['shop_id']);

        $saveData = $validate;
        try {
            DB::beginTransaction();

            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);

//            if ($saveData['link_type'] == 0){
//                $saveData['contact'] = $saveData['phone'];
//            }elseif ($saveData['link_type'] == 1){
//                $saveData['contact'] = $saveData['common_phone'];
//            }elseif ($saveData['link_type'] == 2){
//                $saveData['contact'] = $saveData['company_phone'] ?? '';
//            }
            $address_id = $orderService->sendCreateShopAddress($saveData);
            $saveData['address_id'] = $address_id;

            // 如果设置为默认退货地址，则将其他地址设为非默认
//            if (isset($params['is_default']) && $params['is_default'] == 1) {
//                PlatformAddress::where('shop_id', $params['shop_id'])
//                    ->where('is_default', 1)
//                    ->update(['is_default' => 0]);
//            }
//
//            // 如果设置为默认发货地址，则将其他地址设为非默认
//            if (isset($params['is_send_default']) && $params['is_send_default'] == 1) {
//                PlatformAddress::where('shop_id', $params['shop_id'])
//                    ->where('is_send_default', 1)
//                    ->update(['is_send_default' => 0]);
//            }

            $address = PlatformAddress::create($saveData);

            DB::commit();
            return $this->success($address);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建平台地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $validate,
            ]);
            return $this->fail('创建平台地址失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取平台地址详情
     * @param int $id
     * @return JsonResponse
     */
    public function show(int $id): JsonResponse
    {
        $address = PlatformAddress::find($id);
        if (!$address) {
            return $this->fail('平台地址不存在', 404);
        }

        return $this->success($address);
    }

    /**
     * 更新平台地址
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request): JsonResponse
    {
        $params = $this->validate($request, [
            'id' => 'required|integer',
        ]);

        $id = $params['id'];
        $address = PlatformAddress::find($id);
        if (!$address) {
            return $this->fail('平台地址不存在', 404);
        }

        $params = $this->validate($request, [
            'shop_id' => 'required|int',
            'name' => 'nullable|string|max:32',
//            'contact' => 'nullable|string|max:50',
            'phone' => 'nullable|string|max:11',
            'common_phone' => 'nullable|string|max:50',
            'company_phone' => 'nullable|string|max:50',
//            'postal_code' => 'nullable|string|max:32',
            'province' => 'required|string|max:32',
            'province_id' => 'required|int',
            'city' => 'required|string|max:32',
            'city_id' => 'required|int',
            'district' => 'required|string|max:32',
            'district_id' => 'required|int',
            'town' => 'string|max:32',
            'town_id' => 'int',
            'address' => 'nullable|string|max:64',
            'is_default' => 'nullable|integer|in:0,1',
            'is_send_default' => 'nullable|integer|in:0,1',
            'link_type' => 'nullable|integer|in:0,1,2',
            'remark' => 'nullable|string|max:255',
        ]);
        if ($address->shop_id != $params['shop_id']) {
            return $this->fail('店铺ID不匹配', 400);
        }
        $shop = Shop::query()->findOrFail($address->shop_id);

        $saveData = $params;
        try {
            DB::beginTransaction();

            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);

            // 转换参数为接口需要的格式

//            if ($saveData['link_type'] == 0){
//                $saveData['contact'] = $saveData['phone'];
//            }elseif ($saveData['link_type'] == 1){
//                $saveData['contact'] = $saveData['common_phone'];
//            }elseif ($saveData['link_type'] == 2){
//                $saveData['contact'] = $saveData['company_phone'] ?? '';
//            }
            $saveData['address_id'] = $address->address_id;
            $orderService->sendUpdateShopAddress($saveData);

            $address->update($saveData);

            DB::commit();
            return $this->success($address);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新平台地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'params' => $params,
            ]);
            return $this->fail('更新平台地址失败: ' . $e->getMessage(), 500);
        }
    }


    /**
     * 设置默认退货地址
     * @param Request $request
     * @return JsonResponse
     */
    public function setDefault(Request $request): JsonResponse
    {
        $params = $this->validate($request, [
            'id' => 'required|integer',
        ]);

        $id = $params['id'];
        $address = PlatformAddress::find($id);
        if (!$address) {
            return $this->fail('平台地址不存在', 404);
        }

        try {
            DB::beginTransaction();

            // 将其他地址设为非默认
            PlatformAddress::where('shop_id', $address->shop_id)
                ->where('id', '!=', $id)
                ->where('is_default', 1)
                ->update(['is_default' => 0]);

            // 设置当前地址为默认
            $address->is_default = 1;
            $address->save();

            DB::commit();
            return $this->success(null, '设置成功');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置默认退货地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id,
            ]);
            return $this->fail('设置默认退货地址失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 设置默认发货地址
     * @param Request $request
     * @return JsonResponse
     */
    public function setDefaultSend(Request $request): JsonResponse
    {
        $params = $this->validate($request, [
            'id' => 'required|integer',
        ]);

        $id = $params['id'];
        $address = PlatformAddress::find($id);
        if (!$address) {
            return $this->fail('平台地址不存在', 404);
        }

        try {
            DB::beginTransaction();

            // 将其他地址设为非默认
            PlatformAddress::where('shop_id', $address->shop_id)
                ->where('id', '!=', $id)
                ->where('is_send_default', 1)
                ->update(['is_send_default' => 0]);

            // 设置当前地址为默认
            $address->is_send_default = 1;
            $address->save();

            DB::commit();
            return $this->success(null, '设置成功');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('设置默认发货地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'id' => $id,
            ]);
            return $this->fail('设置默认发货地址失败: ' . $e->getMessage(), 500);
        }
    }

    /**
     * 同步平台地址数据
     * @param Request $request
     * @return JsonResponse
     */
    public function syncAddress(Request $request): JsonResponse
    {
        $params = $this->validate($request, [
            'shop_id' => 'required|integer',
        ]);

        $shopId = $params['shop_id'];
        $shop = Shop::query()->findOrFail($shopId);
        $orderService = OrderServiceManager::create();
        $orderService->setShop($shop);
        $addressList = $orderService->sendShopAddressList();
        Log::info('addressList', [$addressList]);

        if (empty($addressList)) {
            return $this->success([], '没有地址数据需要同步');
        }

        try {
            DB::beginTransaction();

            $successCount = 0;
            $failCount = 0;
            $errors = [];

            $addressIds = array_column($addressList, 'address_id');
            foreach ($addressList as $addressData) {
                try {
                    $addressParams = $addressData;
                    $addressParams['shop_id'] = $shopId;

                    PlatformAddress::query()->firstOrCreate([
                        'shop_id' => $shopId,
                        'address_id' => $addressParams['address_id'],
                    ],$addressParams);

                    $successCount++;
                } catch (\Exception $e) {
                    $failCount++;
                    $errors[] = [
                        'address_data' => $addressData,
                        'error' => $e->getMessage(),
                    ];
                    Log::error('同步平台地址失败', [
                        'message' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                        'address_data' => $addressData,
                    ]);
                }
            }

            if (!empty($addressIds)){
                PlatformAddress::query()->where('shop_id', $shopId)
                    ->whereNotIn('address_id', $addressIds)
                    ->delete();
            }
            DB::commit();

            return $this->success([
                'total' => count($addressList),
                'success' => $successCount,
                'fail' => $failCount,
                'errors' => $errors,
            ], '同步平台地址完成');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('同步平台地址失败', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return $this->fail('同步平台地址失败: ' . $e->getMessage(), 500);
        }
    }
}
