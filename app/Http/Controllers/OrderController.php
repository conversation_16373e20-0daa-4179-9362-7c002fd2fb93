<?php

namespace App\Http\Controllers;

use App\Constants\DoudianMsgTag;
use App\Constants\ErrorConst;
use App\Constants\OperationLogTypeConst;
use App\Constants\OrderIndexTabConst;
use App\Constants\PlatformConst;
use App\Events\Orders\OrderDecryptEvent;
use App\Events\Orders\OrderDeliveryEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Events\Orders\OrderQueryEvent;
use App\Events\Orders\OrderShippingOrder;
use App\Events\Orders\OrderUpdateEvent;
use App\Events\SqlLogEvent;
use App\Exceptions\ApiException;
use App\Exceptions\ClientException;
use App\Exceptions\ErrorCodeException;
use App\Exceptions\PrintException;
use App\Http\StatusCode\StatusCode;
use App\Jobs\DoudianMsg\MsgServiceManagerJob;
use App\Jobs\Orders\SyncOrderByScopeJob;
use App\Jobs\KsMsg\KsMsgServiceManagerJob;
use App\Models\Address;
use App\Models\Company;
use App\Models\AbnormalOrder;
use App\Models\OperationLog;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\Order;
use App\Models\OrderCipherInfo;
use App\Models\OrderExtra;
use App\Models\OrderItemExtra;
use App\Models\Package;
use App\Models\OrderItem;
use App\Models\PackageOrder;
use App\Models\PrintRecord;
use App\Models\ShopBind;
use App\Models\ShopExtra;
use App\Models\Template;
use App\Models\User;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Models\Shop;
use App\Models\QueryArea;
use App\Models\QueryTemplate;
use App\Services\AntiSpam\SensitiveType;
use App\Services\BusinessException;
use App\Services\CommonResponse;
use App\Services\Order\Impl\DyOrderImpl;
use App\Services\Client\YchClient;
use App\Services\Order\Impl\TaobaoOrderImpl;
use App\Services\Order\OderLogicService;
use App\Services\Order\OrderDeliveryService;
use App\Services\Order\OrderQueryBuilder;
use App\Services\Order\OrderRemarkService;
use App\Services\Order\OrderServiceManager;
use App\Services\Order\OrderWaybillService;
use App\Services\Order\Request\OrderSearchRequest;
use App\Services\PrintCheckService;
use App\Services\Printing\OrderPrintService;
use App\Services\Waybill\WaybillServiceManager;
use App\Utils\ArrayUtil;
use App\Utils\Environment;
use App\Utils\ShopUtil;
use Carbon\Carbon;
use Closure;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Maatwebsite\Excel\Facades\Excel;
use phpDocumentor\Reflection\Types\Collection;
use App\Services\Goods\GoodsQueryRequest;
use App\Services\Goods\GoodsQueryService;
use App\Services\Goods\PickingGoodsRequest;
use App\Services\AntiSpam\AntiSpamService;
use App\Services\AntiSpam\OrderAntispamRequest;
use App\Services\AntiSpam\OperationType;

class OrderController extends Controller
{
    private $goodsQueryService;
    private $antiSpamService;
    private $defaultGroupColumn = 'receiver_phone';

    /**
     * @param GoodsQueryService $goodsQueryService
     * @param AntiSpamService $antiSpamService
     */
    public function __construct(GoodsQueryService $goodsQueryService, AntiSpamService $antiSpamService)
    {
        $this->goodsQueryService = $goodsQueryService;
        $this->antiSpamService = $antiSpamService;
    }

    /**
     * 订单列表
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ErrorCodeException
     * @throws ValidationException
     */
    public function index(Request $request)
    {
        $data = $this->validate($request, [
            "search" => 'string',
            "begin_at" => 'date',
            "end_at" => 'date',
            "tab_flag" => "string",

            "addressGroupId" => 'int',   //筛选地址
            //"fixedCondition"       => "array", //"HAVA_SELLER_MEMO", "BUYER_REQ_REFUND", "LOCKED", "MORE_GOOD_COUNT"
            "displayMerge" => "boolean",   //合并展示

            "flag" => 'sometimes|nullable',
            "goodsId" => 'sometimes|nullable',
            //"goodsNum"         => 'sometimes|nullable',
            "skuStyle" => 'sometimes|nullable',
            // "remark_status"    => 'int',
            "sku_value" => 'string',
            // "wp_code"          => 'string',
            "selectItem" => 'int',
            "ownerIdList" => 'array',
            "factoryOwnerIdList" => 'array',
            "quickFilterValue" => 'string',
            "skuIdList" => 'array',
            "includeOrNot" => 'int',
            "smart_logistics" => 'string',
            "printMode" => 'int',
            "custom_print_content" => 'string',
            "custom_group" => 'string',
            "tidList" => 'array',
        ]);

        $massMode = $request->input('massMode', 'false');
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();

//        $groupColumn = $this->defaultGroupColumn;

        $displayMerge = $request->input('displayMerge'); //合并展示
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);

        $orderSearchRequest = new OrderSearchRequest();
        if (true) {
            $orderSearchRequest->validate($request);
            $orderQueryBuilder = new OrderQueryBuilder();
            $orderSearchRequest->authShopId = $request->auth->shop_id;
            $groupColumn = $orderSearchRequest->getGroupColumn();
            $query = $orderQueryBuilder->buildQuery($orderSearchRequest);
            $rows_found = $orderQueryBuilder->getCount();
            $orderCount = $orderQueryBuilder->getOrderCount();
            $buyerCount = $orderQueryBuilder->getBuyerCount();
        }
        // 修复模型 appends 导致数据查询过多
        $with = ['orderItem', 'orderItem.customGoodsSkus', 'orderItem.customGoods', 'orderItem.shippedPackageOrders',
            'shop', 'trace', 'orderCipherInfo',
            'packages' => function ($query) use ($orderSearchRequest) {
                if ($orderSearchRequest->isUnshippedList) {
                    // 没有发货且没有预发货的包裹
//                    $query->whereNull('send_at');
                    $query->where(DB::raw('IFNULL(packages.status, 0)'), '!=', Package::ORDER_STATUS_DELIVERED);
                    $query->whereNull('pre_shipment_at');
                }
            }, 'packages.orders.orderItem.shippedPackageOrders',
            'packages.waybillHistory:id,package_id,created_at',
            'orderExtra', 'orderItem.orderItemExtra'];
        if ($orderSearchRequest->isShippedList){ // 已发货列表
            $with[] = 'packages.printPackageOrders';
            $with[] = 'ptLogistics';
        }
        $query->with($with);


        $sql = getSqlByQuery($query);
        $ret = $query->get();

        event(new SqlLogEvent($user, $shop, time(), $sql));


        $ret = $this->handleOrderData($ret);

        $sort = $orderSearchRequest->sortArr;
        $tidArr = collect($ret)->pluck('tid')->toArray();
        if ($displayMerge) {
            // 增加运行内存防止溢出
            ini_set('memory_limit', '1024M');
//            $mergeFlagOrders = $this->handleMergeFlagOrders($ret, $tab_flag, $shopIds);
            $mergeFlagOrders = [];

            $orderBy = $sort[0] ?? 'order_created_at';
            $orderSort = $sort[1] ?? 'desc';
            $ret = $this->handleMergeOrder($request, $ret, $groupColumn, $mergeFlagOrders, $orderBy, $orderSort, $orderSearchRequest);
            $orderSort = $orderSort == 'asc' ? SORT_ASC : SORT_DESC;

            // 排序
            if ($orderBy == 'num') {
                array_multisort(array_column($ret, 'total_num'), $orderSort, $ret);
            } elseif ($orderBy == 'payment') {
                array_multisort(array_column($ret, 'total_payment'), $orderSort, $ret);
            } elseif ($orderBy == 'promise_ship_at') {
                array_multisort(array_column($ret, 'min_promise_ship_at'), $orderSort, $ret);
            } else {
                array_multisort(array_column($ret, $orderBy), $orderSort, $ret);
//                if ($orderSearchRequest->isShippedList){
//                    array_multisort(array_column($ret, $orderBy), $orderSort, $ret);
//                }
            }

        } else {
            $ret = $this->handleOrderPackages($ret);
        }


        if ($massMode == 'true') {
            $ret = $this->handleOrderFieldFilter($ret);
        } else {
            foreach ($ret as $index => $item) {
                unset($ret[$index]['shop']);
                foreach ($ret[$index]['order_item'] as $index2 => $order_item) {
                    unset($ret[$index]['order_item'][$index2]['custom_goods_skus']);
                    unset($ret[$index]['order_item'][$index2]['custom_goods']);
                }
                foreach ($ret[$index]['packages'] as $index2 => $order_item) {
                    unset($ret[$index]['packages'][$index2]['orders']);
                }
            }
        }

        // add Ych Order Log
        if (config('app.platform') == PlatformConst::TAOBAO && $rows_found && config('app.env') != 'dev') {
            $tids = collect($ret)->pluck('tid')->toArray();
            if (count($tids) > 0) {
                $tids = array_chunk($tids, 100);
                foreach ($tids as $tidGroup) {
                    $ati = request()->cookie('_ati', '');
                    $ychClient = new YchClient();
                    $params = [];
                    $params["ati"] = $ati;
                    $params["userId"] = $request->auth->shop_name ?? '未知';  // 当前的用户名， 如果是子账号， 填写子账号nick
                    $params["userIp"] = getRealIp();
                    $params["url"] = env('APP_DOMAIN') . '/order/index';  // 当前访问的url
                    $params["tradeIds"] = implode(",", $tidGroup);
                    $params["operation"] = 'operation';
                    # 御城河 订单日志
                    $ychClient->orderLog($params);
                }
            }
        }

        $pagination = [
            'rows_found' => $rows_found,
            'order_count' => $orderCount,
            'buyer_count' => $buyerCount,
            'current_count' => count($ret),
            'offset' => $offset,
            'limit' => $limit,
        ];

        if (!empty($tidArr)) {
            event((new OrderQueryEvent($user, $shop, time(), $tidArr))->setClientInfoByRequest($request));
        }

        return $this->success(['pagination' => $pagination, 'hidePrivacy' => true, $ret]);
    }


    /**
     * 手动合单
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function editReceiver(Request $request)
    {
        $params = $this->validate($request, [
            'tid' => 'string',
            'order_no_list' => 'required|array',
        ]);
        $tid = $request->input('tid', '');
        $noList = array_pull($params, 'order_no_list');
        $ownerIdList = $request->input('ownerIdList', '');
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $userIds = collect($shops)->pluck('user_id')->toArray();
        $shopIds = collect($shops)->pluck('id')->toArray();

        //查询主订单
        $order = Order::query()->with('orderCipherInfo')->where(['tid' => $tid])->first();
        //更改订单收件人数据
        $data = [
            'receiver_state' => $order['receiver_state'],
            'receiver_city' => $order['receiver_city'],
            'receiver_district' => $order['receiver_district'],
            'receiver_town' => $order['receiver_town'],
            'receiver_address' => $order['receiver_address'],
            'receiver_phone' => $order['receiver_phone'],
            'receiver_name' => $order['receiver_name'],
            'address_md5' => $order['address_md5'],
            'merge_flag' => 'merge_' . microtime(true) . rand(100, 999)
        ];

        $query = Order::query()
            ->whereIn('shop_id', $shopIds)
            ->whereIn('tid', $noList);
        $columns = ['id', 'tid', 'receiver_state', 'receiver_city', 'receiver_district', 'receiver_address', 'receiver_phone', 'receiver_name', 'address_md5'];
        $beforeOrders = $query->get($columns)->toArray();
//        $address_md5_count = collect($beforeOrders)->pluck('address_md5')->unique()->count();
//        if ($address_md5_count > 1){
//            return $this->fail('订单地址不一致，不能合并！', 400);
//        }
        $res = $query->update($data);
        if (!$res) {
            return $this->fail('编辑失败', 400);
        }
        //查询主订单上是否有加密数据 并更改加密数据
        if (!empty($order->orderCipherInfo)) {
            $replaceData = [
                'receiver_phone_ciphertext' => $order->orderCipherInfo['receiver_phone_ciphertext'],
                'receiver_name_ciphertext' => $order->orderCipherInfo['receiver_name_ciphertext'],
                'receiver_address_ciphertext' => $order->orderCipherInfo['receiver_address_ciphertext'],
                'receiver_phone_mask' => $order->orderCipherInfo['receiver_phone_mask'],
                'receiver_name_mask' => $order->orderCipherInfo['receiver_name_mask'],
                'receiver_address_mask' => $order->orderCipherInfo['receiver_address_mask'],
            ];
            $orderIdArr = collect($beforeOrders)->pluck('id')->toArray();
            $res = OrderCipherInfo::query()->whereIn('order_id', $orderIdArr)->update($replaceData);
            if (!$res) {
                return $this->fail('编辑失败', 400);
            }
        }
        $afterOrders = $query->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        $sql = getSqlByQuery($query);
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $afterOrders, 'merge'))->setClientInfoByRequest($request));
        event(new SqlLogEvent($user, $shop, time(), $sql));

        return $this->success('编辑成功');
    }

    /**
     * 手动更改地址
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function editAddress(Request $request)
    {
        $data = $this->validate($request, [
            'receiver_state' => 'string',
            'receiver_city' => 'string',
            'receiver_district' => 'string',
            'receiver_address' => 'string',
            'receiver_phone' => 'string',
            'receiver_name' => 'string',
            'order_no_list' => 'required|array',
        ]);
        $noList = array_pull($data, 'order_no_list');
        $shopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        $data['merge_flag'] = uniqid($request->auth->shop_id);
        $query = Order::query()->with('orderCipherInfo')
            ->whereIn('shop_id', $shopIds)
            ->whereIn('tid', $noList);
        $columns = ['id', 'tid', 'receiver_state', 'receiver_city', 'receiver_district', 'receiver_address', 'receiver_phone', 'receiver_name', 'receiver_town'];
        $beforeOrders = $query->get($columns)->toArray();
        $res = $query->update($data);
        if (!$res) {
            return $this->fail('编辑失败', 400);
        }
        //手动编辑 删除加密数据 不然打印出来还是原来的地址
        /*foreach ($beforeOrders as $item) {
            if ($item['order_cipher_info']) {
                OrderCipherInfo::query()->where('id', $item['order_cipher_info']['id'])->delete();
            }
        }*/
        $afterOrders = $query->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        $sql = getSqlByQuery($query);
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $afterOrders, 'address'))->setClientInfoByRequest($request));
        event(new SqlLogEvent($user, $shop, time(), $sql));

        return $this->success('编辑成功');
    }

    /**
     * 订单列表状态数
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function tabNum(Request $request)
    {
        $data = $this->validate($request, [
            "search" => 'string',
            "timeField" => 'string',
            "begin_at" => 'date',
            "end_at" => 'date',
            "tab_flag" => "string",
            "displayMerge" => "boolean",   //合并展示
            "flag" => 'sometimes|nullable',
            //"wp_code"              => 'string',
            "goodsNum" => 'sometimes|nullable',

            "goodsId" => 'sometimes|nullable',
            "sku_value" => 'string',
            "selectItem" => 'int',
            "ownerIdList" => 'array',
            "factoryOwnerIdList" => 'array',
            "skuIdList" => 'array',
            "printMode" => 'int',
            "custom_print_content" => 'string',
            "custom_group" => 'string',
            //"remark_status"        => 'int',
            "tidList" => 'array',
        ]);
        $tab_flag = $request->input('tab_flag', OrderIndexTabConst::ALL); // 当前 tab
        $countList = [];
        if (true) {
            $orderSearchRequest = new OrderSearchRequest();
            $orderSearchRequest->validate($request);
            $orderSearchRequest->authShopId = $request->auth->shop_id;
            $orderSearchRequest->isCountApi = true;
            $orderQueryBuilder = new OrderQueryBuilder();
            $orderQueryBuilder->buildQuery($orderSearchRequest);
            $count = $orderQueryBuilder->getCount();

            // 显示异常订单
            $orderSearchRequest->showAbnormalType = 1;
            $orderQueryBuilder2 = new OrderQueryBuilder();
            $orderQueryBuilder2->buildQuery($orderSearchRequest);
            $abnormal_count = $orderQueryBuilder2->getCount();
        }
        $maxLimit = 5000;
        $count = $count >= $maxLimit ? ($maxLimit - 1) . '+' : $count;

        return $this->success(['count' => $count, 'abnormal_count' => $abnormal_count, 'countList' => $countList]);

    }


    private function getTabNumCount(Builder $query, bool $displayMerge, $maxLimit, $onlyShowMergeOrder, $notShowMergeOrder,
                                            $handToShowMergeOrder, $salesAttributes, $groupColumn, $rows_found, $goodsNum,
                                            $ordersNum, $ordersKind, $amountArr, $accurateWhere = [], $finalWhere = [], $groupWhere = [])
    {
        $sql = '';
        if ($displayMerge) {
            $selectSprintSql = 'select /*+ max_execution_time(20000)*/ sum(distinct_merge_orders_num) as count from (%s) as c';
            $this->handleOrderGroup($sql, $query, $onlyShowMergeOrder, $notShowMergeOrder, $handToShowMergeOrder,
                $salesAttributes, $groupColumn, $rows_found, $goodsNum, $ordersNum, $ordersKind, $amountArr, [], 0, 0,
                $accurateWhere, $finalWhere, $groupWhere);
        } else {
            $selectSprintSql = 'select /*+ max_execution_time(20000)*/ count(*) as count from (%s) as c';
            $this->handleSingleOrder($sql, $query, $salesAttributes, $rows_found, $goodsNum, $ordersNum,
                $ordersKind, $amountArr, [], 0, 0, $accurateWhere, $finalWhere, $groupWhere);
        }
        $sql .= ' limit ' . $maxLimit;
        $selectSql1 = sprintf($selectSprintSql, $sql);
        $count = \DB::selectOne($selectSql1);
        \Log::info("tabNumCount " . $selectSql1);
        if (empty($count)) {
            throw new ApiException(ErrorConst::SYSTEM_DB_EXECUTION_TIMEOUT);
        }
        $tabCount = $count->count;

        return !$tabCount ? 0 : $tabCount;
    }


    public function refundTabNum(Request $request)
    {
        $data = $this->validate($request, [
            "search" => 'string',
            "begin_at" => 'date',
            "end_at" => 'date',
            "tab_flag" => "int",
            "refund_status" => "int",
            "receiver_state" => "string",
            "receiver_city" => "string",
            "receiver_district" => "string",
            "fixedCondition" => "array", //"HAVA_SELLER_MEMO", "BUYER_REQ_REFUND", "LOCKED", "MORE_GOOD_COUNT"
            "ownerIdList" => 'array',

        ]);
        $conditions = [];
        if ($timeField = $request->input('timeField', '')) {
            $conditions[] = [$timeField, '>=', $request->input('begin_at', Carbon::now()->addMonth(-3)->toDateTimeString())];
            $conditions[] = [$timeField, '<=', $request->input('end_at', Carbon::now())];
        }
        if ($receiverState = $request->input('receiver_state', '')) {
            $conditions[] = ['receiver_state', $receiverState];
        }
        if ($receiverCity = $request->input('receiver_city', '')) {
            $conditions[] = ['receiver_city', $receiverCity];
        }
        if ($receiverDistrict = $request->input('receiver_district', '')) {
            $conditions[] = ['receiver_district', $receiverDistrict];
        }
        $relation = ['orderItem'];
        $ownerIdList = $request->input('ownerIdList', '');
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $userIds = collect($shops)->pluck('user_id')->toArray();
        $shopIds = collect($shops)->pluck('id')->toArray();
        $query = Order::with($relation)->whereIn('orders.shop_id', $shopIds)
            ->where($conditions)
            ->where('refund_status', Order::REFUND_STATUS_YES);

        $selectItem = $request->input('selectItem', 0);
        $search = $request->input('search', '');
        $areaId = $request->input('area_id', 0);
        $sellerMemo = $request->input('seller_memo', '');
        $checkedMsg = $request->input('checkedMsg', '');
        $goodsInclude = $request->input('goodsInclude', '1');
        $goods = $request->input('goods', []);

        $query = Order::handleSearch($query, $search, $selectItem, $areaId, $sellerMemo, $checkedMsg, $goodsInclude, $goods, $userIds, $shopIds);

        switch ($request->input('tab_flag', 0)) {
            case 0: //未打印
                $query->where('order_status', Order::ORDER_STATUS_PAYMENT)
                    ->where('orders.print_status', Order::PRINT_STATUS_NO);
                break;
            case 1: //已打印未发货
                $query->where('order_status', Order::ORDER_STATUS_PAYMENT)
                    ->where('orders.print_status', Order::PRINT_STATUS_YES);
                break;
            case 2: //已发货
//				$query->where('order_status', Order::ORDER_STATUS_DELIVERED);
                $query->whereIn('order_status', [Order::ORDER_STATUS_DELIVERED, Order::ORDER_STATUS_RECEIVED, Order::ORDER_STATUS_SUCCESS]);
                break;
            case 3: //未发货
                $query->where('order_status', Order::ORDER_STATUS_PAYMENT);
                break;
            case 4: //已发货
                $query->where('order_status', Order::ORDER_STATUS_DELIVERED);
                break;
            default:
                break;
        }
        $query = Order::handleFixCondition($query, $data['fixedCondition'] ?? []);
        $ret = $query->get();

        $t1 = collect($ret)->where('item_refund_created_at', '>=', date('Y-m-d H:i:s', strtotime('-1 day')))->count();
        $t2 = 0;
        $t3 = 0;
        $t4 = 0;
        $t5 = 0;
        $t6 = 0;
        $t7 = 0;
        $t8 = 0;
        foreach ($ret as $item) {
            if (in_array(1, $item->item_refund_status)) {
                $t2 = $t2 + 1;
            }
            if (in_array(2, $item->item_refund_status)) {
                $t3 = $t3 + 1;
            }
            if (in_array(3, $item->item_refund_status)) {
                $t4 = $t4 + 1;
            }
            if (in_array(4, $item->item_refund_status)) {
                $t5 = $t5 + 1;
            }
            if (in_array(5, $item->item_refund_status)) {
                $t6 = $t6 + 1;
            }
            if (in_array(6, $item->item_refund_status)) {
                $t7 = $t7 + 1;
            }
            if (in_array(7, $item->item_refund_status)) {
                $t8 = $t8 + 1;
            }
        }

        return $this->success([$t1, $t2, $t3, $t4, $t5, $t6, $t7, $t8]);
    }


//    /**
//     * 取消单号
//     * @param Request $request
//     * @return JsonResponse
//     * @throws BusinessException
//     */
//    public function waybillRecovery(Request $request)
//    {
//        $this->validate($request, [
//            "ids" => "required|array",
//            "ownerIdList" => "required|array",
//        ]);
//
//        $ownerIdList = $request->input('ownerIdList', []);
////        $shops = Shop::getListByIdentifiers($ownerIdList);
//        $currentShopId = $request->auth->shop_id;
//        $shopIds = ShopBind::getAllRelationShopIds($currentShopId);
//        $orderIds = $request->input('ids');
//        $ret = OrderWaybillService::recoveryByOrderId($orderIds, $shopIds, $request->auth->user_id, $currentShopId);
//        \Log::info("运单号回收", $ret);
//        return $this->success($ret);
//    }


    /**
     * 取消多个单号
     * 接口：order/waybill_recovery_nums
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     */
    public function waybillRecovery(Request $request)
    {
        $this->validate($request, [
            "ids" => "required|array",
            "ownerIdList" => "required|array",
            "isForcedRecovery" => "int", // 是否强制回收
        ]);

//        $ownerIdList = $request->input('ownerIdList', []);
        $isForcedRecovery = $request->input('isForcedRecovery', 1); // 默认强制回收
        $shopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        $ret = OrderWaybillService::batchRecoveryByWaybillCode($request->input('ids'), $shopIds, $request->auth->user_id,
            $request->auth->shop_id,$isForcedRecovery);

        return $this->success($ret);
    }


    /**
     * 锁定订单
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function lock(Request $request)
    {
        $data = $this->validate($request, [
            "ids" => "required|array",
            "locked" => "required",
        ]);

        $shop_id = $request->auth->shop_id;
        $ret = Order::lock($data,$shop_id);
        if (!$ret) {
            return $this->fail('操作失败');
        }

        return $this->success();
    }

    /**
     * 打印状态通知
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function notifyPrintResult(Request $request)
    {
        $data = $this->validate($request, [
            "result" => "required|array",
            "version" => "int",
        ]);
        $orderIds = collect($data['result'])
            //			->where('status', Order::PRINT_RESULT_SUCCESS)
            ->pluck('orderId')
            ->unique()
            ->toArray();
        $version = $data['version'] ?? 0;
        if (empty($orderIds)) {
            return $this->fail('面单号订单不存在');
        }
        unset($orderIds);
        foreach ($data['result'] as $item) {
            $package = null;
            if ($version == 2) {
                $orderIdArr = explode('-', $item['orderId']);
                $packageId = $orderIdArr[0];
                $package = Package::query()
                    ->with('packageOrders')
                    ->where('id', $packageId)
                    ->first();
                $oidArr = explode('_', $orderIdArr[1]);
            } else {
                $oidArr = explode('_', $item['orderId']);
            }

            foreach ($oidArr as $id) {
                $subIdArr = [];
                if ($version == 2) {
                    $printStatus = Order::PRINT_STATUS_PART;
                    $subIdArr = collect($package['packageOrders'])->pluck('order_item_id')->toArray();
                } else {
                    $printStatus = Order::PRINT_STATUS_YES;
                    if (strstr($id, ":")) {
                        $temp = explode(":", $id);
                        $id = (int)$temp[0];
                        if (empty($temp[1])) {
                            $printStatus = Order::PRINT_STATUS_NO;
                        } else {
                            $printStatus = Order::PRINT_STATUS_PART;
                            $subIdArr = explode(',', $temp[1]);
                        }
                    }
                }

                $order = Order::findOrFail($id);
                //合单子订单勾选打印,子订单被拆出
                if ($printStatus == Order::PRINT_STATUS_NO) {
                    Order::query()
                        ->where('id', $id)
                        ->update([
                            'print_status' => $printStatus,
                            'printed_at' => null,
                            'express_no' => null,
                            'express_code' => null,
                        ]);
                } else {
                    Order::query()
                        ->where('id', $id)
                        ->update([
                            'print_status' => $printStatus,
                            'express_no' => null,
                            'printed_at' => date('Y-m-d H:i:s'),
                        ]);
                    $orderItemQuery = OrderItem::query()
//                    ->where('user_id', $order->user_id)
                        ->where('order_id', $id);
                    if (!empty($subIdArr)) {
                        $orderItemQuery->whereIn('id', $subIdArr);
                    }
                    $orderItemQuery->update([
                        'print_status' => OrderItem::PRINT_STATUS_YES,
                        'print_num' => DB::raw('print_num + 1')
                    ]);

                    if ($printStatus == Order::PRINT_STATUS_PART) {
                        $noPrintCount = OrderItem::query()
                            ->where('order_id', $id)
                            ->where('print_status', OrderItem::PRINT_STATUS_NO)
                            ->count();
                        if ($noPrintCount == 0) {
                            $order->print_status = Order::PRINT_STATUS_YES;
                            $order->save();
                        }
                    }

                }


                $packageOrders = PackageOrder::where('order_id', $id)->get();
                $packageIds = collect($packageOrders)->pluck('package_id')->toArray();
                Package::query()
                    ->where('shop_id', $order->shop_id)
                    ->where('print_status', Package::PRINT_STATUS_NO)
                    ->where('recycled_at', null)
                    ->whereIn('id', $packageIds)
                    ->update(['print_status' => Package::PRINT_STATUS_YES]);
                //写入打印序号
                PrintRecord::query()->where(['order_id' => $id, 'batch_no' => $item['batch_no']])->update([
                    'print_index' => $item['index'],
                    'print_count' => $item['total']
                ]);
            }
        }

        return $this->success();
    }

    /**
     * 打印状态通知
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function notifyPrintResultNo(Request $request)
    {
        $data = $this->validate($request, [
            "result" => "required|array",
        ]);
        $orderIds = collect($data['result'])
            //			->where('status', Order::PRINT_RESULT_SUCCESS)
            ->pluck('orderId')
            ->unique()
            ->toArray();
        if (empty($orderIds)) {
            return $this->fail('面单号订单不存在');
        }

        $allOidArr = [];
        foreach ($orderIds as $orderIdStr) {
            $oidArr = explode('_', $orderIdStr);
            $allOidArr = array_merge($allOidArr, $oidArr);
        }

        foreach ($allOidArr as $id) {
            $printStatus = Order::PRINT_STATUS_NO;
            if (strstr($id, ":")) {
                $temp = explode(":", $id);
                $id = (int)$temp[0];
            }
            $order = Order::findOrFail($id);
            //合单子订单勾选打印,子订单被拆出
            Order::query()
                ->where('id', $id)
                ->update([
                    'print_status' => $printStatus,
                ]);

            $packageOrders = PackageOrder::where('order_id', $id)->get();
            $packageIds = collect($packageOrders)->pluck('package_id')->toArray();
            Package::query()
                ->where('shop_id', $order->shop_id)
                ->where('print_status', Package::PRINT_STATUS_YES)
                ->whereIn('id', $packageIds)
                ->update(['print_status' => Package::PRINT_STATUS_NO]);
        }

        return $this->success();
    }

    /**
     * 打印序号回调
     * @param Request $request
     * @return JsonResponse
     */
    public function notifyPrintResultIndex(Request $request)
    {
        $data = $this->validate($request, [
            "list" => "required|array", // [['i'=>1,'wc'=>'waybill_code'],['i'=>1,'wc'=>'waybill_code']]
            "batch_no" => "required|string",
            "total" => "required|int",
        ]);
        $list = array_get($data,'list');
        $total = array_get($data,'total');
        $batch_no = array_get($data,'batch_no');
        foreach ($list as $item) {
            $waybill_code = $item['wc'];
            PrintRecord::query()->where(['waybill_code' => $waybill_code, 'batch_no' => $batch_no])->update([
                'print_index' => $item['i'],
                'print_count' => $total
            ]);
        }
        return $this->success();
    }

    /**
     * 根据订单号同步订单
     * @param Request $request
     * @return JsonResponse
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Throwable
     */
    public function syncPull(Request $request)
    {
        $this->validate($request, [
            "no_list" => "array",
        ]);
        $noList = $request->input('no_list', '');
        //同步整店,需要限制请求
        $shopId = $request->auth->shop_id;
        \Log::info("同步syncPull shopId=" . $shopId);
        if (empty($noList)) {
            $redis = Redis::connection('cache');
            $key = 'manual_update' . $shopId;
            if (!$redis->exists($key)) {
                $redis->setex($key, 60, 'success');

                Shop::syncPullOrders($request->auth->user_id, $shopId);
            }
        } else {
            //同步订单号
            Shop::syncPullOrders($request->auth->user_id, $shopId, $noList);
        }

        return $this->success();
    }


    /**
     * @param string $sql
     * @param Builder $query
     * @param int $salesAttributes
     * @param $count
     * @param int $goodsNum
     * @param int $ordersNum
     * @param array $amountArr
     * @param array $sort
     * @param int $offset
     * @param int $limit
     * @param array $accurateWhere
     */
    private function handleSingleOrder(string &$sql, Builder &$query, $salesAttributes = 0, &$count, $goodsNum = 0,
                                              $ordersNum = 0, $ordersKind, $amountArr = [], $sort = [], $offset = 0, $limit = 0,
                                              $accurateWhere = [], $finalWhere = [], $groupWhere = [])
    {
        $queryBase = clone $query;
//		$queryGroup = clone $query;
        $whereStr = '1=1';
        $whereArr = $finalWhere;
        if ($goodsNum) {
            if ($goodsNum == 1) {
                $whereStr .= ' and order_items_num = 1';
                $whereArr[] = ['order_items_num', '=', 1];
            } else if (is_Array($goodsNum)) {
                $whereStr .= ' and goods_sum_num >=' . $goodsNum[0] . ' and goods_sum_num <=' . $goodsNum[1];
                $whereArr[] = ['goods_sum_num', '>=', $goodsNum[0]];
                $whereArr[] = ['goods_sum_num', '<=', $goodsNum[1]];

            } else {
                $whereStr .= ' and goods_sum_num = ' . $goodsNum;
                $whereArr[] = ['goods_sum_num', '=', $goodsNum];
            }
        }

        //订单数量
        if ($ordersNum) {
            if (is_Array($ordersNum)) {
                $whereStr .= ' and merge_orders_num >=' . $ordersNum[0] . ' and merge_orders_num <=' . $ordersNum[1];
                $whereArr[] = ['merge_orders_num', '>=', $ordersNum[0]];
                $whereArr[] = ['merge_orders_num', '<=', $ordersNum[1]];
            } else {
                $whereStr .= ' and merge_orders_num = ' . $ordersNum;
                $whereArr[] = ['merge_orders_num', '=', $ordersNum];
            }
        }
        //商品种类
        if ($ordersKind) {
            if (is_Array($ordersKind)) {
                $whereStr .= ' and unique_sku_num >=' . $ordersKind[0] . ' and unique_sku_num <=' . $ordersKind[1];
                $whereArr[] = ['unique_sku_num', '>=', $ordersKind[0]];
                $whereArr[] = ['unique_sku_num', '<=', $ordersKind[1]];
            } else {
                $whereStr .= ' and unique_sku_num = ' . $ordersKind;
                $whereArr[] = ['unique_sku_num', '=', $ordersKind];
            }
        }

        if ($salesAttributes == 1) { //单商品单规格单件
            $whereStr .= ' and unique_goods_num = 1 and unique_sku_num = 1 and goods_sum_num = 1';
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '=', 1];
        } else if ($salesAttributes == 2) { //单商品单规格多件
            $whereStr .= ' and unique_goods_num = 1 and unique_sku_num = 1 and goods_sum_num > 1';
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 3) {//单商品多规格多件
            $whereStr .= ' and unique_goods_num = 1 and unique_sku_num > 1 and goods_sum_num > 1';
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 4) {//多商品多规格多件
            $whereStr .= ' and unique_goods_num > 1 and unique_sku_num > 1';
            $whereArr[] = ['unique_goods_num', '>', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
        } else if ($salesAttributes == 5) {//多规格多件
            $whereArr[] = ['unique_sku_num', '>', 1];
        }

        if ($amountArr) {
            //实付金额
            if (isset($amountArr['payment'])) {
                if (is_Array($amountArr['payment'])) {
                    $whereStr .= ' and order_items_payment >=' . $amountArr['payment'][0] . ' and order_items_payment <=' . $amountArr['payment'][1];
                    $whereArr[] = ['order_items_payment', '>=', $amountArr['payment'][0]];
                    $whereArr[] = ['order_items_payment', '<=', $amountArr['payment'][1]];
                } else {
                    $whereStr .= ' and order_items_payment =' . $amountArr['payment'];
                    $whereArr[] = ['order_items_payment', '=', $amountArr['payment']];
                }
            }

            //订单金额
            if (isset($amountArr['total_fee'])) {
                if (is_Array($amountArr['total_fee'])) {
                    $whereStr .= ' and order_items_total_fee >=' . $amountArr['total_fee'][0] . ' and order_items_total_fee <=' . $amountArr['total_fee'][1];
                    $whereArr[] = ['order_items_total_fee', '>=', $amountArr['total_fee'][0]];
                    $whereArr[] = ['order_items_total_fee', '<=', $amountArr['total_fee'][1]];
                } else {
                    $whereStr .= ' and order_items_total_fee =' . $amountArr['total_fee'];
                    $whereArr[] = ['order_items_total_fee', '=', $amountArr['total_fee']];
                }
            }
        }


        $orderBy = isset($sort[0]) ? $sort[0] : 'pay_at';
        $orderSort = isset($sort[1]) ? $sort[1] : 'desc';

        // 前置筛选出需要数量的 sql
        $queryBase->join('order_items', 'orders.id', 'order_items.order_id');
        $maxLimit = 50000;
        if (in_array($orderBy, ['outer_iid', 'outer_sku_iid'])) {
            $queryBase->orderBy("order_items.$orderBy", $orderSort)->limit($maxLimit);
        } else {
            $queryBase->orderBy("orders.$orderBy", $orderSort)->limit($maxLimit);
        }
        $this->handleLazyJoinOrderExtras($queryBase);
        $this->handleLazyJoinOrderItemExtras($queryBase);
        $queryBase->select([
            'orders.*',
            'order_items.num_iid',
            'order_items.sku_id',
            'order_items.outer_iid',
            'order_items.outer_sku_iid',
            'order_items.print_status as item-print_status',
            'order_items.refund_status as item-refund_status',
        ]);
        $queryInGroupColumn = clone $queryBase;
        $this->handleAccurateWhere($queryBase, $accurateWhere);
        $baseSql = getSqlByQuery($queryBase);
        // 套一层子查询不会报错。
        $queryInGroupColumn->whereRaw("orders.id in (select id from ($baseSql) as base)");

        // 查询分组取出 $groupColumn
        $sqlInGroupColumn = $queryInGroupColumn->toSql();
        $queryGroup = \DB::query()->fromRaw("($sqlInGroupColumn) as orders", $queryInGroupColumn->getBindings());
        $groupBindings = [
            'COUNT(distinct orders.id) as merge_orders_num',
            "orders.id as order_ids",
            "receiver_phone",
            "num",
            "sku_num",
            "COUNT(1) as order_items_num",
            "orders.$orderBy as sort_column",
            "count(distinct num_iid) as unique_goods_num",
            "count(distinct sku_id) as unique_sku_num",
            "num as goods_sum_num",
            "sku_num as sku_sum_num",
            "count(distinct receiver_address) as unique_address_num",
            "count(address_md5) as address_md5_num",
            "orders.payment as order_items_payment",
            "orders.total_fee as order_items_total_fee",
            "smart_logistics",
        ];
        for ($groupSelectArr = [], $i = 0; $i < count($groupBindings); $i++) $groupSelectArr[] = '%s';
        $selectRawStr = sprintf(implode(',', $groupSelectArr), ...$groupBindings);
        $queryGroup->where($groupWhere);
        $queryGroup->selectRaw($selectRawStr);
//		$queryGroup->join('order_items', 'orders.id', 'order_items.order_id');
        $queryGroup->groupBy(['orders.id'])->orderBy("sort_column", $orderSort)->limit($maxLimit);
//        $bindings = $queryGroup->getBindings();
//        $sql       = str_replace('%', '%%', $queryGroup->toSql());
//        $sql       = str_replace('?', '"%s"', $sql);
//        $sql       = sprintf($sql, ...$bindings);
        $sql = getSqlByQuery($queryGroup);
        $selectRawStr = 'order_ids, receiver_phone, sort_column, order_items_num, unique_goods_num, unique_sku_num,
        goods_sum_num, sku_sum_num, unique_address_num ,address_md5_num,order_items_payment , order_items_total_fee,smart_logistics';
//		$sql = sprintf("SELECT $selectRawStr FROM (%s) as b where %s ORDER BY `sort_column` %s", $sql, $whereStr, $orderSort);
        $queryFinally = \DB::query()
            ->fromRaw("($sql) as b")
            ->selectRaw($selectRawStr)
            ->where($whereArr)
            ->orderBy('sort_column', $orderSort);
        $sql = getSqlByQuery($queryFinally);

        $selectSql = sprintf('select /*+ max_execution_time(20000)*/ count(*) as count from (%s) as c', $sql);
        $count = \DB::selectOne($selectSql);
        if (!isset($count)) {
            throw_error_code_exception(StatusCode::TOO_MANY_RECORD);
        }
        $count = $count->count;
        if ($limit > 0) {
            $slectSql = $sql . ' limit ' . $limit . ' offset ' . $offset;
            $groupResult = \DB::select($slectSql);
            $groupColumnArr = collect($groupResult)->pluck('order_ids');
            $queryOrders = (array)$query->getQuery()->orders;
            $query = Order::query();
            $query->selectRaw('/*+ max_execution_time(20001) */ orders.*');
            if (in_array($orderBy, ['outer_iid', 'outer_sku_iid'])) {
                $query->leftJoin('order_items', 'orders.id', 'order_items.order_id')
                    ->select(['orders.*', 'order_items.outer_iid', 'order_items.outer_sku_iid'])
                    ->groupBy('orders.id');
            }
            $query->whereIn('orders.id', $groupColumnArr)
                ->limit(5000);
            foreach ($queryOrders as $queryOrder) {
                if ($queryOrder['type'] == 'Raw') {
                    $query->orderByRaw($queryOrder['sql']);
                }
            }
            $query->orderBy($orderBy, $orderSort);
        }
    }

    /**
     * @param Builder $query
     * @param bool $onlyShowMergeOrder
     * @param string $groupColumn
     * @param $offset
     * @param $limit
     * @param $count
     * <AUTHOR>
     */
    private function handleOrderGroup(string &$sql, Builder &$query, bool $onlyShowMergeOrder,
                                      bool   $notShowMergeOrder = false, bool $handToShowMergeOrder = false,
                                             $salesAttributes = 0, string $groupColumn, &$count, $goodsNum = 0, $ordersNum = 0,
                                             $ordersKind, $amountArr = [], $sort = [], $offset = 0, $limit = 0, $accurateWhere = [],
                                             $finalWhere = [], $groupWhere = [])
    {
        if (empty($groupColumn)) {
            return;
        }
        if ($groupColumn == 'express_no') {
            $query->whereNotNull('orders.express_no');
        }

        $queryBase = clone $query;
//		$queryGroup = clone $query;
        $whereStr = '1=1';
        $whereArr = $finalWhere;
        //筛出合并订单
        if ($onlyShowMergeOrder) {
            $whereStr .= ' and merge_orders_num > 1';
            $whereArr[] = ['merge_orders_num', '>', 1];
        }
        //筛出非合并订单
        if ($notShowMergeOrder) {
            $whereStr .= ' and merge_orders_num = 1';
            $whereArr[] = ['merge_orders_num', '=', 1];
        }
        //商品数量
        if ($goodsNum) {
            if ($goodsNum == 1) {
                $whereStr .= ' and goods_sum_num = 1 and merge_orders_num = 1';
                $whereArr[] = ['goods_sum_num', '=', 1];
                $whereArr[] = ['merge_orders_num', '=', 1];
            } else if (is_Array($goodsNum)) {
                $whereStr .= ' and goods_sum_num >=' . $goodsNum[0] . ' and goods_sum_num <=' . $goodsNum[1];
                $whereArr[] = ['goods_sum_num', '>=', $goodsNum[0]];
                $whereArr[] = ['goods_sum_num', '<=', $goodsNum[1]];
            } else {
                $whereStr .= ' and goods_sum_num = ' . $goodsNum;
                $whereArr[] = ['goods_sum_num', '=', $goodsNum];
            }
        }
        //订单数量
        if ($ordersNum) {
            if (is_Array($ordersNum)) {
                $whereStr .= ' and merge_orders_num >=' . $ordersNum[0] . ' and merge_orders_num <=' . $ordersNum[1];
                $whereArr[] = ['merge_orders_num', '>=', $ordersNum[0]];
                $whereArr[] = ['merge_orders_num', '<=', $ordersNum[1]];
            } else {
                $whereStr .= ' and merge_orders_num = ' . $ordersNum;
                $whereArr[] = ['merge_orders_num', '=', $ordersNum];
            }
        }
        //商品种类
        if ($ordersKind) {
            if (is_Array($ordersKind)) {
                $whereStr .= ' and unique_sku_num >=' . $ordersKind[0] . ' and unique_sku_num <=' . $ordersKind[1];
                $whereArr[] = ['unique_sku_num', '>=', $ordersKind[0]];
                $whereArr[] = ['unique_sku_num', '<=', $ordersKind[1]];
            } else {
                $whereStr .= ' and unique_sku_num = ' . $ordersKind;
                $whereArr[] = ['unique_sku_num', '=', $ordersKind];
            }
        }

        if ($salesAttributes == 1) { //单商品单规格单件
            $whereStr .= ' and unique_goods_num = 1 and unique_sku_num = 1 and merge_orders_num = 1 and goods_sum_num = 1';
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['merge_orders_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '=', 1];
        } else if ($salesAttributes == 2) { //单商品单规格多件
            $whereStr .= ' and unique_goods_num = 1 and unique_sku_num = 1  and goods_sum_num > 1';
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '=', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 3) {//单商品多规格多件
            $whereStr .= ' and unique_goods_num = 1 and unique_sku_num > 1 and goods_sum_num > 1';
            $whereArr[] = ['unique_goods_num', '=', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
            $whereArr[] = ['goods_sum_num', '>', 1];
        } else if ($salesAttributes == 4) {//多商品多规格多件
            $whereStr .= ' and unique_goods_num > 1 and unique_sku_num > 1';
            $whereArr[] = ['unique_goods_num', '>', 1];
            $whereArr[] = ['unique_sku_num', '>', 1];
        } else if ($salesAttributes == 5) {//多规格多件
            $whereArr[] = ['unique_sku_num', '>', 1];
        }

        //筛出同一人多个地址疑似订单
        if ($handToShowMergeOrder) {
            $whereStr .= ' and unique_address_num > 1';
            $whereArr[] = ['unique_address_num', '>', '1'];
        }

        if ($amountArr) {
            //实付金额
            if (isset($amountArr['payment'])) {
                if (is_Array($amountArr['payment'])) {
                    $whereStr .= ' and merge_orders_payment >=' . $amountArr['payment'][0] . ' and merge_orders_payment <=' . $amountArr['payment'][1];
                    $whereArr[] = ['merge_orders_payment', '>=', $amountArr['payment'][0]];
                    $whereArr[] = ['merge_orders_payment', '<=', $amountArr['payment'][1]];
                    $whereArr[] = ['merge_orders_payment', '<=', $amountArr['payment'][1]];
                } else {
                    $whereStr .= ' and merge_orders_payment =' . $amountArr['payment'];
                    $whereArr[] = ['merge_orders_payment', '=', $amountArr['payment']];
                }
            }

            //订单金额
            if (isset($amountArr['total_fee'])) {
                if (is_Array($amountArr['total_fee'])) {
                    $whereStr .= ' and merge_orders_total_fee >=' . $amountArr['total_fee'][0] . ' and merge_orders_total_fee <=' . $amountArr['total_fee'][1];
                    $whereArr[] = ['merge_orders_total_fee', '>=', $amountArr['total_fee'][0]];
                    $whereArr[] = ['merge_orders_total_fee', '<=', $amountArr['total_fee'][1]];
                } else {
                    $whereStr .= ' and merge_orders_total_fee =' . $amountArr['total_fee'];
                    $whereArr[] = ['merge_orders_total_fee', '=', $amountArr['total_fee']];
                }
            }
        }
        $orderBy = isset($sort[0]) ? $sort[0] : 'pay_at';
        $orderSort = isset($sort[1]) ? $sort[1] : 'desc';
        $sort = $orderBy;

        $maxOrder = "MAX(orders.$sort)";
        if ($sort == 'num') {
            $maxOrder = "sum(`item-goods_num`)";
        } else if ($sort === 'promise_ship_at') {
            $maxOrder = "MIN(orders.$sort)";
        } else if ($orderSort == 'asc') {
            $maxOrder = "MIN(orders.$sort)";
        }
        // 前置筛选出需要数量的 sql
        // 关联 order_items 忽略索引依赖
        $queryBase->join(DB::raw('order_items' . ' ignore index(idx_skuid)'), 'orders.id', 'order_items.order_id');
        $maxLimit = 50000;
        if (in_array($sort, ['outer_iid', 'outer_sku_iid'])) {
            $queryBase->orderBy("order_items.$sort", $orderSort)->limit($maxLimit);
        } else {
            $queryBase->orderBy("orders.$sort", $orderSort)->limit($maxLimit);
        }
        $this->handleLazyJoinOrderExtras($queryBase);
        $this->handleLazyJoinOrderItemExtras($queryBase);
//        $queryBase->select(['orders.*','order_items.num_iid','order_items.sku_id']);
        $queryBase->select([$groupColumn]);
        // $queryInGroupColumn 用作 IN 查询出来的 $groupColumn，避免条件 合并订单+商品ID 出问题。
        $queryInGroupColumn = clone $queryBase;
        $this->handleAccurateWhere($queryBase, $accurateWhere);
        $baseSql = getSqlByQuery($queryBase);
        // 套一层子查询不会报错。
        $baseSqlSub = "select $groupColumn from ($baseSql) as base";

        $queryInGroupColumn->whereRaw("$groupColumn in ($baseSqlSub)");
        $queryInGroupColumn->select([
            'orders.*',
            'order_items.id as item-id',
            'order_items.num_iid',
            'order_items.sku_id',
            'order_items.outer_iid',
            'order_items.outer_sku_iid',
            'order_items.goods_num as item-goods_num',
            'order_items.print_status as item-print_status',
            'order_items.refund_status as item-refund_status',
        ]);

        // 查询分组取出 $groupColumn
        $sqlInGroupColumn = $queryInGroupColumn->toSql();
        $queryGroup = \DB::query()->fromRaw("($sqlInGroupColumn) as orders", $queryInGroupColumn->getBindings());
        $groupBindings = [
            $groupColumn,
            "COUNT(distinct orders.id) as merge_orders_num",
            "COUNT(distinct $groupColumn) as distinct_merge_orders_num",
            "$maxOrder as sort_column",
            'count(distinct num_iid) as unique_goods_num',
            'count(distinct sku_id) as unique_sku_num',
            'sum(`item-goods_num`) as goods_sum_num',
            'count(`item-id`) as sku_sum_num',
//            'sum(sku_num) as sku_sum_num',
            'count(distinct receiver_address) as unique_address_num',
            'count(address_md5) as address_md5_num',
            'sum(orders.payment) as merge_orders_payment',
            'sum(orders.total_fee) as merge_orders_total_fee',
            'GROUP_CONCAT(orders.id) as orderIds',
            'smart_logistics',
        ];
        for ($groupSelectArr = [], $i = 0; $i < count($groupBindings); $i++) $groupSelectArr[] = '%s';
        $selectRawStr = sprintf(implode(',', $groupSelectArr), ...$groupBindings);
        $queryGroup->where($groupWhere);
        $queryGroup->selectRaw($selectRawStr);
//		$queryGroup->join('order_items', 'orders.id', 'order_items.order_id');
        $queryGroup->groupBy([$groupColumn])->orderBy("sort_column", $orderSort)->limit($maxLimit);
//		$this->handleAccurateWhere($queryGroup, $accurateWhere);
//		$bindings = $queryGroup->getBindings();
//		$sql       = str_replace('%', '%%', $queryGroup->toSql());
//		$sql       = str_replace('?', '"%s"', $sql);
//		$sql       = sprintf($sql, ...$bindings);
        $sql = getSqlByQuery($queryGroup);
        $selectRawStr = "$groupColumn, sort_column, merge_orders_num,distinct_merge_orders_num, unique_goods_num, unique_sku_num,
        goods_sum_num, sku_sum_num, unique_address_num ,address_md5_num,merge_orders_payment ,merge_orders_total_fee,
        orderIds,smart_logistics";
        $queryFinally = \DB::query()
            ->fromRaw("($sql) as b")
            ->selectRaw($selectRawStr)
//            ->where($whereArr)
            ->orderBy('sort_column', $orderSort);

        $this->handleFinalWhere($queryFinally, $whereArr);

//        $sql = sprintf("SELECT  FROM (%s) as b where %s ORDER BY `sort_column` %s", $sql, $whereStr, $orderSort);
        $sql = getSqlByQuery($queryFinally);

        // max_execution_time sql执行超时时间（毫秒）
        $selectSql = sprintf('select /*+ max_execution_time(20000)*/ count(*) as count from (%s) as c', $sql);
        $count = \DB::selectOne($selectSql);
//        \Log::info("selectSql=" . $selectSql);
        if (empty($count)) {
            throw new ApiException(ErrorConst::SYSTEM_DB_EXECUTION_TIMEOUT);
        }
        $count = $count->count;
        if ($limit > 0) {
            $slectSql = $sql . ' limit ' . $limit . ' offset ' . $offset;
            $groupResult = \DB::select($slectSql);
            $groupColumnArr = collect($groupResult)->pluck($groupColumn);
            $orderIdArr = [];
            collect($groupResult)->pluck('orderIds')->map(function ($item) use (&$orderIdArr) {
                $arr = explode(',', $item);
                $orderIdArr = array_merge($orderIdArr, $arr);
            });
            $queryOrders = (array)$query->getQuery()->orders;

            // limit 是为了防止有的手机号下几百单导致订单溢出
            $query = Order::query();
            $query->selectRaw('/*+ max_execution_time(20001) */ orders.*');
            if (in_array($sort, ['outer_iid', 'outer_sku_iid'])) {
                $query->leftJoin('order_items', 'orders.id', 'order_items.order_id')
                    ->select(['orders.*', 'order_items.outer_iid', 'order_items.outer_sku_iid'])
                    ->groupBy('orders.id');
            }
            $query->whereIn('orders.id', $orderIdArr)
                ->limit(5000);
            foreach ($queryOrders as $queryOrder) {
                if ($queryOrder['type'] == 'Raw') {
                    $query->orderByRaw($queryOrder['sql']);
                }
            }
            $query->orderBy($orderBy, $orderSort);
        }
    }


    public function orderInfo(Request $request, $tid)
    {
        $shopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);
        $order = Order::query()
            ->with(['orderItem', 'packages', 'orderCipherInfo'])
//            ->where('user_id', $request->auth->user_id)
//            ->where('shop_id', $request->auth->shop_id)
            ->whereIn('shop_id', $shopIds)
            ->where(function ($query) use ($tid) {
                $query->where('tid', $tid);
                $query->orWhere('oid', $tid);
                if (Environment::isDy()) {
                    $query->orWhere('tid', $tid . 'A');
                }
            })
            ->first();

        return $this->success($order);
    }

    public function liveOrderItemInfo(Request $request)
    {
        $validate = $this->validate($request, [
            'oid' => 'required|string',
            'startTime' => 'date',
            'endTime' => 'date',
        ]);
        $oid = $validate['oid'];
        $startTime = array_get($validate, 'startTime');
        $endTime = array_get($validate, 'endTime');
        $shopIds = ShopBind::getAllRelationShopIds($request->auth->shop_id);




        $query1 = \App\Models\Fix\OrderItem::query()
            ->leftJoin('orders', 'orders.id', 'order_items.order_id')
            ->select(['order_items.*'])
            ->with(['packages', 'order', 'order.orderCipherInfo', 'order.blacklist'])
            ->whereIn('order_items.shop_id', $shopIds)
            ->where('oid', $oid);

        $query2 = \App\Models\Fix\Order::query()
            ->rightJoin('order_items', 'orders.id', 'order_items.order_id')
            ->where('order_items.status', OrderItem::ORDER_STATUS_PAYMENT);

        if (!empty($startTime)) {
            $query1->where('orders.pay_at', '>=', $startTime);
            $query2->where('orders.pay_at', '>=', $startTime);
        }
        if (!empty($endTime)) {
            $query1->where('orders.pay_at', '<', $endTime);
            $query2->where('orders.pay_at', '<', $endTime);
        }
        try {
            $query1->firstOrFail();
        } catch (\Exception $e) {
            // 找不到订单去同步订单
            $tid = tidAddA($oid);
            $orderService = OrderServiceManager::create();
            $shop = Shop::find($request->auth->shop_id);
            $orderService->setShop($shop);
            $tradesOrder = $orderService->batchGetOrderInfo([['tid'=>$tid,'id'=>$tid]], true);
            Log::info('找不到订单去同步订单', [$tradesOrder]);
            Order::batchSave($tradesOrder, $shop->user_id, $shop->id);
        }
        $orderItem = $query1->firstOrFail();
        $query2->where('receiver_phone', $orderItem['order']['receiver_phone'])
            ->where('orders.shop_id', $orderItem['shop_id']);

        $count = $query2->count();
        $orderItem->unshippedCount = $count;
        return $this->success($orderItem);
    }

    /**
     * 旗帜
     * @param Request $request
     * @return JsonResponse
     */
    public function setFlag(Request $request)
    {
        $data = $this->validate($request, [
            'order_no_list' => 'required|array',
            'seller_flag' => 'required|string',
        ]);

        $noList = array_pull($data, 'order_no_list');
        $res = Order::query()
            ->where('shop_id', $request->auth->shop_id)
            ->whereIn('tid', $noList)
            ->update($data);
        if (!$res) {
            return $this->fail('编辑失败', 400);
        }

        return $this->success('编辑成功');
    }

    /**
     * 批量操作
     * @param Request $request
     * @return JsonResponse
     */
    public function setRemark(Request $request)
    {
        $data = $this->validate($request, [
            'order_no_list' => 'required|array',
            'seller_flag' => 'string',
            'seller_memo' => 'string',
            'locked' => 'boolean',
        ]);

        $flag = $request->input('seller_flag', 'GRAY');
        $noList = array_pull($data, 'order_no_list');
        $sellerMemoStr = array_pull($data, 'seller_memo', '');
        $locked = array_pull($data, 'locked', false);
        if ($sellerMemoStr) {
            $data['seller_memo'] = json_encode([$sellerMemoStr]);
        }

        $data['locked_at'] = null;
        if ($locked) {
            $data['locked_at'] = date('Y-m-d H:i:s');
        }

        $columns = ['id', 'tid', 'seller_flag', 'seller_memo'];
        $beforeOrders = Order::query()->whereIn('tid', $noList)->get($columns)->toArray();
        foreach ($noList as $tid) {
            $order = Order::query()->where('tid', $tid)->first();
            if ($order) {
                $shop = $order->shop;
                $userId = $order->user_id;
                $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
                $orderService->setUserId($userId);
                $orderService->setShop($shop);

                //请求平台修改留言备注
                $res = $orderService->sendEditSellerRemark($order->tid, $flag, $sellerMemoStr);
                if (!$res) {
                    return false;
                }
                Order::query()
                    ->where('shop_id', $order->shop_id)
                    ->where('tid', $tid)
                    ->orWhere('tid', $tid . 'A')
                    ->update($data);
            } else {
                return $this->fail('订单号' . $tid . '未找到', 400);
            }
        }

        if (!$res) {
            return $this->fail('编辑失败', 400);
        }

        $afterOrder = Order::query()->whereIn('tid', $noList)->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $afterOrder, 'split'))->setClientInfoByRequest($request));

        return $this->success('编辑成功');
    }

    /**
     * 店铺手动同步更新时间
     * @param Request $request
     * @return JsonResponse
     */
    public function syncTime(Request $request)
    {
        $shops = ShopBind::getAllRelationShop($request->auth->shop_id);

        $key = collect($shops)->pluck('identifier')->toArray();
        $value = collect($shops)->pluck('last_operated_at')->toArray();

        foreach ($value as &$item) {
            $item = strtotime($item);
        }

        $res = [
            'key' => $key,
            'value' => $value,
        ];

        return $this->success($res);
    }

//    /**
//     * 手动同步订单
//     * @param Request $request
//     * @return JsonResponse
//     */
//    public function syncCondition(Request $request)
//    {
//        $this->validate($request, [
//            'start' => 'int',
//            'end' => 'int',
//        ]);
//
//        $start = $request->input('start', strtotime('-5 day'));
//        $end = $request->input('end', time());
//
//        $end = $end > time() ? time() : $end;
//
//        if ($end - $start > 31 * 24 * 60 * 60) {
//            return $this->fail('时间范围超过31天');
//        }
//
//        //同步天数大，限制同步
//        $rangeDay = ceil(($end - $start) / (24 * 60 * 60));
//
//        $beginAt = date('Y-m-d 00:00:00', $start);
//        $endAt = date('Y-m-d H:i:s', $end);
//
//        $shop = Shop::query()->findOrFail($request->auth->shop_id);
//        //限制频率
//        $redis = Redis::connection('cache');
//        $key = 'sync_order_by_time_range' . $shop->id;
//        if ($redis->exists($key) && $rangeDay > 5) {
//            return $this->fail('操作频繁');
//        }
//
//        $redis->setex($key, 24 * 60 * 60, 'sync');
//        dispatch(new SyncOrdersJob($shop, $beginAt, $endAt));
//
//        return $this->success();
//    }


//    /**
//     * 打印前 验证符合打印条件
//     * @param Request $request
//     * @return JsonResponse
//     */
//    public function validOrders(Request $request)
//    {
//        $tidArr = $request->input();
//        $orders = Order::query()->whereIn('tid', $tidArr)->get();
//
//        $result = new \stdClass();
//        foreach ($orders as $order) {
//            $pass = '';
//
//            $bool1 = $order->order_status == Order::ORDER_STATUS_PAYMENT &&
//                $order->refund_status == Order::REFUND_STATUS_NO;
//            $bool2 = $order->template_id != 0 && !empty($order->express_no);
//            if ($bool1 || $bool2) {
//                $pass = 'PASS';
//            } else {
//                $pass = $order->item_refund_status[0] . ',' . $order->refund_status . ',' . $order->order_status;
//            }
//            $tid = $order->tid;
//            $result->$tid = $pass;
//        }
//
//        return response()->json($result);
//    }


    public function queryGoodsName(Request $request)
    {
        $data = $this->validate($request, [
            "begin_at" => 'date',
            "end_at" => 'date',
            "timeField" => 'string',
            "ownerIdList" => 'array',
        ]);
        $goodsQueryRequest = new GoodsQueryRequest($request->input('ownerIdList', ''),
            $request->input('begin_at', date('Y-m-d H:i:s', strtotime('-180 days'))),
            $request->input('end_at', date('Y-m-d H:i:s', time())),
            $request->input('timeField', 'pay_at'),
            $request->input('includeAllGoods', 0),
            $request->input('includeLocked', 0),
            $request->input('order_status', []),
            $request->input('refund_status', [])
        );

        $ret = $this->goodsQueryService->queryGoodsName($goodsQueryRequest);
        //是否合并
        if ($request->input('mergeDisplay')) {
            $ret = $this->handleMergeGoods($ret);
        }

        return $this->success($ret);
    }

    public function syncHistoryOrder(Request $request)
    {
        $this->validate($request, [
            "end_time" => 'date',
            "begin_time" => 'date'
        ]);

        $isAsync = $request->input('is_async');
        $endTime = $request->input('end_time');
        $beginTime = $request->input('begin_time');
        //同步去拉订单 不同步删掉十天前的数据
        $orderService = OrderServiceManager::create(config('app.platform'));
        $shop = Shop::query()->findOrFail($request->auth->shop_id);
        $orderService->setShop($shop);
        if ($isAsync) {
            $orderService->syncHistoryOrder($beginTime, $endTime);
        }
        //else {
        //$orderService->delHistoryOrder($beginTime, $endTime);
        //}

        return $this->success();
    }

    /**
     * 一键查询
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     * <AUTHOR>
     */
    public function postSearch(Request $request)
    {
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at" => 'date',
            "oneKeyId" => 'int',
            "displayMerge" => "boolean",
            "ownerIdList" => 'array',
        ]);
        $relation = ['orderItem'];
        $conditions = [];
        $timeField = $request->input('timeField', '');
        $beginAt = $request->input('begin_at', '');
        $endAt = $request->input('end_at', '');
        $ownerIdList = $request->input('ownerIdList', '');
        $oneKeyId = $request->input('oneKeyId', 0);
        $queryTemplates = $request->input('queryTemplates', []);
        $includeOrNot = $request->input('includeOrNot', 1);  //包含 不包含
        $displayMerge = $request->input('displayMerge',true); //合并展示
        $onlyShowMergeOrder = $request->input('onlyShowMergeOrder', false); //仅展示合并订单
        $onlyShowPrintedOrder = $request->input('onlyShowPrintedOrder', false); //未发货退款 仅显示已打印的订单
        $authorName = $request->input('authorName');

        if (!$oneKeyId && empty($queryTemplates)) {
            return $this->fail('请选择您一键查询的条件！');
        }


        $query = Order::query();

        if ($oneKeyId > 0) {
            //一键搜索选中查询
            $template = QueryTemplate::query()->where('id', $oneKeyId)->first();
            if (!$template) {
                return $this->fail('未查询到您选中的条件！');
            }
            $data = json_decode($template->data, true);
        } else {
            //一键搜索弹框里查询 $oneKeyId -1
            $data = $queryTemplates;
        }


        if (!empty($data['buyer_message'])) {
            $conditions[] = ['buyer_message', $data['buyer_message']];
        }

        if (!empty($data['flag'])) {
            if (is_array($data['flag'])) {
                $query->whereIn('seller_flag', $data['flag']);
            } else {
                $conditions[] = ['seller_flag', $data['flag']];
            }
        }
        if (isset($data['is_pre_sale']) && $data['is_pre_sale'] >= 0) {
            $conditions[] = ['order_items.is_pre_sale', $data['is_pre_sale']];
        }

        if (!empty($data['buyer_nick'])) {
            $conditions[] = ['buyer_nick', $data['buyer_nick']];
        }

        if (!empty($data['receiver_phone'])) {
            $conditions[] = ['receiver_phone', $data['receiver_phone']];
        }

        if (!empty($data['volume'])) {
            $conditions[] = ['volume', $data['volume']];
        }
        if (!empty($data['weight'])) {
            $conditions[] = ['weight', $data['weight']];
        }


        $goodsInclude = $data['includeOrNot'] ?? '';
        $checkedMsg = $data['checkedMsg'] ?? '';
        $sellerMemo = $data['seller_memo'] ?? '';
        $areaId = $data['area_id'] ?? '';
        $goods_num = $data['goods_num'] ?? '';     //商品数量
        $orders_num = $data['num'] ?? '';           //订单数量
        $total_fee = $data['total_fee'] ?? '';
        $goods = $data['goods'] ?? [];
        $print_status = $data['print_status'] ?? -1;   //打印状态 -1 是全部
        $order_status = $data['order_status'] ?? -1;   //订单状态  -1 是全部
        $refund_status = $data['refund_status'] ?? -1;  //订单状态  -1 是全部
        $payment = $data['payment'] ?? '';  // 实付金额
        $sku_num = $data['sku_num'] ?? '';  // 规格数量


        $accurateWhere = [];
        $finalWhere = [];
        $selectItem = 0;
        $search = '';
        $sort = $request->input('sort', 'pay_at desc');
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);
        //备注
        if (!empty($data['seller_memo'])) {
            $search = $data['seller_memo'];
            $selectItem = 9;
        }
        $rows_found = 0;
        $amountArr = [];   //订单金额
        $goodsNum = null; //商品数量
        $ordersNum = null; //订单数量

        //商品数量1,1-3
        if ($goods_num) {
            if (is_numeric($goods_num)) {
                $goodsNum = $goods_num;
            } else {
                $searchArr = explode('-', $goods_num);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $goodsNum = $searchArr;
                }
            }
        }
        //订单数量1,1-3
        if ($orders_num) {
            if (is_numeric($orders_num)) {
                $ordersNum = $orders_num;
            } else {
                $searchArr = explode('-', $orders_num);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $ordersNum = $searchArr;
                }
            }
        }

        //订单金额1,1-3
        if ($total_fee) {
            if (is_numeric($total_fee)) {
                $amountArr['total_fee'] = $total_fee;
            } else {
                $searchArr = explode('-', $total_fee);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $amountArr['total_fee'] = $searchArr;
                }
            }
        }
        //实付金额1,1-3
        if ($payment) {
            if (is_numeric($payment)) {
                $amountArr['payment'] = $payment;
            } else {
                $searchArr = explode('-', $payment);
                if (is_numeric($searchArr[0]) && is_numeric($searchArr[1])) {
                    $amountArr['payment'] = $searchArr;
                }
            }
        }
        if ($sku_num) {
            // 单个商品规格数量
            if (strpos($sku_num, '-')) {
                list($left, $right) = explode('-', $sku_num);
                $accurateWhere[] = [
                    'func' => 'where',
                    'args' => ['order_items.goods_num', '>=', $left]
                ];
                $accurateWhere[] = [
                    'func' => 'where',
                    'args' => ['order_items.goods_num', '<=', $right]
                ];
            } else {
                $accurateWhere[] = [
                    'func' => 'where',
                    'args' => ['order_items.goods_num', $sku_num]
                ];
            }
        }

        $shops = Shop::getListByIdentifiers($ownerIdList);
        $userIds = collect($shops)->pluck('user_id')->toArray();
        $shopIds = collect($shops)->pluck('id')->toArray();
        if ($timeField && $beginAt && $endAt) {
            $conditions[] = [$timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $conditions[] = [$timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }

        if (!empty($data['goods_id'])) {
            $accurateWhere[] = [
                'func' => 'where',
                'args' => ['order_items.num_iid', $data['goods_id']]
            ];
        }
        if (!empty($data['sku_id'])) {
            $accurateWhere[] = [
                'func' => 'where',
                'args' => ['order_items.sku_id', $data['sku_id']]
            ];
        }
        if (!empty($data['goods_kind'])) {
            $arr = explode('-', $data['goods_kind']);
            if ($displayMerge) {
                if (count($arr) == 2) {
                    $finalWhere[] = ['unique_sku_num', '>=', $arr[0]];
                    $finalWhere[] = ['unique_sku_num', '<=', $arr[1]];
                } else {
                    $finalWhere[] = ['unique_sku_num', '=', $arr[0]];
                }
            } else {
                if (count($arr) == 2) {
                    $finalWhere[] = ['sku_num', '>=', $arr[0]];
                    $finalWhere[] = ['sku_num', '<=', $arr[1]];
                } else {
                    $finalWhere[] = ['sku_num', '=', $arr[0]];
                }
            }
        }

        if (!empty($data['goods_include'])) {
            $isInclude = true;
            $accurateWhere = Order::getAccurateWhereByGoods($data['goods_include'], $shopIds, $accurateWhere, $isInclude);
        }
        if (!empty($data['goods_no_include'])) {
            $isInclude = false;
            $accurateWhere = Order::getAccurateWhereByGoods($data['goods_no_include'], $shopIds, $accurateWhere, $isInclude);
        }

        if (!empty($data['sku_include'])) {
            $isInclude = true;
            $accurateWhere = Order::getAccurateWhereByGoodsSku($data['sku_include'], $shopIds, $accurateWhere, $isInclude);
        }
        if (!empty($data['sku_no_include'])) {
            $isInclude = false;
            $accurateWhere = Order::getAccurateWhereByGoodsSku($data['sku_no_include'], $shopIds, $accurateWhere, $isInclude);
        }

        $orderSearchRequest = new OrderSearchRequest();
        $orderSearchRequest->validate($request);
        $orderSearchRequest->shopIds = $shopIds;
        $orderQueryBuilder = new OrderQueryBuilder();
        $orderQueryBuilder->buildGoodsCondition($query, $orderSearchRequest);
        $query->with($relation)
//            ->whereIn('orders.user_id', $userIds)
            ->whereIn('orders.shop_id', $shopIds)
            ->where($conditions);
        $query = Order::handleSearch($query, $search, $selectItem, $areaId, $sellerMemo, $checkedMsg, $goodsInclude,
            $goods, $userIds, $shopIds, $accurateWhere, $includeOrNot);

        $queryGroup = null;
//		$groupColumn = 'express_no';
//        $groupColumn = $this->defaultGroupColumn;
        $groupColumn = $orderSearchRequest->getGroupColumn();

        //未打印，未发货
        if ($print_status !== Order::PRINT_STATUS_YES || $order_status == Order::ORDER_STATUS_PAYMENT) {
//            $groupColumn = 'receiver_phone';
        }

        //订单状态
        if ($order_status > 0) {
            switch ($order_status) {
                case Order::ORDER_STATUS_PAYMENT:
                    $query->whereIn('order_status', [
                        Order::ORDER_STATUS_PAYMENT,
                        Order::ORDER_STATUS_PART_DELIVERED,
                    ]);
                    break;
                case Order::ORDER_STATUS_DELIVERED:
                    $query->whereIn('order_status', [
                        Order::ORDER_STATUS_PART_DELIVERED,
                        Order::ORDER_STATUS_DELIVERED
                    ]);
                    break;
                default:
                    $query->whereIn('order_status', [$order_status]);
                    break;
            }
        } else {
            //全部订单状态
            $query->whereIn('order_status', Order::ORDER_STATUS_ALL_ARRAY);
        }

        //打印状态
        if ($print_status >= 0) {
            $query->whereIn('orders.print_status', [$print_status]);
        } else {
            //全部
            $query->whereIn('orders.print_status', [Order::PRINT_STATUS_NO, Order::PRINT_STATUS_YES]);
        }


        //退款状态
        if ($refund_status >= 0) {
            $query->whereIn('orders.refund_status', [$refund_status, Order::REFUND_STATUS_PART]);
        } else {
            //全部
            $query->whereIn('orders.refund_status', [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART, Order::REFUND_STATUS_YES]);
        }
        // 达人名字
        if (!empty($authorName)) {
            $query->where('order_items.author_name', 'like', '%' . $authorName . '%');
        }
        $query = Order::handleOrderBy($query, $sort);
        $handToShowMergeOrder = false; //手动合并疑似合单
        $notShowMergeOrder = false;  //非合单
        $salesAttributes = 0;  //销售属性

        $sql = '';
        $ordersKind = null;
        if ($displayMerge) {
            $this->handleOrderGroup($sql, $query, $onlyShowMergeOrder, $notShowMergeOrder, $handToShowMergeOrder,
                $salesAttributes, $groupColumn, $rows_found, $goodsNum, $ordersNum, $ordersKind, $amountArr, $sort, $offset,
                $limit, $accurateWhere, $finalWhere);
        } else {
            $this->handleSingleOrder($sql, $query, $salesAttributes, $rows_found, $goodsNum, $ordersNum,
                $ordersKind, $amountArr, $sort, $offset, $limit, $accurateWhere, $finalWhere);
        }

        $ret = $query->with(['orderItem', 'orderItem.customGoodsSkus', 'orderItem.customGoods', 'shop', 'trace',
            'orderCipherInfo', 'operationLogs',
            'packages' => function ($query) {
                $query->whereNull('recycled_at');
            }])->get();


        if ($displayMerge) {
            $ret = $this->handleMergeOrder($request, $ret, $groupColumn);
            $orderBy = isset($sort[0]) ? $sort[0] : 'order_created_at';
            // 排序
            if ($orderBy == 'num') {
                $orderSort = isset($sort[1]) ? $sort[1] : 'desc';
                $orderSort = $orderSort == 'asc' ? SORT_ASC : SORT_DESC;
                array_multisort(array_column($ret, 'total_num'), $orderSort, $ret);
            }
            if ($orderBy == 'payment') {
                $orderSort = isset($sort[1]) ? $sort[1] : 'desc';
                $orderSort = $orderSort == 'asc' ? SORT_ASC : SORT_DESC;
                array_multisort(array_column($ret, 'total_payment'), $orderSort, $ret);
            }
        } else {
            $ret = $this->handleOrderPackages($ret);
        }
        foreach ($ret as $index => $item) {
            unset($ret[$index]['shop']);
            foreach ($ret[$index]['order_item'] as $index2 => $order_item) {
                unset($ret[$index]['order_item'][$index2]['custom_goods_skus']);
                unset($ret[$index]['order_item'][$index2]['custom_goods']);
            }
        }
        $pagination = [
            'rows_found' => $rows_found,
            'offset' => $offset,
            'limit' => $limit
        ];

        return $this->success(['pagination' => $pagination, 'hidePrivacy' => $request->hidePrivacy, $ret]);
    }


//
    public function printCheck(Request $request)
    {
        $this->validate($request, [
            'order_ids' => 'required|array',
            'template_id' => 'required|int',
            'package_num' => 'required|int', //订单打印包裹数
            'total_count' => 'required|int', //取号总数量
        ]);

        $orderIds = $request->input('order_ids');
        $templateId = $request->input('template_id');
        $isNewPrint = $request->input('new_print');
        $totalCount = $request->input('total_count');

        // 抖音风控
        if (PlatformConst::DY == config('app.platform')) {
            $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
            $orderAntispamRequest = new OrderAntispamRequest($shop['user_id'], 1, $request->auth->shop_id, null, OperationType::print_order_list, null, null, null, $request);
            $this->antiSpamService->checkAntispamOrderSend($orderAntispamRequest);

        }

        try {
            $printCheckService = new PrintCheckService($templateId, $isNewPrint, $totalCount, $orderIds);
            //校验电子面单是否过期以及余额
            $printCheckService->checkAuthStatusAndWaybillBalance();
            //校验订单状态
            $printCheckService->checkPrintStatusAndOrderStatus();
        } catch (PrintException $e) {
            \Log::info("电子面单账号失效", [$e->getData()]);
            return $this->success($e->getData(), $e->getMsg(), $e->getStatusCode());
        }

        return $this->success();
    }

    public function abnormalOrderCount(Request $request)
    {
        $shopId = $request->auth->shop_id;
        $beginTime = date('Y-m-d', strtotime('-2 day'));
        $endTime = date('Y-m-d 23:59:59');
        $abnormalOrderList = AbnormalOrder::query()
            ->with('order')
            ->join('orders', 'abnormal_order.order_id', '=', 'orders.id')
            ->where(['abnormal_order.shop_id' => $shopId, 'abnormal_order.status' => AbnormalOrder::STATUS_OF_UNREAD])
            ->where('orders.order_status',Order::ORDER_STATUS_PAYMENT)
            ->whereBetween('abnormal_order.created_at', [$beginTime, $endTime])
            ->get();
//        foreach ($abnormalOrderList as $key => $item) {
////            if ($item['order']['order_status'] != Order::ORDER_STATUS_PAYMENT) {
////                unset($abnormalOrderList[$key]);
////            }
//        }
        $orderService = OrderServiceManager::create();
        //$shopList = User::getShopAndInvite($request->auth->user_id, $request->auth->shop_id);
        $shopList = ShopBind::getAllRelationShop($request->auth->shop_id);
        foreach ($shopList as $index => $item) {
            $item = json_decode(json_encode($item));
            $orderService->setShop($item);
            $isShopAuth = $orderService->checkAuthStatus();
            // 店铺授权状态 true 正常
            $shopList[$index]['isShopAuth'] = $isShopAuth;
        }
        //$refundCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ORDER_REFUND)->count();
        $addressCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ADDRESS_UPDATE)->count();
        //$remarkCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ORDER_REMARK)->count();
        //$addressWarCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ADDRESS_ABNORMAL)->count();
        $data = [
            'order_count' => collect($abnormalOrderList)->count(),
            'address_count' => $addressCount,
            'shop_list' => $shopList,
        ];

        return $this->success($data);
    }

    public function abnormalOrderList(Request $request)
    {
        $shopId = $request->auth->shop_id;
        $offset = $request->input('offset');
        $limit = $request->input('limit');
        $beginTime = date('Y-m-d', strtotime('-2 day'));
        $endTime = date('Y-m-d 23:59:59');
        $abnormalOrderList = AbnormalOrder::query()
            ->where(['shop_id' => $shopId, 'status' => AbnormalOrder::STATUS_OF_UNREAD])
            ->whereBetween('created_at', [$beginTime, $endTime])
            ->get()->toArray();

        $abnormalOrderData = AbnormalOrder::query()
            ->where(['shop_id' => $shopId, 'status' => AbnormalOrder::STATUS_OF_UNREAD])
            ->whereBetween('created_at', [$beginTime, $endTime])
            ->offset($offset)
            ->limit($limit)
            ->get()->toArray();

        foreach ($abnormalOrderList as $key => $item) {
            $order = Order::query()->where('id', $item['order_id'])->first();
            if ($order['order_status'] != Order::ORDER_STATUS_PAYMENT) {
                unset($abnormalOrderList[$key]);
            }
        }

        foreach ($abnormalOrderData as $key => $item) {
            $order = Order::query()->with('orderCipherInfo')->where('id', $item['order_id'])->first();
            if ($order['order_status'] != Order::ORDER_STATUS_PAYMENT) {
                unset($abnormalOrderData[$key]);
            } else {
                $abnormalOrderData[$key]['order'] = $order;
            }
        }
        $refundCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ORDER_UNSENT_REFUND)->count();
        $addressCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ADDRESS_UPDATE)->count();
        $remarkCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ORDER_REMARK)->count();
        $addressWarCount = collect($abnormalOrderList)->where('type', AbnormalOrder::TYPE_OF_ADDRESS_ABNORMAL)->count();

        $data = [
            'refund_count' => $refundCount,
            'address_count' => $addressCount,
            'remark_count' => $remarkCount,
            'address_war_count' => $addressWarCount,
            'rows_found' => collect($abnormalOrderList)->count(),
            'offset' => $offset,
            'limit' => $limit
        ];
        $abnormalOrderData = array_values($abnormalOrderData);
        return $this->success(['pagination' => $data, $abnormalOrderData]);
    }

    public function editAbnormalOrder(Request $request)
    {
        $this->validate($request, [
            "ids" => 'array|required',
        ]);
        $ids = $request->input('ids');
        $result = AbnormalOrder::query()->whereIn('id', $ids)->update(['status' => AbnormalOrder::STATUS_OF_READ]);
        if (!$result) {
            return $this->fail('修改失败', 400);
        }

        return $this->success('修改成功');
    }


    /**
     * 手动拆单
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function orderSplit(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'order_no_list' => 'required|array',
        ]);
        $orderNoList = array_get($data, 'order_no_list');
        DB::transaction(function () use ($orderNoList, $request) {
            foreach ($orderNoList as $index => $orderNo) {
                $data = [
                    'merge_flag' => 'split_' . uniqid($request->auth->shop_id),
                ];
                $res = Order::query()->where('tid', $orderNo)
                    ->update($data);
                if (!$res) {
                    return $this->fail('编辑失败', 400);
                }
            }
            $orderService = OrderServiceManager::create(config('app.platform'));
            foreach ($orderNoList as $tid) {
                $order = Order::query()->where('tid', $tid)->first()->toArray();
                $shop = Shop::query()->where('id', $order['shop_id'])->first();
                $orderService->setShop($shop);

                $orderInfo = $orderService->getOrderInfo($tid);
                Order::batchSave([$orderInfo], $order['user_id'], $order['shop_id']);
            }
        });

        $columns = ['id', 'tid'];
        $beforeOrders = Order::query()->whereIn('tid', $orderNoList)->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $beforeOrders, 'split'))->setClientInfoByRequest($request));
        return $this->success([]);
    }

    /**
     * 处理后端合单
     * @param Request $request
     * @param $ret
     * @param string $groupColumn
     * @param array $mergeFlagOrders
     * @param null $orderBy
     * @param null $orderSort
     * @param OrderSearchRequest|null $orderSearchRequest
     * @return array $list
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function handleMergeOrder(Request $request, $ret, string $groupColumn, $mergeFlagOrders = [], $orderBy = null,
                                             $orderSort = null, OrderSearchRequest $orderSearchRequest = null): array
    {
        $tab_flag = $request->input('tab_flag', 0);
//        $groupColumn = 'receiver_phone';
//        if (Environment::isWxOrWxsp()) {
//            $groupColumn = 'address_md5';
//        }
        if(Environment::isJD()){
            $groupColumn = 'receiver_name';
        }
        if (in_array($orderBy, ['sku_value', 'sku_value_last', 'goods_title', 'goods_title_last', 'outer_iid', 'outer_sku_iid'])) {
            $groupColumn = 'address_md5';
        }
        $list = [];
        if (empty($ret)) {
            return $list;
        }
        $ret = collect($ret)->toArray();

        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        $shopExtra = ShopExtra::query()->where([
            //'user_id' => $request->auth->user_id,
            'shop_id' => $request->auth->shop_id,
        ])->first();
        $tempPackageList = [];
        $groupArr = collect($ret)->groupBy($groupColumn)->toArray();
        $packageMax = $shopExtra->merge_order_num ?? ShopExtra::MERGE_ORDER_NUM_DEFAULT; // 每个包裹最大数量
        // 根据分组字段拆分
        foreach ($groupArr as $groupItem) {
            // 疑似合单标记
            $last_like_merge_flag = '';
            // 可合单标记
            $last_merge_flag = '';
            // 可合单标记索引
            $last_merge_flag_index = 0;
            if (count($groupItem) == 1) {
                $tempPackageList[] = $groupItem;
                continue;
            }
            array_multisort(array_column($groupItem, 'address_md5'), SORT_ASC, $groupItem);
            // 标记疑似合单和可合单
            foreach ($groupItem as $groupItemIndex => $groupItemValue) {
                $merge_flag_array = [
//                    $groupItemValue['shop_id'],
//                    $groupItemValue['user_id'],
//                    $groupItemValue['buyer_id'],
//                    $groupItemValue['buyer_nick'],
//                    $groupItemValue['receiver_phone'],
                    $groupItemValue['address_md5'],
//                    $groupItemValue['receiver_name'],
//                    $groupItemValue['receiver_state'],
//                    $groupItemValue['receiver_city'],
//                    $groupItemValue['receiver_district'],
                ];
                if (!empty($orderSearchRequest->authorIdList) && $orderSearchRequest->isAuthorMerge){
                    $author_ids = collect($groupItemValue['order_item'])->pluck('author_id')->unique()->implode(',');
                    $merge_flag_array[] = $author_ids;
                }
                $like_merge_array = [
                    $groupItemValue['receiver_phone'],
                    $groupItemValue['receiver_name'],
                    $groupItemValue['receiver_state'],
                    $groupItemValue['receiver_city'],
                    $groupItemValue['receiver_district'],
                ];
                //如果是JD，相似合并判断不能有receiver_phone 参与，因为JD这手机号是一个掩码，手机号是随机的
                if (Environment::isJD()) {
                    array_shift($like_merge_array);
                }
//                if (PlatformConst::KS == config('app.platform')) {
//                    // ks 可以跨店铺合单
//                    $merge_flag_array = [
////                        $groupItemValue['shop_id'],
////                        $groupItemValue['user_id'],
////                    $groupItemValue['buyer_nick'], // 快手有的人可能取不到 buyer_nick
//                        $groupItemValue['receiver_phone'],
////                        $groupItemValue['receiver_name'],
////                        $groupItemValue['receiver_state'],
////                        $groupItemValue['receiver_city'],
////                        $groupItemValue['receiver_district'],
//                    ];
//                }
                $like_merge_flag = implode('-', $like_merge_array);
                $merge_flag = implode('-', $merge_flag_array);

                if (Environment::isWxOrWxsp()) {
                    $merge_flag = $groupItemValue['address_md5'];
                }

                $merge_flag = md5($merge_flag);
                $groupItem[$groupItemIndex]['is_merge'] = false;
                $groupItem[$groupItemIndex]['is_like'] = false;
                if ($merge_flag == $last_merge_flag) {
                    $groupItem[$groupItemIndex]['is_merge'] = true;
                    $groupItem[$last_merge_flag_index]['is_merge'] = true;
                } elseif ($like_merge_flag == $last_like_merge_flag) {
                    $groupItem[$groupItemIndex]['is_like'] = true;
                    $groupItem[$last_merge_flag_index]['is_like'] = true;
                    $last_merge_flag_index = $groupItemIndex;
                } else {
                    $last_merge_flag_index = $groupItemIndex;
                }
                if (empty($groupItem[$groupItemIndex]['merge_flag'])) {
                    $groupItem[$groupItemIndex]['merge_flag'] = $merge_flag;
                }

                $last_merge_flag = $merge_flag;
                $last_like_merge_flag = $like_merge_flag;
            }


            // 拆分出有退款成单独订单
            foreach ($groupItem as $index => $item) {
                if ($item['refund_status'] == 1) {
                    $tempPackageList[] = [$item];
                    unset($groupItem[$index]);
                }
            }
            // 筛选出疑似合单
            $likeMergeTrueArr = collect($groupItem)->filter(function ($item) {
                return $item['is_like'] == true;
            })->toArray();
            // 接口检查疑似合单
            if (in_array($tab_flag, [OrderIndexTabConst::ALL, OrderIndexTabConst::UNPRINT, OrderIndexTabConst::PRINTED])
                &&( Environment::isDy()||Environment::isJd()) && count($likeMergeTrueArr) > 1 && empty($orderSearchRequest->isAuthorMerge)) {
                $groupShopIdItems = collect($groupItem)->groupBy('shop_id')->toArray();
                foreach ($groupShopIdItems as $idxShopId => $items) {
                    Log::info('疑似合单检查', ["items"=>$items]);
                    $orderService = OrderServiceManager::create();
                    $thisShop = \App\Models\Fix\Shop::query()->where('id', $idxShopId)->first();
                    $orderService->setShop($thisShop);
                    $checkMergeOrderList = $orderService->sendCheckMergeOrder($items);
                    $groupItem = collect($groupItem)->keyBy('tid')->toArray();
                    foreach ($checkMergeOrderList as $item2) {
                        $tidArr = explode(',', $item2);
                        foreach ($tidArr as $tid) {
                            if (isset($groupItem[$tid])) {
                                $groupItem[$tid]['is_merge'] = true;
                                $groupItem[$tid]['merge_flag'] = 'pt_merge_' . md5($item2);
                            }
                        }
                    }
                }
            }


            // 拆分出可合单和不可合单
            $isMergeArr = collect($groupItem)->groupBy('is_merge')->toArray();
            // 不可合单的 拆分成单个
            if (!empty($isMergeArr[0])) {
                $array_chunk1 = array_chunk($isMergeArr[0], 1);
                array_push($tempPackageList, ...$array_chunk1);
            }

            // 可合单的
            $isMergeArrTrue = $isMergeArr[1] ?? [];
            if (!empty($isMergeArrTrue)) {
                foreach ($isMergeArrTrue as $isMergeArrTrueIndex => $isMergeArrTrueItem) {
                    if (empty($isMergeArrTrueItem)) {
                        continue;
                    }
                    // 只有一个的时候，去掉合单标记
                    if (count($isMergeArrTrueItem) == 1) {
                        $firstMergeFlagItem = array_first($isMergeArrTrueItem);
                        $firstMergeFlagItem['is_merge'] = false;
                        $isMergeArrTrue[$isMergeArrTrueIndex] = [$firstMergeFlagItem];
                    }
                    // 非待发货 不显示合单标记
                    if (!in_array($isMergeArrTrueItem['order_status'], [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED])) {
                        $isMergeArrTrue[$isMergeArrTrueIndex]['is_merge'] = false;
                    }
                }

                // 根据手动合单拆分
                $mergeFlagArr = collect($isMergeArrTrue)->groupBy('merge_flag')->toArray();
                $mergeFlagArrList = [];
                foreach ($mergeFlagArr as $merge_flag => $mergeFlagItem) {
                    if (!empty($merge_flag)) {
                        // 合并合单标记的订单
                        $mergeFlagItem = collect($mergeFlagItem)
                            ->merge($mergeFlagOrders[$merge_flag][0]['merge_flag_orders'] ?? [])
                            ->unique('id')
                            ->toArray();
                    }
                    // 根据包裹最大数拆分
                    $array_chunk2 = array_chunk($mergeFlagItem, $packageMax);
                    array_push($mergeFlagArrList, ...$array_chunk2);
//                    foreach ($array_chunk as $array_chunk_item) {
//                        $tempList[] = $array_chunk_item;
//                    }
                }
                // 合单排序
                if (!is_null($orderBy) && !is_null($orderSort)) {
                    $orderSort = $orderSort == 'asc' ? SORT_ASC : SORT_DESC;
                    $tempList = [];
                    foreach ($mergeFlagArrList as $i => $array_chunk2_item) {
                        $k = $array_chunk2_item[0][$orderBy] . '_' . $array_chunk2_item[0]['id'];
                        $tempList[$k] = $array_chunk2_item;
                    }
                    $mergeFlagArrList = collect($tempList)->sortKeys(SORT_REGULAR, $orderSort == SORT_DESC)->values()->toArray();
                }
                array_push($tempPackageList, ...$mergeFlagArrList);
            }

        }
        // 组装子订单数据
        foreach ($tempPackageList as $index => $groupItemValue) {
//                $tempItem = $item[0];
            $notDeliveredCount = count(array_filter($groupItemValue, function ($item) {
                if (in_array($item['order_status'], [Order::ORDER_STATUS_PAYMENT, Order::ORDER_STATUS_PART_DELIVERED]) && in_array($item['refund_status'], [Order::REFUND_STATUS_NO, Order::REFUND_STATUS_PART])) {
                    return true;
                }
                return false;
            }));
            $packageAll = [];
            collect($groupItemValue)->pluck('packages')->each(function ($items) use (&$packageAll) {
                foreach ($items as $item) {
                    $tidOidArr = json_decode($item['tid_oids'], true);
                    $item['tid_oids'] = $tidOidArr;
                    $subArr = collect($tidOidArr)->pluck('subIds')->filter()->values()->toArray();
                    $subIdArr = [];
                    if (!empty($subArr) && !empty($subArr[0])) {
                        $subIdArr = array_merge(...$subArr);
                    }
                    $item['sub_id_arr'] = $subIdArr;
                    array_push($packageAll, $item);
                }
            });
            $packageAll = collect($packageAll)->unique('id')->sortBy('id')->map(function ($item) {
                return array_only($item, [
                    'id',
                    'auth_source',
                    'error_info',
                    'print_status',
                    'recycled_at',
                    'sub_waybill_codes',
                    'template_id',
                    'tid_oids',
                    'updated_at',
                    'is_split',
                    'waybill_code',
                    'waybill_status',
                    'wp_code',
                    'created_at',
                    'waybill_history',
                    'is_split',
                    'printPackageOrders',
                ]);
            })->values()->toArray();
            $mergeOrderCount = count($groupItemValue);
            $totalNum = array_sum(array_column($groupItemValue, 'num'));
            $totalPayment = array_sum(array_column($groupItemValue, 'payment'));
            $min_promise_ship_at = min(array_column($groupItemValue, 'promise_ship_at'));
            $tempItem = array_shift($groupItemValue);
            // 有子订单，把主订单的可合并和疑似去掉
            if (count($groupItemValue) > 0 || $tempItem['refund_status'] > 0) {
                $tempItem['is_merge'] = false;
                $tempItem['is_like'] = false;
            }
            $tempItem['package_all'] = $packageAll;
            $tempItem['not_delivered_count'] = $notDeliveredCount;
            $tempItem['merge_orders'] = $groupItemValue;
            $tempItem['merge_order_count'] = $mergeOrderCount;
            $tempItem['total_num'] = $totalNum;
            $tempItem['total_payment'] = $totalPayment;
            $tempItem['min_promise_ship_at'] = $min_promise_ship_at;
            $list[] = $tempItem;
        }
        // 处理合单 已发货过滤 (只要是不在这个合单里面的都会过滤)
        foreach ($list as $index => $item) {
            $orderIdArr = array_pluck($item['merge_orders'], 'id');
            $orderIdArr[] = $item['id'];
            if (!empty($item['package_all']) && is_array($item['package_all'])) {
                foreach ($item['package_all'] as $pIdx => $package) {
                    $tidOids = $package['tid_oids'];
                    if (is_array($tidOids)) {
                        foreach ($tidOids as $idx => $tidOid) {
                            if (!in_array($tidOid['id'], $orderIdArr)) {
                                unset($tidOids[$idx]);
                            }
                        }
                        $list[$index]['package_all'][$pIdx]['tid_oids'] = array_values($tidOids);
                    }

                }
            }

        }

        return $list;
    }


    /**
     * 批量获取订单信息
     * @param Request $request
     * @return JsonResponse
     * @throws ErrorCodeException
     * <AUTHOR>
     */
    public function getOrderInfoBatch(Request $request): JsonResponse
    {
        $this->validate($request, [
            "idArrStr" => "required|string",
            "displayMerge" => "boolean",   //合并展示
            "tab_flag" => "required|string",
            "ownerIdList" => "required|array"
        ]);
        $idArrStr = $request->input('idArrStr');
        $displayMerge = $request->input('displayMerge', false); //合并展示
        $tab_flag = $request->input('tab_flag', 0);        //tab 菜单项
        $ownerIdList = $request->input('ownerIdList', ''); //店铺列表
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $shopIds = collect($shops)->pluck('id')->toArray();

        $orderIdArr = explode(',', $idArrStr);
        $query = Order::query();
        $query->whereIn('id', $orderIdArr);
        $query->with(['orderItem', 'orderItem.customGoodsSkus', 'orderItem.customGoods', 'shop', 'trace',
            'packages', 'orderCipherInfo', 'packages.orders.orderItem', 'orderItem.orderItemExtra']);

        $ret = $query->get();
        $ret = $this->handleOrderData($ret);
        if ($displayMerge) {
            switch ($tab_flag) {
                case 0: //未打印
                case 4: //全部
                case 6: //部分打印未发货
                case 1: //已打印
                case 2: //已打印有退款
                case 3: //已发货
                default:
                    $groupColumn = $this->defaultGroupColumn;
                    break;
            }
            $mergeFlagOrders = $this->handleMergeFlagOrders($ret, $tab_flag, $shopIds);
            $ret = $this->handleMergeOrder($request, $ret, $groupColumn, $mergeFlagOrders);
        } else {
            $ret = $this->handleOrderPackages($ret);
        }

        $ret = $this->handleOrderFieldFilter($ret);
        return $this->success(['hidePrivacy' => $request->hidePrivacy, 'pagination' => [], $ret]);

    }

    /**
     * 批量获取订单和商品信息
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function getOrderAndItemInfoBatch(Request $request): JsonResponse
    {
        $this->validate($request, [
            "idArr" => "array",
            "tidArr" => "array",
        ]);
        $idArr = $request->input('idArr');
        $tidArr = $request->input('tidArr');
        $query = \App\Models\Fix\Order::query();
        if (!empty($idArr)){
            $query->whereIn('id', $idArr);
        }else{
            $tidArr = batchAddA($tidArr);
            $query->whereIn('tid', $tidArr);
        }
        $query->with(['orderItem', 'orderCipherInfo','operationLogs.printRecord:id,send_content','packages']);
        $list = $query->get();
        return $this->success($list);
    }

    /**
     * 处理精准查询
     * 在合单情况下要显示查询出来订单的合单
     * @param Builder|QueryBuilder $query
     * @param array $accurateWhere
     * <AUTHOR>
     */
    private function handleAccurateWhere($query, array $accurateWhere)
    {
        foreach ($accurateWhere as $where) {
            $func = $where['func'];
            if ($where['args'] instanceof Closure) {
                $query->$func($where['args']);
            } else {
                $query->$func(...$where['args']);
            }
        }
    }

    private function handleOrderPackages($list)
    {
        $data = [];
        foreach ($list->toArray() as $index => $item) {
            $packageAll = [];
            foreach ($item['packages'] as $pIndex => $package) {
                $tidOidArr = json_decode($package['tid_oids'], true);
                $package['tid_oids'] = $tidOidArr;
                $subArr = collect($tidOidArr)->pluck('subIds')->filter()->values()->toArray();
                $subIdArr = [];
                if (!empty($subArr) && !empty($subArr[0])) {
                    $subIdArr = array_merge(...$subArr);
                }
                $package['sub_id_arr'] = $subIdArr;
                $packageAll[] = $package;
            }
            $item['package_all'] = $packageAll;
            $data[] = $item;
        }
        return $data;
    }


    /**
     * 掩码解码 订单信息解密
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * @throws ValidationException
     * @throws ClientException
     * @throws ErrorCodeException
     */
    public function decrypt(Request $request)
    {
        $this->validate($request, [
            'tid' => 'required|string',
            'decrypt_columns' => 'array',
        ]);

        $default = ['receiver_name', 'receiver_phone', 'buyer_nick', 'receiver_address'];
        $tid = $request->input('tid');
        $decryptColumns = $request->input('decrypt_columns', $default);
        array_push($decryptColumns, 'tid');

//        $mergeLog = OperationLog::query()
//            ->where(['tid' => $tid, 'type' => OperationLogTypeConst::ORDER_MERGE])
//            ->orderBy('id', 'desc')
//            ->first();
//        if (!empty($mergeLog)) {
//            $requestParams = json_decode($mergeLog->append_info, true);
//            //防止读取不到 加一层判断
//            if ($requestParams) {
//                $tid = $requestParams['tid'];
//            }
//        }
        $query = Order::query()->where('tid', $tid . '');
        $orderFirst = Order::query()->with('orderCipherInfo')->where('tid', $tid . '')->first();
        if (empty($orderFirst)) {
            throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
        }
        $order = $orderFirst->toArray();
        $maskOrder = [];
        foreach ($decryptColumns as $column) {
            if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS, PlatformConst::WX, PlatformConst::WXSP, PlatformConst::XHS]) && !empty($order['order_cipher_info'])) {
                if ($column == 'buyer_nick') {
                    continue;
                }
                if ($column == 'tid') {
                    $maskOrder[$column] = array_get($order, $column);
                } else {
                    $maskOrder[$column] = $order['order_cipher_info'][$column . '_ciphertext'];
                }
            } else {
                $maskOrder[$column] = array_get($order, $column, '');
            }
        }
//        if (PlatformConst::TAOBAO == config('app.platform')) {
//        if (Environment::isTaoBao()) {
//            //淘宝通过oaid进行解密
//            //如果是JD有oaid的话，通过oaid进行解密
//            $maskOrder['oaid'] = $order['order_cipher_info']['oaid'];
//        }
        // 现在基本上都有 oaid 了
        $maskOrder['oaid'] = $order['order_cipher_info']['oaid'] ?? '';
        //抖音需要请求解密接口
        $shop = Shop::query()->where('id', $order['shop_id'])->first();

        $oaid = ArrayUtil::getArrayValue($order, 'order_cipher_info.oaid');
        if (Environment::isJd() && $oaid) {
            Log::info("JD oaid订单获取加密数据");
            //如果京东的订单并包含了ooid，先调用JD的解码接口返回收件人的加密信息
            $maskOrder['oaid'] = $oaid;
            $maskOrder['tid'] = $order['tid'];
            $orderService = OrderServiceManager::create();
//            $orderService->setUserId($shop->user_id);
            $orderService->setShop($shop);
            $result = $orderService->getEncryptData($maskOrder);
            if (!empty($result)) {
//                ['receiver_name', 'receiver_phone', 'buyer_nick', 'receiver_address'
                $maskOrder['receiver_name'] = $result['receiverName'];
                $maskOrder['receiver_phone'] = $result['receiverPhone'];
                $maskOrder['receiver_address'] = $result['receiverAddress'];
            }

        }

        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::TAOBAO])) {
            $identifyInfoList = [
                ['name' => 'encrypt_post_receiver', 'encypted' => false],
                ['name' => 'encrypt_post_tel', 'encypted' => false],
                ['name' => 'encrypt_detail', 'encypted' => false]
            ];
            $sensitiveDataList = SensitiveType::toPlatformCode([SensitiveType::address, SensitiveType::name, SensitiveType::mobile]);


            $orderAntiSpamRequest = new OrderAntispamRequest($request->auth->user_id, 1, $order['shop_id'], [$tid], OperationType::view_order, null, $identifyInfoList, $sensitiveDataList, $request);
            $this->antiSpamService->checkAntispamOrderSend($orderAntiSpamRequest);
        }
        \Log::info('解密请求', ['tid' => $tid]);
        if (in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS, PlatformConst::WX, PlatformConst::WXSP, PlatformConst::XHS]) && !empty($order['order_cipher_info'])) {
            $orderService = OrderServiceManager::create();
            $orderService->setUserId($shop->user_id);
            $orderService->setShop($shop);
            $result = $orderService->batchDecrypt($maskOrder) ?? $maskOrder;
        } else {
            \Log::info('解密失败', ['tid' => $tid]);
            $result = $maskOrder;
        }
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        $sql = getSqlByQuery($query);
        event((new OrderDecryptEvent($user, $shop, time(), [$tid]))->setClientInfoByRequest($request));
        event(new SqlLogEvent($user, $shop, time(), $sql));

        $result['buyer_nick'] = $order['buyer_nick'];
        if (empty($result['receiver_phone'])) {
            $result['receiver_phone'] = $order['order_cipher_info']['receiver_phone_mask'];
        }
        return $this->success($result);
    }

//    public function batchSetRemark(Request $request)
//    {
//        $this->validate($request, [
//            'orderSnList' => 'required|array',
//            'flag' => 'string',
//            'remark' => 'string',
//            'locked' => 'boolean',
//        ]);
//
//        $noList = $request->input('orderSnList');
//        $remark = $request->input('remark');
//        $locked = $request->input('locked', false);
//        $flag = $request->input('flag', 'GRAY');
//
//        $data = [
//            'seller_flag' => $flag
//        ];
//        $data['locked_at'] = null;
//        if ($locked) {
//            $data['locked_at'] = date('Y-m-d H:i:s');
//        }
//        if ($remark) {
//            $data['seller_memo'] = json_encode([$remark]);
//        }
//
//        $columns = ['id', 'tid', 'seller_flag', 'seller_memo'];
//        $beforeOrders = Order::query()->whereIn('tid', $noList)->get($columns)->toArray();
//        foreach ($noList as $tid) {
//            $order = Order::query()->where('tid', $tid)
//                ->orWhere('tid', $tid . 'A')->first();
//            if ($order) {
//                $shop = $order->shop;
//                $userId = $order->user_id;
//                $orderService = OrderServiceManager::create(Shop::PLATFORM_TYPE_MAP_REVERT[$shop->type]);
//                $orderService->setUserId($userId);
//                $orderService->setShop($shop);
//
//                //请求平台修改留言备注
//                $res = $orderService->sendEditSellerRemark($order->tid, $flag, $remark);
//                if (!$res) {
//                    return false;
//                }
//                Order::query()
//                    ->where('shop_id', $order->shop_id)
//                    ->where('tid', $tid)
//                    ->orWhere('tid', $tid . 'A')
//                    ->update($data);
//            } else {
//                return $this->fail('订单号' . $tid . '未找到', 400);
//            }
//        }
//
//        $afterOrder = Order::query()->whereIn('tid', $noList)->get($columns)->toArray();
//        $user = User::query()->where('id', $request->auth->user_id)->first();
//        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
//        event((new OrderUpdateEvent($user, $shop, time(), $beforeOrders, $afterOrder, 'flag'))->setClientInfoByRequest($request));
//        return $this->success('操作成功');
//    }

    /**
     * 获取打印中列表总数
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function getPrintingListCount(Request $request): JsonResponse
    {
        $this->validate($request, [
            "ownerIdList" => 'required|array',
            "begin_at" => 'date',
            "end_at" => 'date',
        ]);
        $query = $this->buildPrintingListQuery($request);
        $count = $query->count();
        return $this->success(['count' => $count]);

    }

    /**
     * 获取打印中列表
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * <AUTHOR>
     */
    public function getPrintingList(Request $request): JsonResponse
    {
        $this->validate($request, [
            "ownerIdList" => 'required|array',
            "begin_at" => 'date',
            "end_at" => 'date',
            "offset" => 'int',
            "limit" => 'int',
        ]);
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);

        $query = $this->buildPrintingListQuery($request);
        $rowsFound = $query->count();
        $ret = $query->with(['orderItem', 'orderItem.customGoodsSkus', 'orderItem.customGoods', 'shop', 'trace', 'orderCipherInfo',
            'packages' => function ($query) {
                $query->whereNull('recycled_at');
            }])
            ->offset($offset)->limit($limit)->get();
        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $offset,
            'limit' => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);

    }

    private function buildPrintingListQuery(Request $request): Builder
    {
        $beginAt = $request->input('begin_at', '');
        $endAt = $request->input('end_at', '');
        $ownerIdList = $request->input('ownerIdList', '');
        $timeField = $request->input('timeField', '');
        $conditions = [];
        if ($timeField && $beginAt && $endAt) {
            $conditions[] = ['orders.' . $timeField, '>=', date('Y-m-d H:i:s', strtotime($beginAt))];
            $conditions[] = ['orders.' . $timeField, '<=', date('Y-m-d H:i:s', strtotime($endAt))];
        }
        $shops = Shop::getListByIdentifiers($ownerIdList);
        $userIds = collect($shops)->pluck('user_id')->toArray();
        $shopIds = collect($shops)->pluck('id')->toArray();
        $query = Order::query()->where($conditions)
//            ->whereIn('orders.user_id', $userIds)
            ->whereIn('orders.shop_id', $shopIds)
            ->where('print_status', Order::PRINT_STATUS_PRINTING);
        return $query;
    }

    /**
     * 批量修改打印状态
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function editPrintStatus(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            'print_status' => [
                'required',
                'int',
                Rule::in([
                    Order::PRINT_STATUS_YES,
                    Order::PRINT_STATUS_NO,
                    Order::PRINT_STATUS_PART,
//                    Order::PRINT_STATUS_PRINTING
                ]),
            ],
            'order_no_list' => 'required|array',
        ]);
        $noList = array_get($data, 'order_no_list');
        $print_status = array_get($data, 'print_status');
        //$shopList = User::getShopAndInvite($request->auth->user_id, $request->auth->shop_id);
        $shopList = ShopBind::getAllRelationShop($request->auth->shop_id);
        $shopArr = collect($shopList)->pluck('id');

        foreach ($noList as $ids) {
            $package = Package::query()
                ->whereIn('shop_id', $shopArr)
                ->where('waybill_code', $ids)->first();
            if ($package) {
                $tids = explode(',', $package->tids);
                $res = Order::query()
                    ->whereIn('shop_id', $shopArr)
                    ->whereIn('tid', $tids)
                    ->update([
                        'print_status' => $print_status,
                        'printed_at' => $package->created_at,
                    ]);
                if (!$res) {
                    return $this->fail('订单操作失败！', 400);
                }
                if (in_array($print_status, [Order::PRINT_STATUS_YES, Order::PRINT_STATUS_PART])) {
                    //取号package
                    Package::query()
                        ->whereIn('shop_id', $shopArr)
                        ->where('waybill_code', $ids)
                        ->update([
                            'print_status' => Order::PRINT_STATUS_YES,
                        ]);
                }
            } else {
                return $this->fail('未查询到面单号' . $ids, 400);
            }
        }
        return $this->success($res);
    }

    public function printBySearchMock(Request $request)
    {
        if (rand(0, 5) == 5) {
            return $this->success([], '', 201);
        }
        $normalJson = '{"orders":[{"documentID":"15559478_15559508","express_code":"zhongtong","express_no":"75504655571014","is_dy":1,"contents":[{"templateURL":"https://sf3-ttcdn-tos.pstatp.com/obj/logistics-davinci/template/template_76.xml","params":"app_key=6839207088506275342&method=logistics.getShopKey&access_token=91667bc0-c55d-457f-affd-daef1ede7d36&param_json={}&timestamp=2021-10-16 10:52:37&v=2&sign=04cf3d9056235032c681510dd6d45a96","signature":"cizxhQ81xwgb79mB6E+E85Icb8WPOyHAuI6cKXDRi4DcJsoNt1rf4z5MACRs9wG15qo8AXNR+Nv48mcsVEhaMhgPXM5f+cDY0NPDBJjL3DH3oWB2xDetwMuiLb5ODEi/X3kHDNFZ3IBMGnfviHUVoGI6UgnMMGHj3O/MooJ6/ZTjne7IbwbIGDkBqgpSLRnEGACgX1F/jaV1z8EcjCJMwvYJdD++5G16+Hp3XFzrGFTXBwo1RZqqGBC6dI9eA6/q4rQaPwm+Fko+3SwH1+CNByNkr60I2KqNHKAkfADg/NjodTtll1v3kZzx4UdDgaVmaM8lnpq9KDvgSI7zG+tjPA==","encryptedData":"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","addData":{"senderInfo":{"address":{"provinceName":"北京市","cityName":"北京市","districtName":"海淀区","detailAddress":"互联网金融中心","town":"","countryCode":"CHN"},"contact":{"name":"test","mobile":"15115231524","phone":""}}}},{"data":{"template":{"width":"72.0","height":"52.0"},"printNextItemBeans":[{"itemType":"goodsinfo","locationX":0,"locationY":0,"width":220,"height":35,"itemName":"【测试商品勿拍】【测试商品勿拍】女装裙,【测试商品勿拍】女装裙;,✖3","itemStyle":"{\"fontSize\":\"12\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}","tip":"allProductDetail"},{"itemType":"goodsinfo","locationX":0,"locationY":35,"width":220,"height":35,"itemName":"【测试商品勿拍】修身连衣裙,杏色S;,✖9","itemStyle":"{\"fontSize\":\"12\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}","tip":"allProductDetail"},{"itemType":"watermark","locationX":107,"locationY":75,"width":80,"height":50,"itemName":"12件","tip":"watermark","itemStyle":"{\"fontSize\":\"40\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}"},{"itemType":"goodsinfo","locationX":220,"locationY":0,"width":80,"height":35,"itemStyle":"{\"fontSize\":\"12\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}","itemName":"","tip":"remark"},{"itemType":"goodsinfo","locationX":220,"locationY":35,"width":80,"height":35,"itemStyle":"{\"fontSize\":\"12\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}","itemName":"","tip":"remark"}]},"templateURL":"https://sddy.mayiapps.cn/dy_extra.xml"}]},{"documentID":"15480181","express_code":"zhongtong","express_no":"75504655570352","is_dy":1,"contents":[{"templateURL":"https://sf3-ttcdn-tos.pstatp.com/obj/logistics-davinci/template/template_76.xml","params":"app_key=6839207088506275342&method=logistics.getShopKey&access_token=91667bc0-c55d-457f-affd-daef1ede7d36&param_json={}&timestamp=2021-10-16 10:52:37&v=2&sign=04cf3d9056235032c681510dd6d45a96","signature":"deegvYUde8sVROJn9TjTRdXZYzItsi+JXcOIgZb7XSHtKJc+4pDzPI/946aSkN2rXMken2HIQ21yhFZKZx35GHLlwmhh4dGf1qodoom07j6VM0aVgpzZBrT4o/AOvTxh94afR3F7CAYtkiCXZt3eRvisoQTgIOmV/68zNOqlRHCS0U+eVBkyGlx1Vo0ECrzf/YRMTtcPS8IAd2UZAnt6nFZ3WnJi67f2UEpv4lWq+xFA0ylC9P+Sbu9YkZ/ZV3VBgXxgguv/AKDpGJeOy3As50VtgjTB7eTaojK+q7/IbBE/6rFcz1MlYdK7lkaEGQ+LxwWmaICpVc92PXxDDgM/bw==","encryptedData":"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","addData":{"senderInfo":{"address":{"provinceName":"北京市","cityName":"北京市","districtName":"海淀区","detailAddress":"互联网金融中心","town":"","countryCode":"CHN"},"contact":{"name":"test","mobile":"15115231524","phone":""}}}},{"data":{"template":{"width":"72.0","height":"52.0"},"printNextItemBeans":[{"itemType":"goodsinfo","locationX":0,"locationY":0,"width":220,"height":35,"itemName":"【测试商品勿拍】本来水果,佳沛;,✖1","itemStyle":"{\"fontSize\":\"12\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}","tip":"allProductDetail"},{"itemType":"watermark","locationX":107,"locationY":75,"width":80,"height":50,"itemName":"1件","tip":"watermark","itemStyle":"{\"fontSize\":\"40\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}"},{"itemType":"goodsinfo","locationX":220,"locationY":0,"width":80,"height":35,"itemName":"[新增客服][18317846805]211015-649194392362555","itemStyle":"{\"fontSize\":\"12\",\"fontWeight\":\"bold\",\"fontFamily\":\"simsun\"}","tip":"remark"}]},"templateURL":"https://sddy.mayiapps.cn/dy_extra.xml"}]}],"failed":[]}';
        $failJson = '{"orders":[],"failed":["{\"text\":\"取号错误\",\"tid\":[\"4854114253453385012A\"],\"info\":\"未知错误\"}","{\"text\":\"取号错误\",\"tid\":[\"4854144657555236348A\"],\"info\":\"未知错误\"}"]}';
        $json = $normalJson;
        if (rand(0, 10) == 5) {
            $json = $failJson;
        }
        return $this->success(json_decode($json, true));
    }


    /**
     * @param array $ret
     * @return array
     * <AUTHOR>
     */
    private function handleOrderFieldFilter(array $ret): array
    {
        // 排除
        $orderExceptArr = [
            'shop',
            //'buyer_nick',
//            'address_md5',
            'has_buyer_message',
            'goods_title',
            'goods_title_last',
            'sku_value',
            'sku_value_last',
            'merge_flag',
            'finished_at',
            'groupon_at',
            'recycled_at',
            'is_comment',
            'created_at',
            'updated_at',
            'deleted_at',
//                'item_refund_status', //?
            'item_refund_created_at',
            'identifier',
            'latest_trace',
            'trace_list',
            'packages',
        ];
        // only
        $itemOnlyArr = [
            'id',
            'order_id',
            'tid',
            'oid',
            'goods_type',
            'goods_price',
            'goods_pic',
            'goods_title',
            'goods_num',
            'send_remain_num',
            'send_num',
            'pre_send_num',
            'pre_send_remain_num',
            'num_iid',
            'sku_id',
            'sku_value',
            'outer_iid',
            'outer_sku_iid',
            'refund_status',
            'refund_sub_status',
            'refund_sub_status_desc',
            'waybill_code',
            'status',
            'send_at',
            'custom_order_sku_value',
            'sku_desc',
            'custom_title',
            'custom_sku_value',
            'status_desc',
            'refund_created_at',
            'order_created_at',
            'print_status',
            'print_num',
            'order_item_extra',
            'author_id',
            'author_name',
            'shipped_package_orders',
            'is_pre_sale',
            'promise_ship_at',
        ];
        $cipherOnlyArr = [
            'receiver_phone_mask',
            'receiver_name_mask',
            'receiver_address_mask',
            'oaid',
        ];

        foreach ($ret as $index => $item) {
            $ret[$index] = array_except($item, $orderExceptArr);
            foreach ($item['order_item'] as $index2 => $item2) {
                $ret[$index]['order_item'][$index2] = array_only($item2, $itemOnlyArr);
            }
//            foreach ($ret[$index]['packages'] as $index2 => $order_item) {
//                unset($ret[$index]['packages'][$index2]['orders']);
//            }
            if ($item['order_cipher_info']) {
                $ret[$index]['order_cipher_info'] = array_only($item['order_cipher_info'], $cipherOnlyArr);
            }
            if (!empty($item['merge_orders'])) {
                $ret[$index]['merge_orders'] = $this->handleOrderFieldFilter($item['merge_orders']);
            }
        }
        return $ret;
    }

    public function shippingOrderLog(Request $request)
    {
        $this->validate($request, [
            'tids' => 'required|array',
        ]);
        $tids = $request->input('tids');
        $columns = ['id', 'tid'];
        $orderList = Order::query()->whereIn('tid', $tids)->get($columns)->toArray();
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderShippingOrder($user, $shop, time(), $orderList))->setClientInfoByRequest($request));
        //更改order发货单打印时间
        Order::query()->whereIn('tid', $tids)->update(['print_shipping_at' => date('Y-m-d H:i:s', time())]);
        return $this->success();
    }

    public function getOrderOperationLog(Request $request)
    {
        $this->validate($request, [
            'orderId' => 'required',
        ]);
        $orderId = $request->input('orderId');
        $res = OperationLog::query()->where('order_id', $orderId)->get();

        return $this->success($res);
    }

    public function batchUpdateSkuValue(Request $request)
    {
        $this->validate($request, [
            'tids' => 'required|array',
            'type' => 'required|string',
            'is_append' => 'required|string',
            'sku_value' => 'string',
        ]);

        $tids = $request->input('tids');
        $type = $request->input('type');//1自定义 2使用留言
        $isAppend = $request->input('is_append');//1完全替换 2后面追加
        $skuValue = $request->input('sku_value');

        $failed = 0;
        foreach ($tids as $item) {
            $buyerMessage = Order::query()->where('id', $item['tid'])->value('buyer_message');
            $skuValue = $type == '1' ? $skuValue : $buyerMessage;
            //考虑到首页排序问题，同步修改orders中的sku_value、sku_value_last
            if ($isAppend == '1') {
                Order::query()->where('id', $item['tid'])->update([
                    'sku_value' => $skuValue,
                    'sku_value_last' => $skuValue
                ]);
                OrderItem::query()->whereIn('id', $item['oids'])->update(['custom_order_sku_value' => $skuValue]);
            } else {
                Order::query()->where('id', $item['tid'])->update([
                    'sku_value' => \DB::raw("concat(sku_value, '$skuValue')"),
                    'sku_value_last' => \DB::raw("concat(sku_value_last, '$skuValue')")
                ]);
                OrderItem::query()->whereIn('id', $item['oids'])->update(['custom_order_sku_value' => \DB::raw("concat(sku_value, '$skuValue')")]);
            }
        }
        return $this->success(['failed' => $failed]);
    }

    public function batchUpdateOrder(Request $request)
    {
        $this->validate($request, [
            'tids' => 'required|array'
        ]);
        $tids = $request->input('tids');
        $userId = $request->auth->user_id;
        $shopId = $request->auth->shop_id;
//        $orderService = OrderServiceManager::create(config('app.platform'));
//        foreach ($tids as $tid) {
//            $order = Order::query()->where('tid', $tid)->first()->toArray();
//            $shop = Shop::query()->where('id', $order['shop_id'])->first();
//            $orderService->setShop($shop);
//
//            $orderInfo = $orderService->getOrderInfo($tid);
//        }
        $tids = batchAddA($tids);
        $orders = Order::query()->whereIn('tid', $tids)->get()->toArray();
        $orderInfos = Order::batchGetOrderInfo($orders);
        $orderGroupArr = collect($orderInfos)->groupBy('shop_id')->toArray();
        foreach ($orderGroupArr as $item) {
            Order::batchSave($item, $item[0]['user_id'], $item[0]['shop_id']);
        }

//        $orderArray = array_map(function ($tid) {
//            return ['tid' => $tid, 'id' => $tid]; // 这个 id 是索引用
//        }, $tids);
//        $orderService = OrderServiceManager::create(config('app.platform'));
//        $orderService->setUserId($userId);
//        $orderService->setShop(Shop::find($shopId));
//        $tradesOrder = $orderService->batchGetOrderInfo($orderArray);
//        Order::batchSave($tradesOrder, $userId, $shopId);

        return $this->success();
    }

    /**
     * 批量通过订单号同步订单
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     * @throws \Psr\SimpleCache\InvalidArgumentException
     * @throws \Throwable
     */
    public function batchSyncOrderByTids(Request $request)
    {
        $this->validate($request, [
            'identifierArr' => 'required|array|max:10',
            'tids' => 'required|array',
        ]);
        $identifierArr = $request->input('identifierArr');
        $tids = $request->input('tids');
        foreach ($identifierArr as $index => $identifier) {
            $shop = Shop::firstByIdentifier($identifier);

            $userId = $shop->user_id;
            $shopId = $shop->id;

            $orderArray = array_map(function ($tid) {
                return ['tid' => $tid, 'id' => $tid]; // 这个 id 是索引用
            }, $tids);

            $orderService = OrderServiceManager::create(config('app.platform'));
            $orderService->setUserId($userId);
            $orderService->setShop($shop);
            $tradesOrder = $orderService->batchGetOrderInfo($orderArray);
            Order::batchSave($tradesOrder, $userId, $shopId);
        }


        return $this->success();
    }

    /**
     * 获取解密参数
     * <AUTHOR>
     */
    public function getDecryptionParams(Request $request)
    {
        $this->validate($request, [
            'sec_token' => 'string',
            'method' => 'string',
            'ati' => 'required|string',
            'identifier' => 'required|string',
        ]);
        $secToken = $request->input('sec_token');
        $identifier = $request->input('identifier');
        $ati = $request->input('ati');
        $method = $request->input('method');
//        $ati = request()->cookie('_ati', '');
        $shop = Shop::firstByIdentifier($identifier);
        ShopBind::checkShopRelation($request->auth->shop_id, $shop->id);

        $ychClient = new YchClient();
        $data = [];
        $data["ati"] = $ati;
        $data["userId"] = (string)$request->auth->user_id;
        $data["taobaoUserId"] = $shop['name'] ?? '';
        $data["userIp"] = getRealIp();
        $taobaoOrderImpl = new TaobaoOrderImpl();
        $taobaoOrderImpl->setShop($shop);
        $topToken = '';
        if (!empty($secToken)) {
            $topToken = $taobaoOrderImpl->getOnceToken($secToken);
            $data['secToken'] = $secToken;
            $data['topToken'] = $topToken;
            $data['method'] = $method;
        }
        $decryptionParams = $ychClient->getDecryptionParams($data);

        return $this->success([
//            'topToken' => $topToken,
            'params' => $decryptionParams,
        ]);
    }

    /**
     * 上报解密成功
     * <AUTHOR>
     */
    public function reportDecryptionSuccess(Request $request)
    {
        $this->validate($request, [
            'decrypt_request_id' => 'required|string',
            'decrypt_time' => 'required|string|date',
            'tid' => 'required|string',
        ]);
        $tid = $request->input('tid');
        $decrypt_time = $request->input('decrypt_time');
        $orderInfo = Order::firstByTid($tid);
        if (empty($orderInfo)) {
            throw new ApiException(ErrorConst::ORDER_DATA_EMPTY);
        }
        $shop = Shop::firstById($orderInfo->shop_id);
        ShopBind::checkShopRelation($request->auth->shop_id, $shop->id);
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $orderDecryptEvent = new OrderDecryptEvent($user, $shop, time(), [$tid]);
        $orderDecryptEvent->decryptRequestId = $request->input('decrypt_request_id');
        $orderDecryptEvent->decryptTime = strtotime($decrypt_time);
        $orderDecryptEvent->setClientInfoByRequest($request);
        event($orderDecryptEvent);

        return $this->success();
    }

    /**
     * 质检 检查质检状态
     * @param Request $request
     * <AUTHOR>
     */
    public function qualityCheckStatus(Request $request)
    {
        $this->validate($request, [
            'orderList' => 'required|array',
            'orderList.*.tid' => 'required|string',
        ]);
        $shop = Shop::firstById($request->auth->shop_id);
        $orderList = $request->input('orderList');
        $orderInfoList = Order::query()->with('orderItem.orderItemExtra')->whereIn('tid', array_column($orderList, 'tid'))->get();
        $orderService = OrderServiceManager::create();
        $orderService->setShop($shop);
        $commonResponseList = $orderService->getQualityInspectionResult($orderInfoList);
        foreach ($commonResponseList as $index => $commonResponse) {
            if ($commonResponse->isSuccess()) {
                $data = $commonResponse->getData();
                $orderItemInfo = OrderItem::query()->where('oid', $data['oid'])->first();
                OrderItemExtra::query()->where(['order_item_id' => $orderItemInfo['id']])->update($data['order_item_extras']);
            }
        }
        return $this->success();
    }


    private function handleFinalWhere($queryFinally, $whereArr)
    {
        foreach ($whereArr as $item) {
            if (!empty($item['func'])) {
                $this->handleAccurateWhere($queryFinally, [$item]);
            } else {
                $queryFinally->where(...$item);
            }
        }
    }

    /**
     * 处理订单数据
     * @param $ret
     * @return array
     * <AUTHOR>
     */
    private function handleOrderData($ret)
    {
        foreach ($ret as $orderIndex => $order) {
            if (empty($ret[$orderIndex]->seller_nick)) {
                $ret[$orderIndex]->seller_nick = $ret[$orderIndex]->shop->shop_name;
            }
            // 开始过滤 packages 的 tid_oids 退款订单
            $needFilterItemId = [];
//            $order = $order->toArray();
//            foreach ($order['order_item'] as $itemIdx => $item) {
//                if ($item['refund_status'] != OrderItem::REFUND_STATUS_NO) {
//                    $needFilterItemId[] = $item['id'];
//                }
//            }
            $order['receiver_phone_idx'] = $order['receiver_phone'];
            foreach ($order['packages'] as $packIndex => $package) {
                $tidOids = json_decode($package['tid_oids'], true);
                if (is_array($tidOids)) {
//                    $packageOrders = array_pluck($package['orders'], null, 'id');
                    $packageOrders = $package['orders']->pluck(null, 'id');
                    $needFilterOrderIdArr = [];
                    $orderItemAllIdArr = [];
                    // 提取要过滤的退款的 orderId
                    foreach ($packageOrders as $packageOrder) {
//                        $orderItemAllIdArr[$packageOrder['id']] = array_pluck($packageOrder['order_item'], 'id');
                        $orderItemAllIdArr[$packageOrder['id']] = $packageOrder['orderItem']->pluck('id');
                        if (Order::REFUND_STATUS_YES == $packageOrder['refund_status']) {
                            $needFilterOrderIdArr[] = $packageOrder['id'];
                        } elseif (Order::REFUND_STATUS_PART == $packageOrder['refund_status']) {
                            foreach ($packageOrder['orderItem'] as $itemIdx => $item) {
                                if ($item['refund_status'] != OrderItem::REFUND_STATUS_NO) {
                                    $needFilterItemId[] = $item['id'];
                                }
                            }
                        }
                    }
                    foreach ($tidOids as $idx => $tidOid) {
                        if (in_array($tidOid['id'], $needFilterOrderIdArr)) {
                            unset($tidOids[$idx]);
                        } else if (empty($tidOid['subIds'])) {
                            // 如果 subIds 空的就是全选
                            //TODO 临时修复数据被删除的问题
//                            if ($order['shop_id'] == 2865) {
                            $itemArr = $orderItemAllIdArr[$tidOid['id']] ?? [];
//                            } else {
//                                $itemArr = $orderItemAllIdArr[$tidOid['id']];
//                            }
                            $count = count($itemArr);
                            // 退款订单也可打印不用过滤了
                            /*foreach ($itemArr as $subIdx => $itemId) {
                                if (in_array($itemId, $needFilterItemId)) {
                                    unset($itemArr[$subIdx]);
                                }
                            }*/
                            if ($count != count($itemArr)) {
                                $tidOids[$idx]['subIds'] = $itemArr;
                            }

                        } else {
                            // 如果是指定子订单 id
                            foreach ($tidOid['subIds'] as $subIdx => $itemId) {
                                if (in_array($itemId, $needFilterItemId)) {
                                    unset($tidOids[$idx]['subIds'][$subIdx]);
                                }
                            }
                            // subIds 删光了，这个ID就没意义了
                            if (empty($tidOids[$idx]['subIds'])) {
                                unset($tidOids[$idx]);
                            }
                        }
                        if (isset($tidOids[$idx])) {
                            $tidOids[$idx]['subIds'] = array_values($tidOids[$idx]['subIds']);
                        }
                    }
                }
                if (!empty($tidOids) && is_array($tidOids)) {
                    $ret[$orderIndex]['packages'][$packIndex]['tid_oids'] = json_encode(array_values($tidOids));
                }
            }
        }
        foreach ($ret as $orderIndex => $order) {
            foreach ($order['orderItem'] as $index => $order_item) {
                $send_remain_num = $order_item['goods_num'] - $order_item['send_num'];
//                if (Environment::isTaoBao() && $send_remain_num <= 0) {
//                    $send_remain_num = 1; // 淘宝至少数量1
//                }
                $ret[$orderIndex]['orderItem'][$index]['send_remain_num'] = $send_remain_num;
                $pre_send_remain_num = $send_remain_num - $order_item['pre_send_num'];
                $pre_send_remain_num <= 0 && $pre_send_remain_num = 0;
                $ret[$orderIndex]['orderItem'][$index]['pre_send_remain_num'] = $pre_send_remain_num;

            }
            foreach ($order['packages'] as $packIndex => $package) {
                unset($ret[$orderIndex]['packages'][$packIndex]['orders']);
            }
        }

        return $ret;
    }

    /**
     * 订单区域筛选
     * @param $addressGroupId
     * @param $query
     * <AUTHOR>
     */
    protected function buildAddressGroupId($addressGroupId, $query): void
    {
        $area = QueryArea::query()->findOrFail($addressGroupId);
        $provinceArr = explode(',', $area->province_str);
        $cityArr = explode(',', $area->city_str);
        $districtArr = explode(',', $area->district_str);
        $customDistrictArr = explode(',', $area->custom_district_str);
        $districtArr = array_merge($districtArr, $customDistrictArr);
        $cityArr[] = '市辖区';
        if ($area->include == '1') {
//            $query->whereIn('receiver_state', $provinceArr);
//            // 抖音，如果 区域有数据，那就不过滤市，用于处理[直辖市]
//            if (!in_array(config('app.platform'), [PlatformConst::DY]) || empty($districtArr)) {
//                $query->whereIn('receiver_city', $cityArr);
//            }
//            $query->whereIn('receiver_district', $districtArr);
            $query->whereIn('district_code', $districtArr);
        } else {
//            $addressCollect = Address::getAddressByCache();
//            $provinceCodeArr = $addressCollect->whereIn('name', $provinceArr)
//                ->where('level', Address::LEVEL_PROVINCE)
//                ->pluck('code')->toArray();
//            $cityCodeArr = $addressCollect->whereIn('name', $cityArr)
//                ->whereIn('parent_code', $provinceCodeArr)
//                ->where('level', Address::LEVEL_CITY)
//                ->pluck('code')->toArray();
//            $districtCodeArr = $addressCollect->whereIn('name', $districtArr)
//                ->whereIn('parent_code', $cityCodeArr)
//                ->where('level', Address::LEVEL_DISTRICT)
//                ->pluck('code')->toArray();
//            $query->whereNotIn('receiver_state', $provinceArr);
//            if (!in_array(config('app.platform'), [PlatformConst::DY]) || empty($districtArr)) {
//                $query->whereNotIn('receiver_city', $cityArr);
//            }
//            $query->whereNotIn('receiver_district', $districtArr);
//            Log::debug('$districtCodeArr',[$provinceCodeArr,$cityCodeArr,$districtCodeArr]);
//            if (!empty($districtCodeArr)) {
//                $query->whereNotIn('district_code', $districtCodeArr);
//            }
            $query->whereNotIn('district_code', $districtArr);
        }
    }

    /**
     * @param $ret
     * @param $tab_flag
     * @param array $shopIds
     * @param $mergeFlagOrders
     * <AUTHOR>
     */
    protected function handleMergeFlagOrders($ret, $tab_flag, array $shopIds): array
    {
        $mergeFlagOrderIdMap = array_pluck($ret, 'id', 'merge_flag');
        $mergeFlagOrderIdArr = array_values($mergeFlagOrderIdMap);
        $mergeFlagOrders = [];
        if (in_array($tab_flag, ['0', '1'])) {
            $query = \App\Models\Fix\Order::query()->whereIn('id', $mergeFlagOrderIdArr);
            $query->with(['mergeFlagOrders' => function ($query) use ($shopIds) {
                $query->whereIn('shop_id', $shopIds);
            },
                'mergeFlagOrders.orderItem',
                'mergeFlagOrders.orderItem.orderItemExtra',
                'mergeFlagOrders.orderItem.customGoodsSkus',
                'mergeFlagOrders.orderItem.customGoods',
                'mergeFlagOrders.shop',
                'mergeFlagOrders.trace',
                'mergeFlagOrders.orderCipherInfo',
                'mergeFlagOrders.packages.orders.orderItem',
            ]);
            $mergeFlagOrders = $query->get()->groupBy('merge_flag')->toArray();
        }
        return $mergeFlagOrders;
    }

    /**
     * @param $displayMerge
     * @param Builder $queryLogistics
     * @param $onlyShowMergeOrder
     * @param bool $notShowMergeOrder
     * @param bool $handToShowMergeOrder
     * @param $salesAttributes
     * @param string $groupColumn
     * @param int $rows_found
     * @param $goodsNum
     * @param $ordersNum
     * @param $ordersKind
     * @param array $amountArr
     * @param $sort
     * @param array $accurateWhere
     * @param array $finalWhere
     * @param array $groupWhere
     * @return array
     * @throws ApiException
     * <AUTHOR>
     */
    protected function handleIndexLogistics($displayMerge, Builder $queryLogistics, $onlyShowMergeOrder,
                                            bool $notShowMergeOrder, bool $handToShowMergeOrder, $salesAttributes,
                                            string $groupColumn, int $rows_found, $goodsNum, $ordersNum, $ordersKind,
                                            array $amountArr, $sort, array $accurateWhere, array $finalWhere, array $groupWhere): array
    {
        $sql = '';
        if ($displayMerge) {
            $countSql = 'sum(distinct_merge_orders_num) as count';
            $this->handleOrderGroup($sql, $queryLogistics, $onlyShowMergeOrder, $notShowMergeOrder, $handToShowMergeOrder,
                $salesAttributes, $groupColumn, $rows_found, $goodsNum, $ordersNum, $ordersKind, $amountArr, $sort, 0,
                0, $accurateWhere, $finalWhere, $groupWhere);
        } else {
            $countSql = 'count(*) as count';
            $this->handleSingleOrder($sql, $queryLogistics, $salesAttributes, $rows_found, $goodsNum, $ordersNum,
                $ordersKind, $amountArr, $sort, 0, 0, $accurateWhere, $finalWhere, $groupWhere);
        }
        $logisticsList = \DB::query()->fromSub($sql, 'cc')
            ->selectRaw("/*+ max_execution_time(10000)*/ smart_logistics,$countSql")
            ->groupBy(['smart_logistics'])
            ->get()->toArray();
        return $logisticsList;
    }

    /**
     * @param $print_status
     * @param string $groupColumn
     * @param $order_status
     * @param $query
     * @param array $groupWhere
     * <AUTHOR>
     */
    protected function buildByPrintStatus($query, $print_status, $order_status, array &$groupWhere): void
    {
        if ($print_status >= 0) {
            //已打印
            if ($print_status == Order::PRINT_STATUS_YES) {
                // 已发货需要显示部分发货的订单，改成判断 printed_at
                if (Order::ORDER_STATUS_DELIVERED == $order_status) {
                    $query->whereNotNull('printed_at');
                } else {
                    $query->whereIn('orders.print_status', [$print_status, Order::PRINT_STATUS_PART]);
                }
            }
            //未打印
            if ($print_status == Order::PRINT_STATUS_NO) {
                $query->whereIn('orders.print_status', [$print_status, Order::PRINT_STATUS_PART]);
//                $query->where('order_items.print_status', [$print_status, Order::PRINT_STATUS_PART]);
//                $query->whereIn('orders.print_status', [$print_status, Order::PRINT_STATUS_PART]);
                $groupWhere[] = ['item-print_status', OrderItem::PRINT_STATUS_NO];
//                $groupWhere[] = ['item-refund_status', OrderItem::REFUND_STATUS_NO];
//                $query->whereHas('orderItem',function ($query2){
//                    $query2->where('print_status',OrderItem::PRINT_STATUS_NO);
//                    $query2->where('refund_status',OrderItem::REFUND_STATUS_NO);
//                });
            }

            //打印一次
            if ($print_status == Order::PRINT_STATUS_ONE) {
                $query->whereNotNull('printed_at')->where('orders.print_num', 1);
            }

            //打印多次
            if ($print_status == Order::PRINT_STATUS_MORE) {
                $query->whereNotNull('printed_at')->where('orders.print_num', '>', 1);
            }

            //已打印发货单
            if ($print_status == Order::PRINT_STATUS_SHIPPING) {
                $query->whereNotNull('print_shipping_at');
            }

        } else {
            //全部
//            $query->whereIn('orders.print_status', [Order::PRINT_STATUS_NO, Order::PRINT_STATUS_YES,
//                Order::PRINT_STATUS_PART, Order::PRINT_STATUS_PRINTING]);
        }
    }

    /**
     * @param $order_status
     * @param $quickFilterValue
     * @param $query
     * @param string $groupColumn
     * <AUTHOR>
     */
    protected function buildByOrderStatus($query, $order_status, $quickFilterValue): void
    {
        if ($order_status > 0) {
            switch ($order_status) {
                case Order::ORDER_STATUS_PAYMENT:
                    if ($quickFilterValue == '12') {
                        $query->whereNull('orders.send_at');
                        $query->whereIn('order_status', Order::ORDER_STATUS_ALL_ARRAY);
                    } else {
                        $query->whereIn('order_status', [
                            Order::ORDER_STATUS_PAYMENT,
                            Order::ORDER_STATUS_PART_DELIVERED,
                        ]);
                    }
                    break;
                case Order::ORDER_STATUS_DELIVERED:
                    $query->whereNotNull('orders.send_at');
                    $query->whereIn('order_status', Order::ORDER_STATUS_DELIVERED_ARRAY);
                    break;
                default:
                    if ($quickFilterValue != '12') {
                        $query->where('order_status', $order_status);
                    }
                    break;
            }
        } else {
            //全部订单状态
            $query->whereIn('order_status', Order::ORDER_STATUS_ALL_ARRAY);
        }
    }

    private function handleMergeGoods(array $ret)
    {
        if (empty($ret)) {
            return $ret;
        }

        $list = [];
        //根据外部编码分组
        $groupOutGoodsId = collect($ret)->groupBy(['outer_goods_id']);
        foreach ($groupOutGoodsId as $outGoodsId => $groupOutGoodsIdItem) {
            //商品简称分组
            $groupCustomTitle = collect($groupOutGoodsIdItem)->groupBy(['custom_title']);
            foreach ($groupCustomTitle as $groupCustomTitleKey => $groupCustomTitleItem) {
                //商品简称为空的不合并
                if ($groupCustomTitleKey == "") {
                    foreach ($groupCustomTitleItem as $item) {
                        $list[] = $item;
                    }
                } else {
                    //sku分组合并
                    $childrenArr = [];
                    $newChildrenArr = [];
                    foreach ($groupCustomTitleItem as $item) {
                        foreach ($item['children'] as $v) {
                            $childrenArr[] = $v;
                        }
                    }
                    $groupOutSkuId = collect($childrenArr)->groupBy(['outer_sku_id']);
                    foreach ($groupOutSkuId as $groupOutSkuIdKey => $groupOutSkuIdItem) {
                        //sku简称分组
                        $groupCustomSkuTitle = collect($groupOutSkuIdItem)->groupBy(['custom_sku_value']);
                        foreach ($groupCustomSkuTitle as $groupCustomSkuTitleKey => $groupCustomSkuTitleItem) {
                            //简称为空不合并
                            if ($groupCustomSkuTitleKey == "") {
                                foreach ($groupCustomSkuTitleItem as $item) {
                                    $newChildrenArr[] = $item;
                                }
                            } else {
                                $temp = [
                                    //'label' => $groupCustomSkuTitleItem[0]['label'],
                                    'label' => $groupCustomSkuTitleItem[0]['custom_sku_value'] ? $groupCustomSkuTitleItem[0]['custom_sku_value'] : $groupCustomSkuTitleItem[0]['label'],
                                    'sku_value' => implode(',', $groupCustomSkuTitleItem->pluck('sku_value')->toArray()),
                                    'sku_id' => implode(',', $groupCustomSkuTitleItem->pluck('sku_id')->toArray()),
                                    'custom_sku_value' => $groupCustomSkuTitleItem[0]['custom_sku_value'],
                                    'outer_sku_id' => $groupCustomSkuTitleItem[0]['outer_sku_id'],
                                    'goods_pic' => $groupCustomSkuTitleItem[0]['goods_pic'],
                                    'to_shipped_count' => array_sum($groupCustomSkuTitleItem->pluck('to_shipped_count')->toArray())
                                ];

                                $newChildrenArr[] = $temp;
                            }
                        }
                        $temp = [
                            'goods_id' => implode(',', $groupCustomTitleItem->pluck('goods_id')->toArray()),
                            'to_shipped_count' => array_sum($groupCustomTitleItem->pluck('to_shipped_count')->toArray()),
                            'children' => $newChildrenArr,
                            'label' => $groupCustomTitleItem[0]['custom_title'] ? $groupCustomTitleItem[0]['custom_title'] : $groupCustomTitleItem[0]['label'],
                            'goods_title' => implode(',', $groupCustomTitleItem->pluck('goods_title')->toArray()),
                            'custom_title' => $groupCustomTitleItem[0]['custom_title'],
                            'outer_goods_id' => $groupCustomTitleItem[0]['outer_goods_id'],
                            'goods_pic' => $groupCustomTitleItem[0]['goods_pic'],
                        ];
                        $list[] = $temp;
                    }
                }
            }
        }
        return $list;
    }

    /**
     * @param $quickFilterValue
     * @param bool $onlyShowMergeOrder
     * @param bool $notShowMergeOrder
     * @param bool $handToShowMergeOrder
     * @param Builder $query
     * @return bool[]
     * <AUTHOR>
     */
    protected function handleQuickFilter(Builder $query, $quickFilterValue, bool $onlyShowMergeOrder,
                                         bool    $notShowMergeOrder, bool $handToShowMergeOrder): array
    {
        //快捷筛选
        if ($quickFilterValue) {
            switch ($quickFilterValue) {
                case '1': //合并订单
                    $onlyShowMergeOrder = true;
                    break;
                case '2': //非合单
                    $notShowMergeOrder = true;
                    break;
                case '3': //同一人多个地址
                    $handToShowMergeOrder = true;
                    break;
                case '4': //未锁定订单
                    //$query->whereNull('locked_at');
                    break;
                case '5': //已锁定订单
                    //$query->whereNotNull('locked_at');
                    break;
                case '6': //乡镇
                    $query->where('village_flag', 1);
                    break;
                case '7': //非乡镇
                    $query->where('village_flag', 0);
                    break;
                case '20': //超时订单
                    $query->where('promise_ship_at', '<=', date('Y-m-d H:i:s', time()))
                        ->whereIn('order_status', [Order::ORDER_STATUS_PART_DELIVERED, Order::ORDER_STATUS_PAYMENT]);
                    break;
                case '21': //有退款待发货
                    $query->whereNull('orders.send_at');
                    break;
                case '22': //虚假地址
                    $query->where('orders.address_flag', 1);
                    break;
                case '23': //已分配厂家订单
                    $query->where('orders.factory_id', '>', 0);
                    break;
                case '24': //已代发订单
                    $query->where('order_extras.is_factory_shipped', 1);
                    break;
                case '25': //质检订单
                    $query->where('order_extras.order_biz_type', OrderExtra::BIZ_TYPE_QUALITY_INSPECTION);
                    break;
                case '26': //质检通过
                    $query->where('order_item_extras.quality_status', OrderItemExtra::QUALITY_STATUS_CHECKED_PASS);
                    break;
                case '27': //质检未发货
                    $query->where('order_extras.order_biz_type', OrderExtra::BIZ_TYPE_QUALITY_INSPECTION);
                    $query->where('order_item_extras.quality_delivery_status', OrderItemExtra::QUALITY_DELIVERY_STATUS_NOT_DELIVERY);
                    break;
                default:
                    break;
            }
        }
        return array($onlyShowMergeOrder, $notShowMergeOrder, $handToShowMergeOrder);
    }

    /**
     * 按需 懒join order_extras 表
     * @param Builder $queryBase
     * <AUTHOR>
     */
    protected function handleLazyJoinOrderExtras(Builder $queryBase): void
    {
        foreach ($queryBase->getQuery()->wheres as $index => $where) {
            $column = $where['column'] ?? '';
            if (!empty($column) && strpos($column, 'order_extras.') !== false) {
                $queryBase->leftJoin('order_extras', 'orders.id', 'order_extras.order_id');
                break;
            }
        }
    }

    /**
     * 按需 懒join order_item_extras 表
     * @param Builder $queryBase
     * <AUTHOR>
     */
    protected function handleLazyJoinOrderItemExtras(Builder $queryBase): void
    {
        foreach ($queryBase->getQuery()->wheres as $index => $where) {
            $column = $where['column'] ?? '';
            if (!empty($column) && strpos($column, 'order_item_extras.') !== false) {
                $queryBase->leftJoin('order_item_extras', 'order_items.id', 'order_item_extras.order_item_id');
                break;
            }
        }
    }

    /**
     * @param $printMode
     * @param Request $request
     * @param $ownerIdList
     * @return array
     */
    public function getOwnerIdListByPrintMode($printMode, Request $request, $ownerIdList): array
    {
        if ($printMode == 1) {
            $ownerIdList = ShopBind::getValidIdentifierByRelation($request->auth->shop_id, $ownerIdList, $printMode);
        } else {
            $relationShops = ShopBind::getAllRelationShop($request->auth->shop_id);
            $tempOwnerIdList = [];
            foreach ($relationShops as $relationShop) {
                $tempOwnerIdList = array_merge($tempOwnerIdList, ShopBind::getValidIdentifierByRelation($relationShop['id'], $ownerIdList, $printMode));
            }
            $ownerIdList = $tempOwnerIdList;
        }
        return $ownerIdList;
    }
}
