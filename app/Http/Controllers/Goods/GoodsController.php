<?php

namespace App\Http\Controllers\Goods;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Exceptions\ErrorCodeException;
use App\Http\Controllers\Controller;
use App\Jobs\Goods\SyncGoodsJob;
use App\Jobs\Goods\SyncGoodsJobNew;
use App\Models\Goods;
use App\Models\GoodsSku;
use App\Models\OrderItem;
use App\Models\Shop;
use App\Services\BusinessException;
use App\Services\Goods\GoodsQueryService;
use App\Services\Goods\GoodsService;
use App\Services\Goods\GoodsServiceManager;
use App\Services\WxEncrypt\ErrorCode;
use App\Utils\Environment;
use App\Utils\ObjectUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class GoodsController extends Controller
{

    /**
     * @var GoodsService $goodsService
     */
    protected $goodsService;

    /**
     * @param GoodsService $goodsService
     */
    public function __construct(GoodsService $goodsService)
    {
        $this->goodsService = $goodsService;
    }


    /**
     * 收货地址列表
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $this->validate($request, [
            "is_onsale" => 'int',
            "title" => 'string',
            "is_set" => 'int',
            "keyword" => 'string',
        ]);
        $condition = [];
        //$condition[] = ['goods.user_id', $request->auth->user_id];
        //$condition[] = ['goods.shop_id', $request->auth->shop_id];
        if ($title = $request->input('title', '')) {
            $condition[] = ['goods_title', 'like', '%' . $title . '%'];
        }
        if (in_array($request->input('is_onsale'), array(Goods::IS_ONSALE_NO, Goods::IS_ONSALE_YES))) {
            $condition[] = ['is_onsale', '=', $request->input('is_onsale')];
        }
        if ($isSet = $request->input('is_set', 0)) {
            $condition[] = ['custom_title', '=', null];
        }

        $shopIds = [intval($request->input('shopId', $request->auth->shop_id))];
//        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);

        $keyword = trim($request->input('keyword', ''));
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);
        $orderBy = $request->input('order_by', "goods.id desc");
        $result = GoodsService::search($shopIds, $condition, $offset, $limit, $orderBy, $keyword);
        $rowsFound = $result->total;
        $ret = $result->data;
//        if ($keyword) {
//            $query = Goods::query()->with('skus')->whereIn('shop_id', $shopIds)->where($condition);
//            $rowsFound = $query->where(function ($query) use ($keyword) {
//                $query->where('custom_title', 'like', '%' . $keyword . '%')
//                    ->orWhere('goods_title', 'like', '%' . $keyword . '%')
//                    ->orWhere('outer_goods_id', $keyword)
//                    ->orWhere('num_iid', $keyword)
//                    ->orWhereHas('skus', function ($query) use ($keyword) {
//                        $query->where('sku_value', 'like', '%' . $keyword . '%')
//                            ->orWhere('custom_sku_value', 'like', '%' . $keyword . '%')
//                            ->orWhere('sku_id', $keyword)
//                            ->orWhere('outer_id', $keyword);
//                    });
//            })->count();
//        } else {
//            $rowsFound = Goods::query()->whereIn('shop_id', $shopIds)->where($condition)->count();
//        }
        $ret = (new GoodsQueryService())->handleMerge($ret);
        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $offset,
            'limit' => $limit
        ];
        return $this->success(['pagination' => $pagination, $ret]);
    }

    /**
     * 设置简称
     * @param Request $id
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function edit(Request $request, $id)
    {
        $this->validate($request, [
            "custom_title" => "required|string",
            "goodId" => "required|string",
            "userId" => "int",
            "shopId" => "int",
        ]);
        $condition = [];
        //$condition[] = ['user_id', $request->auth->user_id];
        $condition[] = ['shop_id', intval($request->input('shopId', $request->auth->shop_id))];
        //$goods = Goods::query()->where('num_iid', $request->input('goodId'))
        $goods = Goods::query()->whereIn('id', explode(',', $id))
            ->update([
                'custom_title' => $request->input('custom_title')
            ]);
        if (!$goods) {
            return $this->fail('设置简称失败，请先到[设置]-[商品简称]中同步商品');
        }
        return $this->success();
    }

    /**
     * 商品检索
     * @param Request $request
     * @return JsonResponse
     * @throws \App\Exceptions\ErrorCodeException
     */
    public function searchGoods(Request $request)
    {
        $goodsQueryRequest = new GoodsSearchRequest();
        ObjectUtil::mapToObject($request->all(), $goodsQueryRequest);
        Log::info('searchGoods', [$goodsQueryRequest]);
//        if($goodsQueryRequest->isEmpty()){
//            return $this->success();
//        }
        $shop = Shop::firstById($goodsQueryRequest->shopId);
        if (empty($shop)) {
            throw_error_code_exception(ErrorConst::SHOP_NOT_EXIST);
        }
        $platGoodService = GoodsServiceManager::newSearchInstance($shop->access_token, $shop);
        if ($goodsQueryRequest->isSearchByPlatform()) {
            Log::info('通过平台查找商品', [$goodsQueryRequest]);
            if (Environment::isSupportGoodsTitleSearch()
                || (empty($goodsQueryRequest->goodsTitle) && empty($goodsQueryRequest->numIid))  //商品标题和商品id都为空就是空白搜索
            ) {
                $result = $platGoodService->searchGoods($goodsQueryRequest);

            } else {
                if (!empty($goodsQueryRequest->goodsTitle)) {
                    throw_error_code_exception(ErrorConst::UN_SUPPORT_OPERATION);
                }
                if (empty($goodsQueryRequest->numIid)) {
                    throw_error_code_exception(ErrorConst::PARAM_ERROR);
                }
                $goods = $platGoodService->getGoodsListByGoodsId([$goodsQueryRequest->numIid]);
                $result = new GoodsSearchResponse();
                $result->hasNext = false;
                $result->data = $goods;
                $result->total = sizeof($goods);
            }
            $platformGoods = $result->data;
            if (!empty($platformGoods)) {
                $numIids = array_column($platformGoods, 'num_iid');
                //用$numIids查询商品信息
                $localGoods = $this->goodsService->findManyGoodsByNumIids($numIids, ['skus']);
                $this->mergePlatformAndLocalGoods($localGoods, $platformGoods);
            }

            $pagination = [
                'rows_found' => $result->total,
                'offset' => $goodsQueryRequest->getOffset(),
                'limit' => $goodsQueryRequest->pageSize
            ];
            $ret = $platformGoods;

        } else {
            Log::info('通过本地查找商品', [$goodsQueryRequest]);
            $result = $this->goodsService->searchGoods($goodsQueryRequest);
            $localGoods = $result->data;
            Log::info('localGoods', [$localGoods]);
            $numIids = $localGoods->pluck('num_iid')->toArray();
            if (!empty($numIids)) {
                $platformGoods = $platGoodService->getGoodsListByGoodsId($numIids);
                $this->mergePlatformAndLocalGoods($localGoods, $platformGoods);
            }

            $pagination = [
                'rows_found' => $result->total,
                'offset' => $goodsQueryRequest->getOffset(),
                'limit' => $goodsQueryRequest->pageSize
            ];
            $ret = $platformGoods ?? [];

        }


        return $this->success(['pagination' => $pagination, $ret]);


    }

    /**
     *  批量设置商品的简称，规格的简称
     * @param Request $request
     * @return JsonResponse
     */
    public function batchSetAlias(Request $request)
    {
        $goodsList = $request->input('goodsList');
        $skuList = $request->input('skuList');
        $shopId = $request->input('shopId');
        if (!empty($goodsList)) {
            //修改商品简称
//            $updateData = [
//                1635146968944930538 => ['score' => 100, 'score_status' => 1],
//                1635146968944930539 => ['score' => 101, 'score_status' => 2]
//            ];
            Goods::batchUpdateData(['custom_title'], $goodsList, 'goods', 'num_iid', 'shop_id = ' . $shopId);
        }
        if (!empty($skuList)) {
            //修改规格简称
            Goods::batchUpdateData(['custom_sku_value'], $skuList, 'goods_skus', 'sku_id', 'shop_id = ' . $shopId);
        }
        return $this->success();
    }


    /**
     * 通过选中num_iid更新商品
     * @param Request $request
     * @return JsonResponse
     * @throws ErrorCodeException
     */
    public function editByNumiid(Request $request)
    {

        $this->validate($request, [
            "custom_title" => "string",
            "goodId" => "required|string",
            "userId" => "int",
            "shopId" => "int",
        ]);
        $shopId = $request->input('shopId');
        if (isset($shopId)) {
            $shop = Shop::find($shopId);
        } else {
            $orderItemId = $request->input('id');
            $shopId = OrderItem::find($orderItemId)['shop_id'];
            $shop = Shop::find($shopId);
        }

        $numIid = $request->input('goodId');
        $goodService = GoodsServiceManager::newInstance($shop->access_token, $shop);
        $customerTitle = $request->input('custom_title');
        $remark = $request->input('remark');
        $flag = $request->input('flag');
        $success = $goodService->goodsEditByNumiid($request->auth->user_id, $shop, $numIid, $customerTitle, $remark, $flag);
        if ($success) {
            return $this->success();
        } else {
            return $this->fail("更新失败，原因：无法获取商品信息");
        }
    }

    //设置sku 简称
    public function skuEdit(Request $request, $id)
    {
        $this->validate($request, [
            "custom_sku_value" => "string",
            "skuId" => "required|string",
            "userId" => "int",
            "shopId" => "int",

        ]);

        $isOrder = $request->input('isOrder');
        $numIid = $request->input('numIid');

        $customerSkuValue = $request->input('custom_sku_value', null);

        if ($isOrder) {
            $updateCount= OrderItem::query()->where('id', $request->input('id'))->update(
                ['custom_order_sku_value' => $customerSkuValue]
            );
            return $updateCount>0?$this->success():$this->fail('更新失败');
        } else {
            $condition = [];
            //$condition[] = ['user_id', $request->input('userId')];
            $shopId = $request->input('shopId');
            $condition[] = ['shop_id', $shopId];
            $skuId = $request->input('skuId');
            $sku = GoodsSku::query()->where('sku_id', $skuId)->first();
            if ($sku) {
                $sku->custom_sku_value = $customerSkuValue;
                $sku->save();
                return $this->success();
            } else {
                $shop = Shop::find($shopId);
                //没有匹配到GoodsSku就先同步
                $goodService = GoodsServiceManager::newInstance($shop->access_token, $shop);
                $result=$goodService->skuEditBySkuId($shop, $numIid, $skuId, $customerSkuValue);
                return $result?$this->success():$this->fail('更新失败');

            }
        }



    }

    /**
     * 同步商品
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     * <AUTHOR>
     */
    public function syncGoods(Request $request): JsonResponse
    {
        //$shopId = $request->auth->shop_id;
//        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
//        $shopIdentifier = $shop->shop_identifier ?? $shop->identifier;
//        $shops = Shop::query()->whereIn('identifier', explode(',', $shopIdentifier))->get();
//        $shopIds = collect($shops)->pluck('id')->toArray();
        $shopIds = [intval($request->input('shopId', $request->auth->shop_id))];
//        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
        foreach ($shopIds as $shopId) {
            $redis = \redis('cache');
            $key = 'syncGoodsLock:' . $shopId;
            $lock = $redis->set($key, 1, 'nx', 'ex', 60);
            $lock = 1;
            if (!$lock) {
                throw new ApiException(ErrorConst::ORDER_REPEAT_SYNC);
            }
            $action = 'syncGoods';
            $key = "$action:sync_finish:$shopId";
            $redis->setex($key, 86400, 0);
            // 需要同步总数量
            $key = "$action:sync_total:$shopId";
            $redis->setex($key, 86400, 0);
            // 当前同步的数量
            $key = "$action:sync_current:$shopId";
            $redis->setex($key, 86400, 0);

            $shop = Shop::query()->where('id', $shopId)->first();
            if (Environment::isXhs()) {
                dispatch((new SyncGoodsJob($shop, 1, date('Y-m-d H:i:s')))->setAction($action));
            } else {
                dispatch((new SyncGoodsJobNew($shop, date('Y-m-d H:i:s')))->setAction($action));
            }
        }

        return $this->success();
    }

    /**
     * 获取同步商品的进度
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function getSyncGoodsSchedule(Request $request): JsonResponse
    {
        $shop_id = intval($request->input('shopId', $request->auth->shop_id));
        $redis = \redis('cache');
        $action = 'syncGoods';
        $key = "$action:sync_current:$shop_id";
        $order_sync_current = $redis->get($key);
        $key = "$action:sync_total:$shop_id";
        $order_sync_total = $redis->get($key);
        $key = "$action:sync_finish:$shop_id";
        $order_sync_finish = $redis->get($key);
        return $this->success([
            'sync_current' => $order_sync_current,
            'sync_total' => $order_sync_total,
            'sync_finish' => $order_sync_finish,
        ]);
    }

    /**
     * 获取同步商品的最后时间
     * @param Request $request
     * @return JsonResponse
     * <AUTHOR>
     */
    public function getSyncGoodsTime(Request $request)
    {
        $shop = Shop::find(intval($request->input('shopId', $request->auth->shop_id)));
        $res = [
            'sync_time_status' => $shop->last_goods_sync_at ? 1 : 0,
        ];
        return $this->success($res);
    }

    /**
     * 把平台的商品和本地配置的商品进行合并
     * @param $localGoods
     * @param array $platformGoods
     */
    public function mergePlatformAndLocalGoods(&$localGoods, array &$platformGoods)
    {
        $localGoodsKeyNumIid = $localGoods->keyBy('num_iid');
        //吧查询到的商品信息都循环一下，然后看看配置的商品信息，进行合并
        foreach ($platformGoods as &$goods) {
            $numIid = $goods['num_iid'];
            if ($localGoodsKeyNumIid->has($numIid)) {
                $localGoods = $localGoodsKeyNumIid->get($numIid);
                Log::info('localGoods', [$localGoods]);
                $goods['remark'] = $localGoods->remark;
                $goods['custom_title'] = $localGoods->custom_title;
                /**
                 * @var $localGoodsSkus Collection
                 */
                $localGoodsSkus = $localGoods->skus->keyBy('sku_id');
                foreach ($goods['skus'] as &$sku) {
                    $skuId = $sku['sku_id'];

                    if ($localGoodsSkus->has($skuId)) {
                        $sku['custom_sku_value'] = $localGoodsSkus->get($skuId)->custom_sku_value;
                    }

                }
            }

        }

    }


    /**
     * 批量编辑商品
     * @param Request $request
     * @return JsonResponse
     */
    public function batchEdit(Request $request)
    {
        $batchGoodsEditRequest = BatchGoodsEditRequest::ofArray($request->all());

        $numIids = $batchGoodsEditRequest->getNumIids();
        $shop = Shop::firstById($batchGoodsEditRequest->shopId);
        $platGoodService = GoodsServiceManager::newInstance($shop->access_token, $shop);
        $platGoods = $platGoodService->getGoodsListByGoodsId($numIids);
        if (sizeof($platGoods) == 0) {
            return $this->fail("商品不存在", ErrorConst::PARAM_ERROR_CODE);
        }
        $errors = $this->goodsService->batchEdit($batchGoodsEditRequest, $platGoods, $shop->user_id);
        if (sizeof($errors) > 0) {
            return $this->fail("编辑失败", ErrorConst::OPERATION_FAIL, [], $errors);
        } else {

            return $this->success();
        }


    }


}
