<?php

namespace App\Http\Controllers\Admin;

use App\Constants\PlatformConst;
use App\Http\Controllers\Controller;
use App\Models\Activity;
use App\Models\ApiAuth;
use App\Models\ApiShopBind;
use App\Models\FeedBack;
use App\Models\Fix\Order;
use App\Models\PlatformOrder;
use App\Models\Shop;
use App\Models\ShopExtra;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserExtra;
use App\Models\UserShopStatistic;
use App\Models\UserStatistic;
use App\Models\WaybillHistory;
use App\Services\BusinessException;
use App\Services\Order\OrderServiceManager;
use App\Services\OssService;
use App\Services\Shop\ShopStatisticService;
use Firebase\JWT\JWT;
use Illuminate\Database\Query\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class AdminController extends Controller
{
    /**
     * 商家列表
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function index(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), strval($request->auth->identifier)) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            "begin_at"  => 'date',
            "end_at"    => 'date',
            "stat_at"   => 'date',
            "keyword"   => 'string',
            "offset"    => 'int',
            "limit"     => 'int',
            "order_by"  => 'string',
            "open_live_mode"  => 'string',
        ]);
        $condition = [];
        if ($request->input('begin_at')) {
            $condition[] = ['shops.created_at', '>=', $request->input('begin_at')];
        }
        if ($request->input('end_at')) {
            $condition[] = ['shops.created_at', '<=', $request->input('end_at')];
        }

        $statAt    = $request->input('stat_at', date('Y-m-d'));
        $keyword   = $request->input('keyword', '');
        $orderByFlag   = $request->input('orderByFlag', '');
        $ascDescFlag   = $request->input('ascDescFlag', 'a');
        $offset    = (int)$request->input('offset', 0);
        $limit     = (int)$request->input('limit', 20);
        $orderBy   = $request->input('order_by', "today_print_count desc");
        $openLiveMode   = $request->input('open_live_mode');
        if ($orderByFlag && $ascDescFlag) {
            $ascDescFlag = $ascDescFlag == 'a' ? 'asc': 'desc';
            $orderBy = "$orderByFlag $ascDescFlag";
        }
        list($ret, $rowsFound) = ShopStatisticService::shopUsageStatisticsSearch($condition, $keyword, $offset, $limit, $orderBy, $statAt, $openLiveMode);

//        if ($keyword) {
//            $query = Shop::where($condition)->where('identifier', $keyword)
//                ->orWhere('name', 'like', '%' . $keyword . '%')
//                ->orWhere('shops.shop_name', 'like', '%' . $keyword . '%')
//                ->orWhere('shops.shop_code', $keyword)
//                ->orWhere('id', $keyword);
//        }else {
//            $query = Shop::query()->where($condition);
//        }
//        if (!is_null($openLiveMode)){
//            $query->whereHas('shopExtra',function ($query) use ($openLiveMode){
//                $query->where('open_live_mode', $openLiveMode);
//            });
//        }
//        $rowsFound = $query->count();

        foreach ($ret as $shop) {
            $shop->address_count          = 0;
            $shop->template_count         = 0;
            $shop->total_print_count      = 0;
            $shop->total_order_count      = 0;
            $shop->total_delivery_count   = 0;
            if ($shop->totalStatistic) {
                $shop->address_count        = $shop->totalStatistic->address_count;
                $shop->template_count       = $shop->totalStatistic->template_count;
                $shop->total_print_count    = $shop->totalStatistic->print_count;
                $shop->total_order_count    = $shop->totalStatistic->order_count;
                $shop->total_delivery_count = $shop->totalStatistic->delivery_count;
            }
            $shop->is_open_api              = 0;
            $res = ApiShopBind::query()->where('shop_id', $shop->id)->get()->toArray();
            if (!empty($res)) {
                $apiAuth = ApiAuth::query()->whereIn('app_id', array_column($res, 'app_id'))->get()->toArray();
                $shop->is_open_api          = 1;
                $shop->app_id               = implode(',', array_column($apiAuth, 'app_id'));
                $shop->app_name             = implode(',', array_column($apiAuth, 'name'));
            }
        }

	    $arr1    = collect($ret)->where('today_print_count', '<>', 0)->sortByDesc('today_print_count')->values()->all();
	    $arr2    = collect($ret)->where('today_print_count', 0)->sortByDesc('total_print_count')->values()->all();
	    $summArr = array_merge($arr1, $arr2);
	    $ret = collect($summArr)->values()->all();

        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    public function summary(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at"   => 'date',
            "offset"   => 'int',
            "limit"    => 'int',
            "order_by" => 'string',
        ]);
        $condition   = [];
        $condition[] = ['shop_id', '=', 0];
        if ($request->input('begin_at')) {
            $condition[] = ['stat_at', '>=', $request->input('begin_at')];
        }
        if ($request->input('end_at')) {
            $condition[] = ['stat_at', '<=', $request->input('end_at')];
        }
        $offset     = (int)$request->input('offset', 0);
        $limit      = (int)$request->input('limit', 20);
        $orderBy    = $request->input('order_by', "stat_at desc");
        $ret        = UserShopStatistic::search($condition, $offset, $limit, $orderBy);
        $rowsFound  = UserShopStatistic::where($condition)->count();
        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    public function userStatistics(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at"   => 'date',
            "offset"   => 'int',
            "limit"    => 'int',
            "order_by" => 'string',
        ]);
        $condition = [];
        if ($request->input('begin_at')) {
            $condition[] = ['stat_at', '>=', $request->input('begin_at')];
        }
        if ($request->input('end_at')) {
            $condition[] = ['stat_at', '<=', $request->input('end_at')];
        }
        $offset     = (int)$request->input('offset', 0);
        $limit      = (int)$request->input('limit', 20);
        $orderBy    = $request->input('order_by', "stat_at desc");
        $ret        = UserStatistic::search($condition, $offset, $limit, $orderBy);
        $rowsFound  = UserStatistic::where($condition)->count();
        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    /**
     * 获取登录token
     * @param Request $request
     * @return JsonResponse
     * @throws BusinessException
     */
    public function loginToken(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            "shop_id" => 'required|int',
            "user_id" => 'int',
        ]);
        $shop = Shop::findOrFail($request->input('shop_id'));
        if (!$shop) {
            return $this->fail('用户不存在');
        }
        $payload = [
            'iss'           => 'lumen-jwt',
            'sub'           => !empty($inviter) ? $inviter : $shop->user_id,
            'iat'           => time(),
            'shop_id'       => $shop->id,
            'identifier'    => $shop->identifier,
            'plaftorm_type' => $shop->type,
            'shop_name' => $shop->name,
            'exp'           => time() + env('JWT_EXPIRATION_TIME')
        ];
        $token = JWT::encode($payload, env('JWT_SECRET'));

        return $this->success(['token' => $token]);
    }

    public function outerUserList(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at"   => 'date',
            "offset"   => 'int',
            "limit"    => 'int',
            "order_by" => 'string',
        ]);
        $condition = [];
        if ($request->input('begin_at')) {
            $condition[] = ['created_at', '>=', $request->input('begin_at')];
        }
        if ($request->input('end_at')) {
            $condition[] = ['created_at', '<=', $request->input('end_at')];
        }
        $offset     = (int)$request->input('offset', 0);
        $limit      = (int)$request->input('limit', 20);
        $ret        = ApiAuth::search($condition, $offset, $limit);
        $rowsFound  = ApiAuth::where($condition)->count();
        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    public function createOuterUser(Request $request) {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            'name'  => 'required|string',
            'phone' => 'required|string',
            'desc' => 'string',
            'ip_whitelist' => 'string',
            'request_freq_limit' => 'integer',
            'bind_waybill_shop_limit' => 'required|integer',
        ]);
        $name = $request->input('name');
        $phone = $request->input('phone');
        $shopIdentifier = $request->input('shop_identifier', '');
        $desc = $request->input('desc', '');
        $ipWhitelist = $request->input('ip_whitelist', '');
        $requestFreqLimit = $request->input('request_freq_limit', 0);
        $bindWaybillShopLimit = $request->input('bind_waybill_shop_limit', 50);
        // 替换中文逗号、换行成英文逗号
        $ipWhitelist = str_replace(['，',"\r\n", "\n", "\r"], ',', $ipWhitelist);
        if (!empty($shopIdentifier)) {
//            $shop = Shop::query()->where('identifier', $shopIdentifier)->first();
            $shop = Shop::firstByIdentifier($shopIdentifier);
            if (empty($shop)) {
                return $this->fail('未查询到店铺');
            }
        } else {
            $shopIdentifier = time().rand(1000,9999);
            // 手机号是否已绑定店铺
            $user = User::query()->where('phone', $phone)->first();
            if ($user) {
                return $this->fail('该手机'.$phone.'已绑定店铺，请换个手机号');
            }

            //创建假用户
            $user = User::query()->create([
                'phone' => $phone,
                'nickname' => $name,
                'password' => Hash::make('123456'),
                'invite_code' => getRandStr(16),
            ]);
            if (!$user) {
                return $this->fail('创建用户失败');
            }

            // 创建假店铺
            $shop = Shop::query()->create([
                'user_id' => $user->id,
                'type'    => Shop::PLATFORM_TYPE_MAP[config('app.platform', 'ks')],
                'identifier' => $shopIdentifier,
                'auth_at'    => date('Y-m-d H:i:s',time()),
                'auth_status' => Shop::AUTH_STATUS_SUCCESS,
                'login_count' => 0,
                'shop_name'   => $name,
                'shop_identifier' => $shopIdentifier,
                'name'            => $name,
                'sync_switch'     => 0,
                'created_at'      => date('Y-m-d H:i:s',time()),
                'updated_at'      => date('Y-m-d H:i:s',time())
            ]);
            if (!$shop) {
                return $this->fail('创建店铺失败');
            }
        }

        // 生成appid、appkey
        $app_id = (time()) . rand(1000, 9999);
        $app_key = md5(uniqid('api', true));
        $bool = ApiAuth::query()->create([
            'name'       => $name,
            'app_id'     => $app_id,
            'app_key'    => $app_key,
            'desc'       => $desc,
            'shop_identifier' => $shopIdentifier,
            'request_freq_limit' => $requestFreqLimit,
            'ip_whitelist' => $ipWhitelist,
            'bind_waybill_shop_limit' => $bindWaybillShopLimit,
        ]);
        if (!$bool) {
            return $this->fail('创建失败');
        }

        return $this->success();
    }

    public function deleteOuterUser(Request $request, $id) {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $delete = ApiAuth::where('id', $id)->delete();
        if (!$delete) {
            throw new BusinessException('操作失败！');
        }

        return $this->success();
    }

    public function editOuterUser(Request $request) {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }

        $validate = $this->validate($request, [
            "id" => 'required|int',
            "desc" => 'string',
            'ip_whitelist' => 'string',
            'request_freq_limit' => 'integer',
            'bind_waybill_shop_limit' => 'integer',
        ]);

        $id = $request->input('id');
        $desc = $request->input('desc');
        $validate['ip_whitelist'] = str_replace(['，',"\r\n", "\n", "\r"], ',', $validate['ip_whitelist']);

        $apiInfo = ApiAuth::query()->where('id', $id)->first();
        if (!empty($apiInfo)) {
            $apiInfo->desc = $desc;
            $ret = ApiAuth::query()->where('id', $validate['id'])->update($validate);
            if ($ret) {
                return $this->success();
            }
        }

        return $this->fail('编辑失败');

    }

    public function getUserFeedBack(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), $request->auth->identifier) === false) {
            return $this->fail('权限不够', 401);
        }
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at"   => 'date',
            "offset"   => 'int',
            "limit"    => 'int',
            "order_by" => 'string',
        ]);
        $condition = [];
        if ($request->input('begin_at')) {
            $condition[] = ['created_at', '>=', $request->input('begin_at')];
        }
        if ($request->input('end_at')) {
            $condition[] = ['created_at', '<=', $request->input('end_at')];
        }
        $offset     = (int)$request->input('offset', 0);
        $limit      = (int)$request->input('limit', 20);
        $search      = (int)$request->input('search', '');
        $orderBy    = $request->input('order_by', "created_at desc");
        $ret        = FeedBack::search($condition, $search, $offset, $limit, $orderBy);
        $rowsFound  = FeedBack::where($condition)->count();
        $pagination = [
            'rows_found' => $rowsFound,
            'offset'     => $offset,
            'limit'      => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    public function noticeConfig(Request $request)
    {
        $res = [];
        $noticeConfig = SystemConfig::query()
            ->where('key', SystemConfig::KEY_NOTICE_CONFIG)
            ->first();
        if (!empty($noticeConfig)) {
            $res = json_decode($noticeConfig->value, true);
        }

        return $this->success($res);
    }

    public function editNotice(Request $request)
    {
        $data = [
            'contentStr' => $request->input('contentStr', ''),
            'notice' => $request->input('notice', ''),
            'switch' => $request->input('switch', 0)
        ];
        $res = SystemConfig::query()->updateOrCreate(['key'=>SystemConfig::KEY_NOTICE_CONFIG],[
            'name' => '首页公告',
            'value' => json_encode($data)
        ]);
        if (!$res) {
            return $this->fail();
        }
        return $this->success();
    }

    public function getShop(Request $request)
    {
        $search = $request->input('search', '');
        if (empty($search)) {
            return $this->success();
        }
        /**
         * @var Builder $builder
         */
        $builder = Shop::query()->where(function ($query) use ($search) {
            $query->where('shop_name', 'like', '%' . $search . '%')
                ->orWhere('identifier', $search)
                ->orWhere('shop_code', $search)
                ->orWhere('id', $search);
        });
//        \Log::info("查询店铺sql:",[$builder->toSql(),$builder->getBindings()]);
        $shopList = $builder->get()->toArray();


        $result = [];
        foreach ($shopList as $shop) {
            $result[] = [
                'shopName'        => $shop['shop_name'],
                'userId'          => $shop['user_id'],
                'shopIdentifier'         => $shop['identifier'],
                'shopId'         => $shop['id'],
                'shopCode'       => $shop['shop_code'],
            ];
        }
        return $this->success($result);
    }

    public function getOrder(Request $request)
    {
        $search = $request->input('search', '');

        if (empty($search)) {
            return $this->success();
        }

        $orderNoList = explode(',', $search);
        $orderList = Order::query()->with(['shop','orderCipherInfo'])->whereIn('tid', $orderNoList)->get()->toArray();
        $inDbOrderNoList = array_column($orderList,'tid');
        $notInDbOrderNoList = [];
        forEach($orderNoList as $item) {
            if (!in_array($item, $inDbOrderNoList)) {
                array_push($notInDbOrderNoList,$item);
            }
        }
        $order2WaybillCodeMap = [];
        if (!empty($notInDbOrderNoList)) {
            //查询运单号
            $waybillHistory = WaybillHistory::query()
                -> whereIn('waybill_code',$notInDbOrderNoList)
                -> where( 'waybill_status',WaybillHistory::WAYBILL_RECOVERY_NO)
                ->get()->toArray();
            if (!empty($waybillHistory)) {
                //获取order
                $orders = array_column($waybillHistory, 'order_no');
                $list = Order::query()->with(['shop','orderCipherInfo'])->whereIn('tid', $orders)->get()->toArray();
                if (!empty($list)) {
                    $orderList = array_merge($orderList,$list);
                }
                $order2WaybillCodeMap = array_column($waybillHistory,'waybill_code','order_no');
            }
        }
        $result = [];
        foreach ($orderList as $order) {
            $giftName = '';
            //查询礼品网关系
            $apiShopBind = ApiShopBind::query()->where(['shop_id' => $order['shop_id']])->get()->toArray();
            if (!empty($apiShopBind)) {
                $apiAuthInfo = ApiAuth::query()->whereIn('app_id', array_column($apiShopBind, 'app_id'))->get()->toArray();
                if (!empty($apiAuthInfo)) {
                    $giftName = implode(",", array_column($apiAuthInfo, 'name'));
                }
            }

            //查询运单号
            $waybillCode = '';
            if (array_key_exists($order['tid'], $order2WaybillCodeMap)) {
                $waybillCode = $order2WaybillCodeMap[$order['tid']];
            } else {
                $waybillHistory = WaybillHistory::query()->where(['order_no'=>$order['tid'], 'waybill_status'=>WaybillHistory::WAYBILL_RECOVERY_NO])->get()->toArray();
                if (!empty($waybillHistory)) {
                    $waybillCode = implode(",", array_column($waybillHistory, 'waybill_code'));
                }
            }


             $result[] = [
                'orderNo'         => $order['tid'],
                'province'        => $order['receiver_state'],
                'city'            => $order['receiver_city'],
                'town'            => $order['receiver_district'],
                'receiverName'    => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                ? (!empty($order['OrderCipherInfo']) ? $order['OrderCipherInfo']['receiver_name_mask'] : $order['receiver_name']) : $order['receiver_name'],
                'receiverPhone'   => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                    ? (!empty($order['OrderCipherInfo']) ? $order['OrderCipherInfo']['receiver_phone_mask'] : $order['receiver_phone']) : $order['receiver_phone'],
                'receiverAddress' => in_array(config('app.platform'), [PlatformConst::DY, PlatformConst::JD, PlatformConst::KS])
                    ? (!empty($order['OrderCipherInfo']) ? $order['OrderCipherInfo']['receiver_address_mask'] : $order['receiver_address']) : $order['receiver_address'],
                'shopId'          => $order['shop_id'],
                'mallName'        => $order['shop']['name'],
                'identifier'      =>$order['shop']['identifier'],
                'shopName'        => $order['shop']['shop_name'],
                'waybillCode'     => $waybillCode,
                'giftName'        => $giftName,
                'userId'          => $order['user_id'],
                 'orderStatus'     => $order['order_status'],
                 'refundStatus'    => $order['refund_status'],
                 'printStatus'     => $order['print_status'],
            ];
        }

        return $this->success($result);
    }

    public function otherPlatformVas(Request $request)
    {
        $this->validate($request, [
            "start" => 'required|date',
            "end"   => 'required|date'
        ]);


        $appId = $request->input('appkey', '');
        if ($appId) {
            $apiAuth = ApiAuth::query()->where('app_id', $appId)->get();
        } else {
            $apiAuth = ApiAuth::query()->get();
        }

        $result = [];
        foreach ($apiAuth as $appInfo) {
            $amount = 0;
            //绑定关系的店铺id列表
            $shopBindArr = ApiShopBind::query()->where('app_id', $appInfo['app_id'])->get();
            if (!empty($shopBindArr)) {
                //统计金额 单位：元
                $condition = [
                    ['order_created_at', '>=', $request->input('start')],
                    ['order_created_at', '<=', $request->input('end')],
                ];
                $platformOrder = PlatformOrder::query()->where($condition)->whereIn('shop_id', collect($shopBindArr)->pluck('shop_id'))->get();
                $amount = collect($platformOrder)->sum('pay_fee');
            }

            $result[] = [
                'appKey' => $appInfo['app_id'],
                'name'   => $appInfo['name'],
                'amount' => $amount / 100
            ];
        }

        return $this->success($result);
    }

//    public function platformOrderList(Request $request)
//    {
//        $this->validate($request, [
//            "shop_id" => 'array',
//            "offset"   => 'int',
//            "limit"    => 'int',
//        ]);
//        $condition = [];
//        $shopId = $request->input('shop_id', '');
//        $offset = $request->input('offset', 0);
//        $limit  = $request->input('limit', 20);
//
//        $query = PlatformOrder::query();
//        if (!empty($shopId)) {
//            $query->whereIn('shop_id', $shopId);
//        }
//        $list = $query->with('shop:id,shop_name,bindApiAuth:name')->where($condition)->limit($limit)->offset($offset)->orderBy('id','desc')->get();
//        return $this->success($list);
//    }
//
    public function updatePlatformOrder(Request $request)
    {
        $this->validate($request, [
            "id" => 'required|int',
            "sku_spec" => ['required','string',Rule::in(UserExtra::VERSION_MAP_ARR)],
        ]);
        $id = $request->input('id', 0);
        $skuSpec = $request->input('sku_spec', '');
        $platformOrder = PlatformOrder::query()->where('id', $id)->firstOrFail();
        $platformOrder->sku_spec = $skuSpec;
        $platformOrder->save();

        $shop = Shop::query()->where('id',$platformOrder->shop_id)->firstOrFail();
        $orderService  = OrderServiceManager::create();
        $orderService->setShop($shop);
        $orderService->saveUserEdition();
        return $this->success();
    }

    /**
     * 更新店铺同步开关
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function syncSwitch(Request $request): JsonResponse
    {
        $this->validate($request, [
            "shop_id" => 'required|int',
            "sync_switch" => 'required|int',
        ]);
        $shopId = $request->input('shop_id', 0);
        $syncSwitch = $request->input('sync_switch', 0);
        $shop = Shop::query()->where('id',$shopId)->firstOrFail();
        $shop->sync_switch = $syncSwitch;
        $shop->save();
        return $this->success();
    }

    /**
     * 获取系统配置
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function getSysConfig(Request $request): JsonResponse
    {
        $this->validate($request, [
            "key" => 'required|string',
        ]);
        $key = (string)$request->input('key', '');
        $info = SystemConfig::firstByKey($key);
        return $this->success($info);
    }
    /**
     * 更新系统配置
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function updateSysConfig(Request $request): JsonResponse
    {
        $this->validate($request, [
            "key" => 'required|string',
            "value" => 'required|string',
        ]);
        $key = $request->input('key', '');
        $value = $request->input('value', '');
        $model = SystemConfig::setValue($key, $value);
        return $this->success($model);
    }

    public function ossUpload(Request $request)
    {
        $this->validate($request, [
            "file" => 'required|file',
        ]);
        $uploadedFile = $request->file('file');
        $ossService = new OssService();
        $url = $ossService->upload($uploadedFile->getClientOriginalName(), $uploadedFile->getRealPath());
        return $this->success([
            'url' => $url
        ]);
    }

    /**
     * 编辑店铺扩展
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function editShopExtra(Request $request)
    {
        if (strpos(env('ADMIN_ROLES_IDENTIFIER'), strval($request->auth->identifier)) === false) {
            return $this->fail('权限不够', 401);
        }
        $data1 = $this->validate($request, [
            'id' => 'required|int',
        ]);
        $data2 = $this->validate($request, [
            'open_live_mode'      => 'int',
            'live_mode_expire_at' => 'date',
        ]);
        $shopId =  $data1['id'];

        $res = ShopExtra::query()->updateOrCreate([
            'shop_id' => $shopId,
        ], $data2);

        return $this->success($res);
    }

}
