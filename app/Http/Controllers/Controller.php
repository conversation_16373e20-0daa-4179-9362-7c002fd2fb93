<?php

namespace App\Http\Controllers;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Models\Shop;
use App\Models\ShopBind;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Laravel\Lumen\Routing\Controller as BaseController;

class Controller extends BaseController
{
    /**
     * @return string
     */
    public function getAppId()
    {
        return \request('appId');
    }

    /**
     * 获取OwnerId对应的店铺id
     * 1. 如果有ownerIdList，则根据ownerIdList获取店铺id
     * 2. 如果没有ownerIdList，则根据当前店铺id获取所有绑定的店铺id
     * @param Request $request
     * @return array
     */
    public function getOwnerIdList(Request $request): array
    {

        $currentShopId = $request->auth->shop_id;
        $ownerIdList = $request->input('ownerIdList', []);
        if (empty($ownerIdList)) {
            $ownerIdList=array_column(ShopBind::getAllRelationShop($currentShopId),'identifier');
        }
        return $ownerIdList;
    }



    /**
     * 获取请求参数shopIdList
     * @param Request $request
     * @return array
     */
    public function getShopIdList(Request $request): array
    {

        $shopIdList = $request->input('shopIdList');
        if (!empty($shopIdList)) {
            return $shopIdList;
        } else {
//            $currentShopId = $request->auth->shop_id;
//            return ShopBind::getAllRelationShopIds($currentShopId, false, [ShopBind::TYPE_MY_SHOP, ShopBind::TYPE_ME_BIND]);
            return ShopBind::getAllRelationShopIds($request->auth->shop_id);
        }
    }

    protected function hidePrivacyItem(&$datum)
	{
		$columns = [
			'receiver_name' => [2, 3],
			'receiver_phone' => [4, 5, 6, 7],
			'receiver_address' => [3, 4, 7, 9,  11, 13, 15, 17, 19, 21, 22, 24, 26, 28, 29],
			//'buyer_nick' => [2, 3, 5, 7, 8, 10, 12, 15, 17, 18, 20]
		];

		foreach ($columns as $column => $rule) {
			if (isset($datum[$column])) {
				$datum[$column] = dataDesensitizationOpt($datum[$column], $rule);
			}
		}

		return $datum;
	}

	protected function hidePrivacy($data)
	{
		if (is_array($data)) {
			foreach ($data as &$datum) {
			    if (ksEncryptSwitch($datum['shop_id']) && isset($datum['order_cipher_info'])) {
                    $datum['receiver_name'] = $datum['order_cipher_info']['receiver_name_mask'];
                    $datum['receiver_phone'] = $datum['order_cipher_info']['receiver_phone_mask'];
                    $datum['receiver_address'] = $datum['order_cipher_info']['receiver_address_mask'];
                } else {
                    $this->hidePrivacyItem($datum);
                }
			}
		}
		return $data;
	}


    /**
     * @param mixed $data
     * @param string $message
     * @param int $statusCode
     * @param array $headers
     * @return JsonResponse
     */
    protected function success( $data = [], string $message = 'success', int $statusCode = 200, array $headers = []): JsonResponse
    {
        return $this->output($data,$message,$statusCode,$headers);
    }


    /**
     * 输出结果
     * @param array $data
     * @param string $message
     * @param int $statusCode
     * @param array $headers
     * @return JsonResponse
     */
    protected function output($data = [], $message = 'success', int $statusCode = 200, array $headers = []): JsonResponse
    {
        $pageData = [];
        $hidePrivacy = false;
        if ($data && is_array($data) && isset($data['pagination'])) {
            $pageData = array_pull($data, 'pagination');
	        $hidePrivacy = array_pull($data, 'hidePrivacy');
            $data     = array_get($data, '0');
        }
        if ($hidePrivacy) {
	        $data = collect($data)->toArray();
	        $data = $this->hidePrivacy($data);
        }
        $result = [
            'meta' => [
                'req_id'    => REQ_ID,
                'code'    => $statusCode,
                'message' => $message,
            ],
            'pagination' => null,
            'data' => $data,
        ];
        if ($pageData) {
            $result = array_merge($result, ['pagination' => $pageData]);
        }else{
            unset($result['pagination']);
        }
        return response()->json($result, 200, [], JSON_UNESCAPED_UNICODE)->withHeaders($headers);
    }

    /**
     * @param string $message
     * @param int $code
     * @param array $headers
     * @param array $data
     * @return JsonResponse
     */
    protected function fail($message = '', int $code = 500, array $headers = [],$data=[])
    {
//        $result = [
//            'meta' => [
//                'req_id'  => REQ_ID,
//                'code'    => $code,
//                'message' => $message,
//            ]
//        ];
//        return response()->json($result, $code)->withHeaders($headers);
        return $this->output($data,$message,$code,$headers);
    }

    public function validate(Request $request, array $rules, array $messages = [], array $customAttributes = [])
    {
        $validator = $this->getValidationFactory()->make($request->all(), $rules, $messages, $customAttributes);

        if ($validator->fails()) {
            $this->throwValidationException($request, $validator);
        }
        $params = $this->extractInputFromRules($request, $rules);
        if (array_get($params, 'offset')) {
            $params['offset'] = (int)array_get($params, 'offset');
        }
        if (array_get($params, 'limit')) {
            $params['limit'] = (int)array_get($params, 'limit');
        }

        return $params;
    }

    public function validateCallback(Request $request, $rules=[],\Closure $callback = null){
        $v = Validator::make($request->input(), $rules);
        if (!empty($callback)) {
            $callback($v);
        }
        if ($v->fails()) {
            $this->throwValidationException($request, $v);
        }
    }

    /**
     * @param array $data
     * @param array $rules
     * @param array $messages
     * @param array $customAttributes
     * @return \void
     */
    public function validateData($data, array $rules, array $messages = [], array $customAttributes = [])
    {
        $validator = \Validator::make($data, $rules, $messages, $customAttributes);
        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Get the request input based on the given validation rules.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  array $rules
     * @return array
     */
    protected function extractInputFromRules(Request $request, array $rules)
    {
        return $request->only(collect($rules)->keys()->map(function ($rule) {
            return Str::contains($rule, '.') ? explode('.', $rule)[0] : $rule;
        })->unique()->toArray());
    }

    protected function extractOffsetLimit(Request $request)
    {
        $offset = max((int)$request->get('offset', 0), 0); // 最少0
        $limit  = max((int)$request->get('limit', 10), 1); // 默认10,最小1

        $arr = [
            'offset' => $offset,
            'limit'  => min($limit, 100), // 最大100
        ];

        return $arr;
    }

    protected function successForOpenApi($data = [], $message = '', int $statusCode = 200, array $headers = [])
    {
        $result = [
            'meta' => [
                'req_id'  => REQ_ID,
                'code'    => $statusCode,
                'message' => $message,
            ],
            'data' => $data,
        ];
        return response()->json($result, 200)->withHeaders($headers);
    }
}
