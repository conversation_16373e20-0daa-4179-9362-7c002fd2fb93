<?php

namespace App\Http\Controllers;

use App\Constants\WxNotifyType;
use App\Jobs\WaybillHistory\DeleteWaybillHistoryJob;
use App\Jobs\WxNsg\WxOrderTraceUpdateJob;
use App\Jobs\WxNsg\WxSaveOrUpdateOrderMessageJob;
use App\Services\WxEncrypt\WxBizMsgCrypt;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Laravel\Lumen\Http\ResponseFactory;

class PublicController extends Controller
{
    /**
     * 消息事件接收处理
     * @param Request $request
     * @return Response|ResponseFactory
     */
	public function getTicket(Request $request)
	{
//		Log::info('getTicket:all query params', [$request->input()]);
		$xmlMsg = file_get_contents('php://input');
		Log::info('getTicket:xml_msg', [$xmlMsg]);

		$timeStamp   = !empty($request->input('timestamp')) ? $request->input('timestamp') : '';
		$nonce       = !empty($request->input('nonce')) ? $request->input('nonce') : '';
		$encryptType = !empty($request->input('encrypt_type')) ? $request->input('encrypt_type') : '';
		$msgSign     = !empty($request->input('msg_signature')) ? $request->input('msg_signature') : '';

		$xmlData = $this->decryptMsg($timeStamp, $nonce, $encryptType, $msgSign, $xmlMsg);
//        Log::info('$xmlData',[$xmlData,$xmlMsg]);
		$xml     = new \DOMDocument();
		$xml->loadXML($xmlData);
		$array_e  = $xml->getElementsByTagName('InfoType');
		$infoType = $array_e->item(0)->nodeValue;

		//消息处理
		switch ($infoType) {
			case WxNotifyType::COMPONENT_VERIFY_TICKET :
				$array_e               = $xml->getElementsByTagName('ComponentVerifyTicket');
				$componentVerifyTicket = $array_e->item(0)->nodeValue;
				Log::info('component_verify_ticket', [$componentVerifyTicket]);
				if ($componentVerifyTicket) {
					$redis    = redis('cache');
					$redisKey = 'component_verify_ticket';
					$redis->setex($redisKey, 30 * 60, $componentVerifyTicket);
				}
				break;

			case WxNotifyType::AUTHORIZED:
				$array_e           = $xml->getElementsByTagName('AuthorizationCode');
				$authorizationCode = $array_e->item(0)->nodeValue;
				Log::info('authorized', [$authorizationCode]);
				break;

			case WxNotifyType::UPDATE_AUTHORIZED:
				$array_e           = $xml->getElementsByTagName('AuthorizationCode');
				$authorizationCode = $array_e->item(0)->nodeValue;
				Log::info('updateauthorized', [$authorizationCode]);
				break;
            case WxNotifyType::CHANNELS_EC_ORDER_EXT_INFO_UPDATE:
                $array_e           = $xml->getElementsByTagName('AuthorizerAppid');
                $authorizerAppid = $array_e->item(0)->nodeValue;
                Log::info('unauthorized', [$authorizerAppid]);
                break;
			case WxNotifyType::UN_AUTHORIZED:
                // 这块代码有问题，暂时不用
//				$array_e           = $xml->getElementsByTagName('AuthorizationCode');
//				$authorizationCode = $array_e->item(0)->nodeValue;
//				Log::info('unauthorized', [$authorizationCode]);
				break;
			default:
//				echo 'failed';
                return response('success');
				break;
		}

//		echo "success";
        return response('success');
	}

    /**
     * 微信更新订单通知
     * @param Request $request
     * @return Response|ResponseFactory
     */
    public function wxOrderNotify(Request $request){
//        Log::info('all query params', [$request->input()]);
        $xmlMsg = file_get_contents('php://input');
//        Log::info('xml_msg', [$xmlMsg]);

        $timeStamp   = !empty($request->input('timestamp')) ? $request->input('timestamp') : '';
        $nonce       = !empty($request->input('nonce')) ? $request->input('nonce') : '';
        $encryptType = !empty($request->input('encrypt_type')) ? $request->input('encrypt_type') : '';
        $msgSign     = !empty($request->input('msg_signature')) ? $request->input('msg_signature') : '';

        $xmlData = $this->decryptMsg($timeStamp, $nonce, $encryptType, $msgSign, $xmlMsg);
        $xml     = new \DOMDocument();
        $xml->loadXML($xmlData);
        $event = $xml->getElementsByTagName('Event');
        if($event->item(0)->nodeValue == WxNotifyType::CHANNELS_EC_ORDER_EXT_INFO_UPDATE){
            $array_e  = $xml->getElementsByTagName('OrderId');
            $orderId = $array_e->item(0)->nodeValue;
            Log::info('update_order', [$orderId]);
        }
//        echo "success";
        return response('success');
    }


    public function wxNotify(Request $request,$appId=null){
        $xmlMsg = file_get_contents('php://input');
        try {
            $timeStamp   = !empty($request->input('timestamp')) ? $request->input('timestamp') : '';
            $nonce       = !empty($request->input('nonce')) ? $request->input('nonce') : '';
            $encryptType = !empty($request->input('encrypt_type')) ? $request->input('encrypt_type') : '';
            $msgSign     = !empty($request->input('msg_signature')) ? $request->input('msg_signature') : '';
            $xmlData = $this->decryptMsg($timeStamp, $nonce, $encryptType, $msgSign, $xmlMsg);
            $xml     = new \DOMDocument();
            $xml->loadXML($xmlData);
//        Log::info('wxNotify::xml', [$xml]);
            $event = $xml->getElementsByTagName('Event');
            $eventItem = $event->item(0);
            if(!empty($eventItem) && $eventItem->nodeValue == WxNotifyType::CHANNELS_EC_ORDER_EXT_INFO_UPDATE){
                (new WxSaveOrUpdateOrderMessageJob($xmlData))->handle();
            }
            if(!empty($eventItem) && $eventItem->nodeValue == WxNotifyType::EWAYBILL_PUSH_PATH){
                (new WxOrderTraceUpdateJob($xmlData))->handle();
            }
        }catch (\Exception $e){
            Log::error('wxNotify::error', [$e->getMessage(),$e->getTraceAsString(),$xmlMsg]);
        }

//        echo "success";
        return response('success');
    }

	/**
	 * 消息解密
	 * @param $timeStamp
	 * @param $nonce
	 * @param $encrypt_type
	 * @param $msg_sign
	 * @param $encryptMsg
	 * @return bool|string
	 */
	public function decryptMsg($timeStamp, $nonce, $encrypt_type, $msg_sign, $encryptMsg)
	{
		$encodingAesKey = env('WX_MSG_KEY', 'VrjqE3SSIJEbcCE8RUl1VKJ4lfxakpO0pbE8eKML09p');
		$token          = env('WX_MSG_TOKEN', 'a49d02b525e7e6c088ce27d911239145');
		$appId          = env('WX_CLIENT_ID', 'wxd1d763ebbfd0f174');

		$pc       = new WxBizMsgCrypt($token, $encodingAesKey, $appId);
		$xml_tree = new \DOMDocument();
		$xml_tree->loadXML($encryptMsg);
		$array_e = $xml_tree->getElementsByTagName('Encrypt');
		$encrypt = $array_e->item(0)->nodeValue;

		$format   = "<xml><ToUserName><![CDATA[toUser]]></ToUserName><Encrypt><![CDATA[%s]]></Encrypt></xml>";
		$from_xml = sprintf($format, $encrypt);

		$msg     = '';
		$errCode = $pc->decryptMsg($msg_sign, $timeStamp, $nonce, $from_xml, $msg);
		if ($errCode == 0) {
//			Log::info('all_decode_xml_data', [$msg]);
			return $msg;
		}
		else {
			Log::error('errCode', [$errCode]);
			return false;
		}
	}
}
