<?php

namespace App\Http\Controllers;

use App\Constants\OperationLogTypeConst;
use App\Events\Orders\OrderFlagEvent;
use App\Events\Orders\OrderPrintEvent;
use App\Models\OperationLog;
use App\Models\Order;
use App\Models\Shop;
use App\Models\User;
use App\Services\Order\OrderServiceManager;
use Illuminate\Http\Request;

class CustomizationController extends Controller
{
    public function orderList(Request $request)
    {
        $this->validate($request, [
            'order_no'   => 'required|string',
            'shop_id'    => 'required'
        ]);

        $shopId = $request->input('shop_id');
        $orderNoStr = $request->input('order_no');
        $orderNoList = explode(',', $orderNoStr);

        //直接去拉取订单
        $shop = Shop::query()->where('id', $shopId)->first();
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($shop->user_id);
        $orderService->setShop($shop);
        $orderListArr = [];
        foreach ($orderNoList as $key => $orderNo) {
            $orderInfo = $orderService->getOrderInfo($orderNo);
            if (!empty($orderInfo)) {
                $orderInfo['shop_id'] = $shop->id;
                $orderInfo['user_id'] = $shop->user_id;
                $orderListArr[] = $orderInfo;
            }
        }

        //拉取到订单保存一下
        if (!empty($orderListArr)) {
            Order::batchSave($orderListArr, $shop->user_id, $shop->id);
        }

        $pagination = [
            'rows_found' => count($orderListArr),
        ];
        return $this->success(['pagination' => $pagination, $orderListArr]);
    }

    public function addFlag(Request $request)
    {
        $this->validate($request, [
            'flag'   => 'required|string',
            'order_no'   => 'required|string',
        ]);
        $flag = $request->input('flag');
        $orderNoStr = $request->input('order_no');
        $sellerMemo = $request->input('seller_memo');
        $orderNoList = explode(',', $orderNoStr);

        $failedData = $orderList = [];
        foreach ($orderNoList as $orderNo) {
            $order = Order::query()->where('tid', $orderNo)->first();
            $orderService = OrderServiceManager::create();
            $orderService->setUserId($order->user_id);
            $orderService->setShop($order->shop);

            //请求平台修改留言备注
            $res = $orderService->sendEditSellerRemark($order->tid, $flag, $sellerMemo);
            if (!$res) {
                $failedData[] = $orderNo;
            } else {
                //标记成功修改库中标记颜色
                Order::query()->where('tid', $orderNo)->update(['seller_flag'=>$flag, 'seller_memo'=>json_encode([$sellerMemo])]);
                $order->old_flag = $order->seller_flag;
                $order->new_flag = $flag;
                $order->new_seller_memo = $sellerMemo;
                $orderList[] = $order;
            }
        }
        $user = User::query()->where('id', $request->auth->user_id)->first();
        $shop = Shop::query()->where('id', $request->auth->shop_id)->first();
        event((new OrderFlagEvent($user, $shop, time(), $orderList))->setClientInfoByRequest($request));
        return $this->success(['failed' => $failedData]);
    }

    public function shopList(Request $request)
    {
        [$shopIdList, $shopAddTime] = $this->getCashShopList();
        $shopList = Shop::query()->whereIn('id', $shopIdList)->get();

        $newList = [];
        foreach ($shopList as $item) {
            $item['add_at'] = $shopAddTime[$item['id']];
            $newList[] = $item;
        }

        $pagination = [
            'rows_found' => count($shopList),
        ];
        return $this->success(['pagination' => $pagination, $newList]);
    }

    public function addShop(Request $request)
    {
        $this->validate($request, [
            'shop_code'   => 'required|string',
        ]);
        $shopCode = $request->input('shop_code');

        $shop = Shop::query()->where('shop_code', $shopCode)->first();
        if (empty($shop)) {
            return $this->success([], '未查询到此店铺，请确认该店铺是否订购授权进入过软件', 1001);
        }
        //获取缓存店铺列表
        [$shopIdList, $shopAddTime] = $this->getCashShopList();

        if (!in_array($shop->id, $shopIdList)) {
            file_put_contents(storage_path()."/app/shopId.txt", $shop->id.','.date('Y-m-d H:i:s').PHP_EOL, FILE_APPEND);
        }
        return $this->success([$shop->shop_name ?? '']);
    }

    public function delShop(Request $request)
    {
        //获取缓存店铺列表
        $id = $request->input('id');
        [$shopIdList, $shopAddTime] = $this->getCashShopList();
        //先清空
        file_put_contents(storage_path()."/app/shopId.txt", '');
        foreach ($shopIdList as $k=>$v){
            if ($id == $v){
                continue;
            }
            file_put_contents(storage_path()."/app/shopId.txt", $v.','.$shopAddTime[$v], FILE_APPEND);
        }

        return $this->success();
    }

    public function logList(Request $request)
    {
        $this->validate($request, [
            'order_no'   => 'required|string',
        ]);
        $orderNo = $request->input('order_no');
        $logList = OperationLog::query()->where(['type'=>OperationLogTypeConst::ORDER_FLAG,'tid'=>$orderNo.'A'])->get();

        foreach ($logList as $key => $item) {
            preg_match('/由：([A-Z]+)，更改为：([A-Z]+)/', $item['remark'], $ch);
            $item['old_flag'] = $ch[1];
            $item['new_flag'] = $ch[2];

            $logList[$key]['old_flag'] = $ch[1];
            $logList[$key]['new_flag'] = $ch[2];

            preg_match('/修改备注为：(.*)/', $item['remark'], $ch);
            $logList[$key]['remark'] = $ch[1] ?? "";
        }

        $pagination = [
            'rows_found' => count($logList),
        ];
        return $this->success(['pagination' => $pagination, $logList]);

        return $this->success();
    }

    private function getCashShopList()
    {
//        $file = fopen("/tmp/shopId.txt", "r");
        $file = fopen(storage_path()."/app/shopId.txt", "r");
        $shopIdList = $shopAddTime = [];
        while(!feof($file))
        {
            //fgets() Read row by row
            $contens = fgets($file);
            if ($contens) {
                $contensArr = explode(',', $contens);
                $shopIdList[] = $contensArr[0];
                $shopAddTime[$contensArr[0]] = $contensArr[1];
            }
        }
        fclose($file);

        return [$shopIdList, $shopAddTime];
    }
}
