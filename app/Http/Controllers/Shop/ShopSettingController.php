<?php

namespace App\Http\Controllers\Shop;

use App\Events\Shops\ShopSettingUpdateEvent;
use App\Http\Controllers\Controller;
use App\Models\ShopBind;
use App\Models\ShopExtra;
use App\Models\Shop;
use App\Services\Order\OrderDeliveryService;
use App\Services\Shop\ShopService;
use App\Utils\ArrayUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use function AlibabaCloud\Client\json;

class ShopSettingController extends Controller
{


    /**
     * @var ShopService $shopService
     */
    private $shopService;
    public function __construct(ShopService $shopService)
    {
        $this->shopService = $shopService;
    }


	public function index(Request $request): JsonResponse
    {


        $shopId=intval($request->input('shopId',$request->auth->shop_id));
		$exist   = ShopExtra::query()
			->where('shop_id', $shopId)
			->first();
		if (!empty($exist)) {
			$default = [
			    'user_id'            => $exist->user_id,
			    'shop_id'            => $exist->shop_id,
				'index_combine_show'  => $exist->index_combine_show,
				'warning_address_str' => $exist->warning_address_str,
				'needle_items'        => explode(',', $exist->needle_items),
				'merge_order_open'    => $exist->merge_order_open,
				'merge_order_num'     => $exist->merge_order_num,
				'merge_order_merge_sku'     => $exist->merge_order_merge_sku,
				'is_cross_shop_merge_order' => $exist->is_cross_shop_merge_order,
				'is_auto_refresh'     => $exist->is_auto_refresh,
				'is_automatic_delivery'     => $exist->is_automatic_delivery,
                'print_contents'            => $exist->print_contents,
                'auto_reset_template'       => $exist->auto_reset_template,
                'browser_download'          => $exist->browser_download,
                'warehouse_name'            => $exist->warehouse_name,
                'waybill_extra_province'    => $exist->waybill_extra_province,
                'open_live_mode'    => $exist->open_live_mode,
                'live_mode_expire_at'    => $exist->live_mode_expire_at,
                'print_config'    => $exist->print_config,
            ];
		} else {
            $default = [
                'user_id'            => $request->auth->user_id,
                'shop_id'            => $shopId,
                'index_combine_show'  => ShopExtra::SHOW_SINGLE,
                'warning_address_str' => ShopExtra::WARNING_ADDRESS_DEFAULT,
                'needle_items'        => ShopExtra::NEEDLE_ITEMS,
                'merge_order_open'    => ShopExtra::MERGE_ORDER_OPEN_YES,
                'merge_order_num'     => ShopExtra::MERGE_ORDER_NUM_DEFAULT,
                'merge_order_merge_sku'     => ShopExtra::MERGE_ORDER_MERGE_SKU_YES,
                'is_cross_shop_merge_order'     => ShopExtra::IS_CROSS_SHOP_MERGE_ORDER_DEFAULT,
                'is_auto_refresh'     => ShopExtra::IS_AUTH_REFRESH_YES,
                'is_automatic_delivery'     => ShopExtra::IS_AUTOMATIC_DELIVERY_NO,
                'print_contents'            => ShopExtra::PRINT_CONTENTS_DEFAULT,
                'auto_reset_template'       => ShopExtra::AUTO_RESET_TEMPLATE_NO,
                'browser_download'          => ShopExtra::BROWSER_DOWNLOAD_NO,
                'warehouse_name'            => '',
                'waybill_extra_province'    => '',
                'open_live_mode'    => 0,
                'live_mode_expire_at'    => null,
                'print_config'    => ShopExtra::DEFAULT_PRINT_CONFIG,
            ];
            ShopExtra::query()->create($default);
            $default['needle_items'] = explode(',', ShopExtra::NEEDLE_ITEMS);
        }

		return $this->success($default);
	}

    /**
     * 新增和编辑
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
	public function edit(Request $request): JsonResponse
    {
        $currentShopId = $request->auth->shop_id;
        $shopId=intval($request->input('shopId', $currentShopId));
		$data = $this->validate($request, [
			'warning_address_str' => 'string',
            'print_contents'      => 'string',
            'warehouse_name'      => 'string',
            'waybill_extra_province'      => 'string',
            'print_config'      => 'string',
            'merge_order_merge_sku'      => 'int',
		]);
		$res = ShopExtra::query()->updateOrCreate([
			//'user_id' => $request->auth->user_id,
			'shop_id' => $shopId,
		], $data);
        if($shopId == $currentShopId && !empty($data['print_contents'])){
            //对打印内容进行特殊处理，同级店铺打印内容一并更新
            $brothShopIds = array_column(ShopBind::getAllRelationShop($currentShopId, [ShopBind::TYPE_ME_BIND], false), 'id');
            ShopExtra::query()->whereIn('shop_id', $brothShopIds)->update(['print_contents' => $data['print_contents']]);
        }

		return $this->success($res);
	}

    /**
     * 自动重置模版
     * @param Request $request
     * @return void
     * @throws ValidationException
     */
    public function  autoResetTemplate(Request  $request){
        $data = $this->validate($request, [
            'auto_reset_template' => 'required|int|in:0,1', //二选一
        ]);
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        //		dd($data);
        $exist = ShopExtra::query()->where([
            //'user_id' => $request->auth->user_id,
            'shop_id' => $shopId,
        ])->first();
        $update['auto_reset_template'] = $data['auto_reset_template'];
        \Log::info('自动重置模版', $update);
        if ($exist) {
            $exist->update($update);
        } else {
            ShopExtra::query()->create(array_merge([
                'user_id' => $request->auth->user_id,
                'shop_id' => $shopId,
            ], $update));
        }
        return $this->success();
    }

    /**
     * 文件下载配置
     * @param Request $request
     * @return void
     * @throws ValidationException
     */
    public function  browserDownload(Request  $request){
        $data = $this->validate($request, [
            'browser_download' => 'required|int|in:0,1', //二选一
        ]);
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        //		dd($data);
        $exist = ShopExtra::query()->where([
            //'user_id' => $request->auth->user_id,
            'shop_id' => $shopId,
        ])->first();
        $update['browser_download'] = $data['browser_download'];
        \Log::info('修改文件下载', $update);
        if ($exist) {
            $exist->update($update);
        } else {
            ShopExtra::query()->create(array_merge([
                'user_id' => $request->auth->user_id,
                'shop_id' => $shopId,
            ], $update));
        }
        return $this->success();
    }

    /**
     * 合单配置修改
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
	public function mergeOrder(Request $request)
	{
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $shop = Shop::query()->where('id', $shopId)->first();
		$data = $this->validate($request, [
			'merge_order_open' => 'boolean', //二选一
			'merge_order_num'  => 'int',
            'is_cross_shop_merge_order'  => 'int|in:0,1',
        ]);

		if (isset($data['merge_order_open'])) {
			$switch                   = array_pull($data, 'merge_order_open');
			$data['merge_order_open'] = $switch ? ShopExtra::MERGE_ORDER_OPEN_YES : ShopExtra::MERGE_ORDER_OPEN_NO;
		}

		//		dd($data);
		$exist = ShopExtra::query()->where([
			//'user_id' => $request->auth->user_id,
			'shop_id' => $shopId,
		])->first();
		if ($exist) {
			$exist->update($data);
		} else {
			if (!isset($data['merge_order_num'])) {
				$data['merge_order_num'] = ShopExtra::MERGE_ORDER_NUM_DEFAULT;
			}
			$default = [
				'index_combine_show'  => ShopExtra::SHOW_SINGLE,
				'warning_address_str' => '',
				'needle_items'        => ShopExtra::NEEDLE_ITEMS,
			];
			ShopExtra::query()->create(array_merge([
				'user_id' => $request->auth->user_id,
				'shop_id' => $shopId,
			], $data, $default));
		}
        event((new ShopSettingUpdateEvent($shop->user, $shop, time(),  $data))->setClientInfoByRequest($request));

        return $this->success();
	}

	public function villageSetting(Request $request)
	{
		$data = ShopExtra::WARNING_ADDRESS_DEFAULT;
		return $this->success($data);
	}

	public function shopIdentifier(Request $request)
	{
		$data = $this->validate($request, [
			'shop_identifier' => 'array',
		]);

		$shop_identifier = implode(',', $data['shop_identifier']);
		$res             = Shop::query()->updateOrCreate([
			//'user_id' => $request->auth->user_id,
			'id'      => $request->auth->shop_id
		], [
			'shop_identifier' => $shop_identifier
		]);
		return $this->success($res);
	}

	public function getCurrentShop(Request $request)
	{
		$shop = Shop::query()->where([
			//'user_id' => $request->auth->user_id,
			'id'      => $request->auth->shop_id
		])->first();
		return $this->success($shop);
	}

	public function changeShopSwitch(Request $request){
        $isAutoRefresh       = $request->input('is_auto_refresh', false);
        $isAutomaticDelivery = $request->input('is_automatic_delivery', false);
	    ShopExtra::query()->where([
            //'user_id' => $request->auth->user_id,
            'shop_id' => $request->auth->shop_id
        ])->update([
            'is_auto_refresh'       => $isAutoRefresh ? ShopExtra::IS_AUTH_REFRESH_YES : ShopExtra::IS_AUTH_REFRESH_NO,
            'is_automatic_delivery' => $isAutomaticDelivery ? ShopExtra::IS_AUTOMATIC_DELIVERY_YES : ShopExtra::IS_AUTOMATIC_DELIVERY_NO
        ]);
	    \Log::info('change shopExtra shop_id:'.$request->auth->shop_id. ' is_auto_refresh:' . json_encode($isAutoRefresh) . ' is_automatic_delivery:'.json_encode($isAutomaticDelivery));

        return $this->success();
    }

    /**
     * <AUTHOR>
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updateExtra(Request $request){
        $data = $this->validate($request, [
            'preset_logistics_union_wp_code' => 'string',
            'preset_logistics_district_switch' => 'int|in:0,1',
            'preset_logistics_district_data' => 'array',
            'preset_logistics_district_data.*.query_area_id' => 'int',
            'preset_logistics_district_data.*.index' => 'int|min:0',
        ]);
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $saveData = [];
        if(isset($data['preset_logistics_union_wp_code'])){
            $saveData['preset_logistics_union_wp_code'] = $data['preset_logistics_union_wp_code'];
        }
        if(isset($data['preset_logistics_district_switch'])){
            $saveData['preset_logistics_district_switch'] = $data['preset_logistics_district_switch'];
        }
        if(isset($data['preset_logistics_district_data'])){
            $saveData['preset_logistics_district_data'] = json_encode($data['preset_logistics_district_data'], JSON_UNESCAPED_UNICODE);
        }
        $where = ['shop_id' => $shopId];
        $res = ShopExtra::query()->where($where)->update($saveData);
        return $this->success($res);
    }

    public function getExtraInfo(Request $request)
    {
        $shopId=intval($request->input('shopId',$request->auth->shop_id));
        $where = ['shop_id' => $shopId];
        $res = ShopExtra::query()->where($where)->first();
        return $this->success($res);
    }

    /**
     * 修改店铺信息
     * <AUTHOR>
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Illuminate\Validation\ValidationException
     */
    public function updateShopInfo(Request $request)
    {
        $data = $this->validate($request, [
            'sync_switch' => 'int|in:0,1',
        ]);
        $values = [];
        if(isset($data['sync_switch'])){
            $values['sync_switch'] = $data['sync_switch'];
        }
        Shop::query()->where('id', $request->auth->shop_id)->update($values);

        return $this->success();
    }

    /**
     * 获取发货设置
     * @param Request $request
     * @return JsonResponse
     */
    public function getDeliverSetting(Request $request): JsonResponse
    {
        return $this->success($this->shopService->getDeliverSetting($request->auth->shop_id));
    }

    /**
     * 更新发货设置
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function setDeliverSetting(Request $request): JsonResponse
    {
        $data = $this->validate($request, [
            "isEnable" => "bool",  //是否开启发货设置
            "packageScope" => "int",  //1定时发货,2有物流发货,3剩余发货时间
            "timerDeliverEnable" => "bool",  //定时发货开关
            "timerDeliverTime" => "string", //定时发货时间
            "logisticTraceTriggerEnable" => "bool",  //有物流发货开关
            "timeoutDeliverEnable" => "bool",  //剩余发货时间开关
            "deliverTimeout" => "int",  //剩余发货时间，
            "tracedAfterHour" => "int", //有物流以后几个小时
            "scope" => "int"    //同步到同级店铺  2 同步 1 不同步
        ]);

        return $this->success($this->shopService->updateDeliverSetting($request->auth->shop_id, $data, $data['scope']??1));
    }

}
