<?php

namespace App\Http\Controllers;

use App\Models\Address;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class AddressController extends Controller
{
    /**
     * <AUTHOR>
     * @param Request $request
     */
    public function getList(Request $request)
    {
        $version = $request->input('version', '');

        $currentVersion = Cache::get('address_version');
        empty($currentVersion) && $currentVersion = 'init';
        if ($version == $currentVersion) {
            return $this->success(['version' => $currentVersion, 'addressList' => []]);
        }
        $addressList = Address::getAddressByCache();
        return $this->success(['version' => $currentVersion,'addressList' => $addressList]);

    }

    /**
     * <AUTHOR>
     * @param Request $request
     */
    public function getListV2(Request $request)
    {
        $version = $request->input('version', '');
        $level = $request->input('level', 3);

        $currentVersion = Cache::get('address_version');
        empty($currentVersion) && $currentVersion = 'init';
        if ($version == $currentVersion) {
            return $this->success(['version' => $currentVersion, 'addressList' => []]);
        }
        $addressList = Address::getAddressV2ByCache($level);
        return $this->success(['version' => $currentVersion,'addressList' => $addressList]);

    }
}
