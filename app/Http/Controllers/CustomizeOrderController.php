<?php

namespace App\Http\Controllers;

use App\Constants\PlatformConst;
use App\Models\CustomizeOrder;
use App\Models\CustomizeOrdersFailed;
use App\Models\Order;
use App\Models\PrintRecord;
use App\Models\ShippingAddress;
use App\Models\Shop;
use App\Models\ShopBind;
use App\Models\WaybillHistory;
use App\Services\BusinessException;
use App\Services\CustomizeOrder\CustomizeOrderService;
use App\Services\Order\OrderServiceManager;
use App\Services\Printing\BasePrintService;
use App\Services\Printing\CustomizeOrderPrintService;
use App\Services\Printing\Payload\CustomizeOrderPrintPayload;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\ShopUtil;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

class CustomizeOrderController extends Controller
{
    private $customizeOrderService;
    private $printServices;

    /**
     * @param CustomizeOrderService $customizeOrderService
     * @param CustomizeOrderPrintService $printServices
     */
    public function __construct(CustomizeOrderService $customizeOrderService, CustomizeOrderPrintService $printServices)
    {
        $this->customizeOrderService = $customizeOrderService;
        $this->printService = $printServices;

    }

    /**
     * 手动创建订单列表
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function index(Request $request)
    {
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at" => 'date',
            "print_status" => 'int',
            "offset" => 'int',
            "limit" => 'int',
            "order_by" => 'string',
            "search" => 'string',
            "province" => 'string',
            "city" => 'string',
            "town" => 'string',
            "has_buyer_mark" => 'string',
            "seller_memo_flag" => 'string',
            "waybill_code_flag" => 'string',
            "ownerIdList " => 'array',
            "identifier" => "string",
        ]);
        $condition = [];
//        if ($sonShopId = $request->input('son_shop_id', '')) {
//            $condition[] = ['shop_id', $sonShopId];
//        } else {
//        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
//        //把当前登录的店铺也加进去
//        $shopIds[] = $request->auth->shop_id;
//        $condition[] = ['shop_id','in', $shopIds];
//            $condition[] = ['user_id', $request->auth->user_id];
//        }
        //获取当前店铺的所有同级店铺或子店铺
        $shops = ShopBind::getAllRelationShop($request->auth->shop_id);
        $shopIds = collect($shops)->pluck("id")->toArray();


        $search = trim($request->input('search', ''));
        $timeField = $request->input('timeField', '');
        $beginAt = $request->input('begin_at', '');
        $endAt = $request->input('end_at', '');
        $identifier = $request->input('identifier', '');
        $waybillCodeFlag = $request->input('waybill_code_flag', '');
        if ($timeField && $beginAt && $endAt) {
            $condition[] = ['customize_orders.' . $timeField, '>=', $beginAt];
            $condition[] = ['customize_orders.' . $timeField, '<=', $endAt];
        }
        $condition = $this->addWaybillCodeCondition($waybillCodeFlag, $condition);
        if (in_array($request->input('print_status'), CustomizeOrder::PRINT_STATUS_ARR)) {
            $condition[] = ['print_status', '=', $request->input('print_status')];
        }

        if ($province = $request->input('province', '')) {
            $condition[] = ['receiver_province', '=', $province];
        }
        if ($city = $request->input('city', '')) {
            $condition[] = ['receiver_city', '=', $city];
        }
        if ($town = $request->input('town', '')) {
            $condition[] = ['receiver_district', '=', $town];
        }
        if ($request->input('has_buyer_mark', '')) {
            $condition[] = ['seller_memo', '<>', ''];
        }
        if ($platform = $request->input('platform', 0)) {
            if ($platform == 3) {
                $condition[] = ['platform', '>=', 3];
            } else {
                $condition[] = ['platform', '=', $platform];
            }
        }
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);
        //$orderBy = $request->input('order_by', "id desc");
        $sort = $request->input('sort');
        $printStatus = $request->input('print_status');
        if ($identifier) {
//            $shop = Shop::query()->where('identifier', (string)$identifier)->first();
            $shop = Shop::firstByIdentifier($identifier);
            $condition[] = ['shop_id', '=', $shop->id];
        }

        list($ret, $count) = CustomizeOrder::search($condition, $shopIds, $search, $offset, $limit, $sort, $printStatus);

        if ($search) {
            if ($printStatus == CustomizeOrder::PRINT_STATUS_DELETE) {
                $rowsFound = count($ret);
            } else {
//                $query = CustomizeOrder::where($condition);
//                $query->whereIn('shop_id', $shopIds);
                $rowsFound = $count;
            }
        } else {
            if ($printStatus == CustomizeOrder::PRINT_STATUS_DELETE) {
                $where = CustomizeOrder::where($condition);
                $where->whereIn('shop_id', $shopIds);
                $rowsFound = $where->onlyTrashed()->count();
            } else {
                $where = CustomizeOrder::where($condition);
                $where->whereIn('shop_id', $shopIds);
                Log:
                info("获取手工订单总数", [$where->toSql(), $shopIds]);
                $rowsFound = $where->count();
            }

        }

        $waybillCount = 0;
        foreach ($ret as &$item) {
            if (!empty($item['order_no']) && empty($item['receiver_phone'])) {
                $order = Order::query()->with('orderCipherInfo')->where('tid', $item['order_no'])->first();
                $item['order_cipher_info'] = $order->orderCipherInfo;
                $item['receiver_name'] = $order->orderCipherInfo->receiver_name_mask;
                $item['receiver_phone'] = $order->orderCipherInfo->receiver_phone_mask;
                $item['receiver_address'] = $order->orderCipherInfo->receiver_address_mask;
            } else {
                $item['receiver_name'] = dataDesensitizationForOpenApi($item['receiver_name'], 0, -1); //收件人姓名脱敏处理
                $item['receiver_phone'] = dataDesensitizationForOpenApi($item['receiver_phone'], 3, 4); //收件人手机脱敏处理
                $item['receiver_address'] = addressDesensitization($item['receiver_address']);
            }

            if (!empty($item['goods_info'])) {
                $escapers = array("\n", "\r", "\t");
                $replacements = array("", "", "");
                $item['goods_info'] = str_replace($escapers, $replacements, $item['goods_info']);
            }
        }

        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $offset,
            'limit' => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }

    public function statistic(Request $request)
    {
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at" => 'date',
            "print_status" => 'int',
            "offset" => 'int',
            "limit" => 'int',
            "order_by" => 'string',
            "search" => 'string',
            "province" => 'string',
            "city" => 'string',
            "town" => 'string',
            "has_buyer_mark" => 'string',
        ]);
//        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
//        $shopIds[] = $request->auth->shop_id;
        $shops = ShopBind::getAllRelationShop($request->auth->shop_id);
        $shopIds = collect($shops)->pluck("id")->toArray();
        $printStatus = $request->input('print_status', '');
        $waybillCodeFlag = $request->input('waybill_code_flag', '');
        $condition = [];
        if ($sonShopId = $request->input('son_shop_id', '')) {
            $condition[] = ['shop_id', $sonShopId];
        } else {
//            $condition[] = ['shop_id', $request->auth->shop_id];
//            $condition[] = ['user_id', $request->auth->user_id];
        }
        $search = $request->input('search', '');
        if ($request->input('begin_at')) {
            $condition[] = ['created_at', '>=', $request->input('begin_at')];
        }
        if ($request->input('end_at')) {
            $condition[] = ['created_at', '<=', $request->input('end_at')];
        }
        if ($province = $request->input('province', '')) {
            $condition[] = ['receiver_province', '=', $province];
        }
        if ($city = $request->input('city', '')) {
            $condition[] = ['receiver_city', '=', $city];
        }
        if ($town = $request->input('town', '')) {
            $condition[] = ['receiver_district', '=', $town];
        }
        if ($request->input('has_buyer_mark', '')) {
            $condition[] = ['seller_memo', '<>', ''];
        }
        if ($platform = $request->input('platform', 0)) {
            if ($platform == 3) {
                $condition[] = ['platform', '>=', 3];
            } else {
                $condition[] = ['platform', '=', $platform];
            }
        }
        //是否取号
        $condition = $this->addWaybillCodeCondition($waybillCodeFlag, $condition);
        $query = CustomizeOrder::query();
        $query->whereIn('shop_id', $shopIds);

        if ($search) {
            $waybillHistoryOrderIdArr = WaybillHistory::query()->where('waybill_code', $search)->get()->pluck('order_id')->toArray();
            $queryBase = $query->where($condition)->where(function ($query) use ($search, $waybillHistoryOrderIdArr) {
                $query->where('order_no', $search)
                    ->orWhere('receiver_name', $search)
//                    ->orWhere('waybill_code', $search)
                    ->orWhere('receiver_phone', $search);
                if (!empty($waybillHistoryOrderIdArr)) {
                    $query->orWhereIn('id', $waybillHistoryOrderIdArr);
                }
            });
            $query1 = clone $queryBase;
            $query2 = clone $queryBase;
            $query3 = clone $queryBase;
            $alrePrintCount = $query1->where('print_status', '=', CustomizeOrder::PRINT_STATUS_YES)->count();
            $noPrintCount = $query2->where('print_status', '=', CustomizeOrder::PRINT_STATUS_NO)->count();
            $delPrintCount = $query3->onlyTrashed()->count();
        } else {
            $query1 = clone $query;
            $query2 = clone $query;
            $query3 = clone $query;
            $alrePrintCount = $query1->where($condition)->where('print_status', '=', CustomizeOrder::PRINT_STATUS_YES)->count();
            $noPrintCount = $query2->where($condition)->where('print_status', '=', CustomizeOrder::PRINT_STATUS_NO)->count();
            $delPrintCount = $query3->where($condition)->onlyTrashed()->count();
        }
        $waybillNotRecycledCount = -1;
        // 已打印 未回收的电子面单统计
        if ($printStatus == CustomizeOrder::PRINT_STATUS_YES) {
            $printIdArr = $query1->pluck('id')->toArray();
            $waybillNotRecycledCount = WaybillHistory::query()
                ->where('order_type', WaybillHistory::ORDER_TYPE_FREE)
                ->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
                ->whereIn('order_id', $printIdArr)->count();
        }

        return $this->success([$noPrintCount, $alrePrintCount, $delPrintCount, $waybillNotRecycledCount]);
    }

    /**
     * 添加快递公司
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws BusinessException
     */
    public function add(Request $request)
    {
        $data = $this->validate($request, [
            "receiver_province" => "required|string",
            "receiver_city" => "required|string",
            "receiver_district" => "required|string",
            "receiver_address" => "required|string",
            "receiver_name" => "required|string",
            "receiver_phone" => "string",
            "receiver_zip" => "sometimes|nullable",
            "production_type" => "required|string",
            "product_remark" => "string",
            "order_no" => "string",
            "seller_memo" => "string",
//            "sender_addressinfo" => "string",
            "goods_info" => "string",
//            "sender_name" => "required|string",
//            "sender_phone" => "required|string",
//            "sender_tel" => "sometimes|nullable",
//            "sender_province" => "required|string",
//            "sender_city" => "required|string",
//            "sender_district" => "required|string",
//            "sender_detailaddress" => "required|string",
            "receiver_tel" => "string",
            "outer_order_no" => "string",
            "identifier" => "required|string",
        ]);

        if (empty($data['receiver_phone']) && empty($data['receiver_tel'])) {
            throw new BusinessException('收件人手机号码或固定电话需填写一项');
        }
        $identifier = array_get($data, 'identifier');
        $shop = Shop::firstByIdentifier($identifier);
        //关联订单不存收件人信息
//        if (!empty($data['order_no'])) {
//            unset($data['receiver_address']);
//            unset($data['receiver_name']);
//            unset($data['receiver_phone']);
//        }

        $result = CustomizeOrder::create(array_merge($data, [
            'user_id' => $shop->user_id,
            'shop_id' => $shop->id,
            'order_type' => CustomizeOrder::ORDER_TYPE_SINGLE,
        ]));
        if (!$result) {
            throw new BusinessException('创建失败！');
        }

        return $this->success($result);
    }

    /**
     * 新版本的
     * @param Request $request
     * @return void
     * @throws ValidationException
     */
    public function printNew(Request $request)
    {
        $this->validate($request, [
            'order_ids' => 'required|array',
            'template_id' => 'required|int',
            'package_num' => 'int'
        ]);
        $packageMax = 50;  //一单多包打印限制
        $singleMax = 100; //普通打印限制
        $orderIds = $request->input('order_ids');
        $packageNum = $request->input('package_num', 1);
        if (count($orderIds) > $singleMax) {
            return $this->fail('批量打印数量最大为' . $singleMax, 400);
        }
        if ($packageNum > 1 && count($orderIds) > $packageMax) {
            return $this->fail('一单多包批量打印数量最大为' . $packageMax, 400);
        }
        $payload = new CustomizeOrderPrintPayload();
        $payload->setOrderIds($orderIds);
        $payload->setCurrentShopId($request->auth->shop_id);
        $payload->setUserId($request->auth->user_id);
        $payload->setPrintWaybillStatus($request->input('printWaybillStatus', ''));
        $payload->setPackageNum($packageNum);
        $payload->setTemplateId($request->input('template_id'));
        $payload->setBatchNo($request->input('batch_no'));
        \Log::info("生成payload" . json_encode($payload));
        $this->printService->print($payload);
        return $this->success(['orders' => $payload->getPrintData(), 'company' => $payload->getCompany(), 'failed' => $payload->getFailedData()]);
    }


    /**
     * 获取打印数据
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function print(Request $request)
    {
        $this->validate($request, [
            'order_ids' => 'required|array',
            'template_id' => 'required|int',
            'package_num' => 'int'
        ]);

        $printWaybillStatus = $request->input('printWaybillStatus', '');
        $packageNum = $request->input('package_num', 1);
        $orderIdsArr = $request->input('order_ids');
        $templateId = $request->input('template_id');
        $batchNo = $request->input('batch_no');
        $packageMax = 50;  //一单多包打印限制
        $singleMax = 100; //普通打印限制

        if (count($orderIdsArr) > $singleMax) {
            return $this->fail('批量打印数量最大为' . $singleMax, 400);
        }
        if ($packageNum > 1 && count($orderIdsArr) > $packageMax) {
            return $this->fail('一单多包批量打印数量最大为' . $packageMax, 400);
        }
        //已打印订单，重新取新单号状态为1
        if ($printWaybillStatus == CustomizeOrder::PRINT_WAYBILL_STATUS_NEW) {
            $printData = CustomizeOrder::getPrintHistoryDataWaybill(
                $request->auth->user_id,
                $request->auth->shop_id,
                $templateId,
                $orderIdsArr,
                $printWaybillStatus,
                $batchNo,
                $packageNum
            );
        } else {
            //正常的自由打单，状态不做判断
            $printData = CustomizeOrder::getPrintDataAndWaybill(
                $request->auth->user_id,
                $request->auth->shop_id,
                $templateId,
                $orderIdsArr,
                $batchNo,
                $packageNum
            );

        }

        //抛出取号异常
//	    if (empty($printData['orders']) && count($printData['failed']) > 0) {
//			throw new \Exception($printData['failed'][0]);
//	    }

        return $this->success($printData);
    }

    public function autoRelation(Request $request): JsonResponse
    {
        $this->validate($request, [
            "identifier" => "string",
            'tid' => 'required|string'
        ]);
        $identifier = $request->input('identifier');
        $shop = null;
        if(!empty($identifier)) {
            $shop = Shop::firstByIdentifier($identifier);
        }
        $tid = $request->input('tid');
        $result = [];
        /*if (PlatformConst::DY == config('app.platform')) {
            $tid = $request->input('tid') . "A";
            $order = Order::query()->with('orderCipherInfo')->where('tid', $tid . '')->first()->toArray();
            $shop = Shop::query()->where('id', $order['shop_id'])->first();
            $result = [
                'tid' => $tid,
                'receiver_phone' => $order['order_cipher_info']['receiver_phone_ciphertext'],
                'receiver_name' => $order['order_cipher_info']['receiver_name_ciphertext'],
                'receiver_address' => $order['order_cipher_info']['receiver_address_ciphertext'],
            ];
            $orderService = OrderServiceManager::create();
            $orderService->setUserId($shop->user_id);
            $orderService->setShop($shop);
            $result = $orderService->batchDecrypt($result);
        }*/

        $tid = OrderUtil::appendOrderSuffix($tid);
//        $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
        \Log::info("获取关联订单", ["tid"=>$tid]);
        $query = Order::query()->with('orderCipherInfo')->where([
            'tid' => $tid
        ]);
        if ($shop) {
            $query->where("shop_id", $shop->id);
        } else {
            $shopIds = Shop::getShopIdsByShopIdentifier($request->auth->shop_id);
            $query->whereIn("shop_id", $shopIds);
        }
        $o = $query->first();
        if (!$o) {
            return $this->fail('订单不存在', 400);
        }

        if ($o->orderCipherInfo) {
            $o['receiver_phone'] = $o->orderCipherInfo['receiver_phone_mask'];
            $o['receiver_name'] = $o->orderCipherInfo['receiver_name_mask'];
            $o['receiver_address'] = $o->orderCipherInfo['receiver_address_mask'];
        }

        return $this->success($o);
    }

    /**
     * 批量上传
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function upload(Request $request)
    {
        $this->validate($request, [
            'file' => 'required|file',
            'identifier' => 'required|string',
        ]);
        $identifier = $request->input('identifier');
        $shop = Shop::firstByIdentifier($identifier);

        $file = $request->file('file');
        if ($file->isValid()) {
            $ret = $this->customizeOrderService->batchGenerate($shop->user_id, $shop->id, $file);
            if (!$ret) {
                //return $this->fail('文件上传失败');
                return $this->success([], '文件上传失败', 400);
            }

            return $this->success($ret);
        } else {
            //return $this->fail('文件上传失败');
            return $this->success([], '文件上传失败', 400);
        }
    }


    /**
     * 修改
     * @param Request $request
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     * @throws BusinessException
     */
    public function edit(Request $request, $id)
    {
        $data = $this->validate($request, [
            "receiver_province" => "required|string",
            "receiver_city" => "required|string",
            "receiver_district" => "required|string",
            "receiver_address" => "required|string",
            "receiver_name" => "required|string",
            "receiver_phone" => "string",
            "receiver_tel" => "string",
            "seller_memo" => "string",
            "goods_info" => "string",
        ]);

        if (empty($data['receiver_phone']) && empty($data['receiver_tel'])) {
            throw new BusinessException('手机号码和固定电话需填写一项');
        }

        $res = CustomizeOrder::where([
            'id' => $id,
//            'user_id' => $request->auth->user_id,
            'shop_id' => $request->auth->shop_id,
        ])->update($data);
        if (!$res) {
            throw new BusinessException('修改失败！');
        }

        return $this->success($res);
    }


    /**
     * 批量删除
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws BusinessException
     */
    public function batchDelete(Request $request)
    {
        $this->validate($request, [
            'ids' => 'required|array',
        ]);
        $force = $request->input("force", false);
        $query = CustomizeOrder::query()
            ->whereIn('id', $request->input('ids'));
        if ($force) {
            $delete = $query->forceDelete();
        } else {
            $delete = $query
                ->delete();
        }
        if (!$delete) {
            throw new BusinessException('操作失败！');
        }

        return $this->success();
    }

    public function destroy($id)
    {
        $delete = CustomizeOrder::where('id', $id)->delete();
        if (!$delete) {
            throw new BusinessException('操作失败！');
        }

        return $this->success();
    }

    /**
     * 取消单号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws BusinessException
     */
    public function waybillRecovery(Request $request)
    {
        $this->validate($request, [
            "ids" => "required|array",
        ]);
        $ret = CustomizeOrder::recovery($request->input('ids'), $request->auth->user_id, $request->auth->shop_id);
        if (!$ret) {
            return $this->fail('回收失败');
        }

        return $this->success();
    }

    /**
     * 打印状态通知
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws BusinessException
     */
    public function notifyPrintResult(Request $request)
    {
        $data = $this->validate($request, [
            "result" => "required|array",
        ]);
        if (empty($data['result'])) {
            return $this->fail('参数有误');
        }
        $orderIds = collect($data['result'])
            ->where('status', CustomizeOrder::PRINT_RESULT_SUCCESS)
            ->pluck('orderId')
            ->toArray();
        if (empty($orderIds)) {
            return $this->fail('面单号订单不存在');
        }
        $ret = CustomizeOrder::query()
            //->where('user_id', $request->auth->user_id)
            //->where('shop_id', $request->auth->shop_id)
            ->whereIn('id', $orderIds)
            ->update([
                'print_status' => CustomizeOrder::PRINT_STATUS_YES,
                'printed_at' => Carbon::now()
            ]);
        if (!$ret) {
            \Log::error('更新订单打印状态失败!', ['order_ids' => $orderIds]);
        }

        //修改打印序号
        $total = count($orderIds);
        foreach ($data['result'] as $key => $item) {
            //写入打印序号
            PrintRecord::query()->where(['order_id' => $item['orderId'], 'batch_no' => $request->input('batch_no')])->update([
                'print_index' => $key + 1,
                'print_count' => $total
            ]);
        }

        return $this->success();
    }

    /**
     * 取未回收的旧单号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws BusinessException
     */
    public function getOldWaybillCode(Request $request)
    {
        $this->validate($request, [
            "order_ids" => "required|array",
        ]);
        $orderIds = $request->get('order_ids');
        $waybillHistories = WaybillHistory::where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)
            ->where('package_id', 0)
            ->whereIn('order_id', $orderIds)
            ->orderBy('id', 'desc')
            ->get()
            ->toArray();
        return $this->success($waybillHistories);
    }

    /**
     * 用旧单号构造数据返回
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function getWaybillHistory(Request $request)
    {
        $this->validate($request, [
            "order_ids" => "required|array",
            'template_id' => 'required|int',
            "printwaybillNumList" => "required|array",
        ]);
        $printData = CustomizeOrder::getWaybillHistoryData(
            $request->auth->user_id,
            $request->auth->shop_id,
            $request->input('template_id'),
            $request->input('printwaybillNumList'),
            $request->input('order_ids')
        );

        return $this->success($printData);
    }

    /**
     * 指定一个或多个单号回收
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function batchRecoveryCode(Request $request)
    {
        $this->validate($request, [
            "wp_codes" => "required|array",
        ]);

        $wp_codes = $request->input('wp_codes');

        $ret = CustomizeOrder::batchRecovery($wp_codes, $request->auth->user_id, $request->auth->shop_id);
        if (!$ret) {
            return $this->fail('回收失败');
        }

        return $this->success();
    }

    public function failedOrderList(Request $request)
    {
        $this->validate($request, [
            "begin_at" => 'date',
            "end_at" => 'date',
            "offset" => 'int',
            "limit" => 'int',
            "search" => 'string',
            "has_buyer_mark" => 'string',
        ]);
        $shopId = $request->auth->shop_id;

        $search = trim($request->input('search', ''));
        $beginAt = $request->input('begin_at', date('Y-m-d', strtotime('-10 days')) . ' 00:00:00');
        $endAt = $request->input('end_at', date('Y-m-d', time()) . ' 23:59:59');
        $offset = (int)$request->input('offset', 0);
        $limit = (int)$request->input('limit', 10);

        $condition = [
            ['created_at', '>=', $beginAt],
            ['created_at', '<=', $endAt],
            ['shop_id', $shopId]
        ];
        list($ret, $rowsFound) = CustomizeOrdersFailed::search($condition, $search, $offset, $limit);

        $pagination = [
            'rows_found' => $rowsFound,
            'offset' => $offset,
            'limit' => $limit
        ];

        return $this->success(['pagination' => $pagination, $ret]);
    }


    public function uploadFailedOrder(Request $request)
    {
        $failed = $success = 0;
        $userId = $request->auth->user_id;
        $shopId = $request->auth->shop_id;
        $params = $request->input();
        //发件人信息
        $ship = ShippingAddress::where('user_id', $userId)
            ->where('shop_id', $shopId)
            ->where('tip', ShippingAddress::IS_SENDER_DEFAULT_YES)
            ->where('is_default', ShippingAddress::IS_DEFAULT_YES)
            ->first();
        foreach ($params as $item) {
            if (empty($item['receiver_name']) || empty($item['receiver_province']) || empty($item['receiver_city']) || empty($item['receiver_district']) || empty($item['receiver_address'])) {
                ++$failed;
                continue;
            }
            $goodsInfo = '[{"index":0,"value":"","title":"' . trim($item['goods_name']) . '","code":"","norm":"","num":"' . $item['goods_num'] . '","price":"","weight":null,"volume":null}]';
            $order = [
                'user_id' => $userId,
                'shop_id' => $shopId,
                'receiver_name' => $item['receiver_name'],
                'order_type' => CustomizeOrder::ORDER_TYPE_BATCH,
                'receiver_phone' => $item['receiver_phone'],
                'receiver_province' => $item['receiver_province'],
                'receiver_city' => $item['receiver_city'],
                'receiver_district' => $item['receiver_district'],
                'receiver_address' => $item['receiver_address'],
                'goods_info' => $goodsInfo,
                'num' => $item['goods_num'],
                'seller_memo' => $item['goods_memo'],
                'sender_name' => $ship->sender_name,
                'sender_phone' => $ship->mobile,
                'sender_tel' => $ship->tel,
                'sender_province' => $ship->province,
                'sender_city' => $ship->city,
                'sender_district' => $ship->district,
                'sender_detailaddress' => $ship->address,
            ];

            $ret = CustomizeOrder::query()->create($order);
            if ($ret) {
                ++$success;
                CustomizeOrdersFailed::where(['id' => $item['id'], 'shop_id' => $shopId])->delete();
            } else {
                ++$failed;
            }
        }

        return $this->success(['success' => $success, 'failed' => $failed]);
    }

    public function deleteFailedOrder(Request $request)
    {
        $this->validate($request, [
            "ids" => "required|array"
        ]);
        $ids = $request->input('ids');
        $ret = CustomizeOrdersFailed::where('shop_id', $request->auth->shop_id)->whereIn('id', $ids)->delete();
        if (!$ret) {
            return $this->fail('删除失败');
        }

        return $this->success();
    }

    /**
     * @param $waybillCodeFlag
     * @param array $condition
     * @return array
     */
    public function addWaybillCodeCondition($waybillCodeFlag, array $condition): array
    {
        switch ($waybillCodeFlag) {
            case 'Null':
                $condition[] = ['waybill_code', '=', Null];
                break;
            case 'NotNull':
                $condition[] = ['waybill_code', '!=', Null];
                break;
        }
        return $condition;
    }

    /**
     * 订单解密
     * @param Request $request
     * @return JsonResponse
     * @throws ValidationException
     */
    public function batchDecryption(Request $request)
    {
        $data = $this->validate($request, [
            "ids" => "required|array"
        ]);
        $ids = array_get($data, 'ids');
        $list = CustomizeOrder::query()->whereIn('id', $ids)->get([
            'id',
            'receiver_phone',
            'receiver_name',
            'receiver_address',
        ]);
        return $this->success($list);
    }
}
