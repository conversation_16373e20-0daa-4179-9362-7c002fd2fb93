<?php

namespace App\Http\Controllers;

use App\Models\Company;
use App\Models\Shop;
use App\Models\Template;
use App\Services\BusinessException;
use App\Services\Waybill\CompanyService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CompanyController extends Controller
{
    /**
     * 开通的快递公司列表
     * <AUTHOR>
     * 22-2-14 去掉userId
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $companies = Company::with('templates', 'user')
            ->where('shop_id', $request->auth->shop_id)
            ->get()
            ->toArray();

        return $this->success($companies);
    }

    /**
     * 我的快递公司
     * @param Request $request
     * @return JsonResponse
     */
    public function myCompanies(Request $request): JsonResponse
    {
	    return $this->success(CompanyService::getRelationCompanies($request->auth->user_id, $request->auth->shop_id));
    }

    /**
     * 所有支持快递公司
     * @return JsonResponse
     */
    public function config()
    {
        return $this->success(config('express_company'));
    }

    /**
     * 开启/关闭
     * @param Request $request
     * @param         $id
     * @return JsonResponse
     * @throws BusinessException
     */
    public function switch(Request $request, $id)
    {
        $exist  = Company::findOrFail($id);
        $status = Company::EXPRESS_COMPANY_STATUS_OPEN;
        if ($exist->status == Company::EXPRESS_COMPANY_STATUS_OPEN) {
            $status = Company::EXPRESS_COMPANY_STATUS_CLOSED;
        }
        $exist->status = $status;
        if (!$exist->save()) {
            throw new BusinessException('状态修改失败');
        }

        return $this->success($exist);
    }

    /**
     * <AUTHOR>
     * 22-2-14 去掉了userId
     * @param Request $request
     * @param $id
     * @return JsonResponse
     * @throws BusinessException
     * @throws \Illuminate\Validation\ValidationException
     */
    public function edit(Request $request, $id)
    {
        $data    = $this->validate($request, [
            'branch_code' => "required|int",
            'branch_name' => "string",
            'province'    => "required|string",
            'city'        => "required|string",
            'district'    => "required|string",
            'detail'      => "required|string",
            'wp_code'     => "required|string",
            'wp_name'     => "required|string",
            'owner_id'    => "required|string",
            'owner_name'  => "required|string",
        ]);
        $company = Company::query()->where([
            'shop_id'     => $request->auth->shop_id,
            'wp_code'     => $data['wp_code'],
            'branch_code' => $data['branch_code'],
            'owner_id'    => $data['owner_id'],
            'province'    => $data['province'],
            'city'        => $data['city'],
            'district'    => $data['district'],
            'detail'      => $data['detail']
        ])->first();
        //不存在创建
        if (empty($company)) {
            $addrArr = [
                'province' => $data['province'],
                'city'     => $data['city'],
                'district' => $data['district'],
                'detail'   => $data['detail']
            ];
            $company = Company::generate(
                $data['wp_code'],
                $data['branch_code'],
                $request->auth->user_id,
                $request->auth->shop_id,
                $data['owner_id'],
                $data['owner_name'],
                $data['auth_source'],
                $addrArr
            );
            if (!$company) {
                throw new BusinessException('快递公司创建异常!');
            }
        }
        //        $update = Company::where([
        //                        'user_id' => $request->user['id'],
        //                        'id' => $id,
        //                    ])->update($data);
        //        if(!$update) {
        //            throw new \BusinessException('更新失败！');
        //        }
        Template::where('company_id', $id)
            ->where('shop_id', $request->auth->shop_id)
            ->update([
                'company_id' => $company->id
            ]);

        return $this->success();
    }
    public function getDirectlyCompany()
    {
      $companyList = Company::ZHI_YING_COMPANY_LIST;
      return $this->success($companyList);
    }


}
