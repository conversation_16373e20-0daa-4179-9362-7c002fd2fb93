<?php

namespace App\Http\Middleware;

use App\Http\StatusCode\StatusCode;
use App\Models\ApiShopBind;
use Closure;
use XHProfRuns_Default;

/**
 * 正对Xhprof的插件
 */
class Xhprof
{
    public function handle($request, Closure $next)
    {
        // We will only profile requests if the proper flag is set on the query
        // of the request. You may further customize this to be disabled on
        // production releases of your application.
        $requestRate = config("xhprof.RequestRate");
        if (!config("xhprof.RequestEnable") || $requestRate == 0) {
            return $next($request);
        }
        $rand = rand(0, 100);
        $headXhprofEnable = $request->header('xhprof') == 'true';
        $xhprofPaths = config("xhprof.RequestPaths");
        $path = $request->path();
        $isProfPath=false;
        foreach ($xhprofPaths as $xhprofPath) {
            if(empty($xhprofPath)){
                continue;
            }
            if(str_contains($path,$xhprofPath)){
                $isProfPath=true;
                break;
            }
        }
        if (!$headXhprofEnable && !($rand <= $requestRate &&$isProfPath)){
            return $next($request);
        }
        $runId =null;// str_replace(['/','_'],'',$path). date("mdhisa");
        xhprof_enable(XHPROF_FLAGS_CPU + XHPROF_FLAGS_MEMORY, []);

        $result = $next($request);

        $xhprofData = xhprof_disable();
        $xhprofRuns = new XHProfRuns_Default();

        $xhprofRuns->save_run($xhprofData, 'xhprof_laravel', $runId);
        // We will attach the XHProf run ID as part of the response header.
        // This is a lot better than modifying the actual response body.

        return $result;
    }
}
