<?php

namespace App\Http\Middleware;

use App\Constants\ErrorConst;
use App\Exceptions\ApiException;
use App\Services\BusinessException;
use Closure;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Symfony\Component\HttpFoundation;

class CheckToken
{
    /**
     * token check
     * @param         $request
     * @param Closure $next
     * @param null    $guard
     * @return mixed
     * @throws BusinessException
     */
    public function handle($request, Closure $next, $guard = null)
    {
        if (config('api.mocking')) {
            $user['id']           = 1;
            $user['ks_shop_id']   = 1;
            $user['ks_shop_name'] = 'ks_test小店';
            $user['auth_at']      = date('Y-m-d H:i:s');
            $user['expire_at']    = date('Y-m-d H:i:s', time() + 86399 * 10);
        } else {
            $token = $request->bearerToken();
//            \Log::info('token:' . $token);
            if (empty($token)) {
                $token = $request->input('access_token');
                \Log::info('access_token:' . $token);
            }

            if (!$token) {
//                throw new BusinessException("授权错误或失效，请重新授权!", HttpFoundation\Response::HTTP_FORBIDDEN);
                throw new ApiException(ErrorConst::USER_TOKEN_EXPIRED);
            }
            // 如果传递的是header中的token，需要去掉Bearer

            try {
                $credentials = JWT::decode($token, env('JWT_SECRET'), ['HS256']);
            } catch (ExpiredException $e) {
                throw new ApiException(ErrorConst::USER_TOKEN_EXPIRED);
            } catch (\Exception $e) {
                throw new BusinessException($e->getMessage());
            }
            // $cacheToken = redis('cache')->get('jwt_token:' . $credentials->sub);
            // if ($cacheToken && $cacheToken != $token){
            //     throw new BusinessException('用户已在其他地方登录！', 401);
            // }
        }
        // sql 注入
        if (in_array($credentials->shop_id, explode(',', env('SHOP_BLACKLIST')))) {
            throw new ApiException(ErrorConst::SYSTEM_ERROR);
        }

        $user                = new \stdClass;
        $user->user_id       = $credentials->sub;
        $user->shop_id       = $credentials->shop_id;
        $user->shop_name     = $credentials->shop_name??'';
        $user->identifier    = $credentials->identifier;
        $user->plaftorm_type = $credentials->plaftorm_type;
        $operatorName = empty($user->shop_name) ? '' : $user->shop_name . '-' . '管理员';
        $user->operatorName  = $operatorName;
        $request->auth       = $user;
        !defined('SHOP_ID') && define('SHOP_ID', $credentials->shop_id);
        return $next($request);
    }
}
