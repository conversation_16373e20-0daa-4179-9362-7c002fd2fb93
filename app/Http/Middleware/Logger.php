<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class Logger
{
    /**
     * Handle an incoming request.
     *
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {

        /** @var \Symfony\Component\HttpFoundation\Response $response */
        $response = $next($request);

        // 记录所有请求信息
        $requestMessage = [
//            'url' => $request->url(),
            'method' => $request->method(),
            'path' => $request->path(),
            'ip' => $request->ips(),
            'headers' => $request->header(),
            'query' => $request->query(),
        ];

        $parameterLength = \config('logging.channels.request.value_max_length');
        if ($request->file()) {
            // 文件内容不做日志记录，使用<file>做标识
            $requestMessage['body'] = '<file>';
        }else {
            // 获取请求体Body信息
            $bodyContent = $request->all();

            if ($bodyContent && in_array($request->method(), ['POST', 'PATCH'])) {
//                foreach ($request->all() as $key => $value) {
//                    if (!is_string($value)) {
//                        $value = json_encode($value,JSON_UNESCAPED_UNICODE);
//                    }
//                    if (Str::length($value) > $parameterLength) {
//                        // 参数内容的长度过大的进行裁剪
//                        $bodyContent[$key] = Str::limit($value, $parameterLength);
//                    }
//                }
            }
            $requestMessage['body'] = $bodyContent;
        }

        $content = $response->getContent();
//        if (Str::length($content) > $parameterLength) {
//            // 参数内容的长度过大的进行裁剪
//            $content = Str::limit($content, $parameterLength);
//        }
        if (is_string($content)) {
            $contentArr = json_decode($content, true);
            if (!empty($contentArr)) {
                $content = $contentArr;
            }
        }
        $requestMessage['response'] = $content;
        \Log::channel('request')->info('request message', $requestMessage);

        return $response;
    }
}
