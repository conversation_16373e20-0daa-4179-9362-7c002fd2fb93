<?php

namespace App\Console\Commands\Fix;

use App\Models\Template;
use App\Models\WaybillHistory;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 订正WaybillHistory里面的company_id为0的历史数据
 */
class FixEmptyWaybillHistoryCompanyId extends Command
{

    protected $signature = 'fix:empty-waybill-history-company-id';
    protected $description = '订正WaybillHistory里面的company_id为0的历史数据';

    public function handle()
    {
        Log::info('开始订正WaybillHistory里面的company_id为0的历史数据');
        $count = 0;
        //模板ID和CompanyId的对应关系，如果匹配到就直接用，否则去数据库里面找
        $templateCompanyIdMap = [];
        WaybillHistory::query()->where('company_id', 0)->where('template_id', '>', 0)->chunkById(100,
            function ($waybillHistories) use (&$count, &$templateCompanyIdMap) {
                $waybillHistories->each(function ($waybillHistory) use (&$count, &$templateCompanyIdMap) {
                    $templateId = $waybillHistory->template_id;
                    $company_id = 0;
                    if (array_key_exists($templateId, $templateCompanyIdMap)) {
                        $company_id = $templateCompanyIdMap[$templateId];
                        Log::info("WaybillHistory.id={$waybillHistory->id}里面的company_id已经匹配到，值为{$company_id}");
                    } else {
                        $template = Template::query()->where(['id' => $templateId])->withTrashed()->first();
                        $company_id = $template->company_id;
                        $templateCompanyIdMap[$templateId] = $company_id;
                    }
                    if ($company_id == 0) {
                        Log::error("WaybillHistory.id={$waybillHistory->id}里面的company_id没有对应的数据");
                        return;
                    }
                    $id = $waybillHistory->id;
                    $waybillHistory->company_id = $company_id;
                    $count++;
                    Log::info("订正WaybillHistory.id={$id}里面的company_id为{$company_id}的历史数据成功,第{$count}条");
                    $waybillHistory->save();

                });
            });
    }

}
