<?php

namespace App\Console\Commands;

use App\Models\DeliveryRecord;
use App\Models\Order;
use App\Models\PrintRecord;
use App\Models\ShippingAddress;
use App\Models\Shop;
use App\Models\UserShopStatistic;
use App\Models\Template;
use App\Models\WaybillHistory;
use App\Models\WaybillHistoryStatistic;
use App\Services\Auth\AuthServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UserUsedStatisticsCommand extends Command
{
    protected $signature = 'command:user-shop-statistics {--date=}';

    protected $description = '统计每个用户使用情况';

    const PAGE_SIZE = 200;//每页200条查询

    /**
     * <AUTHOR>
     * 22-2-22 去掉多余的userId
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $date = date('Y-m-d');
        if ($this->option('date')) {
            $date = $this->option('date');
        }
        $beginAt = date('Y-m-d 00:00:00', strtotime($date));
        $endAt   = date('Y-m-d 23:59:59', strtotime($date));

        $authService = AuthServiceManager::create(config('app.platform'));

        $time = time();
        $timeBegin = strtotime('02:00');
        $timeEnd = strtotime('02:10');
        Shop::query()
            ->where('type', $authService->getPlatformType())
            ->chunk(self::PAGE_SIZE, function ($shops) use ($beginAt, $endAt, $date, $time, $timeBegin, $timeEnd) {
            $shops->each(function ($shop) use ($beginAt, $endAt, $date, $time, $timeBegin, $timeEnd) {
                $conditions   = [];
                $conditions[] = ['shop_id', '=', $shop->id];
                $conditions[] = ['created_at', '<=', $endAt];
                $conditions[] = ['created_at', '>=', $beginAt];
                //今日打单数据
                $deliveryCount = DeliveryRecord::where($conditions)->count();
                $realPrintCount = PrintRecord::where($conditions)->count();
                $templateCount = Template::where($conditions)->count();
                //$printCount    = WaybillHistory::where($conditions)->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)->count();
                $orderCount    = Order::where($conditions)->count();
                $addressCount  = ShippingAddress::where($conditions)->where('tip',ShippingAddress::IS_SENDER_DEFAULT_YES)->count();

                $rangeDate = getRangeDate($beginAt, $endAt);
                $todayDate = date('Y-m-d');
                $todayPrintCount = 0;
                // 是否包含今天的数据 需要额外计算，统计表中截止到前一天的
                if (in_array($todayDate, $rangeDate)) {
                    $beginAt = $todayDate.' 00:00:00';
                    $conditions = [
                        ['shop_id', '=', $shop->id],
                        ['created_at', '<=', $endAt],
                        ['created_at', '>=', $beginAt]
                    ];
                    $todayPrintCount    = WaybillHistory::where($conditions)->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)->count();
                }
                $printCount = WaybillHistoryStatistic::getPrintCount($shop->id,$rangeDate) + $todayPrintCount;

                //每天记录
                $res     = UserShopStatistic::updateOrCreate([
                    'user_id'  => $shop->user_id,
                    'shop_id'  => $shop->id,
                    'stat_at'  => $date,
                    'interval' => UserShopStatistic::INTERVAL_DAY,
                ], [
                    'print_count'    => $printCount,
                    'real_print_count'=>$realPrintCount,
                    'order_count'    => $orderCount,
                    'delivery_count' => $deliveryCount,
                    'address_count'  => $addressCount,
                    'template_count' => $templateCount,
                ]);
                if (!$res) {
                    Log::error('店铺【今日】统计数据出错!', [$shop]);
                }

                if ($time > $timeBegin && $time < $timeEnd) {
                    $conditions   = [];
//                    $conditions[] = ['user_id', '=', $shop->user_id];
                    $conditions[] = ['shop_id', '=', $shop->id];

                    //累计打单数据
                    $printCount    = WaybillHistory::where($conditions)->where('waybill_status', WaybillHistory::WAYBILL_RECOVERY_NO)->count();
                    $orderCount    = Order::where($conditions)->count();
                    $deliveryCount = DeliveryRecord::where(['shop_id'=> $shop->id])->count();
                    $addressCount  = ShippingAddress::where([
                        'shop_id' => $shop->id,
                        'tip'     => ShippingAddress::IS_SENDER_DEFAULT_YES,
                    ])->count();
                    $templateCount = Template::where([
                        'shop_id' => $shop->id,
                    ])->count();

                    //每天记录
                    $res     = UserShopStatistic::updateOrCreate([
                        'user_id'  => $shop->user_id,
                        'shop_id'  => $shop->id,
                        'stat_at'  => null,
                        'interval' => UserShopStatistic::INTERVAL_IGNORE,
                    ], [
                        'print_count'    => $printCount,
                        'real_print_count'=>$realPrintCount,
                        'order_count'    => $orderCount,
                        'delivery_count' => $deliveryCount,
                        'address_count'  => $addressCount,
                        'template_count' => $templateCount,
                    ]);
                    if (!$res) {
                        Log::error('店铺【累计】统计数据出错!', [$shop]);
                    }
                }
            });
        });
        $this->info('执行完成 date => ' . $date);

        return 'success';
    }
}
