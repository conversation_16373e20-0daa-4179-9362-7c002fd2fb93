<?php

namespace App\Console\Commands;

use App\Jobs\Goods\SyncGoodsJob;
use App\Models\Shop;
use App\Services\Auth\AuthServiceManager;
use App\Services\ThreeBodySms;
use App\Utils\Environment;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class CronWitchWarnCommand extends Command
{
    protected $signature   = 'cron:witch-warn';
    protected $description = '定时监控报警';
    const CHUNK_SIZE = 500;//每页500条查询

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $redis    = redis('cache');
        $highLength = $redis->llen('queues:high');
        if ($highLength > 10000){
            $text = config('app.name')."队列长度：{$highLength}";
            $this->sendNotify($text);
        }
        if (!Environment::isDy() || in_array(config('app.name'), ['{dy-kddd}'])) {
            return;
        }
        // 查出订单
        $redis = redis('cache');
        $redisKey = 'syncOrderByDataPush:lastTime';
        $lastTime = $redis->get($redisKey);
        // 如果$lastTime 和当前时间间隔大于 15分钟，则发短信报警，短信沉默期为 15分钟
        $delayMinutes = Carbon::parse($lastTime)->diffInMinutes(Carbon::now());
        if (!empty($lastTime) && $delayMinutes > 15) {
            \Log::info('syncOrderByDataPush:diffInMinutes', ['lastTime' => $lastTime]);
            $text = config('app.name')."同步延迟{$delayMinutes}分钟";
            $this->sendNotify($text);
        }
    }

    /**
     * @param  string  $text
     * <AUTHOR>
     */
    protected function sendNotify(string $text): void
    {
        $redis = redis('cache');
        $setNxEx = $redis->set('syncOrderByDataPush:delaySms', Carbon::now()->toDateTimeString(), 'nx', 'ex', 600);
        if ($setNxEx) {//短信报警
//                $threeBodySms = new ThreeBodySms();
//                $threeBodySms->send('230143', [config('app.name'), "同步延迟{$delayMinutes}分钟" ], ['156']);
            Mail::raw($text, function ($message) {
                $message->to('<EMAIL>', 'wzz')->subject('业务报警！');
            });
        }
    }
}
