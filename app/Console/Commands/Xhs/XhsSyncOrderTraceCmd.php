<?php

namespace App\Console\Commands\Xhs;

use App\Models\Package;
use App\Models\Shop;
use App\Services\Waybill\XHS\XHSApi;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class XhsSyncOrderTraceCmd extends Command
{
    protected $signature = 'xhs:sync-order-trace {shop_id} {tid} {waybillCode}  ';
    protected $description = '同步订单轨迹';

    public function handle()
    {
        $shopId = $this->argument('shop_id');
        $tid = $this->argument('tid');
        $waybillCode = $this->argument('waybillCode');
        $shop = Shop::find($shopId);
        $xhsApi = new XhsApi($shop->access_token);
        $xhsApi->setShop($shop);
        $package=Package::where('waybill_code',$waybillCode)->first();
        $waybillHistory = $package->waybillHistory;
        $waybill = [
            'express_code' => $package->wp_code,
            'express_no' => $waybillCode,
            'tid' => $tid,
            'receiver_province' => $waybillHistory->province,
            'receiver_name' => $waybillHistory->receiver_name,
            'send_at' => '',
        ];
        Log::info("获取小红书物流",[$xhsApi->getOrderTraceList($waybill)]);


        //

    }
}
