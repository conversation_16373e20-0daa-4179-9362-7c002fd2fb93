<?php

namespace App\Console\Commands;

use App\Constants\PlatformConst;
use App\Exceptions\ClientException;
use App\Models\Shop;
use App\Models\UserExtra;
use App\Services\Auth\AuthServiceManager;
use App\Utils\Environment;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RefreshUsersToken extends Command
{
    const MAX_TIME = 86399;
    protected $name = 'command:refresh-users-token';

    protected $description = '刷新用户的AccessToken';

    const PAGE_SIZE = 500;//每页500条查询

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if(!Environment::isNeedRefreshToken()){
            Log::info(Environment::platform()."无需刷新Token");
            return ;
        }
        if (config('app.name') == '{albb-sddd}') {
            Log::info("无需刷新Token:".config('app.name'));
            return ;
        }
        $nowTime = date("Y-m-d H:i:s", time());
        $afterOneHour = date("Y-m-d H:i:s", strtotime("5 hour"));
        $beforeTime = date("Y-m-d H:i:s", strtotime("-1 day"));
        $authService = AuthServiceManager::create(config('app.platform'));
        if (config('app.platform') == 'wx' || config('app.platform') == 'wxsp') {
            $component = $authService->getComponentAccessToken();
            $componentToken = $component['component_access_token'];
//            \Log::info("component_access_token = " , [$component['component_access_token']]);
        } else {
            $componentToken = "";
        }
//        \Log::info("refresh Token",[$nowTime,$afterOneHour]);

        /**
         * 半小时刷新一次，对两种情况进行刷新
         * 1. 对授权已经过期了，但状态还是有效的进行刷新
         * 2. 对过期时间在当前和下个小时只能的进行刷新，这个1个小时是因为抖音的规则，如果一个小时以外刷新token的过期时间是不会变的
         */
        $query = Shop::query()
            ->where('type', $authService->getPlatformType())
            ->where(function ($query) use ($nowTime, $afterOneHour, $beforeTime) {
                $query->where([
                    ['auth_status', '=', 2],
                    ['expire_at', '<', $nowTime],
                    ['expire_at', '>=', $beforeTime],
                ])->orWhere([
                    ['expire_at', '>=', $nowTime],
                    ['expire_at', '<=', $afterOneHour],
                ]);

            })
            ->orderBy('id');
        $query
            ->each(function ($shop) use ($authService, $componentToken) {
                try {
//                    \Log::info("refresh Token shopId".$shop->id);
                    //假店铺无refresh_token，有refresh_token才去刷新
                    if ($shop->refresh_token) {
                        if (in_array(config('app.platform'), [PlatformConst::TAOBAO, PlatformConst::XHS])) {
                            // 淘宝降低调用频率
                            sleep(2);
                        }
                        if (PlatformConst::WX == config('app.platform')){
                            // 微信有毒，需要过期了还能刷新
                            $userExtraInfo = UserExtra::query()->where('shop_id', $shop->id)->first();
                            if (!empty($userExtraInfo) && strtotime($userExtraInfo['expire_at']) < time()) {
                                Shop::updateAuthStatus($shop->id, Shop::AUTH_STATUS_ABNORMAL_EXPIRE);
                                Log::info('用户授权失效:RefreshUsersToken', ['shop_id' => $shop->id]);
                            }
                        }
                        \DB::transaction(function () use ($shop, $authService, $componentToken) {
                            $int = $authService->refreshTokenByAuth($shop, $componentToken);
                            echo $shop->id . ':' . $int . PHP_EOL;
                            Log::info("refresh Token shopId".$shop->id.":".$int);
                        });
                    }
                }catch (ClientException $e){
                    // todo 后面要对错误单独处理
                    // {"errcode":42001,"errmsg":"access_token expired rid: 6062c8f2-********-7c9e92bb"}
                    // {"errcode":61003,"errmsg":"component is not authorized by this account rid: 6062c9cb-1a2072ef-6ec9f2d9"}
                    Log::error($e->getMessage());
                    echo $shop->id .'error'.$e->getMessage(). PHP_EOL;
                }catch (\Exception $e){
                    Log::error($e->getMessage());
                    echo $shop->id .'error'.$e->getMessage(). PHP_EOL;
                }

            }, self::PAGE_SIZE);
    }
}
