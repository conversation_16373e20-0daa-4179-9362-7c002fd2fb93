<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/3/16
 * Time: 10:59
 */
namespace App\Console\Commands;

use App\Models\Shop;
use App\Models\WaybillHistory;
use App\Models\WaybillHistoryStatistic;
use Illuminate\Console\Command;

class WaybillHistoryStatisticsCommand extends Command
{
    protected $signature = 'command:waybill_history_statistics {--date=} {--is_first=}';
    protected $description = '统计每个用户面单使用情况';

    const PAGE_SIZE = 200;//每页200条查询
    const MAX_LIMIT = 10000;

    public function handle()
    {
        $date = date('Y-m-d', strtotime('-1 days'));
        if ($this->option('date')) {
            $date = $this->option('date');
        }

        //第一次写入60天的
        if ($this->option('is_first')) {
            $beginDate = date('Y-m-d',strtotime('-61 days'));
            $endDate = date('Y-m-d',strtotime('-1 days'));
            $rangeDate = getRangeDate($beginDate, $endDate);
            foreach ($rangeDate as $date) {
                $this->statistics($date);
            }
        } else {
            $this->statistics($date);
        }
    }

    public function statistics($date)
    {
        // 增加运行内存防止溢出
        ini_set('memory_limit', '1024M');
        $beginAt = date('Y-m-d 00:00:00', strtotime($date));
        $endAt   = date('Y-m-d 23:59:59', strtotime($date));

        Shop::query()
            ->where('type', Shop::PLATFORM_TYPE_MAP[config('app.platform')])
            ->chunk(self::PAGE_SIZE, function ($shops) use ($date, $beginAt, $endAt) {
                $shops->each(function ($shop) use ($date, $beginAt, $endAt) {
                    try {
                        $condition   = [
                            ['shop_id', $shop->id],
                            ['waybill_histories.created_at', '>=', $beginAt],
                            ['waybill_histories.created_at', '<=', $endAt],
                        ];
                        // 获取统计日期所有取号记录
                        $waybillHistoryList = WaybillHistory::query()
                            ->select('wp_code','auth_source','waybill_code','source_shopid', 'package_id', 'waybill_status')
                            ->where($condition)
                            ->get();
                        // 按照快递公司、平台分组
                        $newWaybillHistoryList = $waybillHistoryList->groupBy(['wp_code', 'auth_source']);
                        unset($waybillHistoryList);
                        foreach ($newWaybillHistoryList as $wpCode => $list) {
                            foreach ($list as $authSource => $item) {
                                // 去重
                                $item = collect($item)->unique('waybill_code')->all();
                                // 获取使用面单统计数量
                                list($waybillNums, $recoverWaybill, $waybillOrders, $recoverWaybillOrders, $customOrders,
                                    $recoverCustomOrders) = $this->getUseNum($item);
                                // sourceShopId分组
                                $newItem = collect($item)->filter(function ($value, $key) {
                                    if ($value['source_shopid']) {
                                        return true;
                                    }
                                })->groupBy(['source_shopid']);
                                // 写入数据
                                WaybillHistoryStatistic::query()->updateOrCreate([
                                    'shop_id' => $shop->id,
                                    'stat_at' => $date,
                                    'wp_code' => $wpCode,
                                    'auth_source' => $authSource,
                                    'source_shopid' => 0,
                                    'order_total_use' => $waybillNums,
                                    'order_total_cancel' => $recoverWaybill,
                                    'platform_order_total_use' => $waybillOrders,
                                    'platform_order_total_cancel' => $recoverWaybillOrders,
                                    'customize_order_total_use' => $customOrders,
                                    'customize_order_total_cancel' => $recoverCustomOrders,
                                ],[
                                    'shop_id' => $shop->id,
                                    'stat_at' => $date,
                                    'wp_code' => $wpCode,
                                    'auth_source' => $authSource,
                                    'source_shopid' => 0,
                                ]);

                                // 是否有使用分享面单
                                foreach ($newItem as $sourceShopId => $value) {
                                    // 去重
                                    $item = collect($value)->unique('waybill_code')->all();
                                    // 获取使用面单统计数量
                                    list($waybillNums, $recoverWaybill, $waybillOrders, $recoverWaybillOrders, $customOrders,
                                        $recoverCustomOrders) = $this->getUseNum($item);
                                    // 写入数据
                                    WaybillHistoryStatistic::query()->updateOrCreate([
                                        'shop_id' => $shop->id,
                                        'stat_at' => $date,
                                        'wp_code' => $wpCode,
                                        'auth_source' => $authSource,
                                        'source_shopid' => $sourceShopId,
                                        'order_total_use' => $waybillNums,
                                        'order_total_cancel' => $recoverWaybill,
                                        'platform_order_total_use' => $waybillOrders,
                                        'platform_order_total_cancel' => $recoverWaybillOrders,
                                        'customize_order_total_use' => $customOrders,
                                        'customize_order_total_cancel' => $recoverCustomOrders,
                                    ],[
                                        'shop_id' => $shop->id,
                                        'stat_at' => $date,
                                        'wp_code' => $wpCode,
                                        'auth_source' => $authSource,
                                        'source_shopid' => $sourceShopId,
                                    ]);
                                }
                            }
                        }
                    } catch (\Exception $e) {
                        \Log::info('统计取号记录异常 shop_id:'.$shop->id.' 错误信息:'.$e->getMessage());
                    }
                });
            });
    }

    public function getUseNum($data)
    {
        // 总的面单使用量
        $waybillNums = collect($data)->count();
        // 自由打印订单面单数量
        $customOrders = collect($data)->where('package_id',0)->count();
        // 平台订单的面单使用量
        $waybillOrders  =  $waybillNums-$customOrders;
        //已回收的面单数
        $recoverWaybill = collect($data)->where('waybill_status',WaybillHistory::WAYBILL_RECOVERY_YES)->count();
        // 自由打印订单面单已回收数量
        $recoverCustomOrders = collect($data)->where('package_id',0)->where('waybill_status',WaybillHistory::WAYBILL_RECOVERY_YES)->count();
        // 平台订单的面单已回收数量
        $recoverWaybillOrders = $recoverWaybill - $recoverCustomOrders;

        return [$waybillNums, $recoverWaybill, $waybillOrders, $recoverWaybillOrders, $customOrders, $recoverCustomOrders];
    }
}
