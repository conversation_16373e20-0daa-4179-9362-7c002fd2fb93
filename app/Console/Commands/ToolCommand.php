<?php

namespace App\Console\Commands;

use App\Exports\WaybillHistoryExport;
use App\Models\Company;
use App\Models\ExportTask;
use ACES\TDEClient;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\Shop;
use App\Models\Template;
use App\Models\WaybillHistory;
use App\Models\WaybillShareAction;
use App\Services\Order\OrderServiceManager;
use App\Services\WaybillHistory\WaybillHistoryQueryService;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;

class ToolCommand extends Command
{
	protected $signature   = 'command:tool {--action=} {--tid=} {--shop_id=}'; // php artisan command:tool --action=getRawOrderInfo --tid=6928650894222038908A
	protected $description = '调度工具';

	public function handle()
	{
		$action = $this->option('action');
		if (!$action) {
			$this->info('action invalid!');
			return;
		}

		//获取订单详情
		if ($action == 'getOrderInfo') {
			$this->getOrderInfo();
		}
        //获取原始订单详情
        if ($action == 'getRawOrderInfo') {
            $this->getRawOrderInfo();
        }
		if ($action == 'delInvalidGoods') {
			$this->delInvalidGoods();
		}
       	if ($action == 'getAfterSale') {
			$this->getAfterSale();
		}
		//同步30天内数据库中未发货的订单
        if ($action == 'syncHistoryOrder') {
            $this->syncHistoryOrder();
		}
        //同步30天内数据库中未发货的订单
        if ($action == 'fixWxspTemplates') {
            $this->fixWxspTemplates();
        }
		//临时修复数据
        if ($action == 'fix') {
            $this->fix();
        }
		//修复迁移导致的面单分享的问题
        if ($action == 'fixCompanyShareNum') {
            $this->fixCompanyShareNum();
        }
		//修复迁移导致的面单分享的问题
        if ($action == 'fixCompanyShare') {
            $this->fixCompanyShare();
        }
        // 测试 yac
        if ($action == 'testYac') {
            $this->testYac();
        }
        // 获取淘宝物流公司列表
        if ($action == 'getTaobaoLogisticsCompanies') {
            $this->getTaobaoLogisticsCompanies();
        }
	}

	protected function delInvalidGoods()
	{
		Goods::query()->where('goods_title', '闪电购商品')
			->chunk(100, function ($goods) {
				foreach ($goods as $g) {
					$g->forceDelete();
					foreach ($g->skus as $s) {
						$s->forceDelete();
					}
				}
			});
		$this->info('success');
	}

	protected function getOrderInfo()
	{
		$tid = null;
		if ($this->option('tid')) {
			$tid = $this->option('tid');
		}
		if (empty($tid)) {
			$this->info('tid invalid!');
			return;
		}
		$order = Order::query()->where('tid', $tid)->firstOrFail();
		$orderService = OrderServiceManager::create(config('app.platform'));
		$orderService->setUserId($order->user_id);
		$orderService->setShop($order->shop);
		$order = $orderService->getOrderInfo($tid);

		$this->info(var_export($order, true));
	}

    protected function getAfterSale()
    {
        $tid = null;
        if ($this->option('tid')) {
            $tid = $this->option('tid');
        }
        if (empty($tid)) {
            $this->info('tid invalid!');
            return;
        }
        $orderItem    = OrderItem::query()->where('oid', $tid)->firstOrFail();
        $order        = $orderItem->order;
        $orderService = OrderServiceManager::create(config('app.platform'));
        $orderService->setUserId($order->user_id);
        $orderService->setShop($order->shop);
        $order = $orderService->getAfterSaleOrderInfo($tid);
	$this->info(var_export($order, true));
    }

    protected function syncHistoryOrder()
    {
        $shopId = $this->option('shop_id') ?? null;
        if (empty($shopId)) {
            $this->info('shop_id invalid!');
            return;
        }
        $endAt = date('Y-m-d H:i:s', time());
        $beginAt = date('Y-m-d H:i:s', strtotime('-30 days'));

        $orderService = OrderServiceManager::create(config('app.platform'));
        $shop = Shop::query()->findOrFail($shopId);
        $orderService->setShop($shop);
        $orderService->syncHistoryOrder($beginAt, $endAt);

        $this->info('success');
    }
    protected function fix()
    {
        $packages = Package::query()
            ->where('created_at', '>', '2022-01-05')
            ->where('waybill_status', 1)
            ->where('print_status', 1)
            ->get();
        foreach ($packages as $package){
            $tids = $package['tids'];
            $tid = explode(',', $tids);
            $order = Order::query()->whereIn('tid', $tid)->get();
            $orderIds = collect($order)->pluck('id');
            sort($orderIds);
            $tid_oids = [];
            foreach ($orderIds as $id){
                $tid_oids[] = [
                    'id' => (string)$id,
                    'subIds' => [],
                ];
            }
            $tid_oids = json_encode($tid_oids);
            $history = WaybillHistory::query()
                ->where('order_no', $tid[0])
                ->orderBy('id', 'desc')
                ->first();
            if (!empty($history)) {
                $package->waybill_code = $history->waybill_code;
                $package->wp_code = $history->wp_code;
                $package->template_id = $history->template_id;
                $package->tid_oids = $tid_oids;
                $package->waybill_status = 2;
                $package->save();
            }
        }
    }

    private function getRawOrderInfo()
    {
        $tid = null;
        if ($this->option('tid')) {
            $tid = $this->option('tid');
        }
        if (empty($tid)) {
            $this->info('tid invalid!');
            return;
        }
        $shopId = null;
        if ($this->option('shop_id')) {
            $shopId = $this->option('shop_id');
        }
        $orderService = OrderServiceManager::create(config('app.platform'));
        if ($shopId) {
            $shop = Shop::query()->findOrFail($shopId);
            $orderService->setShop($shop);
        }else{
            $order = Order::query()->where('tid', $tid)->firstOrFail();
            $orderService->setUserId($order->user_id);
            $orderService->setShop($order->shop);
        }

        $orderRaw = $orderService->getRawOrderInfo($tid);
        $this->info(json_encode($orderRaw));
    }

    private function fixWxspTemplates()
    {
        Template::query()->where('type',9)->chunkById(1000, function ($templates){
            foreach ($templates as $index => $template) {
                $custom_config = $template->custom_config;
                // 替换 >{{contents}}< 为 >{{{contents}}}< ，>{{remark}}< 为 >{{{remark}}}< ，>{{buyerMemo}}< 为 >{{{buyerMemo}}}<
                $arr = [
                    '>{{contents}}<' => '>{{{contents}}}<',
                    '>{{remark}}<' => '>{{{remark}}}<',
                    '>{{buyerMemo}}<' => '>{{{buyerMemo}}}<',
                ];
                foreach ($arr as $key => $item) {
                    $custom_config = str_replace($key, $item, $custom_config);
                }
                $template->update(['custom_config' => $custom_config]);
                echo $template->id . ' success' . PHP_EOL;
            }
        });
    }

    protected function fixCompanyShareNum()
    {
        $company = Company::query()->where('source', Company::SOURCE_COMPANY_STATUS_YES)->get();
        //获取分享单号总数
        $shareCount = WaybillShareAction::getShareCount($company->id);
        //获取分享网点使用总数、取消总数
        list($useCount, $cancelCount) = WaybillHistoryQueryService::getShareUseAndCancelCount($company);
        // $company->id, $shareCount, $useCount, $cancelCount, $shareCount-$useCount-$cancelCount
        \Log::info(sprintf('share_count:%s,%s,%s,%s,%s',
            $company->id, $shareCount, $useCount, $cancelCount, $shareCount-$useCount-$cancelCount));
    }

    /**
     * 订正迁移的时候共享面单数据不对的问题
     * @return void
     */
    protected function fixCompanyShare()
    {
        $jsonKdx = '[{"id":855,"shareCount":500,"useCount":34,"cancelCount":0,"surplus":466},{"id":891,"shareCount":100,"useCount":39,"cancelCount":0,"surplus":61},{"id":905,"shareCount":15099,"useCount":10603,"cancelCount":4,"surplus":4492},{"id":970,"shareCount":200,"useCount":89,"cancelCount":0,"surplus":111},{"id":1051,"shareCount":300,"useCount":289,"cancelCount":0,"surplus":11},{"id":1086,"shareCount":189,"useCount":185,"cancelCount":1,"surplus":3},{"id":1187,"shareCount":22733,"useCount":5532,"cancelCount":2,"surplus":17199},{"id":1363,"shareCount":500,"useCount":123,"cancelCount":0,"surplus":377},{"id":1370,"shareCount":99,"useCount":42,"cancelCount":0,"surplus":57},{"id":1376,"shareCount":500,"useCount":182,"cancelCount":0,"surplus":318},{"id":1425,"shareCount":1219,"useCount":1092,"cancelCount":73,"surplus":54},{"id":1440,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1441,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1621,"shareCount":-1,"useCount":39,"cancelCount":0,"surplus":-40},{"id":1641,"shareCount":48,"useCount":48,"cancelCount":0,"surplus":0},{"id":1706,"shareCount":9999,"useCount":82,"cancelCount":0,"surplus":9917},{"id":1839,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":1840,"shareCount":0,"useCount":0,"cancelCount":0,"surplus":0},{"id":1921,"shareCount":543,"useCount":413,"cancelCount":14,"surplus":116},{"id":1969,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2012,"shareCount":169,"useCount":68,"cancelCount":0,"surplus":101},{"id":2021,"shareCount":776,"useCount":774,"cancelCount":2,"surplus":0},{"id":2032,"shareCount":13598,"useCount":13479,"cancelCount":119,"surplus":0},{"id":2107,"shareCount":-1,"useCount":1,"cancelCount":0,"surplus":-2},{"id":2242,"shareCount":2151,"useCount":2106,"cancelCount":9,"surplus":36},{"id":2253,"shareCount":2038,"useCount":1991,"cancelCount":4,"surplus":43},{"id":2307,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2308,"shareCount":99999,"useCount":1143,"cancelCount":60,"surplus":98796},{"id":2314,"shareCount":1292,"useCount":1248,"cancelCount":37,"surplus":7},{"id":2320,"shareCount":20505,"useCount":20496,"cancelCount":3,"surplus":6},{"id":2366,"shareCount":499,"useCount":8,"cancelCount":0,"surplus":491},{"id":2447,"shareCount":64475,"useCount":63491,"cancelCount":2,"surplus":982},{"id":2448,"shareCount":37000,"useCount":36880,"cancelCount":0,"surplus":120},{"id":2453,"shareCount":9173,"useCount":9173,"cancelCount":0,"surplus":0},{"id":2461,"shareCount":135,"useCount":60,"cancelCount":24,"surplus":51},{"id":2463,"shareCount":209,"useCount":200,"cancelCount":0,"surplus":9},{"id":2482,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":2483,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2486,"shareCount":15001,"useCount":1552,"cancelCount":9,"surplus":13440},{"id":2487,"shareCount":199,"useCount":1,"cancelCount":0,"surplus":198},{"id":2488,"shareCount":9999,"useCount":89,"cancelCount":53,"surplus":9857},{"id":2516,"shareCount":2327,"useCount":2053,"cancelCount":85,"surplus":189},{"id":2518,"shareCount":3180,"useCount":1690,"cancelCount":0,"surplus":1490},{"id":2523,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2545,"shareCount":179,"useCount":11,"cancelCount":0,"surplus":168},{"id":2547,"shareCount":300,"useCount":82,"cancelCount":0,"surplus":218},{"id":2617,"shareCount":2100,"useCount":1999,"cancelCount":38,"surplus":63},{"id":2628,"shareCount":1000,"useCount":1000,"cancelCount":0,"surplus":0},{"id":2632,"shareCount":8163,"useCount":8119,"cancelCount":44,"surplus":0},{"id":2889,"shareCount":500,"useCount":500,"cancelCount":0,"surplus":0},{"id":2943,"shareCount":9210,"useCount":8336,"cancelCount":0,"surplus":874},{"id":2978,"shareCount":-1,"useCount":474,"cancelCount":1,"surplus":-476},{"id":2981,"shareCount":500,"useCount":222,"cancelCount":10,"surplus":268},{"id":3189,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":3271,"shareCount":-1,"useCount":16,"cancelCount":0,"surplus":-17},{"id":3373,"shareCount":512,"useCount":414,"cancelCount":1,"surplus":97},{"id":3400,"shareCount":10,"useCount":4,"cancelCount":0,"surplus":6},{"id":3403,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":3455,"shareCount":176,"useCount":145,"cancelCount":1,"surplus":30}]';
        // waybill_share_actions id >= 904 id <= 962
        $jsonSDDY = '[{"id":1177,"shareCount":3,"useCount":0,"cancelCount":0,"surplus":3},{"id":1179,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":1180,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":1334,"shareCount":19437,"useCount":19325,"cancelCount":112,"surplus":0},{"id":1361,"shareCount":400,"useCount":362,"cancelCount":0,"surplus":38},{"id":1362,"shareCount":100,"useCount":84,"cancelCount":0,"surplus":16},{"id":1364,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1377,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1434,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":1439,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1465,"shareCount":110,"useCount":0,"cancelCount":0,"surplus":110},{"id":1475,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1493,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1557,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1558,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1559,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1578,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1756,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1836,"shareCount":5,"useCount":5,"cancelCount":0,"surplus":0},{"id":1858,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1927,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":1986,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2005,"shareCount":-1,"useCount":13,"cancelCount":0,"surplus":-14},{"id":2051,"shareCount":-1,"useCount":0,"cancelCount":1,"surplus":-2},{"id":2055,"shareCount":-1,"useCount":1,"cancelCount":0,"surplus":-2},{"id":2097,"shareCount":100,"useCount":100,"cancelCount":0,"surplus":0},{"id":2100,"shareCount":149,"useCount":12661,"cancelCount":57,"surplus":-12569},{"id":2105,"shareCount":260,"useCount":251,"cancelCount":9,"surplus":0},{"id":2122,"shareCount":6199,"useCount":2690,"cancelCount":55,"surplus":3454},{"id":2153,"shareCount":100,"useCount":87,"cancelCount":7,"surplus":6},{"id":2183,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2184,"shareCount":-1,"useCount":0,"cancelCount":1,"surplus":-2},{"id":2185,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":2217,"shareCount":1000,"useCount":0,"cancelCount":0,"surplus":1000},{"id":2218,"shareCount":1000,"useCount":44,"cancelCount":0,"surplus":956},{"id":2226,"shareCount":1000,"useCount":0,"cancelCount":0,"surplus":1000},{"id":2242,"shareCount":2147483646,"useCount":17742,"cancelCount":0,"surplus":2147465904},{"id":2478,"shareCount":-1,"useCount":1,"cancelCount":0,"surplus":-2},{"id":2730,"shareCount":5500,"useCount":953,"cancelCount":16,"surplus":4531},{"id":2739,"shareCount":1149,"useCount":96,"cancelCount":0,"surplus":1053},{"id":2773,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":2774,"shareCount":100,"useCount":16,"cancelCount":1,"surplus":83},{"id":2824,"shareCount":5000,"useCount":322,"cancelCount":0,"surplus":4678},{"id":2874,"shareCount":500,"useCount":167,"cancelCount":0,"surplus":333},{"id":2893,"shareCount":9999,"useCount":619,"cancelCount":18,"surplus":9362},{"id":2894,"shareCount":9999,"useCount":619,"cancelCount":18,"surplus":9362},{"id":2931,"shareCount":300,"useCount":141,"cancelCount":1,"surplus":158},{"id":3083,"shareCount":151,"useCount":141,"cancelCount":1,"surplus":9},{"id":3111,"shareCount":109999,"useCount":42621,"cancelCount":648,"surplus":66730},{"id":3126,"shareCount":50,"useCount":0,"cancelCount":0,"surplus":50},{"id":3143,"shareCount":-1,"useCount":1,"cancelCount":0,"surplus":-2},{"id":3144,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":3153,"shareCount":4294967293,"useCount":4839,"cancelCount":13,"surplus":4294962441},{"id":3154,"shareCount":6542450939,"useCount":4257,"cancelCount":12,"surplus":6542446670},{"id":3193,"shareCount":-1,"useCount":4,"cancelCount":0,"surplus":-5},{"id":3201,"shareCount":3114,"useCount":3081,"cancelCount":33,"surplus":0},{"id":3205,"shareCount":2534,"useCount":2063,"cancelCount":23,"surplus":448},{"id":3206,"shareCount":300,"useCount":281,"cancelCount":12,"surplus":7},{"id":3275,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":3341,"shareCount":299,"useCount":116,"cancelCount":1,"surplus":182},{"id":3544,"shareCount":-1,"useCount":6,"cancelCount":0,"surplus":-7},{"id":3553,"shareCount":99999,"useCount":178,"cancelCount":1,"surplus":99820},{"id":3569,"shareCount":109999,"useCount":431,"cancelCount":19,"surplus":109549},{"id":3717,"shareCount":500,"useCount":0,"cancelCount":0,"surplus":500},{"id":3718,"shareCount":500,"useCount":0,"cancelCount":0,"surplus":500},{"id":3770,"shareCount":14999,"useCount":3643,"cancelCount":16,"surplus":11340},{"id":3807,"shareCount":3999,"useCount":1696,"cancelCount":0,"surplus":2303},{"id":3885,"shareCount":499,"useCount":384,"cancelCount":0,"surplus":115},{"id":3944,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":4097,"shareCount":371,"useCount":368,"cancelCount":0,"surplus":3},{"id":4100,"shareCount":101,"useCount":0,"cancelCount":0,"surplus":101},{"id":4101,"shareCount":161,"useCount":155,"cancelCount":0,"surplus":6},{"id":4103,"shareCount":1269,"useCount":899,"cancelCount":0,"surplus":370},{"id":4140,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":4256,"shareCount":0,"useCount":0,"cancelCount":0,"surplus":0},{"id":4331,"shareCount":500,"useCount":0,"cancelCount":0,"surplus":500},{"id":4332,"shareCount":500,"useCount":0,"cancelCount":0,"surplus":500},{"id":4337,"shareCount":100,"useCount":47,"cancelCount":0,"surplus":53},{"id":4420,"shareCount":19999,"useCount":12661,"cancelCount":57,"surplus":7281},{"id":4453,"shareCount":10000,"useCount":153,"cancelCount":6,"surplus":9841},{"id":4454,"shareCount":10000,"useCount":138,"cancelCount":0,"surplus":9862},{"id":4460,"shareCount":665,"useCount":105,"cancelCount":0,"surplus":560},{"id":4528,"shareCount":100,"useCount":100,"cancelCount":0,"surplus":0},{"id":4549,"shareCount":999,"useCount":24,"cancelCount":0,"surplus":975},{"id":4556,"shareCount":-1,"useCount":1,"cancelCount":0,"surplus":-2},{"id":4571,"shareCount":499,"useCount":405,"cancelCount":1,"surplus":93},{"id":4580,"shareCount":94000,"useCount":20404,"cancelCount":544,"surplus":73052},{"id":4594,"shareCount":1499,"useCount":191,"cancelCount":0,"surplus":1308},{"id":4595,"shareCount":9999,"useCount":54,"cancelCount":0,"surplus":9945},{"id":4596,"shareCount":999,"useCount":72,"cancelCount":0,"surplus":927},{"id":4664,"shareCount":-1,"useCount":14,"cancelCount":0,"surplus":-15},{"id":4703,"shareCount":3499,"useCount":1581,"cancelCount":0,"surplus":1918},{"id":4704,"shareCount":-1,"useCount":5,"cancelCount":0,"surplus":-6},{"id":4745,"shareCount":-1,"useCount":82,"cancelCount":0,"surplus":-83},{"id":4828,"shareCount":5699,"useCount":5351,"cancelCount":61,"surplus":287},{"id":4897,"shareCount":99998,"useCount":316,"cancelCount":0,"surplus":99682},{"id":4898,"shareCount":99998,"useCount":1099,"cancelCount":290,"surplus":98609},{"id":4902,"shareCount":1005554,"useCount":28,"cancelCount":0,"surplus":1005526},{"id":4903,"shareCount":1055554,"useCount":22,"cancelCount":0,"surplus":1055532},{"id":4904,"shareCount":10099,"useCount":0,"cancelCount":0,"surplus":10099},{"id":4905,"shareCount":10099,"useCount":0,"cancelCount":0,"surplus":10099},{"id":5014,"shareCount":30,"useCount":0,"cancelCount":0,"surplus":30},{"id":5233,"shareCount":-1,"useCount":2,"cancelCount":0,"surplus":-3},{"id":5234,"shareCount":-1,"useCount":5,"cancelCount":0,"surplus":-6},{"id":5259,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":5465,"shareCount":9999,"useCount":41,"cancelCount":0,"surplus":9958},{"id":5795,"shareCount":845,"useCount":571,"cancelCount":4,"surplus":270},{"id":5908,"shareCount":499,"useCount":378,"cancelCount":0,"surplus":121},{"id":6106,"shareCount":1000,"useCount":0,"cancelCount":0,"surplus":1000},{"id":6200,"shareCount":67,"useCount":43,"cancelCount":0,"surplus":24},{"id":6251,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":6277,"shareCount":1000,"useCount":227,"cancelCount":3,"surplus":770},{"id":6982,"shareCount":300,"useCount":31,"cancelCount":0,"surplus":269},{"id":7281,"shareCount":20,"useCount":20,"cancelCount":0,"surplus":0},{"id":7283,"shareCount":80,"useCount":80,"cancelCount":0,"surplus":0},{"id":7311,"shareCount":400,"useCount":227,"cancelCount":0,"surplus":173},{"id":7347,"shareCount":1000,"useCount":490,"cancelCount":10,"surplus":500},{"id":7438,"shareCount":10000,"useCount":1615,"cancelCount":2,"surplus":8383},{"id":7501,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100},{"id":7592,"shareCount":1000,"useCount":41,"cancelCount":1,"surplus":958},{"id":7605,"shareCount":37200,"useCount":36435,"cancelCount":176,"surplus":589},{"id":7624,"shareCount":-1,"useCount":0,"cancelCount":0,"surplus":-1},{"id":7984,"shareCount":28001,"useCount":28001,"cancelCount":0,"surplus":0},{"id":8187,"shareCount":20100,"useCount":19966,"cancelCount":51,"surplus":83},{"id":8207,"shareCount":1999,"useCount":409,"cancelCount":102,"surplus":1488},{"id":8246,"shareCount":4876,"useCount":4017,"cancelCount":3,"surplus":856},{"id":8337,"shareCount":35000,"useCount":32290,"cancelCount":0,"surplus":2710},{"id":8574,"shareCount":300,"useCount":0,"cancelCount":0,"surplus":300},{"id":8827,"shareCount":9999,"useCount":1436,"cancelCount":13,"surplus":8550},{"id":8835,"shareCount":1000,"useCount":1,"cancelCount":0,"surplus":999},{"id":8858,"shareCount":100,"useCount":0,"cancelCount":0,"surplus":100}]';
        // waybill_share_actions id >= 962 id <= 1046
        $data = json_decode($jsonSDDY, true);
        foreach ($data as $index => $datum) {
            $company = Company::withTrashed()->where('source', Company::SOURCE_COMPANY_STATUS_YES)
                ->where('id', $datum['id'])->firstOrFail();
            if ($datum['useCount'] <= 0) {
                continue;
            }
            if ($datum['id'] <= 3553) { // 这里中断了
                continue;
            }
            $ret=WaybillShareAction::create([
                'user_id'    => $company->user_id,
                'shop_id'    => $company->shop_id,
                'shop_name'  => $company->shop_name,
                'name'       => $company->name,
                'company_id' => $company->id,
                'identifier' => $company->identifier??'',
                'branch_name'=> $company->branch_name,
                'wp_code'    => $company->wp_code,
                'wp_name'    => $company->wp_name,
                'balanceLimit' => $datum['useCount'],
                'province'   => $company->province,
                'city'       => $company->city,
                'district'   => $company->district,
                'detail'     => $company->detail,
                'action'     => WaybillShareAction::ACTION_STATUS_REMOVE,
            ]);
            $this->info('fixCompanyShare success:'.$company->id);
        }
    }

    private function testYac()
    {
        if (extension_loaded("yac")){
            $this->info('yac is loaded');
        } else{
            $this->info('yac is not loaded');
        }
        if (ini_get('yac.enable')==1){
            $this->info('yac is enable');
        }else{
            $this->info('yac is not enable');
        }
        if (ini_get('yac.enable_cli')==1){
            $this->info('yac cli is enable');
        }else{
            $this->info('yac cli is not enable');
        }
        $yac = new \Yac('test_');
        $yac->set('key', 'value');
        $this->info($yac->get('key'));
        $this->info('yac is ok');
    }

    /**
     * 获取淘宝物流公司列表
     * @return void
     * @throws \Exception
     */
    private function getTaobaoLogisticsCompanies()
    {
        $shopId = $this->option('shop_id') ?? null;
        if (empty($shopId)) {
            $this->info('shop_id invalid!');
            return;
        }
        $shop = Shop::query()->findOrFail($shopId);
        $taobaoWaybillService = new \App\Services\Waybill\Taobao\TaoBaoApi($shop->access_token);
        $companyList = $taobaoWaybillService->getAllCompany('');
        if (!empty($companyList)) {
            $this->info(json_encode($companyList));
        } else {
            $this->info('获取物流公司列表失败');
        }
    }

}
