<?php

namespace App\Console\Commands\WaybillHistory;

use App\Services\Waybill\CompanyService;
use App\Services\WaybillHistory\WaybillHistoryQueryService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

/**
 * 共享单号的历史统计
 */
class ShareWaybillStatisticsCommand extends Command
{
    protected $signature = 'command:share_waybill_statistics {--deadline=} {--shop_id=}';
    protected $description = '统计共享单号的历史数据';


    /**
     * @var CompanyService $companyService
     */
    protected $companyService;

    /**
     * @var WaybillHistoryQueryService $waybillHistoryQueryService
     */
    protected $waybillHistoryQueryService;


    public function __construct(CompanyService $companyService, WaybillHistoryQueryService $waybillHistoryQueryService)
    {
        parent::__construct();
        $this->companyService = $companyService;
        $this->waybillHistoryQueryService = $waybillHistoryQueryService;

    }

    public function handle()
    {

        $cache=redis('cache');


        $deadline = $this->option('deadline');
        $shopId = $this->option('shop_id');
        //如果没有设置了截止日期，就统计deadline就是当前时间前15天
        if (!$deadline) {
            $deadline = date('Y-m-d', strtotime('-15 days'));
        }

        Log::info('统计共享单号的历史数据', ["deadline" => $deadline, "shopId" => $shopId]);
        $shareCompanies = $this->companyService->queryShareCompanies($shopId);
        foreach ($shareCompanies as $company) {
            try {
                [$useCount, $cancelCount] = $this->waybillHistoryQueryService->getShareUseAndCancelCount($company, $deadline);
                //把数据放到redis中
                $cacheKey = 'share_waybill_statistics_' . $company->id;
                $cache->set($cacheKey, json_encode(['useCount' => $useCount, 'cancelCount' => $cancelCount, 'deadline' => $deadline]));
                Log::info("共享单号的历史数据统计", ["useCount" => $useCount, "cancelCount" => $cancelCount, "deadline" => $deadline, "companyId" => $company->id, "cacheKey" => $cacheKey]);
            } catch (\Exception $e) {
                Log::error("共享单号的历史数据统计失败", ["companyId" => $company->id, "deadline" => $deadline]);
            }

        }
    }
}
