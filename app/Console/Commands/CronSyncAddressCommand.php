<?php
/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2022/3/17
 * Time: 17:22
 */

namespace App\Console\Commands;

use App\Constants\PlatformConst;
use App\Constants\RedisKeyConst;
use App\Models\Address;
use App\Models\Shop;
use App\Services\Order\OrderServiceManager;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class CronSyncAddressCommand extends Command
{
    protected $signature = 'command:cron_sync_address';
    protected $description = '定时同步平台地址';

    public function handle()
    {
        $item = [
            'code' => 1, 'name' => '国内', 'parent_code' => 0, 'level' => Address::LEVEL_COUNTRY,
        ];
        Address::query()->updateOrCreate(['code' => $item['code']], $item);
        if (in_array(config('app.platform'), [PlatformConst::WX, PlatformConst::TAOBAO,PlatformConst::ALC2M])) {
            $data = json_decode(file_get_contents(resource_path('pca-code.json')), true);
            $addressList = $this->formatJsonAddress($data);
        } else {
            $shop = Shop::query()
                ->where('expire_at', '>', date('Y-m-d H:i:s'))
                ->where('auth_status', Shop::AUTH_STATUS_SUCCESS)
                ->firstOrFail();
            $orderService = OrderServiceManager::create();
            $orderService->setShop($shop);
            $addressList = $orderService->sendAddress();
        }
//        \Log::debug('$addressList',$addressList);
        $this->saveAddress($addressList);
        // 删除重复数据
        \DB::select("
    UPDATE address
SET deleted_at = NOW()
WHERE id IN (
    SELECT min_id
    FROM (
             SELECT MIN(id) AS min_id
             FROM address
             WHERE deleted_at IS NULL
             GROUP BY parent_code, name
             HAVING COUNT(*) > 1
         ) AS grouped
);
");
        Cache::set('address_version', date('Ymd_His'), 525600); // 1年
        Cache::delete(RedisKeyConst::ADDRESS_LIST_BY_1_2_3);
        Cache::delete(RedisKeyConst::ADDRESS_LIST_BY_DISTRICT_IDX);
        Cache::delete(RedisKeyConst::ADDRESS_LIST_BY_CODE_LEVEL_IDX);
    }

    protected function saveAddress(array $addressList, $parent_code = 1, $level = 1)
    {
        foreach ($addressList as $index => $item) {
            $values = array_only($item, ['code', 'name', 'parent_code', 'level']);
            Address::query()->updateOrCreate(['code' => $item['code'], 'parent_code' => $item['parent_code']], $values);
            if (!empty($item['sub'])) {
                $this->saveAddress($item['sub'], $item['code'], $level + 1);
            }
        }
        if ($level == 3) {
            $data = [
                'name' => Address::OTHER_DISTRICT_NAME,
                'code' => intval($parent_code . Address::OTHER_DISTRICT_CODE),
                'parent_code' => $parent_code,
                'level' => $level,
            ];
            Address::query()->updateOrCreate(['code' => $data['code'], 'parent_code' => $data['parent_code']], $data);
        }
    }

    private function formatJsonAddress(array $data, $parent_code = 1, $level = 1)
    {
        $list = [];
        foreach ($data as $index => $item) {
            $arr = [
                'name' => $item['name'],
                'code' => $item['code'],
                'parent_code' => $parent_code,
                'level' => $level,
            ];
            if (!empty($item['children'])) {
                $arr['sub'] = $this->formatJsonAddress($item['children'], $item['code'], $level + 1);
            }
            $list[] = $arr;
        }
        return $list;
    }
}
