<?php

namespace App\Console\Commands;

use App\Constants\PlatformConst;
use App\Jobs\OrderTraces\PackageInfo;
use App\Jobs\OrderTraces\SyncOrderTracesJob;
use App\Models\OrderTraceList;
use App\Models\Waybill;
use App\Models\WaybillHistory;
use App\Utils\Environment;
use App\Utils\OrderUtil;
use App\Utils\WaybillUtil;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

/**
 * 主动更新物流轨迹的策略
 * 前提
 * 各个平台会有物流轨迹的主动推送，但是有几种情况需要主动去更新
 * 1. 很多平台要发货以后，才会有物流轨迹的推送。比如抖音就没有，但是有物流轨迹的查询接口，需要自己查询。因为要实现预发货这样的逻辑，所以需要主动去查询物流轨迹
 * 2. 存在物流轨迹推送延迟或者丢失的情况
 * 要解决的问题
 * 1. 对于物流轨迹推送延迟或者丢失的情况，需要主动去更新（这部分考虑是针对latest_updated_at 8小时未更新的主动去查询），定时钟30分钟触发一次的，相当于30分钟会更新一次。
 * 2. 查询物流轨迹以后吧，最后的物流更新时间保存到latest_updated_at,如果物流一致没有更新，就一直是老的时间，就持续会更新。
 * 3. 如果单号回收，那相应的OrderTrace就进行真删除
 * 4. 如果订单签收或者拒收以后，这部分的OrderTrace就不进行更新。
 * 5. 积累下来的一直没有签收或者更新，上线是30天以内的才会更新。
 */
class CronSyncOrderTraceListCommand extends Command
{
//    protected $name        = 'command:cron-sync-order-trace-list';
    protected $signature = 'command:cron-sync-order-trace-list {--waybill_code=} {--shop_id=} {--tid=} {--order_shop_ids=} {--operation_shop_id=} {--redis_key=}';
    protected $description = '同步物流轨迹';
    /**
     *
     */

    const CHUNK_SIZE = 500;//每页500条查询

    /**
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 找出30天以内的未回收的电子面单记录。
     * @return void
     */
    public function handle()
    {
//        if (`in_array(config('app.name'), ['{dy-kddd}'])`){
//            Log::info("同步物流轨迹临时关闭");
//            return;
//        }

        $time = time();
        // 30天以前的就不同步了
        $str_min_created_at = Carbon::today()->subDay(30)->toDateTimeString();
        $lastUpdateTime = Carbon::now()->subHour(8)->toDateTimeString();
        $authSource = Waybill::getAuthSourceByPlatform(Waybill::OPEN_API_DEFAULT, null);
        if (!in_array($authSource, [
//            PlatformConst::WAYBILL_PDD,
            PlatformConst::WAYBILL_TB_TOP,
            PlatformConst::WAYBILL_DY,
            PlatformConst::WAYBILL_JD,
            PlatformConst::XHS
        ])) {
            Log::info("同步物流轨迹临时关闭");
            return;
        }
        $waybillCode = $this->option('waybill_code');
        $shopId = $this->option('shop_id');
        $tid = $this->option('tid');
        $orderShopIds = $this->option('order_shop_ids');
        $operationShopId = $this->option('operation_shop_id');
        $redisKey = $this->option('redis_key');


        //对于更新时间是8小时以前的，创建时间30天以内的，未签收&未拒签的进行同步
        $query = OrderTraceList::query()->select(['express_no', 'express_code', 'shop_id', 'tid', 'auth_source','updated_at']);
        if ($shopId) {
            $query->where('shop_id', $shopId);
        }
        if($orderShopIds){
            $query->whereIn('shop_id', $orderShopIds);
        }
        if($operationShopId){
            $query->join('packages','order_trace_list.express_no','packages.waybill_code');
            $query->where('order_trace_list.shop_id', $operationShopId);
        }

        if ($waybillCode) {
            $query->where('express_no', $waybillCode);
        }elseif(!empty($tid)){
            $tid=OrderUtil::appendOrderSuffix($tid);
            $query->where('tid', $tid);
        } else {
            $query->where('latest_updated_at', '>', $str_min_created_at);
            $query->whereNotNull('tid')->where(DB::raw('length(tid)'),'>',0);
            $query->where('latest_updated_at', '<=', $lastUpdateTime)
                ->whereNotIn('status', OrderTraceList::STATUS_STOP_SYNC);
//            if(Environment::isDy()||){
                //如果是抖音，因为有物流消息的主动推送，主要的手段都是消息推送，只有最后的更新时间和最后的物流更新时间都在8小时以内前的才进行同步
                $query->where('updated_at', '<=', $lastUpdateTime);
//            }
            $query->orderBy('updated_at', 'desc');
//        $query->where('auth_source', $authSource);
//            $query->limit(10000);
        }

        Log::info("查询未及时更新的物流轨迹数据", ["sql" => getSqlByQuery($query)]);
        $count = 0;

        $query->orderBy('latest_updated_at');
        $query->chunkById(self::CHUNK_SIZE, function ($orderTraceList) use (&$count) {
            //如果大于100000条，就停止同步
            if($count>100000){
                Log::error("同步物流轨迹结束 共有" . $count . "条物流轨迹需要同步");
                exit;
            }
            Log::info("开始同步第" . $count . "条物流轨迹");
            foreach ($orderTraceList as $orderTrace) {
                $count++;
                try {
                    if(empty($orderTrace->express_no)){
                        Log::info("同步物流轨迹失败，单号不存在", ["orderTrace" => $orderTrace->toArray()]);
                        continue;
                    }
                    $packageInfo = new PackageInfo();
                    $packageInfo->waybillCode = $orderTrace->express_no;
                    $packageInfo->wpCode = $orderTrace->express_code;
                    $packageInfo->shopId = $orderTrace->shop_id;
                    $packageInfo->tid = $orderTrace->tid;
                    $packageInfo->authSource = $orderTrace->auth_source ?? WaybillUtil::getCurrentAuthSource();
                    $packageInfo->updatedAt=$orderTrace->updated_at;

                    dispatch(new SyncOrderTracesJob($packageInfo));
                } catch (\Throwable $exception) {
//                    Log::error('SyncOrderTracesJob:'.$exception->getMessage(), $exception->getTrace());
                    Log::error('SyncOrderTracesJob:' . $exception->getMessage(), $exception->getTrace());
                }
            }
        });
        if ($redisKey) {
            $redis = \redis('cache');
            $redis->del($redisKey);
            Log::info("删除redis key: " . $redisKey);
        }
        Log::info("同步物流轨迹结束 共有" . $count . "条物流轨迹需要同步");
    }

//    /**
//     * @param Builder $query
//     * @param $lastId
//     * @param int $count
//     * @param \Closure $callback
//     * @return bool
//     * <AUTHOR>
//     */
//    private function chunkByIdAsc(Builder $query, $lastId,int $count, \Closure $callback): bool
//    {
//
//        $column = 'id';
//        $alias = $column;
//        $counter = 0;
//        do {
//            $counter++;
//            $clone = clone $query;
//
//            if (!is_null($lastId)) {
//                $clone->where($column, '>=', $lastId);
//            }else{
//                Log::info("lastId=null");
//            }
//            $pagestartime=microtime(true);
//            $results = $clone->orderBy($column, 'asc')->take($count)->get();
//
//            $totaltime = microtime(true)- $pagestartime;
//            $countResults = $results->count();
//            Log::info("lastId=".$lastId." cost=".$totaltime." count=".$countResults);
//
//            if ($countResults == 0) {
//                break;
//            }
//
//            // On each chunk result set, we will pass them to the callback and then let the
//            // developer take care of everything within the callback, which allows us to
//            // keep the memory low for spinning through large result sets for working.
//            if ($callback($results) === false) {
//                return false;
//            }
//
//            $lastId = $results->last()->{$alias};
//            Log::info("lastId=".$lastId);
//            unset($results);
//        } while ($countResults == $count);
//        return true;
//    }
}
