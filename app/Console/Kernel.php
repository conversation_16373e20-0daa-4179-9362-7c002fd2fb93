<?php

namespace App\Console;

use App\Console\Commands\ChangeDYTemplateUrlCommand;
use App\Console\Commands\ClearDiscardOrderTraceCmd;
use App\Console\Commands\ClearExpiredDeliveryRecordCommand;
use App\Console\Commands\ClearExpiredGiftShopData;
use App\Console\Commands\ClearExpiredGoodsCmd;
use App\Console\Commands\ClearExpiredOrderCmd;
use App\Console\Commands\ClearExpiredOrderTraceCmd;
use App\Console\Commands\ClearExpiredPackageCmd;
use App\Console\Commands\ClearExpiredShopCmd;
use App\Console\Commands\ClearExpiredShopDataCmd;
use App\Console\Commands\ClearExpiredWaybillHistoryCommand;
use App\Console\Commands\CronSyncAddressCommand;
use App\Console\Commands\CronSyncFactoryOrderCommand;
use App\Console\Commands\CronSyncOrderByDataPushCmd;
use App\Console\Commands\CronSyncOrderCommand;
use App\Console\Commands\CronSyncPlatformOrderCommand;
use App\Console\Commands\CronSyncRefundOrderCommand;
use App\Console\Commands\CronWitchWarnCommand;
use App\Console\Commands\Deliver\PreshipmentDeadlineDeliverCmd;
use App\Console\Commands\Deliver\PreshipmentLogisticAfterTimerDeliverCmd;
use App\Console\Commands\Deliver\PreshipmentLogisticScheduleTimerDeliverCmd;
use App\Console\Commands\Fix\FixEmptyWaybillHistoryCompanyId;
use App\Console\Commands\FixAfterSaleRefundStatusCommand;
use App\Console\Commands\FixHistoryOrderStatusCommand;
use App\Console\Commands\FixOrderRelationCommand;
use App\Console\Commands\FixPackageCompanyIdCommand;
use App\Console\Commands\GenApiAuthCmd;
use App\Console\Commands\Jd\CronSyncJdOrderByDataPushCmd;
use App\Console\Commands\Jd\SubscribeDbPushCmd;
use App\Console\Commands\Jd\SyncJdMqCommand;
use App\Console\Commands\OrdersBatchEncryptCommand;
use App\Console\Commands\PlatformOrder\CloseExpireUnpaidPlatformOrderCmd;
use App\Console\Commands\PushExpireShopToCommissionCommand;
use App\Console\Commands\PushShopToCommissionCommand;
use App\Console\Commands\RePushToCommissionCommand;
use App\Console\Commands\RunOnceCommand;
use App\Console\Commands\SubscribeMsgCommand;
use App\Console\Commands\SubscribeMsgWsCommand;
use App\Console\Commands\Subscription\MigrateFreePlatformOrder;
use App\Console\Commands\SyncCheckShopSwitchCommand;
use App\Console\Commands\SyncOrderByScope;
use App\Console\Commands\System\ClearFailedJobsCmd;
use App\Console\Commands\System\ClearPrintRecordsCmd;
use App\Console\Commands\ToolCommand;
use App\Console\Commands\WaybillHistory\ShareWaybillStatisticsCommand;
use App\Console\Commands\WaybillHistoryStatisticsCommand;
use App\Console\Commands\Xhs\XhsGetGoodsBySkuIdCmd;
use App\Console\Commands\Xhs\XhsSearchGoodsCmd;
use App\Console\Commands\Xhs\XhsSyncOrderTraceCmd;
use App\Constants\PlatformConst;
use App\Utils\Environment;
use Illuminate\Console\Scheduling\Schedule;
use Laravel\Lumen\Application;
use Laravel\Lumen\Console\Kernel as ConsoleKernel;

//use App\Console\Commands\CronHistoryGoodsCommand;

class Kernel extends ConsoleKernel
{
    public function __construct(Application $app)
    {
        parent::__construct($app);
        !defined('REQ_ID') && define('REQ_ID', uniqid());
        !defined('LARAVEL_START') && define('LARAVEL_START', microtime(true));
    }

    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \App\Console\Commands\RefreshUsersToken::class,
        \App\Console\Commands\CheckAndReduceUserEdition::class,
        \App\Console\Commands\CronSyncGoodsCommand::class,
        \App\Console\Commands\CronSyncOrderTraceListCommand::class,
        \App\Console\Commands\ClearInvalidShopDataCommand::class,
        \App\Console\Commands\ClearInvalidOrderDataCommand::class,
        \App\Console\Commands\CronSyncWaybillQuantityCommand::class,
        \App\Console\Commands\UserUsedStatisticsCommand::class,
        \App\Console\Commands\UserOrdersSummaryCommand::class,
        \App\Console\Commands\UserStatisticsCommand::class,
        \App\Console\Commands\UserWaybillhistoriesCommand::class,
        \App\Console\Commands\OrdersMd5AddressCommand::class,
        \Laravelista\LumenVendorPublish\VendorPublishCommand::class,
        \App\Console\Commands\Test::class,
        SubscribeMsgCommand::class,
        CronSyncOrderCommand::class,
        CronSyncFactoryOrderCommand::class,
        CronSyncRefundOrderCommand::class,
        SyncCheckShopSwitchCommand::class,
        SubscribeMsgWsCommand::class,
        ToolCommand::class,
        RunOnceCommand::class,
        FixHistoryOrderStatusCommand::class,
        FixOrderRelationCommand::class,
        FixAfterSaleRefundStatusCommand::class,
        SyncOrderByScope::class,
        CronSyncPlatformOrderCommand::class,
        ClearExpiredOrderCmd::class,
        ClearExpiredShopCmd::class,
        OrdersBatchEncryptCommand::class,
        ChangeDYTemplateUrlCommand::class,
        WaybillHistoryStatisticsCommand::class,
        CronSyncAddressCommand::class,
        ClearFailedJobsCmd::class,
        ClearPrintRecordsCmd::class,
//        CronHistoryGoodsCommand::class,
        PushExpireShopToCommissionCommand::class,
        RePushToCommissionCommand::class,
        ClearExpiredWaybillHistoryCommand::class,
        ClearExpiredGoodsCmd::class,
        PushShopToCommissionCommand::class,
        ClearExpiredOrderTraceCmd::class,
        ClearExpiredDeliveryRecordCommand::class,
        ClearExpiredPackageCmd::class,
        ClearExpiredShopDataCmd::class,
        ClearExpiredGiftShopData::class,
        CronSyncOrderByDataPushCmd::class,
        GenApiAuthCmd::class,
        CronWitchWarnCommand::class,
        PreshipmentLogisticScheduleTimerDeliverCmd::class,
        PreshipmentLogisticAfterTimerDeliverCmd::class,
        PreshipmentDeadlineDeliverCmd::class,
        FixPackageCompanyIdCommand::class,
        CronSyncJdOrderByDataPushCmd::class,
        SyncJdMqCommand::class,
        SubscribeDbPushCmd::class,
        ShareWaybillStatisticsCommand::class,
        XhsGetGoodsBySkuIdCmd::class,
        XhsSearchGoodsCmd::class,
        XhsSyncOrderTraceCmd::class,
        ClearDiscardOrderTraceCmd::class,
        MigrateFreePlatformOrder::class,
        CloseExpireUnpaidPlatformOrderCmd::class,
        FixEmptyWaybillHistoryCompanyId::class

    ];

    /**
     * Define the application's command schedule.
     * @param Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //没半小时执行一次触发一次刷新token
        $schedule->command('command:refresh-users-token')->cron('*/5 * * * *');
        //每天执行
        $schedule->command('command:check-and-reduce-user-edition')->dailyAt("00:10");

        //面单余额更新
//        $schedule->command('command:cron-sync-waybill-quantity')->everyFiveMinutes();

        if (Environment::isDy()) {
            //物流轨迹更新
            $schedule->command('command:cron-sync-order-trace-list')
                ->withoutOverlapping()->everyThirtyMinutes();
        }
        if(Environment::isXhs()){
            //物流轨迹更新
            $schedule->command('command:cron-sync-order-trace-list')
                ->withoutOverlapping()->everyTenMinutes();
        }

        //每天1次
        //$schedule->command('command:cron-sync-goods')->dailyAt("01:00");
//        $schedule->command('command:cron-sync-goods')->everyTenMinutes();
        // 同步地址
        $schedule->command('command:cron_sync_address')->cron('30 1 * * 0'); // 每个星期日凌晨1点30分执行

        //厂家代打订单
//        $schedule->command('command:cron-sync-factory-order')->everyFiveMinutes();
        if (PlatformConst::DY == config('app.platform')) {
            //增量拉取订单 抖音 5 分钟一次
            $schedule->command('command:cron-sync-order')->everyFiveMinutes();
            // 增量拉取数据推送订单
//            $schedule->command('command:syncOrderByDataPush')->everyMinute();
        }elseif (Environment::isTaoBao()) {
            //增量拉取订单 淘宝 2 分钟一次
            $schedule->command('command:cron-sync-order')->cron('*/2 * * * *');
        }elseif (Environment::isWxsp()) {
            //增量拉取订单 1 分钟一次
            $schedule->command('command:cron-sync-order')->everyMinute();
        }elseif (Environment::isAlbb()) {
            //增量拉取订单 10 分钟一次
            $schedule->command('command:cron-sync-order')->everyTenMinutes();
        }else{
            //增量拉取订单 5 分钟一次
            $schedule->command('command:cron-sync-order')->everyFiveMinutes();
        }
        //$schedule->command('command:cron-sync-refund-order')->everyFiveMinutes();

        //每日统计
        $schedule->command('command:user-shop-statistics')->everyThirtyMinutes();
        //总体数据统计
        $schedule->command('command:user-orders-summary')->cron('*/20 * * * *');
        //用户统计
        $schedule->command('command:user-statistics')->everyThirtyMinutes();
        //解绑授权店铺数据清理
//        $schedule->command('command:clear-invalid-shop-data')->dailyAt("06:05");
        //订单数据定时清理
//	    $schedule->command('command:clear-invalid-order-data')->dailyAt('02:02');

        if (PlatformConst::TAOBAO == config('app.platform')) {
            //根据最后一次打印时间检查店铺开关
            $schedule->command('command:sync-check-shop-switch')->dailyAt('00:20');
        }

        //快手拉取服务市场订单
        if (config('app.platform') != 'dy' || config('app.platform') != 'taobao_top') {
            $schedule->command('command:cron-sync-platform-order')->dailyAt('03:00');
        }
        // 清理过期店铺
        $schedule->command('clear:expired-shop')->everyMinute();

        $schedule->command('clear:expired-goods')->dailyAt('23:10');
        // 清理过期订单
        $schedule->command('clear:expired-order')->dailyAt('00:30');
        //清理过期的包裹
//        $schedule->command('clear:expired-order')->dailyAt('00:45');
        // 清理过期订单轨迹
        $schedule->command('clear:expired-order-trace')->dailyAt('00:10');
        $schedule->command('clear:expired-package')->dailyAt('00:30');
        //清理过期的打印记录
        $schedule->command('clear:expired-delivery-record')->dailyAt('01:10');

        // 取号记录日结
//        $schedule->command('command:waybill_history_statistics')->dailyAt('02:00');
        //清理失败的任务
        $schedule->command('command:clear-failed-jobs')->dailyAt('06:00');
        //清理打印的历史记录
        $schedule->command('command:clear-print-records-jobs')->dailyAt('05:00');
        //清理取号记录
        $schedule->command('clear:expired-waybill-history')->dailyAt('01:00')->withoutOverlapping();
        // 定时监控报警
        $schedule->command('cron:witch-warn')->everyFiveMinutes();
        //统计共享单号的使用情况
        $schedule->command('command:share_waybill_statistics')->dailyAt('05:30');

        //清理历史假删除商品,历史数据清理完了,这个可以去掉
//        $schedule->command('command:cron-history-goods')->dailyAt('07:00');


        if (config('app.platform') == PlatformConst::TAOBAO) {
            //淘宝消息订阅接口版本
            $schedule->command('command:subscribe-msg')->everyTenMinutes();
            //淘宝推送当天到期店铺到结算系统
            $schedule->command('command:push-expire-shop-to-commission')->dailyAt('00:10');
            //淘宝推送当天到期店铺到结算系统
            $schedule->command('command:repush-to-commission')->dailyAt('00:30');
            //淘宝推送当天到期店铺到结算系统
            $schedule->command('command:push-shop-to-commission')->dailyAt('01:00');
        }

        if(Environment::isDy()){
            $schedule->command(PreshipmentDeadlineDeliverCmd::PRESHIPMENT_LOGISTIC_DEADLINE_TIMER_DELIVER)->everyFiveMinutes();
            $schedule->command(PreshipmentLogisticAfterTimerDeliverCmd::PRESHIPMENT_LOGISTIC_AFTER_TIMER_DELIVER)->everyFiveMinutes();
            $schedule->command(PreshipmentLogisticScheduleTimerDeliverCmd::PRESHIPMENT_LOGISTIC_SCHEDULE_TIMER_DELIVER)->everyFiveMinutes();
        }
        // 阿里C2M或者小红书，因为是自己做的订购支付，所以需要定时关闭过期未支付的订单
        if(Environment::isAlC2M()||Environment::isXhs()){
            $schedule->command(CloseExpireUnpaidPlatformOrderCmd::COMMAND_CLOSE_EXPIRE_UNPAID_PLATFORM_ORDER)->everyFiveMinutes()->withoutOverlapping();
        }
    }
}
