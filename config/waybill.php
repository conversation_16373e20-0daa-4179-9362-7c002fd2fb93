<?php

use App\Constants\PlatformConst;

return [
    PlatformConst::PDD_WB => [
        'client_id' => env('PDD_WB_CLIENT_ID', 'f42d095e23d14540956b3e5bf8db0743'),
        'client_secret' => env('PDD_WB_CLIENT_SECRET', '39b3812c2c72547f5609e16070c215ae2e2dcf70'),
        'code_url' => env('PDD_WB_CODE_URL', 'https://wb.pinduoduo.com/logistics/auth'),
        'token_url' => env('PDD_WB_TOKEN_URL', 'https://open-api.pinduoduo.com/oauth/token'),
        'code_redirect_url' => env('PDD_WB_CODE_REDIRECT_URL', 'http://ddcn.mayiapps.cn/pddwb'),
        'redirect_url' => env('PDD_WB_REDIRECT_URL', 'http://renrenkd.ks.shandiankd.cn/platform/oauth/pdd'),
    ],
    PlatformConst::TWC => [
        'appkey' => env('TWC_APPKEY', '20191215'),
        'secret' => env('TWC_SECRET', '316ada12d5cf340b95cd8725f73ffe8f'),
        'token_url' => env('TWC_TOKEN_URL', 'https://oauth.taobao.com/authorize?response_type=code&client_id=23770680&redirect_uri=http://xqcn.souyousoft.com/callback/entry'),
        'redirect_url' => env('TWC_REDIRECT_URL', 'http://ks-auth.menggouchaquan.cn/platform/oauth/taobao')
    ],
    PlatformConst::NEW_TWC => [
        'appkey' => env('NEW_TWC_APPKEY', '33160165'),
        'secret' => env('NEW_TWC_SECRET', '2b8e7c3da55e13fd62a2f5d8d44ebbe8'),
        'code_url' => env('NEW_TWC_CODE_URL', 'https://oauth.taobao.com/authorize'),
        'token_url' => env('NEW_TWC_TOKEN_URL', 'http://ddcn.mayiapps.cn/token'),
        'code_redirect_url' => env('NEW_TWC_CODE_REDIRECT_URL', 'http://ddcn.mayiapps.cn/callback/got'),
        'redirect_url' => env('NEW_TWC_REDIRECT_URL', 'http://renrenkd.ks.chengwithle.com/platform/oauth/taobao')
    ],
    PlatformConst::LINK => [
        'appkey' => env('LINK_APPKEY', '176402'),
        'secret' => env('LINK_SECRET', '2K15oB33KMtAFg7HVjkM7YfXh301NN96'),
        'code_url' => env('LINK_CODE_URL', 'https://lcp.cloud.cainiao.com/permission/isv/grantpage.do'),
        'token_url' => env('LINK_TOKEN_URL', 'http://ddcn.chengwithle.com/api/permission/exchangeToken.do'),
        'code_redirect_url' => env('LINK_CODE_REDIRECT_URL', 'http://ddcn.chengwithle.com/cnlink'),
        'redirect_url' => env('LINK_REDIRECT_URL', 'http://renrenkd.ks.chengwithle.com/platform/oauth/cnlink')
    ],
    PlatformConst::TB => [
        'appkey' => env('TB_CLIENT_ID', '21448879'),
        'secret' => env('TB_CLIENT_SECRET', '16ddc62014fcc43de2fb9c08b52ef959'),
        'code_url' => env('NEW_TWC_CODE_URL', 'https://oauth.taobao.com/authorize'),
        'token_url' => env('NEW_TWC_TOKEN_URL', 'http://ddcn.mayiapps.cn/token'),
        'code_redirect_url' => env('NEW_TWC_CODE_REDIRECT_URL', 'http://ddcn.mayiapps.cn/callback/got'),
        'redirect_url' => env('TB_CLIENT_RDIRECT', 'http://bbkd.menggouchaquan.cn/callback/taobao'),
    ],

    PlatformConst::DY => [
        'appkey' => env('DY_CLIENT_ID', '21448879'),
        'secret' => env('DY_CLIENT_SECRET', '16ddc62014fcc43de2fb9c08b52ef959'),
        'service_id' => env('SERVICE_ID', 31),
        'code_url' => env('DY_CODE_URL' ),
        'jump_url' => env('DY_WB_REDIRECT_URL')
    ],

    'shareWaybill'=>[
        'calculateByCompanyId'=>env('SHARE_WAYBILL_COUNT_BY_COMPANY_ID',false) ,   //通过company_id统计共享电子面单
    ]
];
