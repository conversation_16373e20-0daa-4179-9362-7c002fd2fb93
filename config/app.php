<?php

use App\Constants\PlatformConst;

return [
    /*
    |--------------------------------------------------------------------------
    | Application Name
    |--------------------------------------------------------------------------
    |
    | This value is the name of your application. This value is used when the
    | framework needs to place the application's name in a notification or
    | any other location as required by the application or its packages.
     */

    'name' => env('APP_NAME', 'ALI-RED-PACKET'),

    /*
    |--------------------------------------------------------------------------
    | Application Environment
    |--------------------------------------------------------------------------
    |
    | This value determines the "environment" your application is currently
    | running in. This may determine how you prefer to configure various
    | services your application utilizes. Set this in your ".env" file.
    |
     */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | Application Debug Mode
    |--------------------------------------------------------------------------
    |
    | When your application is in debug mode, detailed error messages with
    | stack traces will be shown on every error that occurs within your
    | application. If disabled, a simple generic error page is shown.
    |
     */

    'debug' => env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | Application URL
    |--------------------------------------------------------------------------
    |
    | This URL is used by the console to properly generate URLs when using
    | the Artisan command line tool. You should set this to the root of
    | your application so that it is used when running Artisan tasks.
    |
     */

    'url' => env('APP_DOMAIN', 'http://dev.local'),

    /*
    |--------------------------------------------------------------------------
    | Application Timezone
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default timezone for your application, which
    | will be used by the PHP date and date-time functions. We have gone
    | ahead and set this to a sensible default for you out of the box.
    |
     */

    'timezone' => 'Asia/Shanghai',

    /*
    |--------------------------------------------------------------------------
    | Application Locale Configuration
    |--------------------------------------------------------------------------
    |
    | The application locale determines the default locale that will be used
    | by the translation service provider. You are free to set this value
    | to any of the locales which will be supported by the application.
    |
     */

    'locale' => 'zh-CN',

    /*
    |--------------------------------------------------------------------------
    | Application Fallback Locale
    |--------------------------------------------------------------------------
    |
    | The fallback locale determines the locale to use when the current one
    | is not available. You may change the value to correspond to any of
    | the language folders that are provided through your application.
    |
     */

    'fallback_locale' => 'en',

    /*
    |--------------------------------------------------------------------------
    | Encryption Key
    |--------------------------------------------------------------------------
    |
    | This key is used by the Illuminate encrypter service and should be set
    | to a random, 32 character string, otherwise these encrypted strings
    | will not be safe. Please do this before deploying an application!
    |
     */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the log settings for your application. Out of
    | the box, Laravel uses the Monolog PHP logging library. This gives
    | you a variety of powerful log handlers / formatters to utilize.
    |
    | Available Settings: "single", "daily", "syslog", "errorlog"
    |
     */

//    'log' => env('APP_LOG', 'daily'),

//    'log_level' => env('APP_LOG_LEVEL', 'warning'),
    'log_viewer' => env('APP_LOG_VIEWER', false),
    'ide_helper' => env('APP_IDE_HELPER', false),
    'debug_bar' => env('DEBUGBAR_ENABLED', false),

    'aliases' => [
    ],
    /*
    |--------------------------------------------------------------------------
    | auth Configuration
    |--------------------------------------------------------------------------
    |
     */
    'auth' => [
        'b_client' => [
            'key' => env('API_KEY_B_CLIENT', '9f3c14b33398cbaa1989112bbfec4cdecbce1534'),
            'time_difference' => 3600, //token有效期1小时
        ],
    ],

    //sql日志配置
    'sql_log_query' => env('SQL_LOG_QUERY', false),
    //日志配置
    'path_storage' => env('PATH_STORAGE'), //指定日志存放路径，绝对路径
    'app_name' => env('APP_NAME', gethostname()),
    'ych' => [
        'app_key' => env('YCH_APP_KEY', '68756717'),
        'secret' => env('YCH_SECRET', 'wiLQJxqWhqFmG4pIBAaP'),
        'tb_app_key' => env('TB_CLIENT_ID', ''),
        'tb_app_name' => env('TB_APP_NAME', '快递侠'),
        'user_ip' => env('YCH_USER_IP', '127.0.0.1'),
    ],
    'app_domain' => env('APP_DOMAIN'),
    'service_id' => env('SERVICE_ID'),

//    'platform' => PlatformConst::KS,
    'platform' => env('CURRENT_PLATFORM'),
    //厂家版本 1是 0否
    'factory' => env('FACTORY_MODE', 0),
    'sync_order_limit_whitelist' => env('SYNC_ORDER_LIMIT_WHITELIST', ''), //同步订单上线白名单 逗号分割
    'open_api_whitelist' => env('OPEN_API_WHITELIST', ''), //同步订单上线白名单 逗号分割
    'shop_push_url' => env('SHOP_PUSH_URL', ''), //店铺 推送地址
    'new_site_auth_url' => env('NEW_SITE_AUTH_URL', ''), //新站点 授权地址
    'new_site_whitelist' => env('NEW_SITE_WHITELIST', ''), //新站点 白名单 逗号分割
    'sso_url' => env('SSO_URL'), // https://dy-sso.kddadan.com,
];
