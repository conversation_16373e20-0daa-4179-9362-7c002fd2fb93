<?php
return [
    'RequestEnable' => env("XHPROF_REQUEST_ENABLE", "false") == 'true',
    'RequestRate'=>env("XHPROF_REQUEST_RATE",0),
    'RequestPaths'=>explode(',',env("XHPROF_REQUEST_PATH",'')),
    'QueueEnable' => env("XHPROF_QUEUE_ENABLE", "false") == 'true',
    'Queues' =>explode(',',env("XHPROF_QUEUES",'')),
    'QueueRate'=>env("XHPROF_QUEUE_RATE",0),
    'CommandEnable' => env("XHPROF_COMMAND_ENABLE", "false") == 'true',
    'Commands' => explode(',',env("XHPROF_COMMANDS",'')),
    'CommandRate'=>env("XHPROF_COMMAND_RATE",0),
];
