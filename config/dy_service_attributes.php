<?php
/**
 * Created by PhpStorm.
 * User: x<PERSON><PERSON><PERSON><PERSON>
 * Date: 2022/1/4
 * Time: 11:50
 */

use App\Constants\WaybillConst;

return [
    //极兔
    'jtexpress' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":10000,"max":"3000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '生鲜件',
            'service_name' => '生鲜件',
            'service_code' => 'SVC-FRESH',
            'service_attributes' => [
                [
                    'attribute_name' => '生鲜件',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"":"生鲜件"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"0":"标快","TYD":"兔优达"}'

                ]
            ]
        ],

    ],
    //中通
    'zhongtong' => [
//        [
//            'required' => false,
//            'service_desc' => '音尊达',
//            'service_name' => '音尊达',
//            'service_code' => 'SVC-WBHOMEDELIVERY',
//            'service_attributes' => [
//                [
//                    'attribute_name' => '音尊达',
//                    'attribute_type' => 'enum',
//                    'attribute_code' => 'value',
//                    'type_desc'      => '{"":"音尊达"}'
//                ]
//            ]
//        ],
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":3000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => 'T3COD',
            'service_name' => 'T3COD',
            'service_code' => 'SVC-T3COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => 'T1COD',
            'service_name' => 'T1COD',
            'service_code' => 'SVC-T1COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"SVC-TIMING-STANDARD":"中通好快 (原标快)","SVC-VIP":"中通标快 (原尊享)","SVC-TIMING-HIGH":"中通飞快 (原特快)"}'
                ]
            ]
        ],

    ],
    //百世
    'huitongkuaidi' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":1000000}'
                ]
            ]
        ],
    ],
    //韵达
    'yunda' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":3000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":600,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"byte_pt":"标快","byte_tk":"特快"}'
                ]
            ]
        ]

    ],
    //圆通
    'yuantong' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":3000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"TCTK":"同城特快","DYHKJ":"航空件","YTSXD":"圆通时效达"}'
                ]
            ]
        ]
    ],
    //申通
    'shentong' => [
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":1000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":100,"max":2000000}'
                ]
            ]
        ],
        [

            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"FRESH_EXPRESS_TYPE":"生鲜件"}'
                ]
            ]
        ]

    ],
    //众邮
    'zhongyouex' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":50000,"max":3000000}'
                ]
            ]
        ],
    ],
    //邮政快递包裹
    'youzhengguonei' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":2000000}'
                ]
            ]
        ],
    ],
    //邮政
    'ems' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":5000000}'
                ],
            ]
        ],
        [
            'required' => false,
            'service_desc' => '密码投递',
            'service_name' => '密码投递',
            'service_code' => 'SVC-PASSWORD',
            'service_attributes' => [
                [
                    'attribute_name' => '密码投递',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"":"密码投递"}'
                ]
            ]
        ],
    ],
    //京东
    'jd' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":6000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":3000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '温层',
            'service_name' => '温层',
            'service_code' => 'SVC-TEM',
            'service_attributes' => [
                [
                    'attribute_name' => '温层',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"2":"常温","7":"冷藏","8":"冷冻","9":"深冷"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => WaybillConst::JD_PRODUCT_TYPE
                ]
            ]
        ]
    ],
    //德邦物流
    'debangwuliu' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"100","max":"100000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"100","max":"100000000"}'
                ],
                [
                    'attribute_name' => '代收类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value1',
                    'type_desc' => '{"1":"一日退","3":"三日退"}'
                ],
                [
                    'attribute_name' => '客户开户名',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value2',
                    'type_desc' => '{"1":"一日退","3":"三日退"}'
                ],
                [
                    'attribute_name' => '客户开户账号',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value3',
                    'type_desc' => '{"maxLength":128,"minLength":1,"required":true,"type":"string"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"RCP":"大件快递360","NZBRH":"重包入户","ZBTH":"重包特惠","WXJTH":"微小件特惠","PACKAGE":"标准快递","DEAP":"特准快件","TZKJC":"特快专递","ZYSCZD":"泡货特惠","DSZH":"重货特惠","NFLF":"精准卡航","NLRF":"精准汽运","NJZZH":"精准重货","TKDR":"特快当日","HKDJC":"特快次日","HKDJG":"航空大件隔日达","DPTL":"DP联运(K)","TKLY":"DP联运(L)"}'
                ]
            ]
        ]
    ],
    //优速
    'youshuwuliu' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"5000000"}'
                ]
            ]
        ],
    ],
    //顺丰快运
    'shunfengkuaiyun' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"10000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"3000000"}'
                ],
                [
                    'attribute_name' => '代收货款卡号',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value1',
                    'type_desc' => ''
                ]
            ]
        ],
        [

            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"SE0141":"快运标准达 (新增)","SE0088":"顺丰干配 (新增)","SE0114":"大票直送","S1":"顺丰特快","S2":"顺丰标快","SE0122":"特惠专配","SE0091":"专线普运","SE0130":"特惠件","SE010101":"纯重特配"}'
                ]
            ]
        ]
    ],
    //丹鸟
    'danniao' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"2000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"2000000"}'
                ]
            ]
        ],
    ],
    //安能物流
    'annengwuliu' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"2000","max":"500000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"546":"安心达","23":"定时达","270":"普惠达","524":"MiNi电商小件","95":"MiNi电商大件","24":"标准快运"}'

                ]
            ]
        ],

    ],
    //顺心捷达
    'sxjdfreight' => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"2000","max":"500000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"2000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '接货方式',
            'service_name' => '接货方式',
            'service_code' => 'SVC-RECEIVE-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '接货方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"SEND":"上门接货","SELF":"客户自送"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '送货方式',
            'service_name' => '送货方式',
            'service_code' => 'SVC-DELIVERY-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '送货方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"SEND_NO_ELEVATOR":"送货上楼（无电梯）","SELF":"自提","SEND_HAS_ELEVATOR":"送货上楼（有电梯）","SEND_NO_UPSTAIRS":"送货（不含上楼）"}'
                ]
            ]
        ],
    ],
    //中通快运
    "zhongtongkuaiyun" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"2000","max":"500000"}'
                ]
            ]
        ],
    ],
    //D速快递 没有匹配的code
    //快弟来了
    "xlair" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"2000","max":"500000"}'
                ]
            ]
        ],
    ],
    //中通冷链
    "ztocc" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"500000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '温层',
            'service_name' => '温层',
            'service_code' => 'SVC-TEM',
            'service_attributes' => [
                [
                    'attribute_name' => '温层',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"7":"冷藏","8":"冷冻"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '送货方式',
            'service_name' => '送货方式',
            'service_code' => 'SVC-DELIVERY-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '送货方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"SELF":"自提","SEND":"派送"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '签单方式',
            'service_name' => '签单方式',
            'service_code' => 'SVC-SIGN-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '签单方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"PAPER_PAPER":"纸质/纸质","PAPER_ELECTRONIC":"纸质/电子","ELECTRONIC_PAPER":"电子/纸质","ELECTRONIC_ELECTRONIC":"电子/电子"}'
                ]
            ]
        ],
        [
        'required' => false,
        'service_desc' => '产品类型',
        'service_name' => '产品类型',
        'service_code' => 'product_type',
        'service_attributes' => [
            [
                'attribute_name' => '产品类型',
                'attribute_type' => 'enum',
                'attribute_code' => 'value',
                'type_desc' => '{"SINGLE":"单件", "LCL":"零担"}'

            ]
        ]
    ],
    ],
    //跨越速运
    "kuayue" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"100000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":100000000}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '签回单',
            'service_name' => '签回单',
            'service_code' => 'SVC-SIGN-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '签回单',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"回单原件","2":"回单照片"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '包装方式',
            'service_name' => '包装方式',
            'service_code' => 'SVC-PKFEE',
            'service_attributes' => [
                [
                    'attribute_name' => '包装方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"打卡板","2":"打木架","3":"打木箱"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"10":"当天达","20":"次日达","30":"隔日达","40":"陆运件","210":"空运","220":"专运","160":"省内次日","170":"省内即日","50":"同城次日","70":"同城即日"}'

                ]
            ]
        ],
    ],
    //韵达快运
    "yundakuaiyun" => [
        [
            'required' => false,
            'service_desc' => '送货方式',
            'service_name' => '送货方式',
            'service_code' => 'SVC-DELIVERY-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '送货方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"派送","2":"送货上楼","3":"自提"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"100000","max":"20000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '业务类型',
            'service_name' => '业务类型',
            'service_code' => 'SVC-BUSINESS',
            'service_attributes' => [
                [
                    'attribute_name' => '业务类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"0":"默认","1":"韵准达","2":"粤准达","3":"168大件","4":"京津冀电商"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '货物类型',
            'service_name' => '货物类型',
            'service_code' => 'SVC-ITEM',
            'service_attributes' => [
                [
                    'attribute_name' => '货物类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"正常货物","2":"易碎品","3":"药品类"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '签回单',
            'service_name' => '签回单',
            'service_code' => 'SVC-SIGN-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '签回单',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"是","0":"否"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' =>  '{"2":"电商件"}'

                ]
            ]
        ],
    ],
    //京东大件
    "jingdongdajian" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"30000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '代收货款',
            'service_name' => '代收货款',
            'service_code' => 'SVC-COD',
            'service_attributes' => [
                [
                    'attribute_name' => '代收金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":0,"max":1000000000}'
                ]
            ]
        ],
    ],
    //京东快运
    "jingdongkuaiyun" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":"0","max":"5000000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '包装方式',
            'service_name' => '包装方式',
            'service_code' => 'SVC-PKFEE',
            'service_attributes' => [
                [
                    'attribute_name' => '包装方式',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"":"包装方式"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '上楼',
            'service_name' => '上楼',
            'service_code' => 'SVC-DELIVERY-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '上楼',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"":"上楼"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '进仓',
            'service_name' => '进仓',
            'service_code' => 'SVC-fr-a-0008',
            'service_attributes' => [
                [
                    'attribute_name' => '进仓预约号',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value',
                ],
                [
                    'attribute_name' => '进仓备注',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value1',
                ],
                [
                    'attribute_name' => '进仓时间',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value2',
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '签单',
            'service_name' => '签单',
            'service_code' => 'SVC-SIGN-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '签单',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"1":"纸质签单","2":"电子签单","3":"纸质签单+电子签单"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"fr-m-0004":"特快重货", "fr-m-0002":"特惠重货", "fr-m-0001":"特快零担"}'

                ]
            ]
        ],

    ],
    //德邦快运
    "debangkuaiyun" => [
        [
            'required' => false,
            'service_desc' => '保价',
            'service_name' => '保价',
            'service_code' => 'SVC-INSURE',
            'service_attributes' => [
                [
                    'attribute_name' => '保价金额',
                    'attribute_type' => 'number',
                    'attribute_code' => 'value',
                    'type_desc' => '{"min":10000,"max":"40000000"}'
                ]
            ]
        ],
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"JZKH":"快车", "JZQY_LONG":"慢车", "TZKJC":"空运"}'
                ]
            ]
        ],

    ],
    //"京广速递"
    "jinguangsudikuaijian" => [
        [
            'required' => false,
            'service_desc' => '签回单',
            'service_name' => '签回单',
            'service_code' => 'SVC-SIGN-TYPE',
            'service_attributes' => [
                [
                    'attribute_name' => '回单号',
                    'attribute_type' => 'string',
                    'attribute_code' => 'value',
                ]
            ]
        ],
    ],
    //"顺丰速运"
    "shunfeng" => [
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => str_replace(array("\t", "\n"),'',  WaybillConst::SF_PRODUCT_TYPE)  //去掉常量里面的换行符

                ]
            ]
        ],
    ],
    //苏宁
    "suning" => [
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"01":"大件配送","dstributeAndlnstall":"送装一体"}'

                ]
            ]
        ],
    ],
    //邮政电商标快
    "yzdsbk"=>[
        [
            'required' => false,
            'service_desc' => '产品类型',
            'service_name' => '产品类型',
            'service_code' => 'product_type',
            'service_attributes' => [
                [
                    'attribute_name' => '产品类型',
                    'attribute_type' => 'enum',
                    'attribute_code' => 'value',
                    'type_desc' => '{"11510":"电商标快"}'
                ]
            ]
        ],
    ]

];
