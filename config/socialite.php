<?php

use App\Constants\PlatformConst;

return [
	PlatformConst::TAOBAO => [
		'client_id'     => env('TB_CLIENT_ID', '21448879'),
		'client_secret' => env('TB_CLIENT_SECRET', '16ddc62014fcc43de2fb9c08b52ef959'),
		// 'redirect' => env('APP_DOMAIN') . '/callback/taobao',
		'redirect'      => env('TB_CLIENT_RDIRECT', 'http://kd.menggouchaquan.cn/callback/taobao_top'),
		'article_code'  => env('TB_ARTICLE_CODE', ''),
        'openapi_redirect_url' => env('TB_OPENAPI_RDIRECT'),
	],
	PlatformConst::PDD    => [
		'client_id'     => env('PDD_CLIENT_ID', '15a7b8baa8cf46d3847d5da849d328a2'),
		'client_secret' => env('PDD_CLIENT_SECRET', 'fc751d38449661137f11e72791a39fcbc48b239f'),
		'redirect' => env('APP_DOMAIN') . '/callback',
//		'redirect'      => env('APP_DOMAIN') . '/user/auth/pdd',
	],
	PlatformConst::KS     => [
		'client_id'     => env('KS_CLIENT_ID', 'ks689332221973912626'),
		'client_secret' => env('KS_CLIENT_SECRET', 'NoJsiRHs22emg8OtOFnmpA'),
		'sign_secret' => env('KS_SIGN_SECRET', 'cf87e89ac34dc9dacfc73d6f7039ba0a'),
		'redirect'      => env('KS_CALLBACK', env('APP_DOMAIN') . '/callback'),
	],
	PlatformConst::JD     => [
		'client_id'     => env('JD_CLIENT_ID', 'F2E3887D6C904AF84A5EC3D99A2A8F39'),
		'client_secret' => env('JD_CLIENT_SECRET', 'c1d662100d384ae28624f190edc889ad'),
		'code_url'      => env('JD_CODE_URL', 'https://open-oauth.jd.com/oauth2/to_login'),
		'token_url'     => env('JD_TOKEN_URL', 'https://open-oauth.jd.com/oauth2/access_token'),
		// 'redirect_url'  => env('JD_REDIRECT_URL', 'http://renrenkd.ks.chengwithle.com/callback/jd'),
		//'redirect' => env('APP_DOMAIN') . '/callback/pdd',
		'redirect'      => env('APP_DOMAIN') . '/callback/jd',
		'fus_pin'       => env('JD_FWS_PIN', 'beautyjd'),
		'service_code'  => env('JD_SERVICE_CODE', 'FW_GOODS-1409801'),
        'service_item_code'  => env('JD_SERVICE_ITEM_CODE', 'FW_GOODS-1409801-1'),
        'gateway_url'=>env('JD_GATEWAY_URL',"https://api.jd.com/routerjson")  //京东的网关地址
	],
	PlatformConst::WX     => [
		'client_id'     => env('WX_CLIENT_ID', 'wxd1d763ebbfd0f174'),
		'client_secret' => env('WX_CLIENT_SECRET', '3473b3066a7b283b765b6f289600f560'),
        'service_id'    => env('SERVICE_ID', '2575068212342849541'),
        'service_app_id'    => env('SERVICE_APP_ID', 'wxa6dae325683540ed'),
        'service_app_secret' => env('SERVICE_APP_SECRET', 'c46ccce8ff7bb9512d2e22704b99dd38'),
        'token_url'     => env('WX_TOKEN_URL', 'https://api.weixin.qq.com/cgi-bin/token'),
		'redirect'      => env('APP_DOMAIN') . '/callback',
	],
    PlatformConst::WXSP     => [
        'client_id'     => env('WX_CLIENT_ID', 'wxd1d763ebbfd0f174'),
        'client_secret' => env('WX_CLIENT_SECRET', '3473b3066a7b283b765b6f289600f560'),
        'service_id'    => env('SERVICE_ID', '2575068212342849541'),
        'service_app_id'    => env('SERVICE_APP_ID', 'wxa6dae325683540ed'),
        'service_app_secret' => env('SERVICE_APP_SECRET', 'c46ccce8ff7bb9512d2e22704b99dd38'),
        'token_url'     => env('WX_TOKEN_URL', 'https://api.weixin.qq.com/cgi-bin/token'),
        'redirect'      => env('APP_DOMAIN') . '/callback',
    ],
    PlatformConst::DY     => [
        'client_id'     => env('DY_CLIENT_ID', '6839207088506275342'),
        'client_secret' => env('DY_CLIENT_SECRET', '4c80dd27-9f2e-44aa-a5ae-c38d498e8e02'),
        'redirect'      => env('APP_DOMAIN') . '/callback',
        'service_id'    => env('SERVICE_ID', 31),
        'push_db_id'    => env('PUSH_DB_ID', ''),
    ],
    PlatformConst::XHS     => [
        'client_id'     => env('XHS_CLIENT_ID', ''),
        'client_secret' => env('XHS_CLIENT_SECRET', ''),
        'redirect'      => env('APP_DOMAIN') . '/callback',
    ],
    PlatformConst::ALBB => [
        'client_id' => env('ALBB_CLIENT_ID'),
        'client_secret' => env('ALBB_CLIENT_SECRET'),
        'redirect' => env('APP_DOMAIN') . '/callback',
        'article_code' => env('ALBB_ARTICLE_CODE', ''),
        'openapi_redirect_url' => env('ALBB_OPENAPI_RDIRECT'),
    ],
    PlatformConst::CN => [
        'client_id' => env('CN_CLIENT_ID'),
        'client_secret' => env('CN_CLIENT_SECRET'),
        'redirect' => env('CN_CLIENT_REDIRECT'),
    ],
    PlatformConst::ALC2M => [
        'client_id' => env('ALC2M_CLIENT_ID'),
        'client_secret' => env('ALC2M_CLIENT_SECRET'),
        'redirect' => env('ALC2M_CLIENT_REDIRECT'),
    ],
];
