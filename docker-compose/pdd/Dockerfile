FROM centos:7.5.1804

RUN sed -e 's|^mirrorlist=|#mirrorlist=|g' \
           -e 's|^#baseurl=http://mirror.centos.org/centos|baseurl=https://mirrors.huaweicloud.com/centos|g' \
           -i.bak \
           /etc/yum.repos.d/CentOS-*.repo \
    && yum install -y wget  \
        epel-release \
    && mv /etc/yum.repos.d/CentOS-Base.repo /etc/yum.repos.d/CentOS-Base.repo.backup \
    && wget -O /etc/yum.repos.d/CentOS-Base.repo https://mirrors.aliyun.com/repo/Centos-7.repo \
    && mv /etc/yum.repos.d/epel.repo /etc/yum.repos.d/epel.repo.backup \
    && mv /etc/yum.repos.d/epel-testing.repo /etc/yum.repos.d/epel-testing.repo.backup \
    && wget -O /etc/yum.repos.d/epel.repo https://mirrors.aliyun.com/repo/epel-7.repo \
    && yum clean all \
    && rm -rf /var/cache/yum \
    && yum makecache \
    && groupadd apps \
    && useradd -g apps apps \
    && yum install -y supervisor

# pdd 分界线

#COPY docker /tmp/docker/
ADD docker.tar.gz /tmp/

RUN yum install -y --enablerepo=updates \
            which \
            curl \
            curl-devel \
            make  \
            wget \
            gcc  \
            gcc-c++  \
            gettext \
            net-tools \
            python-setuptools \
            openssl \
            openssl-devel \
            bzip2-devel \
            zlib \
            zlib-devel \
            zip \
            unzip \
            yum-utils \
            libpng \
            libjpeg \
            libpng-devel \
            libjpeg-devel \
            ghostscript \
            libtiff \
            libtiff-devel \
            freetype \
            freetype-devel \
            autoconf \
        && yum install -y http://rpms.remirepo.net/enterprise/remi-release-7.rpm \
        && find /etc/yum.repos.d/ -name "remi*.repo" | xargs sed -i "s/#baseurl/baseurl/g" \
        && find /etc/yum.repos.d/ -name "remi*.repo" | xargs sed -i "s/mirrorlist/#mirrorlist/g" \
        && find /etc/yum.repos.d/ -name "remi*.repo" | xargs sed -i "s@http://rpms.remirepo.net@https://mirrors.huaweicloud.com/remi@g" \
        && yum makecache \
        && yum --enablerepo=remi-php73 -y install  php-cli php-fpm php-mysqlnd php-zip php-devel php-gd php-mbstring php-curl php-xml php-pear php-bcmath php-json \
        && yum install -y --enablerepo=updates \
        php-pear \
        php-devel \
    && mkdir -p /app/conf \
    # install swoole \
    && pecl install -D 'enable-sockets="no" enable-openssl="yes" enable-http2="yes" enable-mysqlnd="no" enable-swoole-json="yes" enable-swoole-curl="yes"' /tmp/docker/software/swoole-4.6.7.tgz \
    && echo "extension=swoole.so" > /etc/php.d/40-swoole.ini \
    && cd /tmp/docker/software \
    && tar zxvf nginx-1.14.0.tar.gz \
    && cd nginx-1.14.0 \
    && ./configure --prefix=/usr/local/nginx --user=apps --group=apps --with-http_stub_status_module --with-http_ssl_module --with-http_gzip_static_module \
    && make \
    && make install \
    && rm -rf nginx-1.14.0.tar.gz \
    # cron \
    && cd /tmp/docker/software/ \
    && mv supercronic-linux-amd64-v0.2.30 supercronic \
    && chmod +x supercronic \
    && mv supercronic /usr/bin/supercronic \
    # config
    && ln -s /usr/local/nginx/sbin/* /usr/local/sbin/ \
    && mkdir -p /app /usr/local/supervisor/conf.d /var/log/cron /var/log/supervisor \
    && chown -R apps:apps /app /usr/local/supervisor/conf.d /usr/local/nginx /var/log/php-fpm /var/log/cron /var/log/supervisor \
    && chmod 777 /var/run \
    && chmod 777 /var/log \
    && mkfifo /var/log/stdout \
    && chmod 777 /var/log/stdout \
    && cp /tmp/docker/supervisord/main.conf /app/conf/supervisor.conf \
    && cp /tmp/docker/php/* /app/conf \
    && cp /tmp/docker/nginx/nginx.conf /usr/local/nginx/conf/nginx.template \
    && cp /tmp/docker/crontabs/default /app/conf/cron.conf \
    && cp /tmp/docker/supervisord/supervisord.conf /etc/supervisord.conf \
    && cp /tmp/docker/entrypoint.sh /app/ \
    && chmod +x /app/entrypoint.sh \
    && ln -sf /app/conf/php.ini /etc/php.ini \
    && ln -sf /app/conf/php-fpm.conf /etc/php-fpm.conf \
    && ln -sf /app/conf/supervisor.conf /usr/local/supervisor/conf.d/main.conf \
    && ln -sf /app/conf/www.conf /etc/php-fpm.d/www.conf

# pdd 分界线

RUN wget https://mirrors.tencent.com/composer/composer.phar \
    && mkdir -p /usr/local/bin \
    && mv composer.phar /usr/local/bin/composer \
    && chmod +x /usr/local/bin/composer
# config
#RUN mkdir -p /app /usr/local/supervisor/conf.d /var/log/cron /var/log/supervisor \
#    && chown -R apps:apps /app /usr/local/supervisor/conf.d /usr/local/nginx /var/log/php-fpm /var/log/cron /var/log/supervisor \
#    && chmod 777 /var/run \
#    && chmod 777 /var/log \
#    && mkfifo /var/log/stdout \
#    && chmod 777 /var/log/stdout \
#    && cp /tmp/docker/supervisord/main.conf /usr/local/supervisor/conf.d \
#    && cp /tmp/docker/php/* /app/conf \
#    && cp /tmp/docker/nginx/nginx.conf /usr/local/nginx/conf/nginx.template \
#    && ln -sf /app/conf/php.ini /etc/php.ini \
#    && ln -sf /app/conf/php-fpm.conf /etc/php-fpm.conf \
#    && ln -sf /app/conf/www.conf /etc/php-fpm.d/www.conf

#COPY docker /tmp/docker/
#RUN cp /tmp/docker/supervisord/main.conf /usr/local/supervisor/conf.d \
#    && cp /tmp/docker/php/* /app/conf \
#    && cp /tmp/docker/nginx/nginx.conf /usr/local/nginx/conf/nginx.template \
#    && ln -sf /app/conf/php.ini /etc/php.ini \
#    && ln -sf /app/conf/php-fpm.conf /etc/php-fpm.conf \
#    && ln -sf /app/conf/www.conf /etc/php-fpm.d/www.conf \
#    && cp /tmp/docker/crontabs/default /app/conf/cron.conf \
#    && cp /tmp/docker/supervisord/supervisord.conf /etc/ \
##    && cp /tmp/docker/entrypoint.sh /tmp/docker/ \
#    && cp /tmp/docker/supervisord/main.conf /usr/local/supervisor/conf.d/ \
#    && cp /tmp/docker/entrypoint.sh /app/ \
##COPY docker/supervisord/supervisord.conf /etc/
##COPY docker/entrypoint.sh /tmp/docker/
##COPY * /app/
##COPY docker/supervisord/main.conf /usr/local/supervisor/conf.d/
#    && chmod +x /app/entrypoint.sh \
#    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
#    && echo Asia/Shanghai > /etc/timezone \
#    && yum clean all \
#    && rm -rf /var/cache/yum/*

COPY docker /tmp/docker/

ENV PHP_ENV_FILE .env
ENV SERVER_PORT 8080
ENV CODE_PATH /app

USER root
WORKDIR /app
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["start"]
