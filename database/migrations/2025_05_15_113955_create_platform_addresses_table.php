<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePlatformAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('platform_addresses', function (Blueprint $table) {
            $table->bigIncrements('id')->comment('ID');
            $table->integer('shop_id')->comment('店铺id');
            $table->integer('address_id')->comment('地址库id');
            $table->string('name', 32)->comment('收/发件人');
            $table->string('contact', 50)->comment('默认联系方式，如果是手机，这里展示手机号；如果默认联系方式选择固定电话，这里是固定电话;');
            $table->string('phone', 11)->default('')->comment('手机号码');
            $table->string('common_phone', 50)->default('')->comment('普通座机号码');
            $table->string('company_phone', 50)->default('')->comment('企业座机号码');
            $table->string('postal_code', 32)->comment('邮编编码');
            $table->string('province', 32)->comment('省');
            $table->string('city', 32)->comment('市');
            $table->string('district', 32)->comment('区');
            $table->string('town', 32)->comment('镇/街道');
            $table->string('address', 64)->comment('详细地址');
            $table->tinyInteger('is_default')->default(0)->comment('是否为退货默认');
            $table->tinyInteger('is_send_default')->default(0)->comment('是否为发货默认');
            $table->tinyInteger('link_type')->default(0)->comment('联系方式类型;0-手机，1-普通座机，2-企业座机');
            $table->string('remark', 255)->comment('地址备注信息');
            $table->dateTime('create_time')->nullable()->comment('平台创建时间');
            $table->dateTime('update_time')->nullable()->comment('平台更新时间');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('platform_addresses');
    }
}
