<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreatePackagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('packages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->comment('用户id');
            $table->integer('shop_id')->comment('店铺id');
            $table->bigInteger('merge_pid')->default(0)->comment('合单主单ID');
            $table->tinyInteger('print_status')->default(0)->comment('是否打印 0=未打印, 1=打印');
            $table->timestamp('recycled_at')->nullable()->comment('回收时间');
            $table->string('waybill_code', 255)->nullable()->comment('电子面单号');
            $table->string('tids', 255)->nullable()->comment('订单好，多个订单拼接');
            $table->string('wp_code', 64)->nullable()->comment('物流公司编码');
            $table->integer('template_id')->default(0)->comment('模板id');
            $table->string('batch_no')->nullable()->comment('批次好');
            $table->timestamps();
            $table->index(['user_id', 'shop_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('packages');
    }
}
