<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddToShopIdIndexToPrintRecords2Table extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('print_records', function (Blueprint $table) {
//            $table->index(['to_shop_id','created_at']);
            $table->index(['batch_no']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('print_records', function (Blueprint $table) {
            //
        });
    }
}
