<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class Removegoodsidx extends Migration
{
    private $idxShopIdWithData="idx_shopid_withdata";

    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('goods_skus', function (Blueprint $table) {
            $table->dropIndex('idx_outerid');
            $table->dropIndex('idx_outergoodsid');
            $table->index(['shop_id', 'sku_value', 'sku_id', 'custom_sku_value',  'outer_id','deleted_at','goods_id'], $this->idxShopIdWithData);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('goods_skus', function (Blueprint $table) {
          $table->dropIndex($this->idxShopIdWithData);
        });
    }
}
