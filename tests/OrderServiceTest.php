<?php

use ACES\TDEClient;
use App\Constants\ErrorConst;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Package;
use App\Models\PtLogistics;
use App\Models\Waybill;
use App\Services\Auth\AuthServiceManager;
use App\Services\Auth\Impl\TaobaoAuthImpl;
use App\Services\Bo\PrintDataPackBo;
use App\Services\Bo\PrintPackBo;
use App\Services\Bo\WaybillsPrintDataBo;
use App\Services\Order\Impl\TaobaoOrderImpl;
use App\Services\Order\OrderDeliveryService;
use App\Services\Order\OrderServiceManager;
use PrintingPrintDataPullData\Attribute1;
use PrintingPrintDataPullData\Param1;
use PrintingPrintDataPullData\Parameters;

/**
 * Created by PhpStorm.
 * User: wzz
 * Date: 2020/2/25
 * Time: 22:37
 */
class OrderServiceTest extends TestCase
{

    public function testTaobaoWaybill()
    {
        $requestBatchIndexArr = [
            8394394=>0
        ];
        $successInfos = [];
        $errorInfos = [];
        $responseArr = '[{"modules":{"waybill_cloud_print_response":[{"cp_code":"POSTB","object_id":"8394394","print_data":"{\"encryptedData\":\"AES: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\",\"signature\":\"MD:hWUdPtsvDbiClIbwVSlg/A==\",\"templateURL\":\"http://cloudprint.cainiao.com/template/standard/518556/6\",\"ver\":\"waybill_print_secret_version_1\"}","real_cp_code":"POSTB","waybill_code":"3241324324324","parent_waybill_code":"9876622858014"}]},"request_id":"15rftdhamvzgk"}]';
        $responseArr = json_decode($responseArr, true);
        $printPackBoList = '[{"index":0,"request_id":8394394,"package_id":8394394,"order_infos":[{"id":39001345,"user_id":1,"shop_id":1,"factory_id":0,"tid":"4072111921441143116","type":1,"express_no":null,"template_id":0,"buyer_id":"AAGCUvuWAAcIl0_xWkyKBM_a","buyer_nick":"q**","seller_nick":"1988sunjia","order_status":30,"refund_status":0,"print_status":0,"print_num":0,"printed_at":null,"print_shipping_at":null,"print_tag_at":null,"shop_title":"查米真丝工厂店","receiver_state":"湖南省","receiver_city":"湘西土家族苗族自治州","receiver_district":"永顺县","receiver_town":"灵溪镇","receiver_name":"瞿**","receiver_phone":"88f4781f43cc0f0c696090f6d5542994","receiver_zip":416700,"receiver_address":"灵*镇政府","address_md5":"a0ad0a5f54563ade94cc131634103942","address_flag":0,"payment":"89.00","total_fee":"399.00","discount_fee":"0.00","post_fee":"0.00","seller_flag":"GRAY","seller_memo":"[]","platform_memo":null,"buyer_message":"","has_buyer_message":0,"express_code":null,"num":1,"sku_num":1,"is_pre_sale":0,"village_flag":1,"goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_title_last":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","sku_value":"颜色分类:黑色;尺码:3XL","sku_value_last":"颜色分类:黑色;尺码:3XL","merge_flag":"","promise_ship_at":"2024-10-12 10:09:12","order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","send_at":null,"finished_at":null,"groupon_at":null,"locked_at":null,"recycled_at":null,"pay_at":"2024-10-09 10:09:12","is_comment":0,"smart_logistics":"","district_code":433127,"custom_print_content":"","custom_group":"","store_type":0,"send_type":0,"store_id":null,"created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:30:05","deleted_at":null,"order_code":null,"is_remote_transit":0,"is_split":0,"is_home_delivery":0,"is_live_order":0,"is_gift_order":0,"urge_shipment_at":null,"soft_remark":null,"take_waybill_at":null,"pre_shipment_status":0,"pre_shipment_at":null,"abnormal_type":0,"item_refund_status":[0],"item_refund_created_at":null,"identifier":"75758527","latest_trace":"","trace_list":"","request_id":null,"is_child_parent_order":null,"child_parent_packages_count":null,"order_item":[{"id":52306862,"user_id":1,"shop_id":1,"order_id":39001345,"tid":"4072111921441143116","oid":"4072111921441143116","type":1,"payment":"89.00","total_fee":"89.00","discount_fee":"310.00","goods_type":1,"goods_price":"399.00","goods_pic":"https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN015RqMvM2CrQFZNYPeS_!!75758527.jpg","goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_num":1,"num_iid":"************","sku_id":"5779314842251","sku_uuid":null,"sku_value":"颜色分类:黑色;尺码:3XL","outer_iid":"","outer_sku_iid":"","refund_id":"0","refund_status":0,"is_comment":0,"print_status":0,"print_num":0,"refund_created_at":null,"refund_updated_at":null,"order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null,"status":30,"waybill_code":null,"send_at":null,"first_send_at":null,"first_send_waybill_code":null,"custom_order_sku_value":null,"product_no":"","send_remain_num":1,"send_num":0,"pre_send_num":0,"author_id":null,"author_name":null,"print_tag_at":null,"print_tag_num":0,"sku_desc":"黑色;3XL","custom_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","custom_sku_value":"颜色分类:黑色;尺码:3XL","status_desc":"待发货","custom_goods":null,"custom_goods_skus":null}],"order_cipher_info":{"id":38999740,"order_id":39001345,"tid":"4072111921441143116","receiver_phone_ciphertext":"","receiver_name_ciphertext":"","receiver_address_ciphertext":"","receiver_phone_mask":"***********","receiver_name_mask":"瞿**","receiver_address_mask":"灵*镇政府","receiver_telephone_ciphertext":"","receiver_telephone_mask":"","oaid":"1nvoVcaB0yH3uxjMgvesSziaL2RvVzkBFdU2qzQtVUwVZCZrSRn5d4uqWpr4jcpFqRgXnuK","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null},"shop":{"id":1,"user_id":1,"type":1,"role_type":1,"identifier":"75758527","access_token":"70012100b40544983e1c8770a0f94937cc674400a1adb299393310ffe5c41ad3defdec22215912585362","expire_at":"2025-03-25 06:00:00","auth_at":"2024-10-09 10:29:52","auth_status":2,"login_count":469,"shop_name":"查米真丝工厂店","shop_identifier":"75758527","shop_logo":null,"name":"1988sunjia","auth_user_id":"","last_sync_page":null,"sync_switch":1,"last_sync_at":"2024-09-27 00:19:31","last_operated_at":"2024-09-23 10:01:19","last_factory_sync_at":null,"last_factory_operated_at":null,"last_goods_sync_at":"2022-07-15 01:00:03","last_refund_sync_at":null,"original_user_id":0,"inviter":0,"service_id":"","specification_id":"","shop_code":"7eeeb1116ebd2f4a11ded7cfd4b0aa3c","created_at":"2022-04-13 20:05:29","updated_at":"2024-10-09 10:31:17","deleted_at":null,"expire_days":166,"version":"Standard","version_desc":"标准版","user_extra":{"id":1,"user_id":1,"shop_id":1,"identifier":"75758527","version":"Standard","version_desc":"标准版","buy_at":"2022-04-14 11:15:07","expire_at":"2025-03-25 00:00:00","expire_days":166,"pay_amount":0,"created_at":"2022-04-14 11:15:07","updated_at":"2024-10-09 10:27:12","deleted_at":null}},"trace":null}],"master_order_info":{"id":39001345,"user_id":1,"shop_id":1,"factory_id":0,"tid":"4072111921441143116","type":1,"express_no":null,"template_id":0,"buyer_id":"AAGCUvuWAAcIl0_xWkyKBM_a","buyer_nick":"q**","seller_nick":"1988sunjia","order_status":30,"refund_status":0,"print_status":0,"print_num":0,"printed_at":null,"print_shipping_at":null,"print_tag_at":null,"shop_title":"查米真丝工厂店","receiver_state":"湖南省","receiver_city":"湘西土家族苗族自治州","receiver_district":"永顺县","receiver_town":"灵溪镇","receiver_name":"瞿**","receiver_phone":"88f4781f43cc0f0c696090f6d5542994","receiver_zip":416700,"receiver_address":"灵*镇政府","address_md5":"a0ad0a5f54563ade94cc131634103942","address_flag":0,"payment":"89.00","total_fee":"399.00","discount_fee":"0.00","post_fee":"0.00","seller_flag":"GRAY","seller_memo":"[]","platform_memo":null,"buyer_message":"","has_buyer_message":0,"express_code":null,"num":1,"sku_num":1,"is_pre_sale":0,"village_flag":1,"goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_title_last":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","sku_value":"颜色分类:黑色;尺码:3XL","sku_value_last":"颜色分类:黑色;尺码:3XL","merge_flag":"","promise_ship_at":"2024-10-12 10:09:12","order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","send_at":null,"finished_at":null,"groupon_at":null,"locked_at":null,"recycled_at":null,"pay_at":"2024-10-09 10:09:12","is_comment":0,"smart_logistics":"","district_code":433127,"custom_print_content":"","custom_group":"","store_type":0,"send_type":0,"store_id":null,"created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:30:05","deleted_at":null,"order_code":null,"is_remote_transit":0,"is_split":0,"is_home_delivery":0,"is_live_order":0,"is_gift_order":0,"urge_shipment_at":null,"soft_remark":null,"take_waybill_at":null,"pre_shipment_status":0,"pre_shipment_at":null,"abnormal_type":0,"item_refund_status":[0],"item_refund_created_at":null,"identifier":"75758527","latest_trace":"","trace_list":"","request_id":null,"is_child_parent_order":null,"child_parent_packages_count":null,"order_item":[{"id":52306862,"user_id":1,"shop_id":1,"order_id":39001345,"tid":"4072111921441143116","oid":"4072111921441143116","type":1,"payment":"89.00","total_fee":"89.00","discount_fee":"310.00","goods_type":1,"goods_price":"399.00","goods_pic":"https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN015RqMvM2CrQFZNYPeS_!!75758527.jpg","goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_num":1,"num_iid":"************","sku_id":"5779314842251","sku_uuid":null,"sku_value":"颜色分类:黑色;尺码:3XL","outer_iid":"","outer_sku_iid":"","refund_id":"0","refund_status":0,"is_comment":0,"print_status":0,"print_num":0,"refund_created_at":null,"refund_updated_at":null,"order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null,"status":30,"waybill_code":null,"send_at":null,"first_send_at":null,"first_send_waybill_code":null,"custom_order_sku_value":null,"product_no":"","send_remain_num":1,"send_num":0,"pre_send_num":0,"author_id":null,"author_name":null,"print_tag_at":null,"print_tag_num":0,"sku_desc":"黑色;3XL","custom_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","custom_sku_value":"颜色分类:黑色;尺码:3XL","status_desc":"待发货","custom_goods":null,"custom_goods_skus":null}],"order_cipher_info":{"id":38999740,"order_id":39001345,"tid":"4072111921441143116","receiver_phone_ciphertext":"","receiver_name_ciphertext":"","receiver_address_ciphertext":"","receiver_phone_mask":"***********","receiver_name_mask":"瞿**","receiver_address_mask":"灵*镇政府","receiver_telephone_ciphertext":"","receiver_telephone_mask":"","oaid":"1nvoVcaB0yH3uxjMgvesSziaL2RvVzkBFdU2qzQtVUwVZCZrSRn5d4uqWpr4jcpFqRgXnuK","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null},"shop":{"id":1,"user_id":1,"type":1,"role_type":1,"identifier":"75758527","access_token":"70012100b40544983e1c8770a0f94937cc674400a1adb299393310ffe5c41ad3defdec22215912585362","expire_at":"2025-03-25 06:00:00","auth_at":"2024-10-09 10:29:52","auth_status":2,"login_count":469,"shop_name":"查米真丝工厂店","shop_identifier":"75758527","shop_logo":null,"name":"1988sunjia","auth_user_id":"","last_sync_page":null,"sync_switch":1,"last_sync_at":"2024-09-27 00:19:31","last_operated_at":"2024-09-23 10:01:19","last_factory_sync_at":null,"last_factory_operated_at":null,"last_goods_sync_at":"2022-07-15 01:00:03","last_refund_sync_at":null,"original_user_id":0,"inviter":0,"service_id":"","specification_id":"","shop_code":"7eeeb1116ebd2f4a11ded7cfd4b0aa3c","created_at":"2022-04-13 20:05:29","updated_at":"2024-10-09 10:31:17","deleted_at":null,"expire_days":166,"version":"Standard","version_desc":"标准版","user_extra":{"id":1,"user_id":1,"shop_id":1,"identifier":"75758527","version":"Standard","version_desc":"标准版","buy_at":"2022-04-14 11:15:07","expire_at":"2025-03-25 00:00:00","expire_days":166,"pay_amount":0,"created_at":"2022-04-14 11:15:07","updated_at":"2024-10-09 10:27:12","deleted_at":null}},"trace":null},"print_order_item_bo_list":[{"order_item_info":{"id":52306862,"user_id":1,"shop_id":1,"order_id":39001345,"tid":"4072111921441143116","oid":"4072111921441143116","type":1,"payment":"89.00","total_fee":"89.00","discount_fee":"310.00","goods_type":1,"goods_price":"399.00","goods_pic":"https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN015RqMvM2CrQFZNYPeS_!!75758527.jpg","goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_num":1,"num_iid":"************","sku_id":"5779314842251","sku_uuid":null,"sku_value":"颜色分类:黑色;尺码:3XL","outer_iid":"","outer_sku_iid":"","refund_id":"0","refund_status":0,"is_comment":0,"print_status":0,"print_num":0,"refund_created_at":null,"refund_updated_at":null,"order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null,"status":30,"waybill_code":null,"send_at":null,"first_send_at":null,"first_send_waybill_code":null,"custom_order_sku_value":null,"product_no":"","send_remain_num":1,"send_num":0,"pre_send_num":0,"author_id":null,"author_name":null,"print_tag_at":null,"print_tag_num":0,"sku_desc":"黑色;3XL","custom_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","custom_sku_value":"颜色分类:黑色;尺码:3XL","status_desc":"待发货","order":{"id":39001345,"user_id":1,"shop_id":1,"factory_id":0,"tid":"4072111921441143116","type":1,"express_no":null,"template_id":0,"buyer_id":"AAGCUvuWAAcIl0_xWkyKBM_a","buyer_nick":"q**","seller_nick":"1988sunjia","order_status":30,"refund_status":0,"print_status":0,"print_num":0,"printed_at":null,"print_shipping_at":null,"print_tag_at":null,"shop_title":"查米真丝工厂店","receiver_state":"湖南省","receiver_city":"湘西土家族苗族自治州","receiver_district":"永顺县","receiver_town":"灵溪镇","receiver_name":"瞿**","receiver_phone":"88f4781f43cc0f0c696090f6d5542994","receiver_zip":416700,"receiver_address":"灵*镇政府","address_md5":"a0ad0a5f54563ade94cc131634103942","address_flag":0,"payment":"89.00","total_fee":"399.00","discount_fee":"0.00","post_fee":"0.00","seller_flag":"GRAY","seller_memo":"[]","platform_memo":null,"buyer_message":"","has_buyer_message":0,"express_code":null,"num":1,"sku_num":1,"is_pre_sale":0,"village_flag":1,"goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_title_last":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","sku_value":"颜色分类:黑色;尺码:3XL","sku_value_last":"颜色分类:黑色;尺码:3XL","merge_flag":"","promise_ship_at":"2024-10-12 10:09:12","order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","send_at":null,"finished_at":null,"groupon_at":null,"locked_at":null,"recycled_at":null,"pay_at":"2024-10-09 10:09:12","is_comment":0,"smart_logistics":"","district_code":433127,"custom_print_content":"","custom_group":"","store_type":0,"send_type":0,"store_id":null,"created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:30:05","deleted_at":null,"order_code":null,"is_remote_transit":0,"is_split":0,"is_home_delivery":0,"is_live_order":0,"is_gift_order":0,"urge_shipment_at":null,"soft_remark":null,"take_waybill_at":null,"pre_shipment_status":0,"pre_shipment_at":null,"abnormal_type":0,"item_refund_status":[0],"item_refund_created_at":null,"identifier":"75758527","latest_trace":"","trace_list":"","request_id":null,"is_child_parent_order":null,"child_parent_packages_count":null,"order_cipher_info":{"id":38999740,"order_id":39001345,"tid":"4072111921441143116","receiver_phone_ciphertext":"","receiver_name_ciphertext":"","receiver_address_ciphertext":"","receiver_phone_mask":"***********","receiver_name_mask":"瞿**","receiver_address_mask":"灵*镇政府","receiver_telephone_ciphertext":"","receiver_telephone_mask":"","oaid":"1nvoVcaB0yH3uxjMgvesSziaL2RvVzkBFdU2qzQtVUwVZCZrSRn5d4uqWpr4jcpFqRgXnuK","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null},"order_item":[{"id":52306862,"user_id":1,"shop_id":1,"order_id":39001345,"tid":"4072111921441143116","oid":"4072111921441143116","type":1,"payment":"89.00","total_fee":"89.00","discount_fee":"310.00","goods_type":1,"goods_price":"399.00","goods_pic":"https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN015RqMvM2CrQFZNYPeS_!!75758527.jpg","goods_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","goods_num":1,"num_iid":"************","sku_id":"5779314842251","sku_uuid":null,"sku_value":"颜色分类:黑色;尺码:3XL","outer_iid":"","outer_sku_iid":"","refund_id":"0","refund_status":0,"is_comment":0,"print_status":0,"print_num":0,"refund_created_at":null,"refund_updated_at":null,"order_created_at":"2024-10-09 10:09:02","order_updated_at":"2024-10-09 10:09:13","created_at":"2024-10-09 10:30:05","updated_at":"2024-10-09 10:31:54","deleted_at":null,"status":30,"waybill_code":null,"send_at":null,"first_send_at":null,"first_send_waybill_code":null,"custom_order_sku_value":null,"product_no":"","send_remain_num":1,"send_num":0,"pre_send_num":0,"author_id":null,"author_name":null,"print_tag_at":null,"print_tag_num":0,"sku_desc":"黑色;3XL","custom_title":"【109直播福利】里层桑蚕丝拉绒纯色螺纹打底裤女士保暖裤子","custom_sku_value":"颜色分类:黑色;尺码:3XL","status_desc":"待发货","custom_goods":null,"custom_goods_skus":null}],"shop":{"id":1,"user_id":1,"type":1,"role_type":1,"identifier":"75758527","access_token":"70012100b40544983e1c8770a0f94937cc674400a1adb299393310ffe5c41ad3defdec22215912585362","expire_at":"2025-03-25 06:00:00","auth_at":"2024-10-09 10:29:52","auth_status":2,"login_count":469,"shop_name":"查米真丝工厂店","shop_identifier":"75758527","shop_logo":null,"name":"1988sunjia","auth_user_id":"","last_sync_page":null,"sync_switch":1,"last_sync_at":"2024-09-27 00:19:31","last_operated_at":"2024-09-23 10:01:19","last_factory_sync_at":null,"last_factory_operated_at":null,"last_goods_sync_at":"2022-07-15 01:00:03","last_refund_sync_at":null,"original_user_id":0,"inviter":0,"service_id":"","specification_id":"","shop_code":"7eeeb1116ebd2f4a11ded7cfd4b0aa3c","created_at":"2022-04-13 20:05:29","updated_at":"2024-10-09 10:31:17","deleted_at":null,"expire_days":166,"version":"Standard","version_desc":"标准版","user_extra":{"id":1,"user_id":1,"shop_id":1,"identifier":"75758527","version":"Standard","version_desc":"标准版","buy_at":"2022-04-14 11:15:07","expire_at":"2025-03-25 00:00:00","expire_days":166,"pay_amount":0,"created_at":"2022-04-14 11:15:07","updated_at":"2024-10-09 10:27:12","deleted_at":null}},"trace":null},"custom_goods":null,"custom_goods_skus":null},"num":1}]}]';
        $printPackBoList = json_decode($printPackBoList, true);
        foreach ($responseArr as  $response) {
            if (!empty($response['modules']['waybill_cloud_print_response'])) {
                $successInfos = array_merge($successInfos, $response['modules']['waybill_cloud_print_response']);
            } else {
                $errorInfos = array_merge($errorInfos, $response);
            }
        }
        $successInfos = array_pluck($successInfos, null, 'object_id');
        $errorInfos = array_pluck($errorInfos, null, 'object_id');
        $printDataPackBoList = [];

        foreach ($printPackBoList as $printPackBo) {
            $printDataPackBo = new PrintDataPackBo();
            $printDataPackBo->request_id = $printPackBo['request_id'];
//            $printDataPackBo->copyByPrintPackBo($printPackBo);
            if (isset($successInfos[$printDataPackBo->request_id])) {
                $successInfo = $successInfos[$printDataPackBo->request_id];
                $printDataPackBo->waybill_code = $successInfo['parent_waybill_code'] ?? $successInfo['waybill_code'];
                $printDataPackBo->wp_code = $successInfo['cp_code'];
                // 子母件
                if (isset($successInfo['parent_waybill_code'])) {
                    $printDataPackBo->sub_waybill_code_arr = [$successInfo['waybill_code']];
                }
                $printData = json_decode($successInfo['print_data'], true);
                $waybillsPrintDataBo = new WaybillsPrintDataBo;
                $waybillsPrintDataBo->waybill_code = $successInfo['waybill_code'];
                $waybillsPrintDataBo->parent_waybill_code = $successInfo['parent_waybill_code'] ?? '';
                $waybillsPrintDataBo->package_id = $successInfo['object_id'];
                $waybillsPrintDataBo->encrypted_data = $printData['encryptedData'];
                $waybillsPrintDataBo->templateURL = $printData['templateURL'];
                $waybillsPrintDataBo->sign = $printData['signature'];
                $waybillsPrintDataBo->version = $printData['ver'];
                $printDataPackBo->setWaybillsPrintData($waybillsPrintDataBo);
            } elseif (isset($errorInfos[$printDataPackBo->request_id])) {
                $errInfo = $errorInfos[$printDataPackBo->request_id];
                $err_msg = $errInfo['err_msg'] . "({$errInfo['err_code']})";
                $printDataPackBo->setError(ErrorConst::PLATFORM_DY_ERROR, $err_msg);
            } else {
                $requestIndex = $requestBatchIndexArr[$printPackBo['request_id']];
                $thisResponse = $responseArr[$requestIndex];
                $msg = '';
//                try {
//                    $this->handleResponse($thisResponse);
//                } catch (\Exception $e) {
//                    $msg = $e->getMessage();
//                }
//                if (!empty($msg)) {
//                    $printDataPackBo->setError(ErrorConst::PLATFORM_DY_ERROR, $msg);
//                } else {
//                    $printDataPackBo->setError(ErrorConst::WAYBILL_GET_ERROR);
//                }
            }
            $printDataPackBoList[] = $printDataPackBo;
        }
        $result = [];
        $parentCodeMap = collect($printDataPackBoList)->groupBy('waybill_code')->toArray();
        foreach ($parentCodeMap as $parentWaybillCode => $itemList) {
            $obj = $itemList[0];
            $obj->sub_waybill_code_arr = collect($itemList)->pluck("sub_waybill_code_arr")->flatten()->unique()->toArray();
            $printList = [];
            foreach ($itemList as $item) {
                $printData = $item->getWaybillsPrintData();
                if ($printData->waybill_code == $obj->getWaybillsPrintData()->waybill_code) {
                    continue;
                }
                $printList[] = $printData;
            }
            $obj->setSubWaybillsPrintDataArr($printList);

            var_dump($obj);
//                $obj->sub_waybill_code_arr
        }
        dd();

    }

    public function testNum(){
        $s = '[{"orderId":"4075682258931275403","address":"江苏省苏州市虎丘区斜塘街道斜*街道**路苏州创意**园","waybillCodeStr":"9876656915097","express_code":"POSTB","wp_name":"邮政快递包裹","idStr":"39011189","orderIdArr":["39011189","39011190"],"items":[{"orderId":"39011189","orderItemId":"52326080","num":1},{"orderId":"39011190","orderItemId":"52326082","num":1}]},{"orderId":"4075682258931275403","address":"江苏省苏州市虎丘区斜塘街道斜*街道**路苏州创意**园","waybillCodeStr":"9876656915011","express_code":"POSTB","wp_name":"邮政快递包裹","idStr":"39011189","orderIdArr":["39011189","39011190"],"items":[{"orderId":"39011189","orderItemId":"52326080","num":1},{"orderId":"39011190","orderItemId":"52326082","num":1}]}]';
        $delivers = json_decode($s,true);
        $deliversByIdStrArr = [];
        foreach ($delivers as $deliver) {
            foreach ($deliver['orderIdArr'] as $orderId) {
                if (isset($deliversByIdStrArr[$orderId])) {
                    $deliversByIdStrArr[$orderId] = array_merge($deliversByIdStrArr[$orderId], [$deliver]);
                } else {
                    $deliversByIdStrArr[$orderId] = [$deliver];
                }
            }
        }
        dd($deliversByIdStrArr);
    }


    public function testTaobaoOrder()
    {

        $json = '{"buyer_nick":"\u632f**","buyer_open_uid":"AAFpHPSTAEFPn-lwa95mYud-","combine_logistics_details":{"combine_logistics_detail":[{"invoice_no":"9878174749163","logistics_company":"\u90ae\u653f\u5feb\u9012\u5305\u88f9","send_goods_details":{"send_goods_detail":[{"amount":1,"consign_status":0,"goods_details":[],"type":0}]},"sub_order_id":"2478640224695970670"}]},"consign_time":"2025-02-26 17:31:47","created":"2025-02-25 17:13:40","discount_fee":"0.00","extend_info":"{\"privacy\":\"1\"}","general_new_presell":false,"is_cycle_buy":false,"modified":"2025-02-26 17:31:47","new_presell":false,"no_shipping":false,"oaid":"1q5y7VoCx5cLYbgAUiawicHIc9DQ5ev2Yms56PUbwDDr7lfyOSAib7ibi1bb3kRDu5PQCQcefWsE","orders":{"order":[{"adjust_fee":"0.00","buyer_rate":false,"cid":50023725,"consign_due_time":"2_2","consign_time":"2025-02-26 17:31:47","discount_fee":"0.00","divide_order_fee":"1.00","invoice_no":"9878174749163","is_bybt_order":false,"is_daixiao":false,"is_idle":"0","is_jzfx":false,"is_oversold":false,"logistics_company":"\u90ae\u653f\u5feb\u9012\u5305\u88f9","num":1,"num_iid":698142487990,"oid":"2478640224695970670","oid_str":"2478640224695970670","order_from":"WAP,WAP","payment":"1.00","pic_path":"https:\/\/img.alicdn.com\/bao\/uploaded\/i2\/93794893\/O1CN011DT3SL1m12l3M8d5M_!!93794893.jpg","price":"1.00","refund_status":"NO_REFUND","seller_rate":false,"seller_type":"C","ship_info":{"ship_info":[{"transport_type":"express"}]},"shipping_type":"express","snapshot_url":"y:2478640224695970670_1","status":"WAIT_BUYER_CONFIRM_GOODS","title":"\u90ae\u8d39\u4e13\u62cd","total_fee":"1.00"},{"adjust_fee":"0.00","buyer_rate":false,"cid":50023725,"consign_due_time":"2_2","discount_fee":"0.00","divide_order_fee":"2.00","is_bybt_order":false,"is_daixiao":false,"is_idle":"0","is_jzfx":false,"is_oversold":false,"num":2,"num_iid":838347911356,"oid":"2478640224696970670","oid_str":"2478640224696970670","order_from":"WAP,WAP","payment":"2.00","pic_path":"https:\/\/img.alicdn.com\/bao\/uploaded\/i2\/93794893\/O1CN01kiQv361m12wcIXqv1_!!93794893.png","price":"1.00","refund_status":"NO_REFUND","seller_rate":false,"seller_type":"C","snapshot_url":"y:2478640224696970670_1","status":"WAIT_SELLER_SEND_GOODS","title":"\u8865\u5dee\u4e13\u62cd","total_fee":"2.00"}]},"pay_time":"2025-02-25 17:13:48","payment":"3.00","post_fee":"0.00","receiver_address":"\u4f59*\u8857\u9053\u666f\u745e\u5fa1\u84dd\u6e7e***\u5355\u5143*** \uff08\u9001\u8d27\u4e0a\u95e8\uff09","receiver_city":"\u676d\u5dde\u5e02","receiver_district":"\u4f59\u676d\u533a","receiver_mobile":"***********","receiver_name":"\u5434**","receiver_state":"\u6d59\u6c5f\u7701","receiver_town":"\u4f59\u676d\u8857\u9053","receiver_zip":"000000","seller_flag":0,"seller_nick":"zx475260880","seller_open_uid":"AAH6HPSTAEFPn-lwa95Bd6nd","status":"SELLER_CONSIGNED_PART","tid":"2478640224694970670","tid_str":"2478640224694970670","title":"\u56db\u91cd\u594f\u65e5\u672c\u4ee3\u8d2d","total_fee":"3.00","you_xiang":false}';
        $orders = json_decode($json,true);
        $shop = \App\Models\Shop::query()->find(71874);
        $taobaoOrderImpl = new TaobaoOrderImpl();
        $taobaoOrderImpl->setShop($shop);
        $formatToOrders = $taobaoOrderImpl->formatToOrders([$orders]);
        Order::batchSave($formatToOrders,$shop->user_id,$shop->id);
    }

    public function testMain()
    {
        $json = '{"list":[{"orderId":38954843,"waybillCode":"1","wpCode":"ZTO","items":[{"orderId":"38954843","orderItemId":"52215028","num":1},{"orderId":"38954843","orderItemId":"52215029","num":1}]},{"orderId":38954843,"waybillCode":"2","wpCode":"ZTO","items":[{"orderId":"38954843","orderItemId":"52215030","num":1}]}],"consignStatus":[{"oid":"52215028","isPartConsign":false},{"oid":"52215029","isPartConsign":false},{"oid":"52215030","isPartConsign":false}]}';
        $data = json_decode($json, true);
        $list = $data['list'];
        $orderIds = [];
        $orderItemIds = [];
        foreach ($list as $item) {
            foreach ($item['items'] as $item2) {
                $orderIds[] = $item2['orderId'];
                $orderItemIds[] = $item2['orderItemId'];
            }
        }
        $orderIds = array_unique($orderIds);
        $orderItemIds = array_unique($orderItemIds);
        $orderList = Order::query()->with('shop')->whereIn('id', $orderIds)->get();
        $orderItemList = OrderItem::query()->whereIn('id', $orderItemIds)->get();
        $orderItemIdMap = $orderItemList->groupBy("id")->toArray();
        $groupByOrderIdList = collect($list)->groupBy('orderId')->toArray();
        foreach ($groupByOrderIdList as $groupList) {
            // 有可能一个运单号有不同的主订单进来，所以要根据主订单拆开
            $ids = [];
            $orderIdMap = [];
            foreach ($groupList as $value) {
                $groupByOrderIdItems = collect($value['items'])->groupBy('orderId')->toArray();
                foreach ($groupByOrderIdItems as $tid => $items) {
                    if (!empty($orderIdMap[$tid])) {
                        $orderIdMap[$tid] =  array_merge($orderIdMap[$tid],$items);
                    } else {
                        $orderIdMap[$tid] = $items;
                    }
                }
                $ids = array_merge($ids, collect($value['items'])->pluck('orderId')->unique()->toArray());
            }
            $ids = array_unique($ids);
            foreach ($ids as $tid) {
                //一个订单一个请求
                $order = $orderList->firstWhere('id', $tid);
                $deliversRequest = [];
                $deliversRequest['tid'] = $order['tid'];
                $deliversRequest['shopId'] = $order['shop_id'];
                //组装面单信息
                $consignStatusList = [];
                $waybills = [];
                foreach ($groupList as $value) {
                    $goods = [];
                    $items = collect($value['items'])->where("orderId", $tid)->toArray();
                    foreach ($items as $item) {
                        $orderItem = $orderItemList->firstWhere('id', $item['orderItemId']);
                        $goods[] = [
                            'oid' => $orderItem['oid'],
                            'shippedNum' => $item['num'],
                            'num_iid' => $orderItem['num_iid'],
                            'sku_id' => $orderItem['sku_id'],
                            'sku_uuid' => array_get($orderItem, 'sku_uuid'),
                        ];
                    }
                    $waybills[] = [
                        'waybillCode' => $value['waybillCode'],
                        'expressCode' => $value['wpCode'],
                        'goods' => $goods,
                    ];
                }
                $matchConsignStatus = collect($data['consignStatus'])->whereIn("oid",collect($orderIdMap[$tid])->pluck("orderItemId")->toArray());
                foreach ($matchConsignStatus as $status) {
                    $consignStatusList[] = [
                        "sub_tid" => $orderItemIdMap[$status['oid']][0]['oid'],
                        "is_part_consign" => $status['isPartConsign']
                    ];
                }
                $deliversRequest['waybills'] = $waybills;
                //淘宝需要自己确定是否发完，这个入口进入的就是全部发完
                $deliversRequest['consignStatus'] = $consignStatusList;
                $splitDeliveryList[] = [
                    'shopId' => $order['shop_id'],
                    'shop' => $order['shop'],
                    'order' => clone $order,
                    'request' => $deliversRequest,
                    'tid' => $order['tid']
                ];
            }
        }
        dd();
    }


    public function testFormatToOrder()
    {
        // region json
        $json = '{"trade_fullinfo_get_response":{"trade":{"tid":"842230816345764389","tid_str":"842230816345764389","status":"WAIT_SELLER_SEND_GOODS","type":"fixed","seller_nick":"matche旗舰店","buyer_nick":"mc呀呵","created":"2020-02-09 20:06:12","modified":"2020-02-09 20:06:32","trade_attr":"{\"erpHold\":\"0\"}","adjust_fee":"0.00","alipay_no":"2020020922001199221416841979","alipay_point":0,"available_confirm_fee":"148.20","buyer_alipay_no":"185****5247","buyer_area":"未知","buyer_cod_fee":"0.00","buyer_email":"","buyer_obtain_point_fee":0,"buyer_rate":false,"cod_fee":"0.00","cod_status":"NEW_CREATED","coupon_fee":0,"commission_fee":"0.00","discount_fee":"7.80","has_post_fee":true,"is_3D":false,"is_brand_sale":false,"is_daixiao":false,"is_force_wlb":false,"is_sh_ship":false,"is_lgtype":false,"is_part_consign":false,"is_wt":false,"orders":{"order":[{"adjust_fee":"0.00","buyer_rate":false,"cid":50010167,"discount_fee":"90.00","divide_order_fee":"74.10","is_daixiao":false,"is_oversold":false,"num":1,"num_iid":574463108302,"oid":"842230816346764389","order_from":"WAP,WAP","outer_iid":"M180801-601x","outer_sku_id":"610hei32","part_mjz_discount":"3.90","payment":"78.00","pic_path":"https://img.alicdn.com/bao/uploaded/i3/362390311/O1CN018dwlJr1EATj4FY4a3_!!362390311.jpg","price":"168.00","refund_status":"NO_REFUND","seller_rate":false,"seller_type":"B","sku_id":"4167874432701","sku_properties_name":"颜色:610长裤【单件】;尺码:32[2尺5]","snapshot_url":"q:842230816346764389_1","status":"WAIT_SELLER_SEND_GOODS","title":"牛仔裤男潮牌秋冬修身小脚韩版潮流百搭加厚男士休闲裤子男裤秋季","total_fee":"78.00"},{"adjust_fee":"0.00","buyer_rate":false,"cid":50010167,"discount_fee":"90.00","divide_order_fee":"74.10","is_daixiao":false,"is_oversold":false,"num":1,"num_iid":574463108302,"oid":"842230816347764389","order_from":"WAP,WAP","outer_iid":"M180801-601x","outer_sku_id":"1830jf-hui-32","part_mjz_discount":"3.90","payment":"78.00","pic_path":"https://img.alicdn.com/bao/uploaded/i3/362390311/O1CN01fQCwU01EATg4K0CX8_!!362390311.jpg","price":"168.00","refund_status":"NO_REFUND","seller_rate":false,"seller_type":"B","sku_id":"3940173421993","sku_properties_name":"颜色:B1830九分裤【单件】;尺码:32[2尺5]","snapshot_url":"q:842230816347764389_1","status":"WAIT_SELLER_SEND_GOODS","title":"牛仔裤男潮牌秋冬修身小脚韩版潮流百搭加厚男士休闲裤子男裤秋季","total_fee":"78.00"}]},"pay_time":"2020-02-09 20:06:31","payment":"148.20","pcc_af":0,"platform_subsidy_fee":"0.00","point_fee":0,"post_fee":"0.00","promotion_details":{"promotion_detail":[{"discount_fee":"7.80","id":"842230816345764389","promotion_desc":"多一件减10元:省7.80元","promotion_id":"Tmall$shopPromotionAll-10128104737_98847888906","promotion_name":"多一件减10元"},{"discount_fee":"90.00","id":"842230816347764389","promotion_desc":"活动中:省90.00元","promotion_id":"Tmall$commonItemPromotion-10368056381_104325728089","promotion_name":"活动中"},{"discount_fee":"90.00","id":"842230816346764389","promotion_desc":"活动中:省90.00元","promotion_id":"Tmall$commonItemPromotion-10368056381_104325736131","promotion_name":"活动中"}]},"real_point_fee":0,"received_payment":"0.00","receiver_address":"勐卯镇姐告昌源珠宝城c栋17-18号 （ayao866）","receiver_city":"德宏傣族景颇族自治州","receiver_country":"","receiver_district":"瑞丽市","receiver_mobile":"13312722172","receiver_name":"苗十二","receiver_state":"云南省","receiver_town":"勐卯镇","receiver_zip":"000000","seller_alipay_no":"***<EMAIL>","seller_can_rate":false,"seller_cod_fee":"0.00","seller_email":"<EMAIL>","seller_flag":0,"seller_mobile":"16620368630","seller_name":"上海曼艾琪服饰有限公司","seller_phone":"-","seller_rate":false,"service_tags":{"logistics_tag":[{"logistic_service_tag_list":{"logistic_service_tag":[{"service_tag":"origAreaId=533102;lgType=-4","service_type":"FAST"}]},"order_id":"842230816345764389"}]},"service_type":"","shipping_type":"express","sid":"842230816345764389","snapshot_url":"q:842230816345764389_1","title":"matche旗舰店","total_fee":"336.00","trade_from":"WAP,WAP"}}}';
        $json = '{"order_id":"3722806166254650880","create_time":1726988698,"update_time":1726988823,"status":21,"order_detail":{"product_infos":[{"product_id":"10000043332999","sku_id":"1234892407","thumb_img":"https:\/\/store.mp.video.tencent-cloud.com\/161\/20304\/snscosdownload\/SH\/reserved\/6474a0cc0008238324816562a434b00b000000a100004f50","sale_price":1090,"sku_cnt":3,"title":"\u513f\u7ae5\u76ae\u7b4b\u513f\u624e\u5934\u53d1\u88d9\u8fb9\u76ae\u7b4b\u4e0d\u4f24\u53d1\u513f\u7ae5\u5c0f\u5708\u5934\u7ef3\u65e0\u75d5\u8010\u7528\u6a61\u76ae\u7b4b\u76ae\u5957","on_aftersale_sku_cnt":0,"finish_aftersale_sku_cnt":0,"sku_code":"410942148231","market_price":1090,"sku_attrs":[{"attr_key":"\u6b3e\u5f0f","attr_value":"\u6bdb\u5dfe\u5708\u5c0f\u53f7\u3010\u6625\u5929100\u6761\u3011"}],"real_price":3270,"is_discounted":false,"estimate_price":3270,"out_warehouse_id":"","sku_deliver_info":{"stock_type":0},"extra_service":{"seven_day_return":1,"freight_insurance":0},"voucher_list":[],"order_product_coupon_info_list":[],"package_sku_list":[],"delivery_deadline":1727161504,"merchant_discounted_price":0,"finder_discounted_price":0},{"product_id":"10000043332999","sku_id":"1234890418","thumb_img":"https:\/\/store.mp.video.tencent-cloud.com\/161\/20304\/snscosdownload\/SH\/reserved\/6474a0cc00078506148ea1028b34b00b000000a100004f50","sale_price":1190,"sku_cnt":2,"title":"\u513f\u7ae5\u76ae\u7b4b\u513f\u624e\u5934\u53d1\u88d9\u8fb9\u76ae\u7b4b\u4e0d\u4f24\u53d1\u513f\u7ae5\u5c0f\u5708\u5934\u7ef3\u65e0\u75d5\u8010\u7528\u6a61\u76ae\u7b4b\u76ae\u5957","on_aftersale_sku_cnt":0,"finish_aftersale_sku_cnt":0,"sku_code":"410942148231","market_price":1190,"sku_attrs":[{"attr_key":"\u6b3e\u5f0f","attr_value":"\u9ad8\u5f39\u529b\u88d9\u8fb9\u3010\u82b1\u5f6950+\u6d45\u827250\u3011"}],"real_price":2380,"is_discounted":false,"estimate_price":2380,"out_warehouse_id":"","sku_deliver_info":{"stock_type":0},"extra_service":{"seven_day_return":1,"freight_insurance":0},"voucher_list":[],"order_product_coupon_info_list":[],"package_sku_list":[],"delivery_deadline":1727161504,"merchant_discounted_price":0,"finder_discounted_price":0},{"product_id":"10000043332999","sku_id":"1234902080","thumb_img":"https:\/\/store.mp.video.tencent-cloud.com\/161\/20304\/snscosdownload\/SH\/reserved\/6474a0cc0007529a24883a878b34b00b000000a100004f50","sale_price":990,"sku_cnt":1,"title":"\u513f\u7ae5\u76ae\u7b4b\u513f\u624e\u5934\u53d1\u88d9\u8fb9\u76ae\u7b4b\u4e0d\u4f24\u53d1\u513f\u7ae5\u5c0f\u5708\u5934\u7ef3\u65e0\u75d5\u8010\u7528\u6a61\u76ae\u7b4b\u76ae\u5957","on_aftersale_sku_cnt":0,"finish_aftersale_sku_cnt":0,"sku_code":"410942148231","market_price":990,"sku_attrs":[{"attr_key":"\u6b3e\u5f0f","attr_value":"\u6bdb\u5dfe\u5708\u5c0f\u53f7\u3010\u5f69\u827250\u6839\u3011"}],"real_price":990,"is_discounted":false,"estimate_price":990,"out_warehouse_id":"","sku_deliver_info":{"stock_type":0},"extra_service":{"seven_day_return":1,"freight_insurance":0},"voucher_list":[],"order_product_coupon_info_list":[],"package_sku_list":[],"delivery_deadline":1727161504,"merchant_discounted_price":0,"finder_discounted_price":0}],"pay_info":{"payment_method":1,"prepay_id":"up_wx22150459332358f055226c842b6d450001","prepay_time":1726988699,"pay_time":1726988704,"transaction_id":"4308102246202409222014876444"},"price_info":{"product_price":6640,"order_price":6640,"freight":0,"estimate_product_price":6640,"merchant_receieve_price":6640,"merchant_discounted_price":0,"finder_discounted_price":0},"delivery_info":{"address_info":{"user_name":"\u5434**","postal_code":"311100","province_name":"\u6d59\u6c5f\u7701","city_name":"\u676d\u5dde\u5e02","county_name":"\u4f59\u676d\u533a","detail_info":"****","national_code":"888888","tel_number":"158****8855","house_number":"","virtual_order_tel_number":"","use_tel_number":0,"hash_code":"dc3a52e02ca24d1ffee428e73dfd6d99"},"delivery_product_info":[{"waybill_id":"9469768066272","delivery_id":"EMS","product_infos":[{"product_id":"10000043332999","sku_id":"1234902080","product_cnt":1}],"delivery_name":"EMS","delivery_time":1726988823,"deliver_type":1}],"ship_done_time":0,"deliver_method":0,"ewaybill_order_code":"ofiooKN1T1CWQAZ7Kl9cZAj7_vF6NsHvJdcuyiPj_vnZSjRzucYMZuNjWCHLpwvypmO1VQqMaUlQ"},"ext_info":{"customer_notes":"","merchant_notes":"","order_scene":1},"commission_infos":[],"sharer_info":{"handling_progress":1},"settle_info":{"predict_commission_fee":332},"sku_sharer_infos":[]},"aftersale_detail":{"aftersale_order_list":[],"on_aftersale_order_cnt":0},"openid":"oIVND41Dp0VPnSc9g1ilHgjsvV7A"}';
        // endregion
        $order = new \App\Services\Order\Impl\WxSpOrderImpl();
        $arr = $order->formatToOrder(json_decode($json, true));
        dd($arr);
        $this->assertArrayHasKey('tid', $arr[0]);
    }

    public function testGetTradesOrderTaobao()
    {
        $order = new TaobaoOrderImpl();
        $order->setUserId(1);
//        $data = $order->getTradesOrder(strtotime('2020-03-14 12:00:00'),strtotime('2020-03-15 12:00:00'));
        $data = $order->getTradesOrder(strtotime('2020-01-14 12:00:00'), strtotime('2020-03-15 12:00:00'));
//        $data = $order->getTradesOrderByIncr(strtotime('2020-03-14 12:00:00'),strtotime('2020-03-15 12:00:00'));
        dd($data);
    }

    /**
     * @throws \App\Exceptions\OrderException
     * <AUTHOR>
     */
    public function testGetTradesOrderPdd()
    {
        $order = new \App\Services\Order\Impl\PddOrderImpl();
        $order->setUserId(1);

//        $data = $order->getTradesOrder(strtotime('-30 minute'),time());
//        $data = $order->getOrderTraceList(['express_code'=>'YTO','express_no'=>'YT9226770493660']);

//        $data = $order->getTradesOrderByIncr(strtotime('-30 minute'),time());

        $pddClient = new PddClient(config('socialite.pdd.client_id'), config('socialite.pdd.client_secret'));
        $params = [
            'ship_code' => 'YTO',
            'tel' => '13549981742',
            'track_no' => 'YT9226770493660',
        ];
        $data = $pddClient->execute('pdd.logistics.isv.trace.notify.sub', $params);
        dd($data);
    }

    /**
     * @throws \App\Exceptions\OrderException
     * <AUTHOR>
     */
    public function testGetTradesOrderKs()
    {
        $order = new \App\Services\Order\Impl\KsOrderImpl();
//        $shop = \App\Models\Shop::query()->find(4503);
        $shop = \App\Models\Shop::query()->where('shop_code', 'c33d5dc630b057164bcda576da2defb5')->first();
        $order->setShop($shop);
//        $order->sendAddress();


//        $json = '[{"orderBaseInfo":{"oid":20210987654327,"payTime":1543843735000,"buyerImage":"https://tx2.a.yximgs.com/uhead/AB/2019/10/28/17/B13123213123.jpg","buyerOpenId":"1234567","buyerNick":"赵四","sellerOpenId":"12098765","sellerNick":"张三","expressFee":10,"discountFee":200,"totalFee":1200,"status":10,"sendTime":1543843735000,"refundTime":1543843735000,"createTime":1543843735000,"updateTime":1543843735000,"remark":"买家留言","theDayOfDeliverGoodsTime":0,"promiseTimeStampOfDelivery":1543843735000,"activityType":1,"cpsType":1,"validPromiseShipmentTimeStamp":1543843735000,"preSale":0,"recvTime":1543843735000,"coType":0,"commentStatus":1,"payType":1},"orderItemInfo":{"skuId":120987654,"relSkuId":1209876543,"skuDesc":"水果","skuNick":"12098765","itemId":10987654,"relItemId":120987766,"itemTitle":"西瓜","itemLinkUrl":"https://...","itemPicUrl":"https://...","num":1,"originalPrice":1209,"discountFee":100,"price":1109,"itemType":1,"itemPrevInfo":{"skuId":10987654,"relSkuId":110987654,"skuDesc":"水果","skuNick":"12098765"},"goodsCode":"123","warehouseCode":"111","orderItemId":123},"orderRefundList":[{"refundId":10987654,"refundStatus":10}],"orderLogisticsInfo":[{"expressNo":"SF10987654321","expressCode":1234098765}],"orderNote":{"sellerNote":[]},"orderAddress":{"consignee":"里斯","encryptedConsignee":"abcdef","desensitiseConsignee":"里*","mobile":"18765432136","encryptedMobile":"abcdef","desensitiseMobile":"187****2136","provinceCode":123455,"province":"北京","cityCode":120987,"city":"北京","districtCode":10000,"district":"上地","address":"快手","encryptedAddress":"abcdef","desensitiseAddress":"快*"}}]';
//        $data = $order->formatToOrders(json_decode($json,true));
//        dd($data);

//        $order->setUserId($shop->user_id);
//        $order->setUserId(9);
//        $order->setAccessToken('ChFvYXV0aC5hY2Nlc3NUb2tlbhJg9uUDTKpiHFSy02ovjVhXI_PtS2HBWM_F7gnoeY1K9_GU4s9e_6alkj5SYagUFK9j2gk_bqK10x3OT4Lx0eu6b1my0N-GvfqbrDQW930Ptz1jDoiPU8lbajT62M1kOBZrGhJeYh3ez_1OmqcJLgWXKJeQiGEiIGaEtsG7GOp94JdIBnUaE_IW62XahQthdmwnUwE4m-gFKAUwAQ');
//        $result = $order->getGoodsList(10, 8);

        $result = $order->getOrderInfo('2312500158212149');
        dd($result);
//        $result = $order->getTradesOrderByIncr(strtotime('2021-11-23 20:10:13'), strtotime('2021-11-23 20:20:13'));
//        $order->setShopId('1299724867');
        $data = $order->getTradesOrderByIncr(strtotime('2022-07-12 11:04:03'), strtotime('2022-07-12 11:04:05'));
        \App\Models\Order::batchSave($data, $shop->user_id, $shop->id);
        dd($data);
    }


    public function testDY()
    {
//        $printData = Order::getPrintDataAndWaybill(
//            19,
//            19,
//            4,
//            [[176621]],
//            1
//        );

        $json = '{"fulfill_status":"","sku_order_list":[{"c_biz":8,"order_id":"6940115384550233587","post_tel":"","relation_order":{"write_off_no":null,"relation_order_id":null},"mask_post_addr":{"province":{"name":"\u6e56\u5357\u7701","id":"43"},"city":{"name":"\u6c38\u5dde\u5e02","id":"431100"},"town":{"name":"\u5b81\u8fdc\u53bf","id":"431126"},"street":{"name":"\u821c\u9675\u8857\u9053","id":"*************"},"detail":"***********"},"cid":0,"tax_amount":0,"out_sku_id":"","promise_info":"{\"identifier\":\"S#2025-03-06\"}","room_id":0,"ship_time":1741137877,"sum_amount":41280,"goods_price":2948,"parent_order_id":"6940115384550233587","writeoff_info":[],"sku_car_model_desc":"","promotion_pay_amount_details":[],"biz":2,"pay_amount":39980,"modify_amount":0,"channel_payment_no":"2105012503040505300880655218","update_time":1741137878,"trade_type_desc":"\u666e\u901a","out_product_id":"0","mask_post_receiver":"\u6bd5*","sku_order_tag_ui":[{"tag_type":"","help_doc":"https:\/\/school.jinritemai.com\/doudian\/web\/article\/aHwH9wK4Je2N","extra":"","sort":0,"key":"delay_send_pay_money","text":"\u665a\u53d1\u5373\u8d54","hover_text":""},{"sort":0,"key":"supply_7day_return_label","text":"7\u5929","hover_text":"","tag_type":"","help_doc":"https:\/\/school.jinritemai.com\/doudian\/web\/article\/101835?from=shop_article","extra":""},{"key":"quick_refund_label","text":"\u6781\u901f\u9000","hover_text":"","tag_type":"","help_doc":"https:\/\/school.jinritemai.com\/doudian\/web\/article\/101792?from=shop_article","extra":"","sort":0},{"help_doc":"","extra":"","sort":0,"key":"c_biz_self_sell","text":"\u5c0f\u5e97\u81ea\u5356","hover_text":"\u8fbe\u4eba\u901a\u8fc7\u300c\u6211\u7684\u5e97\u94fa\u300d\u3001\u6216\u901a\u8fc7\u94fe\u63a5\u6dfb\u52a0\u4eba\u5e97\u4e00\u4f53\u5e97\u94fa\u5546\u54c1\uff08\u65e0\u5b98\u65b9\u5e97\u94fa\u60c5\u51b5\u4e0b\uff0c\u6dfb\u52a0\u6e20\u9053\u53f7\u5e97\u94fa\u5546\u54c1\u4e5f\u7b97\uff09\u5230\u6a71\u7a97\u3001\u89c6\u9891\u3001\u76f4\u64ad\uff0c\u4ea7\u751f\u7684\u8ba2\u5355\u4e3a\u81ea\u5356\u8ba2\u5355","tag_type":"grey"},{"help_doc":"","extra":"","sort":0,"key":"compass_source_not_ad_mark","text":"\u975e\u5e7f\u544a","hover_text":"\u8be5\u8ba2\u5355\u901a\u8fc7\u81ea\u7136\u6d41\u91cf\u6210\u4ea4","tag_type":"grey"},{"sort":0,"key":"compass_source_content_type_product_card","text":"\u5546\u54c1\u5361","hover_text":"\u8be5\u8ba2\u5355\u901a\u8fc7\u5546\u54c1\u5361\u65b9\u5f0f\u6210\u4ea4\uff0c2023\/3\/1\u8d77\u521b\u5efa\u7684\u8ba2\u5355\uff0c\u5c06\u4e8e\u7b2c\u4e8c\u5929\u66f4\u65b0\u3002\u66f4\u591a\u6d41\u91cf\u6765\u6e90\u8bf7\u5728\u8ba2\u5355\u5bfc\u51fa\u660e\u7ec6\u67e5\u770b","tag_type":"grey","help_doc":"","extra":"{\"compass_first_level_entrance\":\"search\",\"compass_first_level_entrance_text\":\"\u641c\u7d22\"}"}],"sp_product_id":"","author_id":0,"promotion_redpack_talent_amount":0,"origin_amount":2064,"campaign_info":[{"extra_map":{"conditionValue":"0","old_business_tag":"LimitTime","tool_code":"shop_time_buy","promotion_desc":"","discountType":"3","discountValue":"2064","conditionType":"2"},"campaign_id":7476458628225597732,"campaign_type":7,"campaign_amount":17680,"campaign_sub_type":0,"share_discount_cost":{"platform_cost":0,"shop_cost":17680,"author_cost":0},"campaign_name":"\u9650\u65f6\u62a2\u8d2d 20240919 11:04","campaign_activity_id":"7476458615235412287"}],"order_level":3,"create_time":1741060256,"promotion_talent_amount":0,"product_channel_info":{"product_channel_type":0,"product_channel_id":null},"promotion_detail":{"shop_discount_detail":{"full_discount_amount":0,"redpack_amount":0,"coupon_info":[{"coupon_activity_id":"7473788565177401636","extra_map":[],"share_discount_cost":{"platform_cost":0,"shop_cost":1300,"author_cost":0},"coupon_id":7477796359551746063,"coupon_type":23,"coupon_amount":1300,"coupon_meta_id":"7473788393223454987","coupon_name":"\u901a\u7528\u4f18\u60e0\u5238 20240609 23:42"}],"full_discount_info":[],"redpack_info":[],"total_amount":1300,"coupon_amount":1300},"platform_discount_detail":{"full_discount_amount":0,"gold_coin_amount":0,"official_deduction_amount":0,"coupon_info":[],"redpack_info":[],"total_amount":0,"redpack_amount":0,"user_balance_amount":0,"allowance_amount":0,"full_discount_info":[],"coupon_amount":0},"kol_discount_detail":{"total_amount":0,"coupon_amount":0,"full_discount_amount":0,"redpack_amount":0,"coupon_info":[],"full_discount_info":[],"redpack_info":[]}},"only_platform_cost_amount":0,"receive_type":0,"product_name":"\u7a7a\u8c03\u76ae\u95e8\u5e18\u8d85\u5e02\u5e97\u94fa\u5546\u7528\u78c1\u5438\u81ea\u5438\u7a7a\u8c03\u51b7\u6c14\u8f6f\u900f\u660e\u6321\u98ce\u9632\u98ce\u56db\u5b63\u9694\u65ad","spec":[{"name":"\u539a\u5ea6\u5206\u7c7b","value":"\u3010\u798f\u5229\u6b3e\u51b2\u9500\u91cf\u3011ABS\u9f99\u9aa8\u30101.6mm\u3011+\u9001\u914d\u91cd"},{"value":"\u5bbd35\u5398\u7c73;\u9ad82\u7c73\/\u4e00\u7247","name":"\u5bbd\u5ea6"}],"main_status":3,"has_tax":false,"cancel_reason":"","b_type_desc":"\u6296\u97f3\u6781\u901f\u7248","voucher_deduction_amount":0,"inventory_list":[{"warehouse_name":"","inventory_type":1,"count":20,"warehouse_type":0,"warehouse_id":"","out_warehouse_id":"","inventory_type_desc":"\u666e\u901a\u5e93\u5b58"}],"post_insurance_amount":0,"promotion_shop_amount":1300,"platform_cost_amount":0,"product_id_str":"3687776323369042224","product_id":3687776323369042224,"order_expire_time":1800,"product_pic":"https:\/\/p3-aio.ecombdimg.com\/obj\/ecom-shop-material\/BzpBZwNE_m_b8107fa9de12ff8b9357a8219d64c870_sx_124080_www800-800","supplier_id":"","reduce_stock_type_desc":"\u4e0b\u5355\u51cf\u5e93\u5b58","trade_type":0,"app_id":2329,"pay_type":4,"appointment_ship_time":0,"code":"","inventory_type_desc":"","master_sku_order_id":"","bundle_sku_info":[],"c_biz_desc":"\u5c0f\u5e97\u81ea\u5356","pre_sale_type":0,"first_cid":20072,"send_pay_desc":"-","author_name":"","quality_inspection_status":0,"warehouse_ids":[],"biz_desc":"\u5c0f\u5e97","order_type_desc":"\u666e\u901a\u8ba2\u5355","relation_sku_order_ids":[],"sku_cargo_future_stock":{"supply_sku_snapshots":[{"inventory_type":0,"batch_detail":[{"inventory_type":0,"arrive_time_type":0,"arrive_time":0,"stock_num":20,"batch_id":0}]}]},"page_id":0,"order_amount":41280,"post_amount":0,"confirm_receipt_time":0,"account_list":{"account_info":[]},"bmp_vertical_market":"","room_id_str":"0","promotion_pay_amount":0,"tax_amount_not_come_out":0,"ad_env_type":"","second_cid":21455,"author_cost_amount":0,"reduce_stock_type":1,"packing_charge_amount":0,"card_voucher":{"valid_days":0,"valid_start":0,"valid_end":0},"bmp_source":"","extra_info":[],"promotion_amount":1300,"main_status_desc":"\u5df2\u5168\u90e8\u53d1\u8d27","theme_type_desc":"-","origin_id":"99514375927_3687776323369042224","order_type":0,"pay_time":**********,"order_status_desc":"\u5df2\u53d1\u8d27","after_sale_info":{"after_sale_status":0,"after_sale_type":0,"refund_status":0},"promotion_platform_amount":0,"source_platform":"","encrypt_post_tel":"$$E9qitWyX9rELZFpv\/ydWhZR3eTLZ2MgTLHDCd0RfJkgGLYFflvYPoopqrDhiuoTYAFZMbn2gjlsVs9CVqlbLI9RNrm6nrPR3PPO1yR\/HzBn2nkc=*CgYIASAHKAESPgo8ehLiKO02OlPXb7ab+8Vg30+o+5725ap2HxzmxQRtS\/dS8KyjW\/2uFqypU5TqUaFuKFxT+wccVlqTySPkGgA=$1$$","is_comment":0,"send_pay":0,"modify_post_amount":0,"promotion_redpack_amount":0,"need_serial_number":false,"sub_b_type":3,"promotion_redpack_platform_amount":0,"logistics_receipt_time":0,"sub_b_type_desc":"H5","post_receiver":"","b_type":11,"theme_type":"0","post_addr":{"street":{"name":"\u821c\u9675\u8857\u9053","id":"*************"},"detail":"","encrypt_detail":"##UbNjkpkUnVosLwT4DSvjCsW0L3DaYxUmNiY71ysAs2ogVIzIuTJgFriMyLoYhoAKPBREF3tdX+VLfoI6OY0BSaoyXvYy6VxyQYaDgS2JDehy7j8vWgwLBHtHHtxTXOwkrbnT*CgYIASAHKAESPgo8CEPbSV5wOE5tlOZ0U7n3I+ysW0hlQ5GczfX+9VfbwLWjJ6MKBJH98y9heF4LEOQ+cLJoF7W04efF3iw4GgA=#1##","province":{"name":"\u6e56\u5357\u7701","id":"43"},"city":{"name":"\u6c38\u5dde\u5e02","id":"431100"},"town":{"name":"\u5b81\u8fdc\u53bf","id":"431126"}},"custom_properties":[],"is_activity":false,"sku_customization_info":[],"mask_post_tel":"1**********","encrypt_post_receiver":"##iwRt42VzSoOSGxHO4zTqQRRKhQvui7rfFeKZJUvmF\/BUeZ6TWpu1t2QOOYz3tgjmdBPReh4XiDWP2WAGLTxV0IoATEyDb+hEdiX33cso*CgYIASAHKAESPgo8zhn1reaZUgCCy8FGxTc6W\/Cs\/IpM85n1+fYyErl1qUIr+VdQdNobpoeXIC87dPUPcCwt8ef6GnTz1a4DGgA=#1##","out_warehouse_ids":[],"inventory_type":"","given_product_type":"","finish_time":0,"exp_ship_time":1741233137,"goods_type":0,"content_id":"0","sku_id":3406005833915138,"third_cid":27789,"item_num":20,"bmp_seller_type":"xiaodian","order_status":3,"shop_cost_amount":18980,"fourth_cid":0,"video_id":""}],"seller_remark_stars":0,"order_amount":41280,"main_status":3,"order_expire_time":1800,"shop_name":"\u946b\u6d2a\u6e90\u95e8\u5e18\u5382","promotion_detail":{"shop_discount_detail":{"redpack_info":[],"total_amount":1300,"coupon_amount":1300,"full_discount_amount":0,"redpack_amount":0,"coupon_info":[{"coupon_name":"\u901a\u7528\u4f18\u60e0\u5238 20240609 23:42","coupon_activity_id":"7473788565177401636","extra_map":[],"share_discount_cost":{"author_cost":0,"platform_cost":0,"shop_cost":1300},"coupon_id":7477796359551746063,"coupon_type":23,"coupon_amount":1300,"coupon_meta_id":"7473788393223454987"}],"full_discount_info":[]},"platform_discount_detail":{"redpack_info":[],"total_amount":0,"coupon_amount":0,"redpack_amount":0,"gold_coin_amount":0,"official_deduction_amount":0,"coupon_info":[],"full_discount_amount":0,"user_balance_amount":0,"allowance_amount":0,"full_discount_info":[]},"kol_discount_detail":{"redpack_amount":0,"coupon_info":[],"full_discount_info":[],"redpack_info":[],"total_amount":0,"coupon_amount":0,"full_discount_amount":0}},"encrypt_post_receiver":"##NrfVJYBACXpk7c0p\/UELq8Z9fRWX061G5JrLaXQ3nYvrCg3uMj3UYJzlVAEdP4esOKDjLheOr8c+Jo\/EaqrTTX29jmtnuPP\/ocIaH3+N*CgYIASAHKAESPgo8pC1teABsDwR1I7ZQeyAHCd0QbxMVTRD74vDxbqErADmtQJAvbl0H3OJRhe+iM6R37CwxtnEHrrz3wnXqGgA=#1##","post_origin_amount":0,"mask_pay_tel":"","order_tag":[],"original_shop_id":0,"post_receiver":"","supermarket_order_serial_no":"","pay_tel":"","encrypt_post_tel":"$$90QOlL15pJlceNPAmxHi5NCxx6NcHh6ozfSfDGA0ysTvYJfjlNQKo+mCkgVoBi4nJ2ef7AckZ7lmk1nc15k0eMQJXSMATtpaODWE4Uaj4Qqw+\/c=*CgYIASAHKAESPgo8bS\/6SDmA1nCdGbJ9i1KZaXaWYu1MjGlS1rcTRY0x+OQ4YzRoNw44BQip2W5VqUiu9rYwZWoXPE+nu7ezGgA=$1$$","order_recycle_info":{"appoint_recycle_end_time":0,"quoted_price":0,"subsidy_receive_status":null,"supplier_code":null,"recycle_order_id":null,"recycle_status":0,"appoint_recycle_start_time":0,"pronduct_desc":null,"supplier_name":null,"recycle_status_desc":null,"recycle_mode":0},"order_level":2,"post_insurance_amount":0,"modify_post_amount":0,"promotion_talent_amount":0,"tax_amount_not_come_out":0,"order_id":"6940115384550233587","tax_amount":0,"recommend_end_ship_time":0,"shop_order_tag_ui":[],"finish_time":0,"only_platform_cost_amount":0,"open_id":"","user_nick_name":"","greet_words":"","promise_detail":{"promise_type":1,"promise_time_detail":{"promise_recommend_start_ship_time":0,"promise_exp_ship_time":1741233137,"promise_appointment_ship_time":0,"promise_recommend_end_ship_time":0,"promise_latest_receipt_time":0},"recommend_logistics_list":[]},"post_amount":0,"shop_cost_amount":18980,"biz_desc":"\u5c0f\u5e97","order_status":3,"recommend_start_ship_time":0,"user_coordinate":{"user_coordinate_longitude":"","user_coordinate_latitude":""},"app_id":2329,"total_promotion_amount":1300,"promotion_amount":1300,"order_phase_list":[],"early_arrival":false,"accept_order_status":1,"biz":2,"receipt_date":"","cancel_reason":"","d_car_shop_biz_data":{"poi_adname":null,"poi_id":null,"poi_name":null,"poi_addr":null,"poi_tel":null,"poi_pname":null,"poi_city_name":null},"bmp_source":"","promise_info":"{\"identifier\":\"S#2025-03-06\"}","post_promotion_amount":0,"make_up_post_amount":0,"target_arrival_time":0,"order_type_desc":"\u666e\u901a\u8ba2\u5355","promotion_platform_amount":0,"encrypt_pay_tel":"","appointment_ship_time":0,"latest_receipt_time":0,"b_type_desc":"\u6296\u97f3\u6781\u901f\u7248","sub_b_type_desc":"H5","mask_post_addr":{"province":{"name":"\u6e56\u5357\u7701","id":"43"},"city":{"name":"\u6c38\u5dde\u5e02","id":"431100"},"town":{"name":"\u5b81\u8fdc\u53bf","id":"431126"},"street":{"name":"\u821c\u9675\u8857\u9053","id":"*************"},"detail":"***********"},"promotion_redpack_talent_amount":0,"packing_amount":0,"seller_words":"","address_tag_ui":[],"sub_b_type":3,"modify_amount":0,"promotion_pay_amount":0,"trade_type_desc":"\u666e\u901a","create_time":1741060256,"update_time":1741137878,"author_cost_amount":0,"user_tag_ui":[{"key":"user_profile_buy_frequency","text":"\u670d\u52a1\u4f18\u5148"},{"key":"user_profile_shop_customer_type","text":"\u5e97\u94fa\u65b0\u5ba2"}],"post_tel":"","promotion_shop_amount":1300,"ship_time":1741137877,"order_status_desc":"\u5df2\u53d1\u8d27","buyer_words":"","serial_number_list":[],"bmp_vertical_market":"","urge_deliver_times":0,"platform_cost_amount":0,"exp_ship_time":1741233137,"mask_post_tel":"1**********","mask_post_receiver":"\u6bd5*","channel_payment_no":"2105012503040505300880655218","shop_id":*********,"trade_type":0,"pay_time":**********,"b_type":11,"aweme_id":"","doudian_open_id":"1@#FyZ0y0GBhcRJVYSVEf4R0tr2FtDwWi\/upxgykZzBJ8eRBpR6LOyQ2A4EdMaMPT2jTs72p0rJapw=","order_type":0,"pay_type":4,"promotion_redpack_platform_amount":0,"main_status_desc":"\u5df2\u5168\u90e8\u53d1\u8d27","logistics_info":[{"company":"yuantong","hour_up_pickup_code":"","transit_merge_type":null,"ship_time":1741137877,"guarantee_amount":0,"sp_discount_price":0,"sp_price":0,"sp_total_price":0,"tracking_no":"YT1921154657448","delivery_id":"146854924779994381","company_name":"\u5706\u901a\u5feb\u9012","product_info":[{"sku_specs":[{"name":"\u539a\u5ea6\u5206\u7c7b","value":"\u3010\u798f\u5229\u6b3e\u51b2\u9500\u91cf\u3011ABS\u9f99\u9aa8\u30101.6mm\u3011+\u9001\u914d\u91cd"},{"name":"\u5bbd\u5ea6","value":"\u5bbd35\u5398\u7c73;\u9ad82\u7c73\/\u4e00\u7247"}],"price":2064,"sku_id":3406005833915138,"product_count":20,"product_id":3687776323369042224,"product_id_str":"3687776323369042224","product_name":"\u7a7a\u8c03\u76ae\u95e8\u5e18\u8d85\u5e02\u5e97\u94fa\u5546\u7528\u78c1\u5438\u81ea\u5438\u7a7a\u8c03\u51b7\u6c14\u8f6f\u900f\u660e\u6321\u98ce\u9632\u98ce\u56db\u5b63\u9694\u65ad","outer_sku_id":"","sku_order_id":"6940115384550233587"}],"added_services":[]},{"sp_total_price":0,"added_services":[],"hour_up_pickup_code":"","transit_merge_type":null,"delivery_id":"CO146854980231035032","company_name":"\u5706\u901a\u5feb\u9012","ship_time":1741141177,"guarantee_amount":0,"sp_discount_price":0,"sp_price":0,"tracking_no":"YT1921181872775","company":"yuantong","product_info":[{"product_id":3687776323369042224,"product_id_str":"3687776323369042224","sku_order_id":"6940115384550233587","price":2064,"sku_id":3406005833915138,"product_count":20,"product_name":"\u7a7a\u8c03\u76ae\u95e8\u5e18\u8d85\u5e02\u5e97\u94fa\u5546\u7528\u78c1\u5438\u81ea\u5438\u7a7a\u8c03\u51b7\u6c14\u8f6f\u900f\u660e\u6321\u98ce\u9632\u98ce\u56db\u5b63\u9694\u65ad","outer_sku_id":"","sku_specs":[{"name":"\u539a\u5ea6\u5206\u7c7b","value":"\u3010\u798f\u5229\u6b3e\u51b2\u9500\u91cf\u3011ABS\u9f99\u9aa8\u30101.6mm\u3011+\u9001\u914d\u91cd"},{"name":"\u5bbd\u5ea6","value":"\u5bbd35\u5398\u7c73;\u9ad82\u7c73\/\u4e00\u7247"}]}]}],"open_address_id":"#f87akHCGklX2SgTmM\/gmHEZLt9XOED\/BDY\/1YWndie6Kv9P9Leq\/oETpaRqO6qLq\/07CiMpcuxxXMajO4Up\/JPd1g3dnm0EvBIoaM89aH7DjdJ4E0\/QDwa5Olb\/VRasFc4mqbkflITk=","earliest_receipt_time":0,"user_icon":"","bmp_seller_type":"xiaodian","receipt_time_map":[],"pay_amount":39980,"promotion_redpack_amount":0,"post_addr":{"encrypt_detail":"##ZzW03SftcahUEiPOIEtH1SIjNRuoM4jA9oS69JfzT5P2g524VGeqfKFO8XChSa238zJOy5byKftW1cxxvyNwor1r\/viXDezDE\/\/C+TMddco\/Y6tyAf1Oe7P\/VMuvCjnUjKLV*CgYIASAHKAESPgo8Hyab95TB2pVMJl1VLrA8e6SF9nwVPXcy7NA1MaV5T4QS2NI453rHPY2G+2OOAI0p9dwuXbC\/YPLsG+nWGgA=#1##","province":{"name":"\u6e56\u5357\u7701","id":"43"},"city":{"name":"\u6c38\u5dde\u5e02","id":"431100"},"town":{"name":"\u5b81\u8fdc\u53bf","id":"431126"},"street":{"name":"\u821c\u9675\u8857\u9053","id":"*************"},"detail":""},"relation_shop_order_ids":[]}';
        $orderInfo = json_decode($json, true);
        $shop = \App\Models\Shop::query()->find(69861);

//        $DYApi = new \App\Services\Waybill\DY\DYApi($shop->access_token);


//        $order = Order::query()->where('tid', '4859048956136545963A')->first();
//        $waybillHistory = \App\Models\WaybillHistory::query()->where('order_no', '4859048956136545963A')->first();
//        $arr = [
//            'express_code'      => $waybillHistory->wp_code,
//            'express_no'        => $waybillHistory->waybill_code,
//        ];
//        $DYApi->getOrderTraceList($arr, $order);
//        $waybillHistory = \App\Models\WaybillHistory::query()->where('shop_id', $shop->id)->orderByDesc('id')->first();
//        $JDApi = new \App\Services\Waybill\DY\DYApi($shop->access_token);
//        $res = $JDApi->getOrderTraceList(array_merge($waybillHistory->toArray(),['express_code' => 'shentong','express_no' => '776324584163169']));
//        dd($res);

        $orderImpl = new \App\Services\Order\Impl\DyOrderImpl();
//        $order->setAccessToken('dc605657-750d-4269-aeee-50d02f32f2eb');
//        $order->setShop($shop);
//        $order->sendAddress();

//        $result = $order->getGoodsList(100,1);
//        dd($order->goodsTotalCount,$order->hasNext);
//        $result = $order->getTradesOrder(strtotime('2022-06-16 15:06:12'), strtotime('2022-06-23 15:06:12'));
//        $result = $order->getTradesOrderByIncr(strtotime('2022-11-10 18:13:10'), strtotime('2022-12-10 18:13:11'));
//        $result = $order->getGoodsList(3, 12);
//        $result[] = $order->getOrderInfo('6933213017655940505A');
//        $result[] = $order->sendByCustom('POST','order.logisticsCompanyList',[]);
        $formatToOrder = $orderImpl->formatToOrders([$orderInfo]);
        Order::batchSave($formatToOrder, $shop->user_id, $shop->id);
//        $result = $order->sendServiceInfo();
//        dd($order->hasNext, $order->getOrderTotalCount());
//        dd(json_encode($result));
    }

    public function testWx()
    {

        $shop = \App\Models\Shop::query()->find(524);
        $order = new \App\Services\Order\Impl\WxOrderImpl();
//        $order->setAccessToken('44_FWGjRgc4XuLwE1-eNMODU7EZ2LTBnN-2Ise5-uwW-fbcfj7Jl6vh5SwFCm14Foedtwhfz6wqGqi94sX5qqbEpYEeblZEVtmC0POi--MwHYX6axPpclCvAJvWnX42PtkyuZlOjW3pSA3Wkc3mOFAaADDBSN');
        $order->setShop($shop);
//        $result = $order->getGoodsList(10, 15);
//        dd($order->hasNext, $result);

        $data = $order->batchGetOrderInfo([['tid' => '3711237251217884672', 'id' => '3201977635549218304']]);
//        dd($data);
//        $startTime = strtotime('2022-04-03 09:22:28');
//        $endTime = strtotime('2022-04-03 09:22:29');
        $data = $order->getOrderInfo('');
        dd($data);
    }

    public function testWxSp()
    {

        $shop = \App\Models\Shop::query()->find(1574);
        $order = new \App\Services\Order\Impl\WxSpOrderImpl();
//        $order->setAccessToken('44_FWGjRgc4XuLwE1-eNMODU7EZ2LTBnN-2Ise5-uwW-fbcfj7Jl6vh5SwFCm14Foedtwhfz6wqGqi94sX5qqbEpYEeblZEVtmC0POi--MwHYX6axPpclCvAJvWnX42PtkyuZlOjW3pSA3Wkc3mOFAaADDBSN');
        $order->setShop($shop);
//        $params = [
//            'order_id' => '3721287537285268736',
//            'delivery_list' => [
//                [
//                    'delivery_id' => 'EMS',
//                    'waybill_id' => '8411504238407',
//                    'deliver_type' => 1,
//                    'product_infos' => [
//                        [
//                            'product_cnt' => 1,
//                            'product_id' => '10000043332999',
//                            'sku_id' => '1234896116',
//                        ]
//                    ],
//                ]
//            ]
//        ];
//        $order->sendByCustom('POST', '/channels/ec/order/delivery/send', $params, []);
//        $result = $order->getGoodsList(10, 15);
//        dd($order->hasNext, $result);
//        $result = $order->getTradesOrderByIncr(strtotime('2023-08-11 13:55:58'), strtotime('2023-08-12 13:55:58'));

        $orderJson = '{"order_id":"3727423321597423872","create_time":1744601748,"update_time":1744697332,"status":20,"order_detail":{"product_infos":[{"product_id":"10000216017534","sku_id":"**********","thumb_img":"https:\/\/wst.wxapp.tc.qq.com\/161\/20304\/snscosdownload\/SZ\/reserved\/67f8fa740001710e287961fc47af1c15000000a000004f50","sale_price":79800,"sku_cnt":1,"title":"DONGDATOU \u771f\u4e1d\u5370\u82b1\u7cfb\u5e26\u8fde\u8863\u88d9","on_aftersale_sku_cnt":0,"finish_aftersale_sku_cnt":0,"sku_code":"","market_price":79800,"sku_attrs":[{"attr_key":"\u5c3a\u7801","attr_value":"M\u301090-115\u3011"}],"real_price":79800,"estimate_price":79800,"out_warehouse_id":"","sku_deliver_info":{"stock_type":1,"predict_delivery_time":1745897748,"full_payment_presale_delivery_type":0},"extra_service":{"seven_day_return":1,"freight_insurance":1},"voucher_list":[],"order_product_coupon_info_list":[],"package_sku_list":[],"delivery_deadline":1745897748,"merchant_discounted_price":0,"finder_discounted_price":0,"product_unique_id":"3727423321597423872_001"}],"pay_info":{"payment_method":1,"prepay_id":"up_wx14113549291400c28de3a9745fd8ff0001","prepay_time":1744601749,"pay_time":1744601756,"transaction_id":"4319002583202504148540620866"},"price_info":{"product_price":79800,"order_price":79800,"freight":0,"original_order_price":79800,"estimate_product_price":79800,"merchant_receieve_price":79800,"merchant_discounted_price":0,"finder_discounted_price":0},"delivery_info":{"address_info":{"user_name":"\u845b**","postal_code":"610066","province_name":"\u56db\u5ddd\u7701","city_name":"\u6210\u90fd\u5e02","county_name":"\u6210\u534e\u533a","detail_info":"****","national_code":"888888","tel_number":"187****2006","house_number":"","virtual_order_tel_number":"","tel_number_ext_info":{"real_tel_number":"187****2006","virtual_tel_number":"13241184319-2006","virtual_tel_expire_time":1747289332},"use_tel_number":1,"hash_code":"7edeeb6e8e3f42c4bddbb1307b212049"},"delivery_product_info":[],"ship_done_time":0,"deliver_method":0,"ewaybill_order_code":"ofZ1moArwReV_4kwG5W4FbnUtueDCtrujPogcqAnkcPzKSJA_yGOPueIctm-uNdl7VgyY8fotOfw"},"ext_info":{"customer_notes":"","merchant_notes":"","finder_id":"sphxl324ZClJxVu","live_id":"export\/UzFfAgtgekIEAQAAAAAAZHUAmXlh5AAAAAstQy6ubaLX4KHWvLEZgBPEw6FwZHpMd-mIzNPgMJqghuCnzZhGF-4oTOn87IW6","order_scene":2,"commission_handling_progress":1},"commission_infos":[],"sharer_info":{"handling_progress":1},"settle_info":{"predict_commission_fee":798},"sku_sharer_infos":[],"source_infos":[{"sku_id":"**********","account_type":1,"account_id":"sphxl324ZClJxVu","sale_channel":0,"account_nickname":"\u8463\u5927\u5934\u6781\u7b80\u8bbe\u8ba1\u5e08\u5973\u88c5","content_type":0,"content_id":""}]},"aftersale_detail":{"aftersale_order_list":[],"on_aftersale_order_cnt":0},"openid":"o3DPj7GyA16KBlJaqIO0hRiqPLkw"}';
//        $orderJson2 = '{"order_id":"3727423453752075008","create_time":**********,"update_time":**********,"status":20,"order_detail":{"product_infos":[{"product_id":"**************","sku_id":"**********","thumb_img":"https:\/\/wst.wxapp.tc.qq.com\/161\/20304\/snscosdownload\/SZ\/reserved\/67edf989000df5fc0a2fa000f4f20115000000a000004f50","sale_price":79800,"sku_cnt":1,"title":"DONGDATOU \u771f\u4e1d\u9999\u4e91\u7eb1\u5370\u82b1\u8fde\u8863\u88d9","on_aftersale_sku_cnt":0,"finish_aftersale_sku_cnt":0,"sku_code":"","market_price":79800,"sku_attrs":[{"attr_key":"\u5c3a\u7801","attr_value":"M\u301090-115\u3011"}],"real_price":79800,"estimate_price":79800,"out_warehouse_id":"","sku_deliver_info":{"stock_type":1,"predict_delivery_time":**********,"full_payment_presale_delivery_type":0},"extra_service":{"seven_day_return":1,"freight_insurance":1},"voucher_list":[],"order_product_coupon_info_list":[],"package_sku_list":[],"delivery_deadline":**********,"merchant_discounted_price":0,"finder_discounted_price":0,"product_unique_id":"3727423453752075008_001"}],"pay_info":{"payment_method":1,"prepay_id":"up_wx141144132643562ee8cfd2510dd5560000","prepay_time":1744602253,"pay_time":1744602259,"transaction_id":"4321402272202504140401938913"},"price_info":{"product_price":79800,"order_price":79800,"freight":0,"original_order_price":79800,"estimate_product_price":79800,"merchant_receieve_price":79800,"merchant_discounted_price":0,"finder_discounted_price":0},"delivery_info":{"address_info":{"user_name":"\u845b**","postal_code":"610066","province_name":"\u56db\u5ddd\u7701","city_name":"\u6210\u90fd\u5e02","county_name":"\u6210\u534e\u533a","detail_info":"****","national_code":"888888","tel_number":"187****2006","house_number":"","virtual_order_tel_number":"","use_tel_number":0,"hash_code":"7edeeb6e8e3f42c4bddbb1307b212049"},"delivery_product_info":[],"ship_done_time":0,"deliver_method":0,"ewaybill_order_code":"ofhXI_YO6t90sBXHLNnZnQwWB6NKNhDL0etBx6Ngoqe5rbqKqicg8Jy6Vp5lVLqyz5mR-eWbnLyQ"},"ext_info":{"customer_notes":"","merchant_notes":"","finder_id":"sphxl324ZClJxVu","live_id":"export\/UzFfAgtgekIEAQAAAAAAZHUAmXlh5AAAAAstQy6ubaLX4KHWvLEZgBPEw6FwZHpMd-mIzNPgMJqghuCnzZhGF-4oTOn87IW6","order_scene":2,"commission_handling_progress":1},"commission_infos":[],"sharer_info":{"handling_progress":1},"settle_info":{"predict_commission_fee":798},"sku_sharer_infos":[],"source_infos":[{"sku_id":"**********","account_type":1,"account_id":"sphxl324ZClJxVu","sale_channel":0,"account_nickname":"\u8463\u5927\u5934\u6781\u7b80\u8bbe\u8ba1\u5e08\u5973\u88c5","content_type":0,"content_id":""}]},"aftersale_detail":{"aftersale_order_list":[],"on_aftersale_order_cnt":0},"openid":"o3DPj7GyA16KBlJaqIO0hRiqPLkw"}';
        $orderinfo = $order->formatToOrder(json_decode($orderJson, true));
//        $orderinfo2 = $order->formatToOrder(json_decode($orderJson2, true));
        Order::batchSave([$orderinfo],1574, 1574);
        // 1574,,,四川省,成都市,成华区,****,葛**,187****2006
        // 1574,,,四川省,成都市,成华区,****,葛**,四川省_成都市_成华区_葛**_187****2006

//        $result = $order->batchGetOrderInfo([['tid' => '3715559651146335744', 'id' => '1']]);
//        $startTime = strtotime('2022-04-03 09:22:28');
//        $endTime = strtotime('2022-04-03 09:22:29');
//        $data = $order->getOrderInfo('');
        dd($result);
    }

    public function testTaoBao()
    {
//        $shop = \App\Models\Shop::query()->find(30660);
        $shop = \App\Models\Shop::query()->where('shop_code', '9cffae80446ca2acd9c08a5d4eb4bc95')->first();

        $order = new \App\Services\Order\Impl\TaobaoOrderImpl();
        $order->setShop($shop);
//        $order->setAccessToken('7001210021674d003b81859c99b11a597b7eae668ab2a886a10e8a719e334ce23e5a9072417352013');
//        $taobaoAuthImpl = new TaobaoAuthImpl();

//        $list = $taobaoAuthImpl->refreshToken($shop,'');
        $list = $order->batchGetOrderInfo([['tid' => '3310531275911332250']]);
//        dd($list);
        // 2022-04-18 15:03:37  start:2022-05-12 13:25:02:end:2022-05-12 14:25:02
//        $authService = AuthServiceManager::create(\App\Constants\PlatformConst::TAOBAO);
//        $authService->refreshTokenByAuth($shop, '');
//        $list = $order->getTradesOrder(strtotime('2022-05-12 13:25:02'), strtotime('2022-05-12 14:25:02'));
        dd($list);
//        do {
//            $list = $order->getTradesOrder(strtotime('2022-05-12 13:25:02'), strtotime('2022-05-12 14:25:02'));
////            $list = $order->getTradesOrder(strtotime('2022-05-12 13:25:02'), strtotime('2022-05-12 14:25:02'));
//            $order->pageTurning();
//            echo 'count'.count($list).':'.$order->getPage().':'.$order->hasNext.PHP_EOL;
//        } while ($order->hasNext);
//        $list = $order->getTradesOrderByIncr(strtotime('2022-05-12 13:25:02'), strtotime('2022-05-12 14:25:02'));
    }

    public function testGetPddTemplates()
    {
        $pddApi = new \App\Services\Waybill\PDDWB\PDDWBApi('0a3567f3aeb5467b822a1a6b11cb7670d40d5d7e');
        //$type = 'pdd.cloudprint.stdtemplates.get';
        $type = 'pdd.waybill.search';
        $res = $pddApi->request($type, []);
        \Log::info('获取拼多多所有模板:' . json_encode($res));
    }

    public function testGetTBTemplates()
    {
        $tbApi = new \App\Services\Waybill\Taobao\NewTBApi('6200523f8034178aee0b00bf16901abdfd4d4a28efbd9403535343766');
        $type = 'cainiao.waybill.ii.search';
        $res = $tbApi->request($type, []);
        \Log::info('获取淘宝所有模板:' . json_encode($res));
    }

    public function testJd()
    {

        $shop = \App\Models\Shop::query()->find(1766);
        $order = new \App\Services\Order\Impl\JdOrderImpl();
        $order->setShop($shop);


        $appKey = config('socialite.jd.client_id');
        $appSecret = config('socialite.jd.client_secret');
        $tde = TDEClient::getInstance($shop['access_token'], $appKey, $appSecret);
        $orderInfo = \App\Models\Fix\Order::firstByTid('284291669082');
        $order_cipher_info = $orderInfo->orderCipherInfo;
        $decrypt = $tde->decrypt($order_cipher_info['receiver_address_ciphertext']);

//        $orderInfo = \App\Models\Fix\Order::firstByTid('284261095548');
//        $order_cipher_info = $orderInfo->orderCipherInfo;
//        $decrypt2 = $tde->decrypt($order_cipher_info['receiver_address_ciphertext']);

        dd($decrypt);

//        $list = $order->batchGetOrderInfo([['tid'=>'258519473454','id'=>'1']]);
//        $result = $order->getTradesOrderByIncr(strtotime('2022-11-10 18:13:10'), strtotime('2022-11-23 18:13:11'));

//        $list = $order->getTradesOrder(strtotime('2022-11-21 17:15:01'), strtotime('2022-11-22 17:30:01'), true);

//        $list = $order->deliveryOrdersForOpenApi('248128552448','SF','1');
        Order::batchSave($list, $shop->user_id, $shop->id);
//        $list = $order->getTradesOrderByIncr(strtotime('2022-06-16 15:59:30'), strtotime('2022-06-16 15:59:31'));
        dd($order->hasNext, $order->getOrderTotalCount());

//        $waybillHistory = \App\Models\WaybillHistory::query()->where('shop_id', $shop->id)->orderByDesc('id')->first();
//        $JDApi = new \App\Services\Waybill\JD\JDApi($shop->access_token);
//        $res = $JDApi->getOrderTraceList(array_merge($waybillHistory->toArray(),['express_code' => 'shentong','express_no' => '776324584163169']));
//        dd($res);


        $c = new JdClient();
        $c->appKey = config('socialite.jd.client_id');
        $c->appSecret = config('socialite.jd.client_secret');
        $c->accessToken = '9c63d997d99c4a6aabb32d3df649c03b2q5o';
//        $req = new PrintingTemplateGetTemplateListRequest();
//        $param1= new \PrintingTemplateGetTemplateList\Param1();
////        $param1->setTemplateId('100001');
//        $param1->setTemplateType('1');
//        $req->setParam1($param1->getInstance());
//        $resp = $c->execute($req, $c->accessToken);
        $req = new PrintingPrintDataPullDataRequest();
        $param1 = new \PrintingPrintDataPullData\Param1();
        $param1->setObjectId('1706370979793');
        $parameters = new \PrintingPrintDataPullData\ParametersFixed();
        $parameters->setKeyValue('eCustomerCode', '598568');
        $param1->setParameters($parameters);

        $wayBillInfo = new \PrintingPrintDataPullData\Attribute1();
        $wayBillInfo->setOrderNo('216993690333');
        $wayBillInfo->setPopFlag(1);
        $wayBillInfo->setWayBillCode('4280252802935');
//        $wayBillInfo->setJdWayBillCode('4280252802935');
        $param1->setWayBillInfos([$wayBillInfo]);
        $param1->setCpCode('YUNDA');
        $param11 = $param1->getInstance();
//        dd(json_encode($param11));
        $param111 = json_decode('{

    "objectId": "1606370979",
    "parameters": {
"key1": "eCustomerCode",
      "value1": "598568"    },
    "wayBillInfos": [
      {
        "orderNo": "216993690333",
        "popFlag": 1,
        "wayBillCode": "4280252802935"
      }
    ],
    "cpCode": "YUNDA"

}', true);
        $param11 = json_decode('{"@type":"com.jd.jcloud.wms.printing.dto.PullDataReqDTO","objectId":"1706370979793","parameters":{"eCustomerCode":"598568"},"wayBillInfos":[{"@type":"com.jd.jcloud.wms.printing.dto.WayBillInfo","orderNo":"216993690333","popFlag":1,"wayBillCode":"4280252802935"}],"cpCode":"YUNDA"}', true);
        $req->setParam1($param11);
        $resp = $c->execute($req, $c->accessToken);
        dd($resp);
        \Log::info('xxxxxxxxresp:' . json_encode($resp));
        dd('done');
//        include base_path("library/jos-php-open-api-sdk-2.0/JdSdk.php");
        $c = new JdClient();
        $c->appKey = config('socialite.jd.client_id');
        $c->appSecret = config('socialite.jd.client_secret');
        $c->accessToken = '1232';
        $req = new PopOrderSearchRequest();
        $req->setStartDate(date('Y-m-d H:i:s', time()));
        $req->setEndDate(date('Y-m-d H:i:s', time()));
        $req->setOrderState('1');

        $req->setOptionalFields('orderId');
        $req->setPage(1);
        $req->setPageSize(10);
        $req->setSortType(2);
        $req->setDateType(2);
        $resp = $c->execute($req, $c->accessToken);
        \Log::info('xxxxxxxxresp:' . json_encode($resp));
    }

    public function testXhs()
    {
//        $json = '{"orderType":4,"orderId":"P750668106304152151","cancelStatus":0,"receiverCityId":"","expressCompanyCode":"jtexpress","expressTrackingNo":"JT3103667806804","totalMerchantDiscount":0,"receiverCountryId":"","transferExtendInfo":{"orderDeclaredAmount":0},"createdTime":1735116106000,"shopId":"6641948651bdb100016273a6","promiseLastDeliveryTime":1735720908000,"orderTagList":[],"finishTime":0,"orderAfterSalesStatus":1,"totalPayAmount":2100,"paidTime":1735116108000,"presaleDeliveryStartTime":1735720908000,"simpleDeliveryOrderList":[{"deliveryOrderIndex":1,"expressCompanyCode":"jtexpress","expressTrackingNo":"JT3103667806804","itemIdList":["66419a36778e4b000103018e"],"skuIdList":["66419a36778e4b000103018e"],"status":6}],"planInfoName":"\u9ed8\u8ba4","skuList":[{"skuAfterSaleStatus":1,"skuSpec":"2 \u4e2a","totalPaidAmount":2100,"isChannel":false,"totalRedDiscount":0,"erpcode":"ljd2ge","skuDetailList":[{"taxPerSku":0,"quantity":1,"scSkuCode":"XHS-2AIN84L2M03K","pricePerSku":2100,"rawPricePerSku":2100,"skuName":"\u6d4b\u8bd5\u5783\u573e\u888b\u5783\u573e\u888b 2 \u4e2a","merchantDiscountPerSku":0,"paidAmountPerSku":2100,"redDiscountPerSku":0,"erpCode":"ljd2ge","depositAmountPerSku":0,"barcode":"XHS-2AIN84L2M03K","skuId":"66419a36778e4b000103018e"}],"skuTag":0,"totalMerchantDiscount":0,"skuName":"\u6d4b\u8bd5\u5783\u573e\u888b\u5783\u573e\u888b 2 \u4e2a","skuQuantity":1,"kolId":"","deliveryMode":0,"totalNetWeight":0,"totalTaxAmount":0,"skuImage":"\/\/qimg.xiaohongshu.com\/arkgoods\/1040g0ao312no2o5l2q105o804uk0aq5vqcdclg0?itemId=66419a36778e4b000103018e&imageView2\/1\/w\/320\/h\/320\/q\/90.jpeg","skuId":"66419a36778e4b000103018e"}],"deliveryTime":1735117954502,"receiverCountryName":"","merchantActualReceiveAmount":2100,"logistics":"red_auto","shopName":"\u6d4b\u8bd5kdx\u7684\u5e97","orderStatus":6,"presaleDeliveryEndTime":1735720908000,"boundExtendInfo":{"zoneCodes":[],"productValue":0,"payAmount":0,"shippingFee":0,"discountAmount":0,"taxAmount":0},"paymentType":7,"receiverDistrictId":"","totalDepositAmount":0,"outTradeNo":"2024122523001458901429738015","totalNetWeightAmount":0,"outPromotionAmount":0,"logisticsMode":1,"unpack":false,"totalChangePriceAmount":0,"receiverProvinceName":"\u6d59\u6c5f\u7701","totalRedDiscount":0,"openAddressId":"4288005f94407065b2d1dab6373667ae","sellerRemarkFlag":0,"totalShippingFree":0,"updateTime":1735117954860,"userId":"610027a800000000010168bf","receiverProvinceId":"","cancelTime":0,"receiverCityName":"\u676d\u5dde\u5e02","receiverDistrictName":"\u4f59\u676d\u533a","planInfoId":"664194cc983d1300018ded27"}';

        $shop = \App\Models\Shop::query()->find(9000178);
        $service = new \App\Services\Order\Impl\XhsOrderImpl();
        $service->setShop($shop);
//        $goods = $service->getGoodsList(100, 1);
//        $res = $service->formatToOrder(json_decode($json, true));
        $res = $service->batchGetOrderInfo([['tid' => 'P751646956692195871']]);
        dd($res);
//        $service->getGoodsListByGoodsId($goodsIds);
    }

    public function testAlbb()
    {
        $orderService = OrderServiceManager::create();
        $orderService->setAccessToken('1e77a7ed-53eb-4903-ba4a-f38d7d155660');
        $res = $orderService->sendByCustom("POST_FORM", 'com.alibaba.account:alibaba.account.basic', [], []);

        dd($res);
    }
    public function testAlc2m()
    {
        $orderService = new \App\Services\Order\Impl\Alc2mOrderImpl();
        $orderService->setAccessToken('9bea2afc-d153-4f2c-9ca9-996eb721621c');

        $res = $orderService->getTradesOrderByIncr(strtotime('2025-03-20 11:17:36'),strtotime('2025-03-20 12:17:36'));

        dd($res);
    }
}
